import { useState, useEffect } from 'react'
import { socketEvents, socketActions } from './utils/socket'

function App() {
  const [connected, setConnected] = useState(false)
  const [sessionId, setSessionId] = useState<string | null>(null)
  const [logs, setLogs] = useState<string[]>([])
  const [backendStatus, setBackendStatus] = useState<'checking' | 'online' | 'offline'>('checking')

  useEffect(() => {
    // Check backend health
    const checkBackend = async () => {
      try {
        const response = await fetch('/api/health')
        if (response.ok) {
          setBackendStatus('online')
        } else {
          setBackendStatus('offline')
        }
      } catch (error) {
        setBackendStatus('offline')
      }
    }

    checkBackend()

    // Socket event listeners
    socketEvents.onConnect(() => {
      setConnected(true)
      addLog('Connected to backend')
    })

    socketEvents.onDisconnect(() => {
      setConnected(false)
      addLog('Disconnected from backend')
    })

    socketEvents.onConnected((data) => {
      addLog(`Backend says: ${data.message}`)
    })

    socketEvents.onLog((data) => {
      addLog(`[${data.level.toUpperCase()}] ${data.message}`)
    })

    // Cleanup
    return () => {
      socketEvents.off('connect')
      socketEvents.off('disconnect')
      socketEvents.off('connected')
      socketEvents.off('log')
    }
  }, [])

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, `[${timestamp}] ${message}`])
  }

  const createTestSession = () => {
    // This will be replaced with actual session creation API call
    const testSessionId = `test-${Date.now()}`
    setSessionId(testSessionId)
    socketActions.joinSession(testSessionId)
    addLog(`Created test session: ${testSessionId}`)
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <header className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            AI Coder Agent
          </h1>
          <p className="text-gray-600">
            An AI-powered coding assistant for repository analysis and modification
          </p>
        </header>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Status Panel */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">System Status</h2>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span>Backend API:</span>
                <span className={`px-2 py-1 rounded text-sm ${
                  backendStatus === 'online' ? 'bg-green-100 text-green-800' :
                  backendStatus === 'offline' ? 'bg-red-100 text-red-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {backendStatus}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span>Socket Connection:</span>
                <span className={`px-2 py-1 rounded text-sm ${
                  connected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {connected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span>Session:</span>
                <span className={`px-2 py-1 rounded text-sm ${
                  sessionId ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {sessionId || 'None'}
                </span>
              </div>
            </div>

            <button
              onClick={createTestSession}
              disabled={!connected}
              className="mt-4 w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              Create Test Session
            </button>
          </div>

          {/* Logs Panel */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Activity Logs</h2>
            <div className="bg-gray-50 rounded p-4 h-64 overflow-y-auto">
              {logs.length === 0 ? (
                <p className="text-gray-500 text-sm">No logs yet...</p>
              ) : (
                <div className="space-y-1">
                  {logs.map((log, index) => (
                    <div key={index} className="text-sm font-mono text-gray-700">
                      {log}
                    </div>
                  ))}
                </div>
              )}
            </div>
            <button
              onClick={() => setLogs([])}
              className="mt-2 text-sm text-gray-500 hover:text-gray-700"
            >
              Clear logs
            </button>
          </div>
        </div>

        <div className="mt-8 text-center text-gray-500 text-sm">
          Phase 1: Foundation Setup Complete ✓
        </div>
      </div>
    </div>
  )
}

export default App
