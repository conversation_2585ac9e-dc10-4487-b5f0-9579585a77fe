"""
File Operations Tools for Pydantic AI Migration

This module provides standalone file operation tools that can be registered
with Pydantic AI agents, avoiding circular import issues.
"""

import logging
from pathlib import Path
from typing import Optional, List

import logfire
from pydantic_ai import RunContext

from ..dependencies import CodingDependencies
from ..models import FileOperationResult

logger = logging.getLogger(__name__)


async def read_file(
    ctx: RunContext[CodingDependencies], 
    file_path: str,
    encoding: Optional[str] = None
) -> FileOperationResult:
    """
    Read file content with encoding detection and error handling.
    
    Args:
        ctx: Run context with dependencies
        file_path: Path to the file to read
        encoding: Optional encoding override
        
    Returns:
        FileOperationResult with file content and metadata
    """
    with logfire.span("read_file", file_path=file_path):
        try:
            result = await ctx.deps.file_operations.read_file(file_path, encoding)
            
            if result['status'] == 'success':
                logfire.info("File read successfully", file_size=len(result['content']))
                return FileOperationResult(
                    operation="read_file",
                    file_path=file_path,
                    success=True,
                    content=result['content'],
                    metadata=result['metadata']
                )
            else:
                logfire.error("File read failed", error=result.get('error'))
                return FileOperationResult(
                    operation="read_file",
                    file_path=file_path,
                    success=False,
                    error_message=result.get('error')
                )
                
        except Exception as e:
            logger.error(f"Failed to read file {file_path}: {e}")
            logfire.error("File read exception", error=str(e))
            return FileOperationResult(
                operation="read_file",
                file_path=file_path,
                success=False,
                error_message=str(e)
            )


async def write_file(
    ctx: RunContext[CodingDependencies],
    file_path: str,
    content: str,
    encoding: str = 'utf-8',
    create_dirs: bool = True
) -> FileOperationResult:
    """
    Write file content with atomic operations and backup support.
    
    Args:
        ctx: Run context with dependencies
        file_path: Path to the file to write
        content: Content to write to the file
        encoding: File encoding (default: utf-8)
        create_dirs: Whether to create parent directories
        
    Returns:
        FileOperationResult with operation status
    """
    with logfire.span("write_file", file_path=file_path, content_length=len(content)):
        try:
            result = await ctx.deps.file_operations.write_file(
                file_path, content, encoding, create_dirs
            )
            
            if result['status'] == 'success':
                logfire.info("File written successfully", file_size=result.get('size'))
                return FileOperationResult(
                    operation="write_file",
                    file_path=file_path,
                    success=True,
                    metadata={
                        'size': result.get('size'),
                        'backup_created': result.get('backup_created'),
                        'backup_path': result.get('backup_path')
                    }
                )
            else:
                logfire.error("File write failed", error=result.get('error'))
                return FileOperationResult(
                    operation="write_file",
                    file_path=file_path,
                    success=False,
                    error_message=result.get('error')
                )
                
        except Exception as e:
            logger.error(f"Failed to write file {file_path}: {e}")
            logfire.error("File write exception", error=str(e))
            return FileOperationResult(
                operation="write_file",
                file_path=file_path,
                success=False,
                error_message=str(e)
            )


async def create_file(
    ctx: RunContext[CodingDependencies],
    file_path: str,
    content: str = ""
) -> FileOperationResult:
    """
    Create a new file with optional content.
    
    Args:
        ctx: Run context with dependencies
        file_path: Path to the new file
        content: Initial content for the file
        
    Returns:
        FileOperationResult with operation status
    """
    with logfire.span("create_file", file_path=file_path):
        try:
            result = await ctx.deps.file_operations.create_file(file_path, content)
            
            if result['status'] == 'success':
                logfire.info("File created successfully")
                return FileOperationResult(
                    operation="create_file",
                    file_path=file_path,
                    success=True,
                    content=content,
                    metadata=result.get('metadata', {})
                )
            else:
                logfire.error("File creation failed", error=result.get('error'))
                return FileOperationResult(
                    operation="create_file",
                    file_path=file_path,
                    success=False,
                    error_message=result.get('error')
                )
                
        except Exception as e:
            logger.error(f"Failed to create file {file_path}: {e}")
            logfire.error("File creation exception", error=str(e))
            return FileOperationResult(
                operation="create_file",
                file_path=file_path,
                success=False,
                error_message=str(e)
            )


async def delete_file(
    ctx: RunContext[CodingDependencies],
    file_path: str,
    create_backup: bool = True
) -> FileOperationResult:
    """
    Delete a file with optional backup creation.
    
    Args:
        ctx: Run context with dependencies
        file_path: Path to the file to delete
        create_backup: Whether to create a backup before deletion
        
    Returns:
        FileOperationResult with operation status
    """
    with logfire.span("delete_file", file_path=file_path, create_backup=create_backup):
        try:
            result = await ctx.deps.file_operations.delete_file(file_path, create_backup)
            
            if result['status'] == 'success':
                logfire.info("File deleted successfully", backup_created=result.get('backup_created'))
                return FileOperationResult(
                    operation="delete_file",
                    file_path=file_path,
                    success=True,
                    metadata={
                        'backup_created': result.get('backup_created'),
                        'backup_path': result.get('backup_path')
                    }
                )
            else:
                logfire.error("File deletion failed", error=result.get('error'))
                return FileOperationResult(
                    operation="delete_file",
                    file_path=file_path,
                    success=False,
                    error_message=result.get('error')
                )
                
        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {e}")
            logfire.error("File deletion exception", error=str(e))
            return FileOperationResult(
                operation="delete_file",
                file_path=file_path,
                success=False,
                error_message=str(e)
            )


async def list_directory(
    ctx: RunContext[CodingDependencies],
    directory_path: str,
    recursive: bool = False,
    include_hidden: bool = False
) -> FileOperationResult:
    """
    List directory contents with optional recursive listing.
    
    Args:
        ctx: Run context with dependencies
        directory_path: Path to the directory to list
        recursive: Whether to list recursively
        include_hidden: Whether to include hidden files
        
    Returns:
        FileOperationResult with directory listing
    """
    with logfire.span("list_directory", directory_path=directory_path, recursive=recursive):
        try:
            result = await ctx.deps.file_operations.list_directory(
                directory_path, recursive, include_hidden
            )
            
            if result['status'] == 'success':
                files = result.get('files', [])
                logfire.info("Directory listed successfully", file_count=len(files))
                return FileOperationResult(
                    operation="list_directory",
                    file_path=directory_path,
                    success=True,
                    metadata={
                        'files': files,
                        'total_files': len(files),
                        'recursive': recursive,
                        'include_hidden': include_hidden
                    }
                )
            else:
                logfire.error("Directory listing failed", error=result.get('error'))
                return FileOperationResult(
                    operation="list_directory",
                    file_path=directory_path,
                    success=False,
                    error_message=result.get('error')
                )
                
        except Exception as e:
            logger.error(f"Failed to list directory {directory_path}: {e}")
            logfire.error("Directory listing exception", error=str(e))
            return FileOperationResult(
                operation="list_directory",
                file_path=directory_path,
                success=False,
                error_message=str(e)
            )
