import React, { useState, useEffect } from 'react';
import { NavigationSidebar } from './NavigationSidebar';
import { StatusBar } from './StatusBar';
import { ChatInterface } from '../features/ChatInterface';
import { FileExplorer } from '../features/FileExplorer';
import { SystemHealth } from '../features/SystemHealth';
import { ProjectDashboard } from '../features/ProjectDashboard';
import { ProjectCreationWizard } from '../features/ProjectCreationWizard';
import { AIPlanningCanvas } from '../features/AIPlanningCanvas';
import { SessionDashboard } from '../features/SessionDashboard';
import { ContextOptimizer } from '../features/ContextOptimizer';

interface AppShellProps {
  children?: React.ReactNode;
}

type Theme = 'spring' | 'summer' | 'autumn' | 'winter';
type Layout = 'chat' | 'code' | 'dashboard' | 'analytics' | 'projects' | 'planning' | 'sessions' | 'context';

export const AppShell: React.FC<AppShellProps> = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [currentTheme, setCurrentTheme] = useState<Theme>('spring');
  const [currentLayout, setCurrentLayout] = useState<Layout>('projects');
  const [showProjectWizard, setShowProjectWizard] = useState(false);
  const [systemHealth, setSystemHealth] = useState({
    api: 'checking',
    database: 'checking',
    services: 'checking'
  });

  // Check system health on mount
  useEffect(() => {
    checkSystemHealth();
    const interval = setInterval(checkSystemHealth, 30000); // Check every 30s
    return () => clearInterval(interval);
  }, []);

  const checkSystemHealth = async () => {
    try {
      // Check API health
      const apiResponse = await fetch('/api/health');
      const apiHealthy = apiResponse.ok;

      // Check database health
      const dbResponse = await fetch('/api/database/health');
      const dbHealthy = dbResponse.ok;

      // Check services health
      const servicesResponse = await fetch('/api/services/health');
      const servicesHealthy = servicesResponse.ok;

      setSystemHealth({
        api: apiHealthy ? 'healthy' : 'error',
        database: dbHealthy ? 'healthy' : 'error',
        services: servicesHealthy ? 'healthy' : 'error'
      });

      // Auto-adjust theme based on system state
      if (apiHealthy && dbHealthy && servicesHealthy) {
        setCurrentTheme('spring'); // All good - growth theme
      } else if (apiHealthy || dbHealthy || servicesHealthy) {
        setCurrentTheme('autumn'); // Partial issues - wisdom theme
      } else {
        setCurrentTheme('winter'); // Problems - clarity theme
      }
    } catch (error) {
      console.error('Health check failed:', error);
      setSystemHealth({
        api: 'error',
        database: 'error',
        services: 'error'
      });
      setCurrentTheme('winter');
    }
  };

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const switchLayout = (layout: Layout) => {
    setCurrentLayout(layout);
  };

  return (
    <div className={`app-shell theme-${currentTheme} min-h-screen bg-mist-gradient`}>
      {/* 🌳 Forest Canopy - Main Container */}
      <div className="flex h-screen overflow-hidden">
        
        {/* 🌿 Tree Trunk - Navigation Sidebar */}
        <NavigationSidebar
          collapsed={sidebarCollapsed}
          onToggle={toggleSidebar}
          currentLayout={currentLayout}
          onLayoutChange={switchLayout}
          systemHealth={systemHealth}
        />

        {/* 🍃 Forest Clearing - Main Content Area */}
        <main className={`flex-1 flex flex-col transition-all duration-300 ${
          sidebarCollapsed ? 'ml-16' : 'ml-64'
        }`}>
          
          {/* Content Header */}
          <header className="bg-crystal-clear border-b border-growth-accent/20 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <h1 className="text-h2 text-forest-primary animate-bloom">
                  🌿 DeepNexus AI Coder
                </h1>
                <div className="flex items-center space-x-2">
                  {Object.entries(systemHealth).map(([service, status]) => (
                    <div
                      key={service}
                      className={`w-2 h-2 rounded-full animate-breathe ${
                        status === 'healthy' ? 'bg-growth-accent' :
                        status === 'error' ? 'bg-crimson-alert' :
                        'bg-bloom-highlight'
                      }`}
                      title={`${service}: ${status}`}
                    />
                  ))}
                </div>
              </div>
              
              {/* Theme Selector */}
              <div className="flex items-center space-x-2">
                {(['spring', 'summer', 'autumn', 'winter'] as Theme[]).map((theme) => (
                  <button
                    key={theme}
                    onClick={() => setCurrentTheme(theme)}
                    className={`w-8 h-8 rounded-full transition-all duration-300 hover-bloom ${
                      currentTheme === theme ? 'ring-2 ring-focus-ring' : ''
                    } ${
                      theme === 'spring' ? 'bg-growth-accent' :
                      theme === 'summer' ? 'bg-meadow-bright' :
                      theme === 'autumn' ? 'bg-amber-data' :
                      'bg-shadow-deep'
                    }`}
                    title={`Switch to ${theme} theme`}
                  />
                ))}
              </div>
            </div>
          </header>

          {/* Dynamic Content Area */}
          <div className="flex-1 flex overflow-hidden">
            
            {/* Left Panel - File Explorer (when in code mode) */}
            {currentLayout === 'code' && (
              <div className="w-80 border-r border-growth-accent/20 bg-crystal-clear">
                <FileExplorer />
              </div>
            )}

            {/* Main Content */}
            <div className="flex-1 flex flex-col">
              {currentLayout === 'chat' && <ChatInterface />}
              {currentLayout === 'code' && (
                <div className="flex-1 flex">
                  <div className="flex-1">
                    {/* Code Editor will go here */}
                    <div className="h-full bg-shadow-deep/5 flex items-center justify-center">
                      <div className="text-center animate-bloom">
                        <div className="text-6xl mb-4">🌳</div>
                        <h3 className="text-h3 text-forest-primary mb-2">Code Editor</h3>
                        <p className="text-earth-base/70">Coming soon in Phase 2!</p>
                      </div>
                    </div>
                  </div>
                  <div className="w-96 border-l border-growth-accent/20">
                    <ChatInterface />
                  </div>
                </div>
              )}
              {currentLayout === 'dashboard' && (
                <div className="flex-1 p-6">
                  <SystemHealth />
                </div>
              )}
              {currentLayout === 'projects' && (
                <div className="flex-1 p-6">
                  <ProjectDashboard />
                </div>
              )}
              {currentLayout === 'planning' && (
                <div className="flex-1">
                  <AIPlanningCanvas />
                </div>
              )}
              {currentLayout === 'sessions' && (
                <div className="flex-1 p-6">
                  <SessionDashboard />
                </div>
              )}
              {currentLayout === 'context' && (
                <div className="flex-1 p-6">
                  <ContextOptimizer />
                </div>
              )}
              {currentLayout === 'analytics' && (
                <div className="flex-1 p-6">
                  {/* Analytics Dashboard will go here */}
                  <div className="h-full bg-mist-gradient rounded-xl flex items-center justify-center">
                    <div className="text-center animate-bloom">
                      <div className="text-6xl mb-4">📊</div>
                      <h3 className="text-h3 text-forest-primary mb-2">Analytics Dashboard</h3>
                      <p className="text-earth-base/70">Coming soon in Phase 4!</p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Right Panel - Context & Actions */}
            <div className="w-80 border-l border-growth-accent/20 bg-crystal-clear">
              {/* Context Management Panel */}
              <div className="p-4 border-b border-growth-accent/20">
                <h3 className="text-lg font-semibold text-forest-primary mb-3">
                  🎯 Smart Context
                </h3>
                <div className="space-y-2">
                  <div className="bg-mist-gradient rounded-lg p-3 hover-bloom cursor-pointer">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-earth-base">Context Size</span>
                      <span className="text-sm font-mono text-growth-accent">2.4KB</span>
                    </div>
                    <div className="w-full bg-frost-light rounded-full h-2 mt-2">
                      <div className="bg-growth-gradient h-2 rounded-full animate-grow" style={{width: '60%'}}></div>
                    </div>
                  </div>
                  
                  <button className="w-full bg-growth-gradient text-crystal-clear rounded-lg py-2 px-3 text-sm font-medium hover-bloom transition-all">
                    🔄 Optimize Context
                  </button>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="p-4">
                <h3 className="text-lg font-semibold text-forest-primary mb-3">
                  ⚡ Quick Actions
                </h3>
                <div className="space-y-2">
                  {[
                    { icon: '🔍', label: 'Analyze Code', action: 'analyze' },
                    { icon: '🛠️', label: 'Refactor', action: 'refactor' },
                    { icon: '🐛', label: 'Debug', action: 'debug' },
                    { icon: '📝', label: 'Generate Docs', action: 'docs' },
                    { icon: '🧪', label: 'Write Tests', action: 'tests' }
                  ].map((item) => (
                    <button
                      key={item.action}
                      className="w-full flex items-center space-x-3 p-3 rounded-lg bg-mist-gradient hover:bg-growth-accent/10 transition-all hover-bloom text-left"
                    >
                      <span className="text-lg">{item.icon}</span>
                      <span className="text-sm text-earth-base">{item.label}</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>

      {/* 🌱 Root System - Status Bar */}
      <StatusBar
        systemHealth={systemHealth}
        currentTheme={currentTheme}
        onThemeChange={setCurrentTheme}
      />

      {/* Project Creation Wizard */}
      <ProjectCreationWizard
        isOpen={showProjectWizard}
        onClose={() => setShowProjectWizard(false)}
        onProjectCreated={(project) => {
          console.log('New project created:', project);
          // Refresh projects if we're on the projects page
          if (currentLayout === 'projects') {
            window.location.reload(); // Simple refresh for now
          }
        }}
      />
    </div>
  );
};
