interactions:
- request:
    body: '{"messages": [{"role": "user", "content": [{"text": "What is the capital of France?"}]}], "system": [{"text": "You
      are a helpful chatbot."}], "inferenceConfig": {"topP": 0.5}}'
    headers:
      amz-sdk-invocation-id:
      - !!binary |
        Yzg0YTcyNjUtYTJhNi00OGJiLWFhZTEtYWNkNzg2MDQwOTQy
      amz-sdk-request:
      - !!binary |
        YXR0ZW1wdD0x
      content-length:
      - '175'
      content-type:
      - !!binary |
        YXBwbGljYXRpb24vanNvbg==
    method: POST
    uri: https://bedrock-runtime.us-east-1.amazonaws.com/model/us.amazon.nova-micro-v1%3A0/converse
  response:
    headers:
      connection:
      - keep-alive
      content-length:
      - '557'
      content-type:
      - application/json
    parsed_body:
      metrics:
        latencyMs: 417
      output:
        message:
          content:
          - text: The capital of France is Paris. Paris is not only the capital city but also the most populous city in France,
              known for its significant cultural, political, and economic influence both within the country and globally.
              It is famous for landmarks such as the Eiffel Tower, the Louvre Museum, and the Notre-Dame Cathedral, among
              many other historical and architectural treasures.
          role: assistant
      stopReason: end_turn
      usage:
        inputTokens: 13
        outputTokens: 71
        totalTokens: 84
    status:
      code: 200
      message: OK
version: 1
