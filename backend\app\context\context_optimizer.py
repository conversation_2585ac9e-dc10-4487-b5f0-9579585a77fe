"""
🧠 Context Optimizer - Reduce token usage intelligently

This module provides intelligent context optimization with:
- Smart token reduction without quality loss
- Multiple optimization strategies
- Performance metrics and analytics
- Adaptive optimization based on context type
- Quality preservation guarantees
"""

import re
import time
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)


class OptimizationStrategy(str, Enum):
    """Context optimization strategies."""
    MINIMAL = "minimal"          # 10% reduction - preserve almost everything
    MODERATE = "moderate"        # 30% reduction - balanced optimization
    AGGRESSIVE = "aggressive"    # 50% reduction - maximum safe optimization
    ADAPTIVE = "adaptive"        # Dynamic based on content analysis


class OptimizationResult(BaseModel):
    """Context optimization result."""
    
    optimization_id: str = Field(..., description="Unique optimization identifier")
    strategy: OptimizationStrategy = Field(..., description="Optimization strategy used")
    
    # Input metrics
    original_content: str = Field(..., description="Original content")
    original_tokens: int = Field(..., description="Original token count")
    
    # Output metrics
    optimized_content: str = Field(..., description="Optimized content")
    optimized_tokens: int = Field(..., description="Optimized token count")
    
    # Performance metrics
    reduction_percentage: float = Field(..., description="Token reduction percentage")
    quality_score: float = Field(..., description="Quality preservation score (0-1)")
    processing_time: float = Field(..., description="Processing time in seconds")
    
    # Optimization details
    techniques_applied: List[str] = Field(default_factory=list, description="Optimization techniques used")
    preserved_elements: List[str] = Field(default_factory=list, description="Elements preserved")
    warnings: List[str] = Field(default_factory=list, description="Optimization warnings")
    
    # Metadata
    timestamp: datetime = Field(default_factory=datetime.now, description="Optimization timestamp")
    success: bool = Field(default=True, description="Whether optimization was successful")


class ContextOptimizer:
    """
    🧠 Advanced Context Optimizer
    
    Intelligently reduces token usage while preserving content quality
    and semantic meaning through multiple optimization strategies.
    """
    
    def __init__(self):
        # Optimization thresholds
        self.optimization_thresholds = {
            OptimizationStrategy.MINIMAL: 0.10,     # 10% reduction
            OptimizationStrategy.MODERATE: 0.30,    # 30% reduction
            OptimizationStrategy.AGGRESSIVE: 0.50,  # 50% reduction
        }
        
        # Quality thresholds
        self.min_quality_score = 0.85  # Minimum acceptable quality
        
        # Preservation patterns (never optimize these)
        self.preserve_patterns = [
            r'```[\s\S]*?```',           # Code blocks
            r'`[^`]+`',                  # Inline code
            r'https?://[^\s]+',          # URLs
            r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',  # Emails
            r'/[^\s]+\.[a-zA-Z]+',       # File paths
            r'\b\d+\.\d+\.\d+\b',        # Version numbers
            r'ERROR|CRITICAL|WARNING',    # Error levels
        ]
        
        # Optimization statistics
        self.optimization_stats = {
            "total_optimizations": 0,
            "total_tokens_saved": 0,
            "average_reduction": 0.0,
            "average_quality": 0.0,
            "strategy_usage": {strategy: 0 for strategy in OptimizationStrategy}
        }
    
    def optimize_context(
        self,
        content: str,
        strategy: OptimizationStrategy = OptimizationStrategy.ADAPTIVE,
        target_reduction: Optional[float] = None,
        preserve_code: bool = True,
        preserve_decisions: bool = True
    ) -> OptimizationResult:
        """Optimize context content using specified strategy."""
        start_time = time.time()
        optimization_id = f"opt_{int(time.time() * 1000)}"
        
        try:
            # Calculate original metrics
            original_tokens = self._count_tokens(content)
            
            # Determine optimization strategy
            if strategy == OptimizationStrategy.ADAPTIVE:
                strategy = self._select_adaptive_strategy(content, original_tokens)
            
            # Apply optimization
            optimized_content, techniques_applied, preserved_elements = self._apply_optimization(
                content, strategy, target_reduction, preserve_code, preserve_decisions
            )
            
            # Calculate results
            optimized_tokens = self._count_tokens(optimized_content)
            reduction_percentage = ((original_tokens - optimized_tokens) / original_tokens) * 100
            quality_score = self._calculate_quality_score(content, optimized_content)
            processing_time = time.time() - start_time
            
            # Create result
            result = OptimizationResult(
                optimization_id=optimization_id,
                strategy=strategy,
                original_content=content,
                original_tokens=original_tokens,
                optimized_content=optimized_content,
                optimized_tokens=optimized_tokens,
                reduction_percentage=reduction_percentage,
                quality_score=quality_score,
                processing_time=processing_time,
                techniques_applied=techniques_applied,
                preserved_elements=preserved_elements
            )
            
            # Validate quality
            if quality_score < self.min_quality_score:
                result.warnings.append(f"Quality score {quality_score:.2f} below threshold {self.min_quality_score}")
                result.success = False
            
            # Update statistics
            self._update_statistics(result)
            
            logger.info(f"Context optimization completed: {reduction_percentage:.1f}% reduction, quality: {quality_score:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"Context optimization failed: {e}")
            return OptimizationResult(
                optimization_id=optimization_id,
                strategy=strategy,
                original_content=content,
                original_tokens=self._count_tokens(content),
                optimized_content=content,  # Return original on failure
                optimized_tokens=self._count_tokens(content),
                reduction_percentage=0.0,
                quality_score=0.0,
                processing_time=time.time() - start_time,
                warnings=[f"Optimization failed: {str(e)}"],
                success=False
            )
    
    def _select_adaptive_strategy(self, content: str, token_count: int) -> OptimizationStrategy:
        """Select optimization strategy based on content analysis."""
        # Analyze content characteristics
        has_code = bool(re.search(r'```[\s\S]*?```|`[^`]+`', content))
        has_errors = bool(re.search(r'ERROR|CRITICAL|WARNING|Exception|Traceback', content, re.IGNORECASE))
        has_decisions = bool(re.search(r'decided|chosen|selected|implemented|approach', content, re.IGNORECASE))
        
        # Very short content - minimal optimization
        if token_count < 1000:
            return OptimizationStrategy.MINIMAL
        
        # Code-heavy content - minimal optimization
        if has_code and content.count('```') > 2:
            return OptimizationStrategy.MINIMAL
        
        # Error/debugging content - minimal optimization
        if has_errors:
            return OptimizationStrategy.MINIMAL
        
        # Decision-heavy content - moderate optimization
        if has_decisions:
            return OptimizationStrategy.MODERATE
        
        # Long content without critical elements - aggressive optimization
        if token_count > 5000:
            return OptimizationStrategy.AGGRESSIVE
        
        # Default to moderate
        return OptimizationStrategy.MODERATE
    
    def _apply_optimization(
        self,
        content: str,
        strategy: OptimizationStrategy,
        target_reduction: Optional[float],
        preserve_code: bool,
        preserve_decisions: bool
    ) -> Tuple[str, List[str], List[str]]:
        """Apply optimization techniques based on strategy."""
        optimized_content = content
        techniques_applied = []
        preserved_elements = []
        
        # Extract and preserve critical elements
        preserved_blocks = {}
        if preserve_code:
            preserved_blocks.update(self._extract_code_blocks(optimized_content))
            if preserved_blocks:
                preserved_elements.append("code_blocks")
        
        # Apply optimization techniques based on strategy
        target_reduction_rate = target_reduction or self.optimization_thresholds[strategy]
        
        # 1. Remove excessive whitespace
        if target_reduction_rate >= 0.10:
            optimized_content = self._remove_excessive_whitespace(optimized_content)
            techniques_applied.append("whitespace_removal")
        
        # 2. Compress repetitive content
        if target_reduction_rate >= 0.20:
            optimized_content = self._compress_repetitive_content(optimized_content)
            techniques_applied.append("repetition_compression")
        
        # 3. Simplify verbose expressions
        if target_reduction_rate >= 0.30:
            optimized_content = self._simplify_verbose_expressions(optimized_content)
            techniques_applied.append("expression_simplification")
        
        # 4. Remove redundant information
        if target_reduction_rate >= 0.40:
            optimized_content = self._remove_redundant_information(optimized_content)
            techniques_applied.append("redundancy_removal")
        
        # 5. Compress low-priority sections
        if target_reduction_rate >= 0.50:
            optimized_content = self._compress_low_priority_sections(optimized_content)
            techniques_applied.append("section_compression")
        
        # Restore preserved blocks
        optimized_content = self._restore_preserved_blocks(optimized_content, preserved_blocks)
        
        return optimized_content, techniques_applied, preserved_elements
    
    def _extract_code_blocks(self, content: str) -> Dict[str, str]:
        """Extract and replace code blocks with placeholders."""
        preserved_blocks = {}
        
        # Extract code blocks
        code_pattern = r'```[\s\S]*?```'
        matches = re.finditer(code_pattern, content)
        
        for i, match in enumerate(matches):
            placeholder = f"__CODE_BLOCK_{i}__"
            preserved_blocks[placeholder] = match.group()
            content = content.replace(match.group(), placeholder, 1)
        
        # Extract inline code
        inline_pattern = r'`[^`]+`'
        matches = re.finditer(inline_pattern, content)
        
        for i, match in enumerate(matches):
            placeholder = f"__INLINE_CODE_{i}__"
            preserved_blocks[placeholder] = match.group()
            content = content.replace(match.group(), placeholder, 1)
        
        return preserved_blocks
    
    def _remove_excessive_whitespace(self, content: str) -> str:
        """Remove excessive whitespace while preserving structure."""
        # Remove multiple consecutive spaces
        content = re.sub(r' {3,}', '  ', content)
        
        # Remove multiple consecutive newlines (keep max 2)
        content = re.sub(r'\n{3,}', '\n\n', content)
        
        # Remove trailing whitespace from lines
        content = re.sub(r'[ \t]+$', '', content, flags=re.MULTILINE)
        
        return content.strip()
    
    def _compress_repetitive_content(self, content: str) -> str:
        """Compress repetitive content patterns."""
        # Compress repeated phrases
        lines = content.split('\n')
        compressed_lines = []
        
        for line in lines:
            # Skip if line is very similar to previous line
            if compressed_lines and self._calculate_line_similarity(line, compressed_lines[-1]) > 0.8:
                continue
            compressed_lines.append(line)
        
        return '\n'.join(compressed_lines)
    
    def _simplify_verbose_expressions(self, content: str) -> str:
        """Simplify verbose expressions and phrases."""
        simplifications = {
            r'\bin order to\b': 'to',
            r'\bdue to the fact that\b': 'because',
            r'\bfor the purpose of\b': 'to',
            r'\bat this point in time\b': 'now',
            r'\bin the event that\b': 'if',
            r'\bwith regard to\b': 'regarding',
            r'\bas a result of\b': 'because of',
            r'\bit is important to note that\b': '',
            r'\bit should be mentioned that\b': '',
            r'\bplease note that\b': '',
        }
        
        for pattern, replacement in simplifications.items():
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
        
        return content
    
    def _remove_redundant_information(self, content: str) -> str:
        """Remove redundant information while preserving meaning."""
        # Remove redundant confirmations
        content = re.sub(r'\b(yes|ok|okay|sure|alright),?\s*', '', content, flags=re.IGNORECASE)
        
        # Remove excessive politeness
        content = re.sub(r'\b(please|kindly)\s+', '', content, flags=re.IGNORECASE)
        
        # Remove redundant transitions
        content = re.sub(r'\b(furthermore|moreover|additionally),?\s*', '', content, flags=re.IGNORECASE)
        
        return content
    
    def _compress_low_priority_sections(self, content: str) -> str:
        """Compress sections with low priority information."""
        # Identify and compress example sections
        content = re.sub(
            r'(for example|e\.g\.|such as)[^.]*\.',
            r'\1 [examples compressed].',
            content,
            flags=re.IGNORECASE
        )
        
        # Compress verbose explanations
        content = re.sub(
            r'(this means that|in other words|to put it simply)[^.]*\.',
            r'\1 [explanation compressed].',
            content,
            flags=re.IGNORECASE
        )
        
        return content
    
    def _restore_preserved_blocks(self, content: str, preserved_blocks: Dict[str, str]) -> str:
        """Restore preserved blocks to the content."""
        for placeholder, original_content in preserved_blocks.items():
            content = content.replace(placeholder, original_content)
        return content
    
    def _calculate_line_similarity(self, line1: str, line2: str) -> float:
        """Calculate similarity between two lines."""
        if not line1 or not line2:
            return 0.0
        
        # Simple word-based similarity
        words1 = set(line1.lower().split())
        words2 = set(line2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
    
    def _count_tokens(self, content: str) -> int:
        """Estimate token count (approximate)."""
        # Simple token estimation: words + punctuation
        words = len(content.split())
        punctuation = len(re.findall(r'[^\w\s]', content))
        return words + punctuation
    
    def _calculate_quality_score(self, original: str, optimized: str) -> float:
        """Calculate quality preservation score."""
        # Extract key elements from both versions
        original_elements = self._extract_key_elements(original)
        optimized_elements = self._extract_key_elements(optimized)
        
        # Calculate preservation ratio
        if not original_elements:
            return 1.0
        
        preserved_count = sum(1 for elem in original_elements if elem in optimized_elements)
        preservation_ratio = preserved_count / len(original_elements)
        
        # Calculate length ratio (penalize excessive reduction)
        length_ratio = len(optimized) / len(original) if original else 1.0
        length_penalty = max(0, 0.3 - length_ratio) * 2  # Penalty if reduced below 30%
        
        # Combined quality score
        quality_score = preservation_ratio - length_penalty
        return max(0.0, min(1.0, quality_score))
    
    def _extract_key_elements(self, content: str) -> List[str]:
        """Extract key elements from content for quality assessment."""
        elements = []
        
        # Extract code blocks
        elements.extend(re.findall(r'```[\s\S]*?```', content))
        elements.extend(re.findall(r'`[^`]+`', content))
        
        # Extract file paths
        elements.extend(re.findall(r'/[^\s]+\.[a-zA-Z]+', content))
        
        # Extract URLs
        elements.extend(re.findall(r'https?://[^\s]+', content))
        
        # Extract important keywords
        important_keywords = re.findall(
            r'\b(ERROR|WARNING|CRITICAL|implemented|decided|created|fixed|updated|added|removed)\b',
            content,
            re.IGNORECASE
        )
        elements.extend(important_keywords)
        
        return elements
    
    def _update_statistics(self, result: OptimizationResult) -> None:
        """Update optimization statistics."""
        if result.success:
            self.optimization_stats["total_optimizations"] += 1
            self.optimization_stats["total_tokens_saved"] += (result.original_tokens - result.optimized_tokens)
            
            # Update averages
            total_opts = self.optimization_stats["total_optimizations"]
            current_avg_reduction = self.optimization_stats["average_reduction"]
            current_avg_quality = self.optimization_stats["average_quality"]
            
            self.optimization_stats["average_reduction"] = (
                (current_avg_reduction * (total_opts - 1) + result.reduction_percentage) / total_opts
            )
            
            self.optimization_stats["average_quality"] = (
                (current_avg_quality * (total_opts - 1) + result.quality_score) / total_opts
            )
            
            # Update strategy usage
            self.optimization_stats["strategy_usage"][result.strategy] += 1
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """Get optimization statistics."""
        return self.optimization_stats.copy()
    
    def reset_statistics(self) -> None:
        """Reset optimization statistics."""
        self.optimization_stats = {
            "total_optimizations": 0,
            "total_tokens_saved": 0,
            "average_reduction": 0.0,
            "average_quality": 0.0,
            "strategy_usage": {strategy: 0 for strategy in OptimizationStrategy}
        }


# Global context optimizer instance
_context_optimizer: Optional[ContextOptimizer] = None


def get_context_optimizer() -> ContextOptimizer:
    """Get the global context optimizer instance."""
    global _context_optimizer
    if _context_optimizer is None:
        _context_optimizer = ContextOptimizer()
    return _context_optimizer
