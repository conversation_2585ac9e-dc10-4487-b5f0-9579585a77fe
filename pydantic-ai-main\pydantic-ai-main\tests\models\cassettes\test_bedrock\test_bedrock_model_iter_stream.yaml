interactions:
- request:
    body: '{"messages": [{"role": "user", "content": [{"text": "What is the temperature of the capital of France?"}]}], "system":
      [{"text": "You are a helpful chatbot."}], "inferenceConfig": {"topP": 0.5}, "toolConfig": {"tools": [{"toolSpec": {"name":
      "get_capital", "description": "Get the capital of a country.", "inputSchema": {"json": {"properties": {"country": {"description":
      "The country name.", "title": "Country", "type": "string"}}, "required": ["country"], "type": "object", "additionalProperties":
      false}}}}, {"toolSpec": {"name": "get_temperature", "description": "Get the temperature in a city.", "inputSchema":
      {"json": {"properties": {"city": {"description": "The city name.", "title": "City", "type": "string"}}, "required":
      ["city"], "type": "object", "additionalProperties": false}}}}]}}'
    headers:
      amz-sdk-invocation-id:
      - !!binary |
        YzIwMzBiODgtNzk1Yy00M2EzLWI2OTktZTNhMGJiOTBlNDNk
      amz-sdk-request:
      - !!binary |
        YXR0ZW1wdD0x
      content-length:
      - '796'
      content-type:
      - !!binary |
        YXBwbGljYXRpb24vanNvbg==
    method: POST
    uri: https://bedrock-runtime.us-east-1.amazonaws.com/model/us.amazon.nova-micro-v1%3A0/converse-stream
  response:
    body:
      string: !!binary |
        AAAAkAAAAFIxQcihCzpldmVudC10eXBlBwAMbWVzc2FnZVN0YXJ0DTpjb250ZW50LXR5cGUHABBh
        cHBsaWNhdGlvbi9qc29uDTptZXNzYWdlLXR5cGUHAAVldmVudHsicCI6ImFiY2RlZmdoaWprbG1u
        b3BxcnMiLCJyb2xlIjoiYXNzaXN0YW50In0RduqwAAAAvAAAAFdFGv4rCzpldmVudC10eXBlBwAR
        Y29udGVudEJsb2NrRGVsdGENOmNvbnRlbnQtdHlwZQcAEGFwcGxpY2F0aW9uL2pzb24NOm1lc3Nh
        Z2UtdHlwZQcABWV2ZW50eyJjb250ZW50QmxvY2tJbmRleCI6MCwiZGVsdGEiOnsidGV4dCI6Ijx0
        aGlua2luZyJ9LCJwIjoiYWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXoifRvDqgwAAACwAAAAV4Dq
        EyoLOmV2ZW50LXR5cGUHABFjb250ZW50QmxvY2tEZWx0YQ06Y29udGVudC10eXBlBwAQYXBwbGlj
        YXRpb24vanNvbg06bWVzc2FnZS10eXBlBwAFZXZlbnR7ImNvbnRlbnRCbG9ja0luZGV4IjowLCJk
        ZWx0YSI6eyJ0ZXh0IjoiPiBUbyBmaW5kIn0sInAiOiJhYmNkZWZnaGlqa2xtbiJ9ivJR8QAAAOYA
        AABXN7kpQQs6ZXZlbnQtdHlwZQcAEWNvbnRlbnRCbG9ja0RlbHRhDTpjb250ZW50LXR5cGUHABBh
        cHBsaWNhdGlvbi9qc29uDTptZXNzYWdlLXR5cGUHAAVldmVudHsiY29udGVudEJsb2NrSW5kZXgi
        OjAsImRlbHRhIjp7InRleHQiOiIgdGhlIHRlbXBlcmF0dXJlIn0sInAiOiJhYmNkZWZnaGlqa2xt
        bm9wcXJzdHV2d3h5ekFCQ0RFRkdISUpLTE1OT1BRUlNUVVZXWFlaMDEyMzQ1Njc4In1qqvNOAAAA
        0gAAAFdjGDcHCzpldmVudC10eXBlBwARY29udGVudEJsb2NrRGVsdGENOmNvbnRlbnQtdHlwZQcA
        EGFwcGxpY2F0aW9uL2pzb24NOm1lc3NhZ2UtdHlwZQcABWV2ZW50eyJjb250ZW50QmxvY2tJbmRl
        eCI6MCwiZGVsdGEiOnsidGV4dCI6IiBvZiB0aGUgY2FwaXRhbCBvZiBGcmFuY2UsIn0sInAiOiJh
        YmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ekFCQ0RFIn3uKUMFAAAAvQAAAFd4etebCzpldmVudC10
        eXBlBwARY29udGVudEJsb2NrRGVsdGENOmNvbnRlbnQtdHlwZQcAEGFwcGxpY2F0aW9uL2pzb24N
        Om1lc3NhZ2UtdHlwZQcABWV2ZW50eyJjb250ZW50QmxvY2tJbmRleCI6MCwiZGVsdGEiOnsidGV4
        dCI6IiBJIG5lZWQgdG8gZmlyc3QifSwicCI6ImFiY2RlZmdoaWprbG1ub3BxcnN0In2Z4zbRAAAA
        6wAAAFfPKe3wCzpldmVudC10eXBlBwARY29udGVudEJsb2NrRGVsdGENOmNvbnRlbnQtdHlwZQcA
        EGFwcGxpY2F0aW9uL2pzb24NOm1lc3NhZ2UtdHlwZQcABWV2ZW50eyJjb250ZW50QmxvY2tJbmRl
        eCI6MCwiZGVsdGEiOnsidGV4dCI6IiBkZXRlcm1pbmUgdGhlIGNhcGl0YWwifSwicCI6ImFiY2Rl
        ZmdoaWprbG1ub3BxcnN0dXZ3eHl6QUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVowMTIzNDU2NyJ9
        5C/XMQAAAKoAAABXqrqcCQs6ZXZlbnQtdHlwZQcAEWNvbnRlbnRCbG9ja0RlbHRhDTpjb250ZW50
        LXR5cGUHABBhcHBsaWNhdGlvbi9qc29uDTptZXNzYWdlLXR5cGUHAAVldmVudHsiY29udGVudEJs
        b2NrSW5kZXgiOjAsImRlbHRhIjp7InRleHQiOiIgb2YgRnJhbmNlIGFuZCJ9LCJwIjoiYWJjIn0e
        3HQEAAAAzwAAAFf7aGQ0CzpldmVudC10eXBlBwARY29udGVudEJsb2NrRGVsdGENOmNvbnRlbnQt
        dHlwZQcAEGFwcGxpY2F0aW9uL2pzb24NOm1lc3NhZ2UtdHlwZQcABWV2ZW50eyJjb250ZW50Qmxv
        Y2tJbmRleCI6MCwiZGVsdGEiOnsidGV4dCI6IiB0aGVuIGdldCJ9LCJwIjoiYWJjZGVmZ2hpamts
        bW5vcHFyc3R1dnd4eXpBQkNERUZHSElKS0xNTk9QUVJTIn1NoRayAAAAvQAAAFd4etebCzpldmVu
        dC10eXBlBwARY29udGVudEJsb2NrRGVsdGENOmNvbnRlbnQtdHlwZQcAEGFwcGxpY2F0aW9uL2pz
        b24NOm1lc3NhZ2UtdHlwZQcABWV2ZW50eyJjb250ZW50QmxvY2tJbmRleCI6MCwiZGVsdGEiOnsi
        dGV4dCI6IiB0aGUgY3VycmVudCJ9LCJwIjoiYWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4In39D4tq
        AAAA1gAAAFeWmJHHCzpldmVudC10eXBlBwARY29udGVudEJsb2NrRGVsdGENOmNvbnRlbnQtdHlw
        ZQcAEGFwcGxpY2F0aW9uL2pzb24NOm1lc3NhZ2UtdHlwZQcABWV2ZW50eyJjb250ZW50QmxvY2tJ
        bmRleCI6MCwiZGVsdGEiOnsidGV4dCI6IiB0ZW1wZXJhdHVyZSBpbiJ9LCJwIjoiYWJjZGVmZ2hp
        amtsbW5vcHFyc3R1dnd4eXpBQkNERUZHSElKS0xNTk9QUVJTVCJ9+I++0wAAALwAAABXRRr+Kws6
        ZXZlbnQtdHlwZQcAEWNvbnRlbnRCbG9ja0RlbHRhDTpjb250ZW50LXR5cGUHABBhcHBsaWNhdGlv
        bi9qc29uDTptZXNzYWdlLXR5cGUHAAVldmVudHsiY29udGVudEJsb2NrSW5kZXgiOjAsImRlbHRh
        Ijp7InRleHQiOiIgdGhhdCBjaXR5LiBUaGUifSwicCI6ImFiY2RlZmdoaWprbG1ub3BxcnN0In0z
        tO/rAAAAyAAAAFdJSLgkCzpldmVudC10eXBlBwARY29udGVudEJsb2NrRGVsdGENOmNvbnRlbnQt
        dHlwZQcAEGFwcGxpY2F0aW9uL2pzb24NOm1lc3NhZ2UtdHlwZQcABWV2ZW50eyJjb250ZW50Qmxv
        Y2tJbmRleCI6MCwiZGVsdGEiOnsidGV4dCI6IiBjYXBpdGFsIG9mIEZyYW5jZSBpcyBQYXJpcyJ9
        LCJwIjoiYWJjZGVmZ2hpamtsbW5vcHFyc3QifVuZUssAAACoAAAAV9B6z2kLOmV2ZW50LXR5cGUH
        ABFjb250ZW50QmxvY2tEZWx0YQ06Y29udGVudC10eXBlBwAQYXBwbGljYXRpb24vanNvbg06bWVz
        c2FnZS10eXBlBwAFZXZlbnR7ImNvbnRlbnRCbG9ja0luZGV4IjowLCJkZWx0YSI6eyJ0ZXh0Ijoi
        LiBJIn0sInAiOiJhYmNkZWZnaGlqa2wifbbV2Z8AAADJAAAAV3QokZQLOmV2ZW50LXR5cGUHABFj
        b250ZW50QmxvY2tEZWx0YQ06Y29udGVudC10eXBlBwAQYXBwbGljYXRpb24vanNvbg06bWVzc2Fn
        ZS10eXBlBwAFZXZlbnR7ImNvbnRlbnRCbG9ja0luZGV4IjowLCJkZWx0YSI6eyJ0ZXh0IjoiIHdp
        bGwgdXNlIn0sInAiOiJhYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ekFCQ0RFRkdISUpLTE0ifUna
        rKYAAADaAAAAV1NofMYLOmV2ZW50LXR5cGUHABFjb250ZW50QmxvY2tEZWx0YQ06Y29udGVudC10
        eXBlBwAQYXBwbGljYXRpb24vanNvbg06bWVzc2FnZS10eXBlBwAFZXZlbnR7ImNvbnRlbnRCbG9j
        a0luZGV4IjowLCJkZWx0YSI6eyJ0ZXh0IjoiIHRoZSBcImdldF90ZW1wZXJhdHVyZVwiIn0sInAi
        OiJhYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ekFCQ0RFRkdISUpLTE1OTyJ9xiZPLgAAAM4AAABX
        xghNhAs6ZXZlbnQtdHlwZQcAEWNvbnRlbnRCbG9ja0RlbHRhDTpjb250ZW50LXR5cGUHABBhcHBs
        aWNhdGlvbi9qc29uDTptZXNzYWdlLXR5cGUHAAVldmVudHsiY29udGVudEJsb2NrSW5kZXgiOjAs
        ImRlbHRhIjp7InRleHQiOiIgdG9vbCB0byBmaW5kIHRoZSBjdXJyZW50IHRlbXBlcmF0dXJlIn0s
        InAiOiJhYmNkZWZnaGlqa2xtbm9wIn1nbMItAAAAqwAAAFeX2rW5CzpldmVudC10eXBlBwARY29u
        dGVudEJsb2NrRGVsdGENOmNvbnRlbnQtdHlwZQcAEGFwcGxpY2F0aW9uL2pzb24NOm1lc3NhZ2Ut
        dHlwZQcABWV2ZW50eyJjb250ZW50QmxvY2tJbmRleCI6MCwiZGVsdGEiOnsidGV4dCI6IiBpbiBQ
        YXJpcy48LyJ9LCJwIjoiYWJjZGVmIn2biUXfAAAA1QAAAFfROOsXCzpldmVudC10eXBlBwARY29u
        dGVudEJsb2NrRGVsdGENOmNvbnRlbnQtdHlwZQcAEGFwcGxpY2F0aW9uL2pzb24NOm1lc3NhZ2Ut
        dHlwZQcABWV2ZW50eyJjb250ZW50QmxvY2tJbmRleCI6MCwiZGVsdGEiOnsidGV4dCI6InRoaW5r
        aW5nIn0sInAiOiJhYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ekFCQ0RFRkdISUpLTE1OT1BRUlNU
        VVZXWFlaIn0q+El1AAAA0wAAAFdeeB63CzpldmVudC10eXBlBwARY29udGVudEJsb2NrRGVsdGEN
        OmNvbnRlbnQtdHlwZQcAEGFwcGxpY2F0aW9uL2pzb24NOm1lc3NhZ2UtdHlwZQcABWV2ZW50eyJj
        b250ZW50QmxvY2tJbmRleCI6MCwiZGVsdGEiOnsidGV4dCI6Ij5cbiJ9LCJwIjoiYWJjZGVmZ2hp
        amtsbW5vcHFyc3R1dnd4eXpBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWjAxMiJ9WEvLvgAAAMAA
        AABWDj/Dcws6ZXZlbnQtdHlwZQcAEGNvbnRlbnRCbG9ja1N0b3ANOmNvbnRlbnQtdHlwZQcAEGFw
        cGxpY2F0aW9uL2pzb24NOm1lc3NhZ2UtdHlwZQcABWV2ZW50eyJjb250ZW50QmxvY2tJbmRleCI6
        MCwicCI6ImFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6QUJDREVGR0hJSktMTU5PUFFSU1RVVldY
        WVowMTIzNDU2NyJ9EPTSwQAAAQ4AAABX5EF2+gs6ZXZlbnQtdHlwZQcAEWNvbnRlbnRCbG9ja1N0
        YXJ0DTpjb250ZW50LXR5cGUHABBhcHBsaWNhdGlvbi9qc29uDTptZXNzYWdlLXR5cGUHAAVldmVu
        dHsiY29udGVudEJsb2NrSW5kZXgiOjEsInAiOiJhYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ekFC
        Q0RFRkdISUpLTE1OT1BRUlMiLCJzdGFydCI6eyJ0b29sVXNlIjp7Im5hbWUiOiJnZXRfdGVtcGVy
        YXR1cmUiLCJ0b29sVXNlSWQiOiJ0b29sdXNlX2xBR196UDhRUkhtU1lPd1p6emFDcUEifX19Vwn4
        5wAAAOsAAABXzynt8As6ZXZlbnQtdHlwZQcAEWNvbnRlbnRCbG9ja0RlbHRhDTpjb250ZW50LXR5
        cGUHABBhcHBsaWNhdGlvbi9qc29uDTptZXNzYWdlLXR5cGUHAAVldmVudHsiY29udGVudEJsb2Nr
        SW5kZXgiOjEsImRlbHRhIjp7InRvb2xVc2UiOnsiaW5wdXQiOiJ7XCJjaXR5XCI6XCJQYXJpc1wi
        fSJ9fSwicCI6ImFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6QUJDREVGR0hJSktMTU5PUFFSU1RV
        VlcifaOerpkAAACUAAAAVsOsqngLOmV2ZW50LXR5cGUHABBjb250ZW50QmxvY2tTdG9wDTpjb250
        ZW50LXR5cGUHABBhcHBsaWNhdGlvbi9qc29uDTptZXNzYWdlLXR5cGUHAAVldmVudHsiY29udGVu
        dEJsb2NrSW5kZXgiOjEsInAiOiJhYmNkZWZnaGlqa2xtbm9wIn1vO+REAAAAqgAAAFFD2Tk8Czpl
        dmVudC10eXBlBwALbWVzc2FnZVN0b3ANOmNvbnRlbnQtdHlwZQcAEGFwcGxpY2F0aW9uL2pzb24N
        Om1lc3NhZ2UtdHlwZQcABWV2ZW50eyJwIjoiYWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXpBQkNE
        RUZHSElKS0xNTk8iLCJzdG9wUmVhc29uIjoidG9vbF91c2UifUNGZLwAAADPAAAATp8DzPQLOmV2
        ZW50LXR5cGUHAAhtZXRhZGF0YQ06Y29udGVudC10eXBlBwAQYXBwbGljYXRpb24vanNvbg06bWVz
        c2FnZS10eXBlBwAFZXZlbnR7Im1ldHJpY3MiOnsibGF0ZW5jeU1zIjo0MjJ9LCJwIjoiYWJjZGVm
        Z2hpamtsbSIsInVzYWdlIjp7ImlucHV0VG9rZW5zIjo0NzEsIm91dHB1dFRva2VucyI6OTEsInRv
        dGFsVG9rZW5zIjo1NjJ9fUKRITE=
    headers:
      connection:
      - keep-alive
      content-type:
      - application/vnd.amazon.eventstream
      transfer-encoding:
      - chunked
    status:
      code: 200
      message: OK
- request:
    body: '{"messages": [{"role": "user", "content": [{"text": "What is the temperature of the capital of France?"}]}, {"role":
      "assistant", "content": [{"text": "<thinking> To find the temperature of the capital of France, I need to first determine
      the capital of France and then get the current temperature in that city. The capital of France is Paris. I will use
      the \"get_temperature\" tool to find the current temperature in Paris.</thinking>\n"}, {"toolUse": {"toolUseId": "tooluse_lAG_zP8QRHmSYOwZzzaCqA",
      "name": "get_temperature", "input": {"city": "Paris"}}}]}, {"role": "user", "content": [{"toolResult": {"toolUseId":
      "tooluse_lAG_zP8QRHmSYOwZzzaCqA", "content": [{"text": "30\u00b0C"}], "status": "success"}}]}], "system": [{"text":
      "You are a helpful chatbot."}], "inferenceConfig": {"topP": 0.5}, "toolConfig": {"tools": [{"toolSpec": {"name": "get_capital",
      "description": "Get the capital of a country.", "inputSchema": {"json": {"properties": {"country": {"description": "The
      country name.", "title": "Country", "type": "string"}}, "required": ["country"], "type": "object", "additionalProperties":
      false}}}}, {"toolSpec": {"name": "get_temperature", "description": "Get the temperature in a city.", "inputSchema":
      {"json": {"properties": {"city": {"description": "The city name.", "title": "City", "type": "string"}}, "required":
      ["city"], "type": "object", "additionalProperties": false}}}}]}}'
    headers:
      amz-sdk-invocation-id:
      - !!binary |
        NjI1OWQ1OGEtYzU2Ni00OTVkLTkzMTItZDlkYmYxYjE4NjRj
      amz-sdk-request:
      - !!binary |
        YXR0ZW1wdD0x
      content-length:
      - '1402'
      content-type:
      - !!binary |
        YXBwbGljYXRpb24vanNvbg==
    method: POST
    uri: https://bedrock-runtime.us-east-1.amazonaws.com/model/us.amazon.nova-micro-v1%3A0/converse-stream
  response:
    body:
      string: !!binary |
        AAAAggAAAFIrYQxDCzpldmVudC10eXBlBwAMbWVzc2FnZVN0YXJ0DTpjb250ZW50LXR5cGUHABBh
        cHBsaWNhdGlvbi9qc29uDTptZXNzYWdlLXR5cGUHAAVldmVudHsicCI6ImFiY2RlIiwicm9sZSI6
        ImFzc2lzdGFudCJ9Ki5zGAAAAKsAAABXl9q1uQs6ZXZlbnQtdHlwZQcAEWNvbnRlbnRCbG9ja0Rl
        bHRhDTpjb250ZW50LXR5cGUHABBhcHBsaWNhdGlvbi9qc29uDTptZXNzYWdlLXR5cGUHAAVldmVu
        dHsiY29udGVudEJsb2NrSW5kZXgiOjAsImRlbHRhIjp7InRleHQiOiJUaGUifSwicCI6ImFiY2Rl
        ZmdoaWprbG1ubyJ9URJ5hwAAAMsAAABXDujC9As6ZXZlbnQtdHlwZQcAEWNvbnRlbnRCbG9ja0Rl
        bHRhDTpjb250ZW50LXR5cGUHABBhcHBsaWNhdGlvbi9qc29uDTptZXNzYWdlLXR5cGUHAAVldmVu
        dHsiY29udGVudEJsb2NrSW5kZXgiOjAsImRlbHRhIjp7InRleHQiOiIgY3VycmVudCB0ZW1wZXJh
        dHVyZSBpbiBQYXJpcywgdGhlIn0sInAiOiJhYmNkZWZnaGlqa2xtbm9wIn31u0aNAAAArwAAAFdi
        WhN5CzpldmVudC10eXBlBwARY29udGVudEJsb2NrRGVsdGENOmNvbnRlbnQtdHlwZQcAEGFwcGxp
        Y2F0aW9uL2pzb24NOm1lc3NhZ2UtdHlwZQcABWV2ZW50eyJjb250ZW50QmxvY2tJbmRleCI6MCwi
        ZGVsdGEiOnsidGV4dCI6IiBjYXBpdGFsIG9mIEZyYW5jZSwifSwicCI6ImFiYyJ9vDc8fgAAAMgA
        AABXSUi4JAs6ZXZlbnQtdHlwZQcAEWNvbnRlbnRCbG9ja0RlbHRhDTpjb250ZW50LXR5cGUHABBh
        cHBsaWNhdGlvbi9qc29uDTptZXNzYWdlLXR5cGUHAAVldmVudHsiY29udGVudEJsb2NrSW5kZXgi
        OjAsImRlbHRhIjp7InRleHQiOiIgaXMgMzDCsEMifSwicCI6ImFiY2RlZmdoaWprbG1ub3BxcnN0
        dXZ3eHl6QUJDREVGR0hJSktMIn0JIvoaAAAAoAAAAFfgCoSoCzpldmVudC10eXBlBwARY29udGVu
        dEJsb2NrRGVsdGENOmNvbnRlbnQtdHlwZQcAEGFwcGxpY2F0aW9uL2pzb24NOm1lc3NhZ2UtdHlw
        ZQcABWV2ZW50eyJjb250ZW50QmxvY2tJbmRleCI6MCwiZGVsdGEiOnsidGV4dCI6Ii4ifSwicCI6
        ImFiY2RlZiJ9JeMRnQAAAJ8AAABWtHybaQs6ZXZlbnQtdHlwZQcAEGNvbnRlbnRCbG9ja1N0b3AN
        OmNvbnRlbnQtdHlwZQcAEGFwcGxpY2F0aW9uL2pzb24NOm1lc3NhZ2UtdHlwZQcABWV2ZW50eyJj
        b250ZW50QmxvY2tJbmRleCI6MCwicCI6ImFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6QSJ9gmux
        AQAAAJsAAABR35ioCgs6ZXZlbnQtdHlwZQcAC21lc3NhZ2VTdG9wDTpjb250ZW50LXR5cGUHABBh
        cHBsaWNhdGlvbi9qc29uDTptZXNzYWdlLXR5cGUHAAVldmVudHsicCI6ImFiY2RlZmdoaWprbG1u
        b3BxcnN0dXZ3eHl6Iiwic3RvcFJlYXNvbiI6ImVuZF90dXJuIn0EgYGjAAAA0AAAAE59s8ynCzpl
        dmVudC10eXBlBwAIbWV0YWRhdGENOmNvbnRlbnQtdHlwZQcAEGFwcGxpY2F0aW9uL2pzb24NOm1l
        c3NhZ2UtdHlwZQcABWV2ZW50eyJtZXRyaWNzIjp7ImxhdGVuY3lNcyI6MjIzfSwicCI6ImFiY2Rl
        ZmdoaWprbG1uIiwidXNhZ2UiOnsiaW5wdXRUb2tlbnMiOjU3Nywib3V0cHV0VG9rZW5zIjoxOCwi
        dG90YWxUb2tlbnMiOjU5NX19aWpwrQ==
    headers:
      connection:
      - keep-alive
      content-type:
      - application/vnd.amazon.eventstream
      transfer-encoding:
      - chunked
    status:
      code: 200
      message: OK
version: 1
