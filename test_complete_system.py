#!/usr/bin/env python3
"""
🎯 COMPREHENSIVE SYSTEM TEST SUITE - ZERO TOLERANCE FOR ERRORS
Complete validation of all DeepNexus AI Coder Agent components

This script performs exhaustive testing of:
- All API endpoints and functionality
- System health and monitoring
- Analytics and performance tracking
- Context management and optimization
- Session management and security
- Error handling and recovery
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime
from typing import Dict, List, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"

class SystemTester:
    """Comprehensive system testing suite."""
    
    def __init__(self):
        self.session = None
        self.test_results = {
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "errors": [],
            "performance_metrics": {},
            "start_time": datetime.now()
        }
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_endpoint(self, method: str, endpoint: str, expected_status: int = 200, 
                          data: Dict = None, description: str = None) -> Dict[str, Any]:
        """Test a single endpoint with comprehensive validation."""
        self.test_results["total_tests"] += 1
        test_name = description or f"{method} {endpoint}"
        
        try:
            start_time = time.time()
            
            if method.upper() == "GET":
                async with self.session.get(f"{BASE_URL}{endpoint}") as response:
                    response_data = await response.json()
                    status_code = response.status
            elif method.upper() == "POST":
                async with self.session.post(f"{BASE_URL}{endpoint}", json=data) as response:
                    response_data = await response.json()
                    status_code = response.status
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            response_time = (time.time() - start_time) * 1000  # Convert to ms
            
            # Validate response
            if status_code == expected_status:
                self.test_results["passed_tests"] += 1
                logger.info(f"✅ PASS: {test_name} ({response_time:.1f}ms)")
                return {
                    "status": "PASS",
                    "response_time_ms": response_time,
                    "data": response_data
                }
            else:
                self.test_results["failed_tests"] += 1
                error_msg = f"Expected status {expected_status}, got {status_code}"
                self.test_results["errors"].append(f"{test_name}: {error_msg}")
                logger.error(f"❌ FAIL: {test_name} - {error_msg}")
                return {
                    "status": "FAIL",
                    "error": error_msg,
                    "response_time_ms": response_time,
                    "data": response_data
                }
                
        except Exception as e:
            self.test_results["failed_tests"] += 1
            error_msg = f"Exception: {str(e)}"
            self.test_results["errors"].append(f"{test_name}: {error_msg}")
            logger.error(f"❌ ERROR: {test_name} - {error_msg}")
            return {
                "status": "ERROR",
                "error": error_msg
            }
    
    async def test_core_health_endpoints(self):
        """Test all core health endpoints."""
        logger.info("🔍 Testing Core Health Endpoints...")
        
        health_tests = [
            ("GET", "/health", 200, "Main Health Check"),
            ("GET", "/api/health", 200, "API Health Check"),
            ("GET", "/api/config/validate", 200, "Config Validation"),
            ("GET", "/api/services/health", 200, "Services Health"),
            ("GET", "/api/database/health", 200, "Database Health"),
        ]
        
        for method, endpoint, expected_status, description in health_tests:
            await self.test_endpoint(method, endpoint, expected_status, description=description)
    
    async def test_session_management(self):
        """Test session management functionality."""
        logger.info("🔍 Testing Session Management...")
        
        session_tests = [
            ("GET", "/api/sessions", 200, "List Sessions"),
            ("GET", "/api/sessions/health", 200, "Session Health"),
        ]
        
        for method, endpoint, expected_status, description in session_tests:
            await self.test_endpoint(method, endpoint, expected_status, description=description)
    
    async def test_project_management(self):
        """Test project management functionality."""
        logger.info("🔍 Testing Project Management...")
        
        project_tests = [
            ("GET", "/api/projects", 200, "List Projects"),
            ("GET", "/api/projects/health", 200, "Projects Health"),
        ]
        
        for method, endpoint, expected_status, description in project_tests:
            await self.test_endpoint(method, endpoint, expected_status, description=description)
    
    async def test_security_system(self):
        """Test security system functionality."""
        logger.info("🔍 Testing Security System...")
        
        security_tests = [
            ("GET", "/api/security/health", 200, "Security Health"),
            ("GET", "/api/security/rate-limits", 200, "Rate Limits Status"),
        ]
        
        for method, endpoint, expected_status, description in security_tests:
            await self.test_endpoint(method, endpoint, expected_status, description=description)
    
    async def test_context_management(self):
        """Test context management functionality."""
        logger.info("🔍 Testing Context Management...")
        
        context_tests = [
            ("GET", "/api/context/health", 200, "Context Health"),
            ("GET", "/api/context/cache/stats", 200, "Context Cache Stats"),
        ]
        
        for method, endpoint, expected_status, description in context_tests:
            await self.test_endpoint(method, endpoint, expected_status, description=description)
    
    async def test_analytics_system(self):
        """Test analytics system functionality."""
        logger.info("🔍 Testing Analytics System...")
        
        analytics_tests = [
            ("GET", "/api/analytics/health", 200, "Analytics Health"),
            ("GET", "/api/analytics/usage-metrics", 200, "Usage Metrics"),
            ("GET", "/api/analytics/real-time-stats", 200, "Real-time Stats"),
            ("GET", "/api/analytics/performance/metrics", 200, "Performance Metrics"),
        ]
        
        for method, endpoint, expected_status, description in analytics_tests:
            await self.test_endpoint(method, endpoint, expected_status, description=description)
    
    async def test_monitoring_system(self):
        """Test monitoring system functionality."""
        logger.info("🔍 Testing Monitoring System...")
        
        monitoring_tests = [
            ("GET", "/api/monitoring/health", 200, "Monitoring Health"),
            ("GET", "/api/monitoring/health/summary", 200, "Health Summary"),
            ("GET", "/api/monitoring/api/performance", 200, "API Performance"),
            ("GET", "/api/monitoring/agents/activity", 200, "Agent Activity"),
            ("GET", "/api/monitoring/dashboard", 200, "Monitoring Dashboard"),
        ]
        
        for method, endpoint, expected_status, description in monitoring_tests:
            await self.test_endpoint(method, endpoint, expected_status, description=description)
    
    async def test_api_documentation(self):
        """Test API documentation accessibility."""
        logger.info("🔍 Testing API Documentation...")
        
        try:
            async with self.session.get(f"{BASE_URL}/docs") as response:
                if response.status == 200:
                    logger.info("✅ PASS: API Documentation Accessible")
                    self.test_results["passed_tests"] += 1
                else:
                    logger.error(f"❌ FAIL: API Documentation - Status {response.status}")
                    self.test_results["failed_tests"] += 1
                self.test_results["total_tests"] += 1
        except Exception as e:
            logger.error(f"❌ ERROR: API Documentation - {str(e)}")
            self.test_results["failed_tests"] += 1
            self.test_results["total_tests"] += 1
    
    async def run_comprehensive_tests(self):
        """Run all comprehensive system tests."""
        logger.info("🚀 Starting Comprehensive System Test Suite...")
        logger.info("=" * 80)
        
        # Run all test suites
        await self.test_core_health_endpoints()
        await self.test_session_management()
        await self.test_project_management()
        await self.test_security_system()
        await self.test_context_management()
        await self.test_analytics_system()
        await self.test_monitoring_system()
        await self.test_api_documentation()
        
        # Calculate final results
        self.test_results["end_time"] = datetime.now()
        self.test_results["duration_seconds"] = (
            self.test_results["end_time"] - self.test_results["start_time"]
        ).total_seconds()
        
        success_rate = (
            self.test_results["passed_tests"] / self.test_results["total_tests"] * 100
            if self.test_results["total_tests"] > 0 else 0
        )
        
        # Print comprehensive results
        logger.info("=" * 80)
        logger.info("🎯 COMPREHENSIVE TEST RESULTS")
        logger.info("=" * 80)
        logger.info(f"📊 Total Tests: {self.test_results['total_tests']}")
        logger.info(f"✅ Passed: {self.test_results['passed_tests']}")
        logger.info(f"❌ Failed: {self.test_results['failed_tests']}")
        logger.info(f"📈 Success Rate: {success_rate:.1f}%")
        logger.info(f"⏱️  Duration: {self.test_results['duration_seconds']:.2f} seconds")
        
        if self.test_results["errors"]:
            logger.info("\n❌ ERRORS ENCOUNTERED:")
            for error in self.test_results["errors"]:
                logger.error(f"   • {error}")
        
        # Final verdict
        if success_rate == 100:
            logger.info("\n🎉 VERDICT: COMPLETE SUCCESS - ALL TESTS PASSED!")
            logger.info("✅ System is FULLY OPERATIONAL with ZERO ERRORS")
        elif success_rate >= 90:
            logger.info(f"\n⚠️  VERDICT: MOSTLY SUCCESSFUL - {success_rate:.1f}% pass rate")
            logger.info("🔧 Minor issues detected, system mostly operational")
        else:
            logger.info(f"\n❌ VERDICT: CRITICAL ISSUES - {success_rate:.1f}% pass rate")
            logger.info("🚨 System requires immediate attention")
        
        return self.test_results


async def main():
    """Main test execution function."""
    print("🎯 DeepNexus AI Coder Agent - Comprehensive System Test")
    print("=" * 80)
    print("Testing all components with ZERO TOLERANCE FOR ERRORS")
    print("=" * 80)
    
    async with SystemTester() as tester:
        results = await tester.run_comprehensive_tests()
        
        # Save results to file
        with open("test_results.json", "w") as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n📄 Detailed results saved to: test_results.json")
        
        return results["passed_tests"] == results["total_tests"]


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
