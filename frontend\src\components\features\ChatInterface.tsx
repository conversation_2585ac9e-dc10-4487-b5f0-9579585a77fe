import React, { useState, useEffect, useRef } from 'react';
import { socketEvents, socketActions } from '../../utils/socket';

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  type?: 'text' | 'code' | 'analysis' | 'error';
  metadata?: {
    tokens?: number;
    model?: string;
    executionTime?: number;
    codeLanguage?: string;
  };
}

interface ChatSession {
  id: string;
  name: string;
  messages: Message[];
  projectId?: string;
  status: 'active' | 'paused' | 'completed';
}

export const ChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [connected, setConnected] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // Initialize chat and load sessions
  useEffect(() => {
    initializeChat();
    loadSessions();
    
    // Socket event listeners
    socketEvents.onConnect(() => {
      setConnected(true);
      addSystemMessage('🌿 Connected to DeepNexus AI');
    });

    socketEvents.onDisconnect(() => {
      setConnected(false);
      addSystemMessage('🍂 Disconnected from DeepNexus AI');
    });

    socketEvents.onLog((data) => {
      if (data.level === 'error') {
        addSystemMessage(`❌ ${data.message}`, 'error');
      }
    });

    return () => {
      socketEvents.off('connect');
      socketEvents.off('disconnect');
      socketEvents.off('log');
    };
  }, []);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const initializeChat = async () => {
    try {
      // Check if we have an active session
      const response = await fetch('/api/sessions');
      if (response.ok) {
        const data = await response.json();
        if (data.sessions && data.sessions.length > 0) {
          const activeSession = data.sessions.find((s: any) => s.status === 'active') || data.sessions[0];
          await loadSession(activeSession.id);
        } else {
          await createNewSession();
        }
      } else {
        await createNewSession();
      }
    } catch (error) {
      console.error('Failed to initialize chat:', error);
      addSystemMessage('⚠️ Failed to initialize chat session', 'error');
    }
  };

  const loadSessions = async () => {
    try {
      const response = await fetch('/api/sessions');
      if (response.ok) {
        const data = await response.json();
        setSessions(data.sessions || []);
      }
    } catch (error) {
      console.error('Failed to load sessions:', error);
    }
  };

  const createNewSession = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/sessions/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: `Chat Session ${new Date().toLocaleString()}`,
          project_id: null, // Will be set when project is selected
          config: {
            model: 'deepseek/deepseek-r1-0528:free',
            max_tokens: 4000,
            temperature: 0.7
          }
        }),
      });

      if (response.ok) {
        const data = await response.json();
        const newSession: ChatSession = {
          id: data.session_id,
          name: data.session_name || 'New Chat',
          messages: [],
          status: 'active'
        };
        setCurrentSession(newSession);
        setSessions(prev => [newSession, ...prev]);
        addSystemMessage('🌱 New chat session created! Ready to help with your coding tasks.');
      } else {
        throw new Error('Failed to create session');
      }
    } catch (error) {
      console.error('Failed to create session:', error);
      addSystemMessage('❌ Failed to create new session', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const loadSession = async (sessionId: string) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/sessions/${sessionId}/messages`);
      if (response.ok) {
        const data = await response.json();
        const sessionMessages = data.messages.map((msg: any) => ({
          id: msg.id || Date.now().toString(),
          role: msg.role,
          content: msg.content,
          timestamp: new Date(msg.timestamp),
          type: msg.type || 'text',
          metadata: msg.metadata
        }));
        
        setMessages(sessionMessages);
        setCurrentSession(prev => prev ? { ...prev, messages: sessionMessages } : null);
      }
    } catch (error) {
      console.error('Failed to load session:', error);
      addSystemMessage('⚠️ Failed to load session messages', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const addSystemMessage = (content: string, type: 'text' | 'error' = 'text') => {
    const message: Message = {
      id: Date.now().toString(),
      role: 'system',
      content,
      timestamp: new Date(),
      type
    };
    setMessages(prev => [...prev, message]);
  };

  const sendMessage = async () => {
    if (!inputValue.trim() || isLoading || !currentSession) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: inputValue.trim(),
      timestamp: new Date(),
      type: 'text'
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // Send to AI via our backend
      const response = await fetch('/api/test/llm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage.content,
          session_id: currentSession.id,
          context: {
            project_id: currentSession.projectId,
            conversation_history: messages.slice(-10) // Last 10 messages for context
          }
        }),
      });

      if (response.ok) {
        const data = await response.json();
        
        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: data.response || data.message || 'I received your message!',
          timestamp: new Date(),
          type: data.type || 'text',
          metadata: {
            tokens: data.tokens_used,
            model: data.model,
            executionTime: data.execution_time
          }
        };

        setMessages(prev => [...prev, assistantMessage]);

        // Join session for real-time updates
        if (connected) {
          socketActions.joinSession(currentSession.id);
        }
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'system',
        content: `❌ Failed to send message: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date(),
        type: 'error'
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const formatMessageContent = (message: Message) => {
    if (message.type === 'code') {
      return (
        <pre className="bg-shadow-deep/10 rounded-lg p-4 overflow-x-auto">
          <code className="font-mono text-sm">{message.content}</code>
        </pre>
      );
    }
    return <div className="whitespace-pre-wrap">{message.content}</div>;
  };

  return (
    <div className="flex flex-col h-full bg-mist-gradient">
      {/* Chat Header */}
      <div className="flex items-center justify-between p-4 border-b border-growth-accent/20 bg-crystal-clear">
        <div className="flex items-center space-x-3">
          <div className={`w-3 h-3 rounded-full animate-breathe ${
            connected ? 'bg-growth-accent' : 'bg-crimson-alert'
          }`} />
          <h2 className="text-lg font-semibold text-forest-primary">
            {currentSession?.name || 'AI Chat'}
          </h2>
          <span className="text-sm text-earth-base/60">
            {messages.filter(m => m.role !== 'system').length} messages
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={createNewSession}
            className="px-3 py-1 bg-growth-gradient text-crystal-clear rounded-lg text-sm hover-bloom transition-all"
            disabled={isLoading}
          >
            🌱 New Chat
          </button>
          
          <button
            onClick={loadSessions}
            className="px-3 py-1 bg-mist-gradient text-earth-base rounded-lg text-sm hover-bloom transition-all"
          >
            📋 Sessions
          </button>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'} animate-bloom`}
          >
            <div
              className={`max-w-[80%] rounded-lg p-4 ${
                message.role === 'user'
                  ? 'bg-growth-gradient text-crystal-clear'
                  : message.role === 'system'
                  ? message.type === 'error'
                    ? 'bg-crimson-alert/10 text-crimson-alert border border-crimson-alert/20'
                    : 'bg-bloom-highlight/10 text-amber-data border border-bloom-highlight/20'
                  : 'bg-crystal-clear border border-growth-accent/20 text-earth-base'
              }`}
            >
              <div className="flex items-start space-x-2">
                <span className="text-lg">
                  {message.role === 'user' ? '👤' : 
                   message.role === 'system' ? '🌿' : '🤖'}
                </span>
                <div className="flex-1">
                  {formatMessageContent(message)}
                  
                  {message.metadata && (
                    <div className="mt-2 text-xs opacity-60">
                      {message.metadata.executionTime && (
                        <span>⏱️ {message.metadata.executionTime}ms </span>
                      )}
                      {message.metadata.tokens && (
                        <span>🔤 {message.metadata.tokens} tokens </span>
                      )}
                      {message.metadata.model && (
                        <span>🧠 {message.metadata.model}</span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start animate-bloom">
            <div className="bg-crystal-clear border border-growth-accent/20 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <span className="text-lg">🤖</span>
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-growth-accent rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-growth-accent rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                  <div className="w-2 h-2 bg-growth-accent rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                </div>
                <span className="text-sm text-earth-base/60">AI is thinking...</span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="p-4 border-t border-growth-accent/20 bg-crystal-clear">
        <div className="flex items-end space-x-3">
          <div className="flex-1">
            <textarea
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything about your code... 🌿"
              className="w-full p-3 border border-growth-accent/20 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-growth-accent/50 bg-mist-gradient"
              rows={3}
              disabled={isLoading || !connected}
            />
          </div>
          
          <button
            onClick={sendMessage}
            disabled={!inputValue.trim() || isLoading || !connected}
            className="px-6 py-3 bg-growth-gradient text-crystal-clear rounded-lg font-medium hover-bloom transition-all disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? '🌱' : '🚀'}
          </button>
        </div>
        
        <div className="flex items-center justify-between mt-2 text-xs text-earth-base/60">
          <span>Press Enter to send, Shift+Enter for new line</span>
          <span className={connected ? 'text-growth-accent' : 'text-crimson-alert'}>
            {connected ? '🟢 Connected' : '🔴 Disconnected'}
          </span>
        </div>
      </div>
    </div>
  );
};
