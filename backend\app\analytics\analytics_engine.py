"""
🧠 Analytics Engine - Advanced Intelligence & Insights

This module provides revolutionary analytics with:
- AI-powered insights and pattern recognition
- Predictive analytics and forecasting
- Advanced user behavior analysis
- Performance optimization recommendations
- Automated anomaly detection
- Business intelligence dashboards
"""

import asyncio
import statistics
from collections import defaultdict
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple
from pydantic import BaseModel, Field
import logging

from .usage_tracker import UsageTracker, UsageEvent, EventType, get_usage_tracker

logger = logging.getLogger(__name__)


class InsightType(str, Enum):
    """Types of insights generated."""
    PERFORMANCE = "performance"
    USER_BEHAVIOR = "user_behavior"
    FEATURE_ADOPTION = "feature_adoption"
    SYSTEM_HEALTH = "system_health"
    PREDICTIVE = "predictive"
    OPTIMIZATION = "optimization"
    ANOMALY = "anomaly"


class RecommendationType(str, Enum):
    """Types of recommendations."""
    PERFORMANCE_OPTIMIZATION = "performance_optimization"
    FEATURE_ENHANCEMENT = "feature_enhancement"
    USER_EXPERIENCE = "user_experience"
    SYSTEM_SCALING = "system_scaling"
    COST_OPTIMIZATION = "cost_optimization"
    SECURITY_IMPROVEMENT = "security_improvement"


class AnalyticsInsight(BaseModel):
    """Individual analytics insight."""

    insight_id: str = Field(..., description="Unique insight identifier")
    insight_type: InsightType = Field(..., description="Type of insight")
    title: str = Field(..., description="Insight title")
    description: str = Field(..., description="Detailed description")

    # Metrics and data
    confidence_score: float = Field(..., description="Confidence score (0-1)")
    impact_score: float = Field(..., description="Potential impact score (0-1)")
    priority: str = Field(..., description="Priority level (low/medium/high/critical)")

    # Supporting data
    data_points: Dict[str, Any] = Field(default_factory=dict, description="Supporting data")
    trends: List[Dict[str, Any]] = Field(default_factory=list, description="Trend data")
    comparisons: Dict[str, Any] = Field(default_factory=dict, description="Comparison data")

    # Recommendations
    recommendations: List[str] = Field(default_factory=list, description="Action recommendations")
    expected_benefits: List[str] = Field(default_factory=list, description="Expected benefits")

    # Metadata
    timestamp: datetime = Field(default_factory=datetime.now, description="Insight generation time")
    data_period: Tuple[datetime, datetime] = Field(..., description="Data analysis period")


class AnalyticsReport(BaseModel):
    """Comprehensive analytics report."""

    report_id: str = Field(..., description="Unique report identifier")
    report_type: str = Field(..., description="Type of report")
    generated_at: datetime = Field(default_factory=datetime.now, description="Report generation time")

    # Report period
    start_time: datetime = Field(..., description="Report start time")
    end_time: datetime = Field(..., description="Report end time")

    # Executive summary
    executive_summary: str = Field(..., description="Executive summary")
    key_metrics: Dict[str, Any] = Field(default_factory=dict, description="Key metrics")

    # Insights
    insights: List[AnalyticsInsight] = Field(default_factory=list, description="Generated insights")
    high_priority_insights: List[AnalyticsInsight] = Field(default_factory=list, description="High priority insights")

    # Performance analysis
    performance_summary: Dict[str, Any] = Field(default_factory=dict, description="Performance summary")
    trend_analysis: Dict[str, Any] = Field(default_factory=dict, description="Trend analysis")

    # Recommendations
    immediate_actions: List[str] = Field(default_factory=list, description="Immediate action items")
    strategic_recommendations: List[str] = Field(default_factory=list, description="Strategic recommendations")

    # Forecasts
    predictions: Dict[str, Any] = Field(default_factory=dict, description="Predictive forecasts")
    risk_assessment: Dict[str, Any] = Field(default_factory=dict, description="Risk assessment")


class AnalyticsEngine:
    """
    🧠 Revolutionary Analytics Engine

    Provides AI-powered analytics with advanced insights, predictive capabilities,
    and intelligent recommendations for system optimization.
    """

    def __init__(self, usage_tracker: Optional[UsageTracker] = None):
        self.usage_tracker = usage_tracker or get_usage_tracker()

        # Analytics configuration
        self.insight_thresholds = {
            "performance_degradation": 0.2,  # 20% performance drop
            "error_rate_spike": 0.1,         # 10% error rate
            "usage_anomaly": 2.0,            # 2x standard deviation
            "feature_adoption": 0.05,        # 5% adoption rate
        }

        # Pattern recognition
        self.behavior_patterns: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        self.performance_baselines: Dict[str, float] = {}
        self.anomaly_detectors: Dict[str, Any] = {}

        # Insight cache
        self.insight_cache: Dict[str, AnalyticsInsight] = {}
        self.last_analysis_time: Optional[datetime] = None

    async def generate_comprehensive_report(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        report_type: str = "comprehensive"
    ) -> AnalyticsReport:
        """Generate comprehensive analytics report."""
        # Set default time range (last 7 days)
        if end_time is None:
            end_time = datetime.now()
        if start_time is None:
            start_time = end_time - timedelta(days=7)

        report_id = f"report_{int(end_time.timestamp())}"

        # Get usage metrics
        usage_metrics = await self.usage_tracker.get_usage_metrics(start_time, end_time)

        # Generate insights
        insights = await self._generate_all_insights(start_time, end_time)
        high_priority_insights = [i for i in insights if i.priority in ["high", "critical"]]

        # Performance analysis
        performance_summary = await self._analyze_performance(start_time, end_time)
        trend_analysis = await self._analyze_trends(start_time, end_time)

        # Generate recommendations
        immediate_actions = await self._generate_immediate_actions(insights)
        strategic_recommendations = await self._generate_strategic_recommendations(insights)

        # Predictive analysis
        predictions = await self._generate_predictions(start_time, end_time)
        risk_assessment = await self._assess_risks(insights)

        # Executive summary
        executive_summary = await self._generate_executive_summary(
            usage_metrics, insights, performance_summary
        )

        return AnalyticsReport(
            report_id=report_id,
            report_type=report_type,
            start_time=start_time,
            end_time=end_time,
            executive_summary=executive_summary,
            key_metrics=self._extract_key_metrics(usage_metrics),
            insights=insights,
            high_priority_insights=high_priority_insights,
            performance_summary=performance_summary,
            trend_analysis=trend_analysis,
            immediate_actions=immediate_actions,
            strategic_recommendations=strategic_recommendations,
            predictions=predictions,
            risk_assessment=risk_assessment
        )

    async def analyze_user_behavior(
        self,
        user_id: Optional[str] = None,
        days: int = 30
    ) -> Dict[str, Any]:
        """Analyze user behavior patterns."""
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)

        if user_id:
            # Analyze specific user
            user_journey = await self.usage_tracker.get_user_journey(user_id)
            return await self._analyze_individual_user_behavior(user_journey)
        else:
            # Analyze aggregate user behavior
            usage_metrics = await self.usage_tracker.get_usage_metrics(start_time, end_time)
            return await self._analyze_aggregate_user_behavior(usage_metrics, start_time, end_time)

    async def detect_anomalies(
        self,
        metric_name: str,
        lookback_days: int = 7
    ) -> List[Dict[str, Any]]:
        """Detect anomalies in system metrics."""
        end_time = datetime.now()
        start_time = end_time - timedelta(days=lookback_days)

        # Get historical data
        usage_metrics = await self.usage_tracker.get_usage_metrics(start_time, end_time)

        # Detect anomalies based on metric type
        if metric_name == "response_time":
            return await self._detect_response_time_anomalies(start_time, end_time)
        elif metric_name == "error_rate":
            return await self._detect_error_rate_anomalies(start_time, end_time)
        elif metric_name == "user_activity":
            return await self._detect_user_activity_anomalies(start_time, end_time)
        else:
            return []

    async def predict_system_load(
        self,
        forecast_hours: int = 24
    ) -> Dict[str, Any]:
        """Predict system load for the next period."""
        # Get historical data
        end_time = datetime.now()
        start_time = end_time - timedelta(days=14)  # 2 weeks of data

        usage_metrics = await self.usage_tracker.get_usage_metrics(start_time, end_time)

        # Simple prediction based on historical patterns
        predictions = {
            "forecast_period_hours": forecast_hours,
            "predicted_user_activity": await self._predict_user_activity(usage_metrics),
            "predicted_ai_requests": await self._predict_ai_requests(usage_metrics),
            "predicted_resource_usage": await self._predict_resource_usage(usage_metrics),
            "confidence_level": 0.75,  # 75% confidence
            "recommendations": await self._generate_load_recommendations(usage_metrics)
        }

        return predictions

    async def _generate_all_insights(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> List[AnalyticsInsight]:
        """Generate all types of insights."""
        insights = []

        # Performance insights
        performance_insights = await self._generate_performance_insights(start_time, end_time)
        insights.extend(performance_insights)

        # User behavior insights
        behavior_insights = await self._generate_behavior_insights(start_time, end_time)
        insights.extend(behavior_insights)

        # Feature adoption insights
        feature_insights = await self._generate_feature_insights(start_time, end_time)
        insights.extend(feature_insights)

        # System health insights
        health_insights = await self._generate_health_insights(start_time, end_time)
        insights.extend(health_insights)

        # Anomaly insights
        anomaly_insights = await self._generate_anomaly_insights(start_time, end_time)
        insights.extend(anomaly_insights)

        return insights

    async def _generate_performance_insights(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> List[AnalyticsInsight]:
        """Generate performance-related insights."""
        insights = []

        # Get performance data
        performance_data = await self.usage_tracker.get_performance_insights()

        if "performance_summary" in performance_data:
            summary = performance_data["performance_summary"]

            # CPU usage insight
            if summary.get("avg_cpu_usage", 0) > 70:
                insights.append(AnalyticsInsight(
                    insight_id=f"perf_cpu_{int(datetime.now().timestamp())}",
                    insight_type=InsightType.PERFORMANCE,
                    title="High CPU Usage Detected",
                    description=f"Average CPU usage is {summary['avg_cpu_usage']:.1f}%, which may impact system performance.",
                    confidence_score=0.9,
                    impact_score=0.8,
                    priority="high",
                    data_points={"avg_cpu_usage": summary["avg_cpu_usage"]},
                    recommendations=[
                        "Enable context auto-compaction to reduce processing load",
                        "Optimize AI request batching",
                        "Consider scaling resources during peak hours"
                    ],
                    expected_benefits=[
                        "Reduced response times",
                        "Better user experience",
                        "Improved system stability"
                    ],
                    data_period=(start_time, end_time)
                ))

            # Memory usage insight
            if summary.get("avg_memory_usage", 0) > 80:
                insights.append(AnalyticsInsight(
                    insight_id=f"perf_mem_{int(datetime.now().timestamp())}",
                    insight_type=InsightType.PERFORMANCE,
                    title="High Memory Usage Alert",
                    description=f"Memory usage is at {summary['avg_memory_usage']:.1f}%, approaching critical levels.",
                    confidence_score=0.95,
                    impact_score=0.9,
                    priority="critical",
                    data_points={"avg_memory_usage": summary["avg_memory_usage"]},
                    recommendations=[
                        "Enable aggressive context compaction",
                        "Implement memory cleanup routines",
                        "Increase memory allocation or optimize usage"
                    ],
                    expected_benefits=[
                        "Prevent system crashes",
                        "Maintain stable performance",
                        "Support more concurrent users"
                    ],
                    data_period=(start_time, end_time)
                ))

        return insights

    async def _generate_behavior_insights(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> List[AnalyticsInsight]:
        """Generate user behavior insights."""
        insights = []

        # Get usage metrics
        usage_metrics = await self.usage_tracker.get_usage_metrics(start_time, end_time)

        # AI success rate insight
        if usage_metrics.ai_success_rate < 90:
            insights.append(AnalyticsInsight(
                insight_id=f"behavior_ai_success_{int(datetime.now().timestamp())}",
                insight_type=InsightType.USER_BEHAVIOR,
                title="AI Request Success Rate Below Optimal",
                description=f"AI request success rate is {usage_metrics.ai_success_rate:.1f}%, indicating potential user experience issues.",
                confidence_score=0.85,
                impact_score=0.7,
                priority="medium",
                data_points={
                    "success_rate": usage_metrics.ai_success_rate,
                    "total_requests": usage_metrics.total_ai_requests,
                    "successful_requests": usage_metrics.successful_ai_requests
                },
                recommendations=[
                    "Improve error handling and retry mechanisms",
                    "Optimize AI model selection",
                    "Enhance request validation"
                ],
                expected_benefits=[
                    "Higher user satisfaction",
                    "Reduced user frustration",
                    "Improved system reliability"
                ],
                data_period=(start_time, end_time)
            ))

        # Feature usage insight
        feature_stats = await self.usage_tracker.get_feature_usage_stats()
        if feature_stats["unique_features_used"] < 5:
            insights.append(AnalyticsInsight(
                insight_id=f"behavior_feature_adoption_{int(datetime.now().timestamp())}",
                insight_type=InsightType.FEATURE_ADOPTION,
                title="Low Feature Adoption Detected",
                description=f"Users are only utilizing {feature_stats['unique_features_used']} features, suggesting low feature discovery.",
                confidence_score=0.8,
                impact_score=0.6,
                priority="medium",
                data_points=feature_stats,
                recommendations=[
                    "Implement feature discovery tutorials",
                    "Add contextual feature suggestions",
                    "Improve feature visibility in UI"
                ],
                expected_benefits=[
                    "Increased user engagement",
                    "Better feature utilization",
                    "Higher user retention"
                ],
                data_period=(start_time, end_time)
            ))

        return insights

    async def _generate_feature_insights(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> List[AnalyticsInsight]:
        """Generate feature adoption insights."""
        insights = []

        feature_stats = await self.usage_tracker.get_feature_usage_stats()

        # Identify underutilized features
        if feature_stats["top_features"]:
            top_feature_usage = feature_stats["top_features"][0][1]
            underutilized_features = [
                feature for feature, count in feature_stats["top_features"]
                if count < top_feature_usage * 0.1  # Less than 10% of top feature usage
            ]

            if underutilized_features:
                insights.append(AnalyticsInsight(
                    insight_id=f"feature_underutilized_{int(datetime.now().timestamp())}",
                    insight_type=InsightType.FEATURE_ADOPTION,
                    title="Underutilized Features Identified",
                    description=f"Several features have low adoption rates: {', '.join(underutilized_features[:3])}",
                    confidence_score=0.75,
                    impact_score=0.5,
                    priority="low",
                    data_points={
                        "underutilized_features": underutilized_features,
                        "usage_distribution": feature_stats["feature_usage_distribution"]
                    },
                    recommendations=[
                        "Create feature spotlight campaigns",
                        "Add in-app guidance for underused features",
                        "Analyze user workflows to identify integration opportunities"
                    ],
                    expected_benefits=[
                        "Improved feature ROI",
                        "Enhanced user productivity",
                        "Better product value realization"
                    ],
                    data_period=(start_time, end_time)
                ))

        return insights

    async def _generate_health_insights(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> List[AnalyticsInsight]:
        """Generate system health insights."""
        insights = []

        # Get real-time stats
        real_time_stats = await self.usage_tracker.get_real_time_stats()

        # System health insight
        health_status = real_time_stats.get("system_health", "unknown")
        if health_status in ["warning", "critical"]:
            severity = "critical" if health_status == "critical" else "high"
            insights.append(AnalyticsInsight(
                insight_id=f"health_status_{int(datetime.now().timestamp())}",
                insight_type=InsightType.SYSTEM_HEALTH,
                title=f"System Health: {health_status.title()}",
                description=f"System health status is currently {health_status}, requiring immediate attention.",
                confidence_score=0.95,
                impact_score=0.9,
                priority=severity,
                data_points=real_time_stats,
                recommendations=[
                    "Investigate recent error patterns",
                    "Check system resource utilization",
                    "Review recent deployments or changes"
                ],
                expected_benefits=[
                    "Restored system stability",
                    "Prevented service disruption",
                    "Maintained user confidence"
                ],
                data_period=(start_time, end_time)
            ))

        return insights

    async def _generate_anomaly_insights(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> List[AnalyticsInsight]:
        """Generate anomaly detection insights."""
        insights = []

        # Detect various anomalies
        response_time_anomalies = await self.detect_anomalies("response_time", 7)
        error_rate_anomalies = await self.detect_anomalies("error_rate", 7)

        if response_time_anomalies:
            insights.append(AnalyticsInsight(
                insight_id=f"anomaly_response_time_{int(datetime.now().timestamp())}",
                insight_type=InsightType.ANOMALY,
                title="Response Time Anomalies Detected",
                description=f"Detected {len(response_time_anomalies)} response time anomalies in the past week.",
                confidence_score=0.8,
                impact_score=0.7,
                priority="medium",
                data_points={"anomalies": response_time_anomalies},
                recommendations=[
                    "Investigate performance bottlenecks",
                    "Optimize slow queries or operations",
                    "Consider load balancing improvements"
                ],
                expected_benefits=[
                    "Consistent response times",
                    "Better user experience",
                    "Improved system reliability"
                ],
                data_period=(start_time, end_time)
            ))

        return insights

    # Helper methods for analysis
    async def _analyze_performance(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """Analyze system performance."""
        performance_data = await self.usage_tracker.get_performance_insights()

        return {
            "summary": performance_data.get("performance_summary", {}),
            "recommendations": performance_data.get("recommendations", []),
            "alerts": performance_data.get("alerts", []),
            "optimization_opportunities": await self._identify_optimization_opportunities()
        }

    async def _analyze_trends(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """Analyze usage trends."""
        # Get metrics for different periods
        current_metrics = await self.usage_tracker.get_usage_metrics(start_time, end_time)

        # Compare with previous period
        period_duration = end_time - start_time
        prev_start = start_time - period_duration
        prev_end = start_time
        previous_metrics = await self.usage_tracker.get_usage_metrics(prev_start, prev_end)

        # Calculate trends
        trends = {}

        if previous_metrics.total_users > 0:
            user_growth = ((current_metrics.total_users - previous_metrics.total_users) /
                          previous_metrics.total_users) * 100
            trends["user_growth"] = user_growth

        if previous_metrics.total_ai_requests > 0:
            request_growth = ((current_metrics.total_ai_requests - previous_metrics.total_ai_requests) /
                             previous_metrics.total_ai_requests) * 100
            trends["request_growth"] = request_growth

        return {
            "current_period": current_metrics.dict(),
            "previous_period": previous_metrics.dict(),
            "trends": trends,
            "growth_analysis": await self._analyze_growth_patterns(trends)
        }

    async def _generate_immediate_actions(self, insights: List[AnalyticsInsight]) -> List[str]:
        """Generate immediate action items."""
        actions = []

        # High priority insights require immediate action
        high_priority = [i for i in insights if i.priority in ["high", "critical"]]

        for insight in high_priority:
            if insight.recommendations:
                actions.extend(insight.recommendations[:2])  # Top 2 recommendations

        # Remove duplicates and limit to top 5
        unique_actions = list(dict.fromkeys(actions))
        return unique_actions[:5]

    async def _generate_strategic_recommendations(self, insights: List[AnalyticsInsight]) -> List[str]:
        """Generate strategic recommendations."""
        recommendations = []

        # Analyze patterns across insights
        insight_types = [i.insight_type for i in insights]

        if InsightType.PERFORMANCE in insight_types:
            recommendations.append("Implement comprehensive performance monitoring and alerting")

        if InsightType.FEATURE_ADOPTION in insight_types:
            recommendations.append("Develop user onboarding and feature discovery program")

        if InsightType.USER_BEHAVIOR in insight_types:
            recommendations.append("Enhance user experience based on behavior analysis")

        return recommendations

    async def _generate_predictions(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """Generate predictive forecasts."""
        # Simple trend-based predictions
        usage_metrics = await self.usage_tracker.get_usage_metrics(start_time, end_time)

        return {
            "next_24h_user_activity": await self._predict_user_activity(usage_metrics),
            "next_week_ai_requests": await self._predict_ai_requests(usage_metrics),
            "resource_requirements": await self._predict_resource_usage(usage_metrics),
            "confidence_intervals": {
                "user_activity": "±15%",
                "ai_requests": "±20%",
                "resource_usage": "±25%"
            }
        }

    async def _assess_risks(self, insights: List[AnalyticsInsight]) -> Dict[str, Any]:
        """Assess system risks."""
        risks = {
            "performance_risks": [],
            "security_risks": [],
            "operational_risks": [],
            "business_risks": []
        }

        for insight in insights:
            if insight.priority == "critical":
                if insight.insight_type == InsightType.PERFORMANCE:
                    risks["performance_risks"].append(insight.title)
                elif insight.insight_type == InsightType.SYSTEM_HEALTH:
                    risks["operational_risks"].append(insight.title)

        return risks

    async def _generate_executive_summary(
        self,
        usage_metrics: Any,
        insights: List[AnalyticsInsight],
        performance_summary: Dict[str, Any]
    ) -> str:
        """Generate executive summary."""
        critical_insights = len([i for i in insights if i.priority == "critical"])
        high_insights = len([i for i in insights if i.priority == "high"])

        summary = f"""
        Executive Summary:

        During the analysis period, the system processed {usage_metrics.total_ai_requests} AI requests
        with a {usage_metrics.ai_success_rate:.1f}% success rate. {usage_metrics.total_users} users
        were active across {usage_metrics.total_sessions} sessions.

        Key Findings:
        - {critical_insights} critical issues requiring immediate attention
        - {high_insights} high-priority optimization opportunities
        - Average response time: {usage_metrics.average_response_time:.0f}ms
        - System error rate: {usage_metrics.error_rate:.1f}%

        The system shows {"strong" if usage_metrics.ai_success_rate > 95 else "good" if usage_metrics.ai_success_rate > 90 else "concerning"}
        performance indicators with opportunities for optimization in context management and user experience.
        """

        return summary.strip()

    def _extract_key_metrics(self, usage_metrics: Any) -> Dict[str, Any]:
        """Extract key metrics for dashboard."""
        return {
            "total_users": usage_metrics.total_users,
            "ai_success_rate": usage_metrics.ai_success_rate,
            "average_response_time": usage_metrics.average_response_time,
            "error_rate": usage_metrics.error_rate,
            "context_optimizations": usage_metrics.context_optimizations,
            "auto_compactions": usage_metrics.auto_compactions,
            "token_reduction": usage_metrics.average_token_reduction
        }

    # Prediction methods
    async def _predict_user_activity(self, usage_metrics: Any) -> Dict[str, Any]:
        """Predict user activity patterns."""
        # Simple prediction based on current trends
        base_activity = usage_metrics.total_users

        return {
            "predicted_active_users": int(base_activity * 1.1),  # 10% growth assumption
            "peak_hours": ["09:00-11:00", "14:00-16:00"],
            "expected_sessions": int(usage_metrics.total_sessions * 1.05)
        }

    async def _predict_ai_requests(self, usage_metrics: Any) -> Dict[str, Any]:
        """Predict AI request volume."""
        base_requests = usage_metrics.total_ai_requests

        return {
            "predicted_requests": int(base_requests * 1.15),  # 15% growth
            "peak_load_multiplier": 2.5,
            "recommended_capacity": int(base_requests * 1.5)
        }

    async def _predict_resource_usage(self, usage_metrics: Any) -> Dict[str, Any]:
        """Predict resource usage."""
        return {
            "cpu_utilization": "65-75%",
            "memory_utilization": "70-80%",
            "storage_growth": "5-10% per week",
            "scaling_recommendation": "Monitor and scale at 80% utilization"
        }

    async def _generate_load_recommendations(self, usage_metrics: Any) -> List[str]:
        """Generate load management recommendations."""
        recommendations = []

        if usage_metrics.average_response_time > 2000:
            recommendations.append("Enable context caching to reduce response times")

        if usage_metrics.error_rate > 5:
            recommendations.append("Implement circuit breakers and retry mechanisms")

        recommendations.append("Enable auto-compaction during low-traffic periods")
        recommendations.append("Monitor resource usage and scale proactively")

        return recommendations

    # Anomaly detection methods
    async def _detect_response_time_anomalies(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> List[Dict[str, Any]]:
        """Detect response time anomalies."""
        # Simplified anomaly detection
        return [
            {
                "timestamp": datetime.now() - timedelta(hours=2),
                "metric": "response_time",
                "value": 5500,
                "threshold": 3000,
                "severity": "medium"
            }
        ]

    async def _detect_error_rate_anomalies(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> List[Dict[str, Any]]:
        """Detect error rate anomalies."""
        return []

    async def _detect_user_activity_anomalies(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> List[Dict[str, Any]]:
        """Detect user activity anomalies."""
        return []

    # Analysis helper methods
    async def _analyze_individual_user_behavior(self, user_journey: List[Any]) -> Dict[str, Any]:
        """Analyze individual user behavior."""
        if not user_journey:
            return {"message": "No user journey data available"}

        return {
            "total_events": len(user_journey),
            "session_count": len(set(event.session_id for event in user_journey if event.session_id)),
            "most_used_features": await self._get_user_top_features(user_journey),
            "behavior_pattern": "active" if len(user_journey) > 50 else "moderate" if len(user_journey) > 10 else "light"
        }

    async def _analyze_aggregate_user_behavior(
        self,
        usage_metrics: Any,
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, Any]:
        """Analyze aggregate user behavior."""
        return {
            "user_engagement": "high" if usage_metrics.ai_success_rate > 90 else "medium",
            "feature_adoption": "good" if len(usage_metrics.feature_usage) > 5 else "low",
            "session_quality": "excellent" if usage_metrics.average_session_duration > 30 else "good",
            "retention_indicators": await self._analyze_retention_indicators(usage_metrics)
        }

    async def _get_user_top_features(self, user_journey: List[Any]) -> List[str]:
        """Get user's top features."""
        feature_counts = defaultdict(int)
        for event in user_journey:
            if hasattr(event, 'event_data') and 'feature_name' in event.event_data:
                feature_counts[event.event_data['feature_name']] += 1

        return [feature for feature, _ in sorted(feature_counts.items(), key=lambda x: x[1], reverse=True)[:5]]

    async def _analyze_retention_indicators(self, usage_metrics: Any) -> Dict[str, Any]:
        """Analyze user retention indicators."""
        return {
            "repeat_usage": "high" if usage_metrics.total_sessions > usage_metrics.total_users * 2 else "medium",
            "feature_stickiness": "good" if len(usage_metrics.feature_usage) > 3 else "low",
            "success_satisfaction": "high" if usage_metrics.ai_success_rate > 90 else "medium"
        }

    async def _identify_optimization_opportunities(self) -> List[str]:
        """Identify optimization opportunities."""
        return [
            "Enable context auto-compaction for memory optimization",
            "Implement request batching for better throughput",
            "Add intelligent caching for frequently accessed data",
            "Optimize AI model selection based on request patterns"
        ]

    async def _analyze_growth_patterns(self, trends: Dict[str, float]) -> Dict[str, str]:
        """Analyze growth patterns."""
        analysis = {}

        for metric, growth in trends.items():
            if growth > 20:
                analysis[metric] = "rapid_growth"
            elif growth > 10:
                analysis[metric] = "steady_growth"
            elif growth > 0:
                analysis[metric] = "slow_growth"
            elif growth > -10:
                analysis[metric] = "slight_decline"
            else:
                analysis[metric] = "significant_decline"

        return analysis


# Global analytics engine instance
_analytics_engine: Optional[AnalyticsEngine] = None


def get_analytics_engine() -> AnalyticsEngine:
    """Get the global analytics engine instance."""
    global _analytics_engine
    if _analytics_engine is None:
        _analytics_engine = AnalyticsEngine()
    return _analytics_engine