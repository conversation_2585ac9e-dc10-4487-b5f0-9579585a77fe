"""
Project Manager - Multi-Project Support Core Service

Manages project lifecycle, workspace isolation, and project-specific configurations.
Ensures complete isolation between projects for embeddings, sessions, and file operations.
"""

import json
import logging
import shutil
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

try:
    import logfire
except ImportError:
    # Mock logfire if not available
    class MockLogfire:
        def span(self, *args, **kwargs):
            from contextlib import nullcontext
            return nullcontext()
    logfire = MockLogfire()

from qdrant_client import QdrantClient
from qdrant_client.models import VectorParams, Distance

try:
    from backend.app.models.project import (
        Project, ProjectConfig, ProjectMetadata, ProjectCreateRequest,
        ProjectListResponse, ProjectResponse
    )
    from backend.config import settings
except ImportError:
    from app.models.project import (
        Project, ProjectConfig, ProjectMetadata, ProjectCreateRequest,
        ProjectListResponse, ProjectResponse
    )
    from config import settings

logger = logging.getLogger(__name__)


class ProjectManager:
    """
    Central project management service providing complete project isolation.
    
    Features:
    - Project workspace creation and management
    - Project-specific embedding collections
    - Project-scoped file operations
    - Project configuration management
    - Project metadata tracking
    """
    
    def __init__(self, base_workspace: str = None):
        self.base_workspace = Path(base_workspace or settings.workspace_root)
        self.projects_db_path = self.base_workspace / "projects.db"
        self.active_projects: Dict[str, Project] = {}
        self.current_project: Optional[Project] = None
        
        # Initialize components
        self.qdrant_client = QdrantClient(url=settings.qdrant_url)
        
        # Ensure base workspace exists
        self.base_workspace.mkdir(parents=True, exist_ok=True)
        
        # Initialize projects database
        self._init_projects_database()
        
        # Load existing projects
        self._load_existing_projects()
        
        logger.info(f"🏗️ Project Manager initialized with workspace: {self.base_workspace}")
    
    def _init_projects_database(self) -> None:
        """Initialize the projects database."""
        with sqlite3.connect(self.projects_db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS projects (
                    project_id TEXT PRIMARY KEY,
                    slug TEXT UNIQUE NOT NULL,
                    config TEXT NOT NULL,
                    metadata TEXT NOT NULL,
                    created_at TIMESTAMP NOT NULL,
                    updated_at TIMESTAMP NOT NULL,
                    last_accessed TIMESTAMP NOT NULL,
                    status TEXT NOT NULL DEFAULT 'active'
                )
            """)
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_projects_slug ON projects(slug)
            """)
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status)
            """)
    
    def _load_existing_projects(self) -> None:
        """Load existing projects from database."""
        try:
            with sqlite3.connect(self.projects_db_path) as conn:
                cursor = conn.execute("""
                    SELECT project_id, slug, config, metadata, created_at, updated_at, last_accessed, status
                    FROM projects WHERE status = 'active'
                """)
                
                for row in cursor.fetchall():
                    project_id, slug, config_json, metadata_json, created_at, updated_at, last_accessed, status = row
                    
                    try:
                        config = ProjectConfig.parse_raw(config_json)
                        metadata = ProjectMetadata.parse_raw(metadata_json)
                        
                        project = Project(
                            project_id=project_id,
                            slug=slug,
                            config=config,
                            metadata=metadata,
                            created_at=datetime.fromisoformat(created_at),
                            updated_at=datetime.fromisoformat(updated_at),
                            last_accessed=datetime.fromisoformat(last_accessed),
                            status=status
                        )
                        
                        self.active_projects[project_id] = project
                        
                    except Exception as e:
                        logger.error(f"Failed to load project {project_id}: {e}")
                
                logger.info(f"📁 Loaded {len(self.active_projects)} existing projects")
                
        except Exception as e:
            logger.error(f"Failed to load existing projects: {e}")
    
    async def create_project(self, request: ProjectCreateRequest) -> ProjectResponse:
        """
        Create a new project with complete workspace isolation.
        
        Args:
            request: Project creation request
            
        Returns:
            Project creation response
        """
        with logfire.span("create_project", project_slug=request.slug):
            try:
                # Check if project already exists
                if await self.project_exists(request.slug):
                    return ProjectResponse(
                        status="error",
                        message=f"Project with slug '{request.slug}' already exists",
                        error="Project already exists"
                    )
                
                # Create project configuration
                config = ProjectConfig(
                    name=request.name,
                    description=request.description,
                    language=request.language,
                    framework=request.framework
                )
                
                # Create project
                project = Project(
                    slug=request.slug,
                    config=config
                )
                
                # Create project workspace structure
                await self._create_project_workspace(project)
                
                # Create project-specific Qdrant collection
                await self._create_project_embeddings_collection(project)
                
                # Initialize project database
                await self._init_project_database(project)
                
                # Save project to database
                await self._save_project(project)
                
                # Add to active projects
                self.active_projects[project.project_id] = project
                
                logger.info(f"✅ Created project: {project.slug} ({project.project_id})")
                
                return ProjectResponse(
                    status="success",
                    message=f"Project '{project.config.name}' created successfully",
                    project=project
                )
                
            except Exception as e:
                logger.error(f"❌ Failed to create project: {e}")
                return ProjectResponse(
                    status="error",
                    message="Failed to create project",
                    error=str(e)
                )
    
    async def _create_project_workspace(self, project: Project) -> None:
        """Create the project workspace directory structure."""
        workspace_path = project.get_workspace_path(str(self.base_workspace))
        
        # Create main directories
        directories = [
            workspace_path,
            workspace_path / "src",
            workspace_path / "docs", 
            workspace_path / "tests",
            workspace_path / ".deepnexus",
            workspace_path / ".deepnexus" / "backups",
            workspace_path / ".deepnexus" / "cache"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
        
        # Create project config file
        config_path = project.get_config_path(str(self.base_workspace))
        with open(config_path, 'w') as f:
            json.dump(project.config.dict(), f, indent=2, default=str)
        
        # Create README.md
        readme_path = workspace_path / "README.md"
        readme_content = f"""# {project.config.name}

{project.config.description}

## Project Information

- **Language**: {project.config.language}
- **Framework**: {project.config.framework}
- **Created**: {project.created_at.strftime('%Y-%m-%d %H:%M:%S')}
- **Project ID**: {project.project_id}

## Getting Started

This project was created with DeepNexus AI Coder Agent.
Use the AI tools to develop, plan, and manage your code.

## Directory Structure

- `src/` - Source code
- `docs/` - Documentation
- `tests/` - Test files
- `.deepnexus/` - AI system files (do not modify manually)
"""
        
        with open(readme_path, 'w') as f:
            f.write(readme_content)
        
        logger.info(f"📁 Created workspace structure for project: {project.slug}")
    
    async def _create_project_embeddings_collection(self, project: Project) -> None:
        """Create project-specific Qdrant collection for embeddings."""
        collection_name = project.get_embeddings_collection_name()
        
        try:
            # Check if collection already exists
            collections = self.qdrant_client.get_collections()
            existing_names = [col.name for col in collections.collections]
            
            if collection_name not in existing_names:
                # Create collection
                self.qdrant_client.create_collection(
                    collection_name=collection_name,
                    vectors_config=VectorParams(
                        size=settings.embedding_dim,
                        distance=Distance.COSINE
                    )
                )
                logger.info(f"🔍 Created embeddings collection: {collection_name}")
            else:
                logger.info(f"🔍 Embeddings collection already exists: {collection_name}")
                
        except Exception as e:
            logger.error(f"❌ Failed to create embeddings collection: {e}")
            raise
    
    async def _init_project_database(self, project: Project) -> None:
        """Initialize project-specific database."""
        db_path = project.get_sessions_db_path(str(self.base_workspace))
        
        with sqlite3.connect(db_path) as conn:
            # Sessions table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id TEXT PRIMARY KEY,
                    project_id TEXT NOT NULL,
                    created_at TIMESTAMP NOT NULL,
                    updated_at TIMESTAMP NOT NULL,
                    session_data TEXT,
                    status TEXT DEFAULT 'active'
                )
            """)
            
            # Plans table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS plans (
                    plan_id TEXT PRIMARY KEY,
                    project_id TEXT NOT NULL,
                    title TEXT NOT NULL,
                    plan_data TEXT NOT NULL,
                    created_at TIMESTAMP NOT NULL,
                    status TEXT DEFAULT 'active'
                )
            """)
            
            # Tasks table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS tasks (
                    task_id TEXT PRIMARY KEY,
                    project_id TEXT NOT NULL,
                    plan_id TEXT,
                    title TEXT NOT NULL,
                    description TEXT,
                    status TEXT DEFAULT 'pending',
                    created_at TIMESTAMP NOT NULL,
                    completed_at TIMESTAMP
                )
            """)
        
        logger.info(f"💾 Initialized project database: {db_path}")
    
    async def _save_project(self, project: Project) -> None:
        """Save project to database."""
        with sqlite3.connect(self.projects_db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO projects 
                (project_id, slug, config, metadata, created_at, updated_at, last_accessed, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                project.project_id,
                project.slug,
                project.config.json(),
                project.metadata.json(),
                project.created_at.isoformat(),
                project.updated_at.isoformat(),
                project.last_accessed.isoformat(),
                project.status
            ))
    
    async def project_exists(self, slug: str) -> bool:
        """Check if a project with the given slug exists."""
        with sqlite3.connect(self.projects_db_path) as conn:
            cursor = conn.execute("SELECT 1 FROM projects WHERE slug = ? AND status = 'active'", (slug,))
            return cursor.fetchone() is not None
    
    async def get_project(self, slug: str) -> Optional[Project]:
        """Get project by slug."""
        for project in self.active_projects.values():
            if project.slug == slug:
                return project
        return None
    
    async def list_projects(self) -> ProjectListResponse:
        """List all projects."""
        projects = list(self.active_projects.values())
        active_count = len([p for p in projects if p.status == 'active'])
        archived_count = len([p for p in projects if p.status == 'archived'])
        
        return ProjectListResponse(
            projects=projects,
            total=len(projects),
            active=active_count,
            archived=archived_count
        )
    
    async def set_current_project(self, slug: str) -> bool:
        """Set the current active project."""
        project = await self.get_project(slug)
        if project:
            self.current_project = project
            # Update last accessed time
            project.last_accessed = datetime.now()
            await self._save_project(project)
            logger.info(f"🎯 Set current project: {slug}")
            return True
        return False
    
    def get_current_project(self) -> Optional[Project]:
        """Get the currently active project."""
        return self.current_project
    
    def get_project_workspace_path(self, project: Optional[Project] = None) -> Path:
        """Get workspace path for project (current project if none specified)."""
        target_project = project or self.current_project
        if not target_project:
            raise ValueError("No project specified and no current project set")
        
        return target_project.get_workspace_path(str(self.base_workspace))


# Global project manager instance
project_manager = ProjectManager()
