@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for the AI Coder Agent */
@layer base {
  html, body {
    @apply h-full;
  }

  body {
    @apply m-0 font-sans antialiased;
  }
}

@layer components {
  .terminal-output {
    @apply font-mono text-sm bg-terminal-bg text-terminal-text p-4 rounded;
  }

  .code-block {
    @apply font-mono text-sm bg-gray-100 dark:bg-gray-800 p-4 rounded border;
  }

  .log-entry {
    @apply text-sm py-1 px-2 border-l-2;
  }

  .log-info {
    @apply border-blue-400 bg-blue-50 text-blue-800;
  }

  .log-warning {
    @apply border-yellow-400 bg-yellow-50 text-yellow-800;
  }

  .log-error {
    @apply border-red-400 bg-red-50 text-red-800;
  }

  .file-tree-item {
    @apply cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded;
  }

  .file-tree-folder {
    @apply font-medium text-blue-600 dark:text-blue-400;
  }

  .file-tree-file {
    @apply text-gray-700 dark:text-gray-300;
  }
}
