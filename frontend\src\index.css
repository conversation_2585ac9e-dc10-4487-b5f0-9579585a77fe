@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import our Digital Forest Design System */
@import './styles/design-system.css';

/* Global Styles */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow-x: hidden;
}

#root {
  height: 100%;
}

/* Scrollbar Styling - Nature Inspired */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--frost-light);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--growth-accent);
  border-radius: var(--radius-sm);
  transition: background var(--duration-normal);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--meadow-bright);
}

/* Selection Styling */
::selection {
  background: var(--growth-accent);
  color: var(--crystal-clear);
}

/* Utility Classes for Tailwind Integration */
@layer utilities {
  .text-forest-primary { color: var(--forest-primary); }
  .text-growth-accent { color: var(--growth-accent); }
  .text-bloom-highlight { color: var(--bloom-highlight); }
  .text-earth-base { color: var(--earth-base); }
  .text-crimson-alert { color: var(--crimson-alert); }
  .text-golden-success { color: var(--golden-success); }
  .text-crystal-clear { color: var(--crystal-clear); }

  .bg-forest-primary { background-color: var(--forest-primary); }
  .bg-growth-accent { background-color: var(--growth-accent); }
  .bg-bloom-highlight { background-color: var(--bloom-highlight); }
  .bg-earth-base { background-color: var(--earth-base); }
  .bg-mist-overlay { background-color: var(--mist-overlay); }
  .bg-crimson-alert { background-color: var(--crimson-alert); }
  .bg-golden-success { background-color: var(--golden-success); }
  .bg-crystal-clear { background-color: var(--crystal-clear); }

  .border-growth-accent { border-color: var(--growth-accent); }
  .border-forest-primary { border-color: var(--forest-primary); }

  .shadow-organic { box-shadow: var(--shadow-organic); }
}
