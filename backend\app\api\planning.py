"""
AI Planning API Endpoints - Phase 8D

FastAPI endpoints for the AI-powered planning system.
Provides REST API for frontend integration.
"""

from fastapi import APIRouter, HTTPException, Query, Body
from typing import List, Dict, Any, Optional
import logging

from ..ai_planning.core import planning_engine
from ..ai_planning.models import (
    PlanningRequest, PlanningResponse, DevelopmentPlan,
    PlanningSession, PlanValidation
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/planning", tags=["AI Planning"])


@router.post("/create", response_model=PlanningResponse)
async def create_development_plan(
    request: PlanningRequest,
    session_id: Optional[str] = Query(None, description="Optional session ID")
):
    """
    Create a comprehensive development plan using AI.
    
    This endpoint uses a multi-AI approach:
    1. Primary planning agent creates the initial plan
    2. Validation agent analyzes and improves the plan
    3. Returns comprehensive plan with validation feedback
    """
    try:
        logger.info(f"📋 Creating AI plan: {request.title}")
        response = await planning_engine.create_plan(request, session_id)
        return response
    except Exception as e:
        logger.error(f"❌ Plan creation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Plan creation failed: {str(e)}")


@router.get("/sessions", response_model=List[PlanningSession])
async def list_planning_sessions(
    user_id: Optional[str] = Query(None, description="Filter by user ID")
):
    """List all planning sessions, optionally filtered by user."""
    try:
        sessions = await planning_engine.list_sessions(user_id)
        return sessions
    except Exception as e:
        logger.error(f"❌ Failed to list sessions: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list sessions: {str(e)}")


@router.get("/sessions/{session_id}", response_model=PlanningSession)
async def get_planning_session(session_id: str):
    """Get a specific planning session by ID."""
    try:
        session = await planning_engine.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        return session
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get session: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get session: {str(e)}")


@router.delete("/sessions/{session_id}")
async def delete_planning_session(session_id: str):
    """Delete a planning session."""
    try:
        success = await planning_engine.delete_session(session_id)
        if not success:
            raise HTTPException(status_code=404, detail="Session not found")
        return {"message": "Session deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to delete session: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete session: {str(e)}")


@router.get("/plans/{plan_id}", response_model=DevelopmentPlan)
async def get_development_plan(plan_id: str):
    """Get a specific development plan by ID."""
    try:
        # Search through all sessions for the plan
        sessions = await planning_engine.list_sessions()
        for session in sessions:
            for plan in session.plans:
                if plan.plan_id == plan_id:
                    return plan
        
        raise HTTPException(status_code=404, detail="Plan not found")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get plan: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get plan: {str(e)}")


@router.put("/plans/{plan_id}", response_model=DevelopmentPlan)
async def update_development_plan(
    plan_id: str,
    updates: Dict[str, Any] = Body(...)
):
    """Update a development plan."""
    try:
        plan = await planning_engine.update_plan(plan_id, updates)
        return plan
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"❌ Failed to update plan: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update plan: {str(e)}")


@router.post("/plans/{plan_id}/validate", response_model=PlanValidation)
async def validate_development_plan(plan_id: str):
    """Re-validate a development plan."""
    try:
        # Find the plan
        sessions = await planning_engine.list_sessions()
        plan = None
        for session in sessions:
            for p in session.plans:
                if p.plan_id == plan_id:
                    plan = p
                    break
            if plan:
                break
        
        if not plan:
            raise HTTPException(status_code=404, detail="Plan not found")
        
        # Validate the plan
        validation = await planning_engine.validation_agent.validate_plan(plan)
        plan.validations.append(validation)
        
        return validation
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Plan validation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Plan validation failed: {str(e)}")


@router.post("/toggle")
async def toggle_ai_planning(enabled: bool = Body(..., embed=True)):
    """
    Toggle AI planning system on/off.
    
    This endpoint allows the frontend to enable/disable the AI planning feature.
    When disabled, all planning requests will be rejected.
    """
    try:
        planning_engine.toggle_planning(enabled)
        status = "enabled" if enabled else "disabled"
        logger.info(f"🎯 AI Planning {status} via API")
        return {
            "message": f"AI Planning {status}",
            "enabled": enabled,
            "timestamp": planning_engine.active_sessions
        }
    except Exception as e:
        logger.error(f"❌ Failed to toggle planning: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to toggle planning: {str(e)}")


@router.get("/status")
async def get_planning_status():
    """
    Get current AI planning system status.
    
    Returns information about:
    - Whether planning is enabled
    - System statistics
    - Active sessions
    - Performance metrics
    """
    try:
        stats = await planning_engine.get_planning_stats()
        return {
            "status": "operational" if planning_engine.is_planning_enabled() else "disabled",
            "enabled": planning_engine.is_planning_enabled(),
            "stats": stats,
            "agents": {
                "primary_planner": planning_engine.primary_planner.agent_name,
                "validation_agent": planning_engine.validation_agent.agent_name
            }
        }
    except Exception as e:
        logger.error(f"❌ Failed to get planning status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get planning status: {str(e)}")


@router.get("/stats")
async def get_planning_statistics():
    """Get detailed planning system statistics."""
    try:
        return await planning_engine.get_planning_stats()
    except Exception as e:
        logger.error(f"❌ Failed to get planning stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get planning stats: {str(e)}")


# Template endpoints for common planning scenarios
@router.post("/templates/feature", response_model=PlanningResponse)
async def create_feature_plan(
    feature_name: str = Body(...),
    description: str = Body(...),
    complexity: str = Body("moderate"),
    timeline_days: Optional[int] = Body(None)
):
    """Create a plan for implementing a new feature."""
    try:
        request = PlanningRequest(
            title=f"Implement {feature_name}",
            description=description,
            max_timeline_days=timeline_days,
            preferred_complexity=complexity,
            required_skills=["programming", "testing", "documentation"]
        )
        
        response = await planning_engine.create_plan(request)
        return response
    except Exception as e:
        logger.error(f"❌ Feature plan creation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Feature plan creation failed: {str(e)}")


@router.post("/templates/bugfix", response_model=PlanningResponse)
async def create_bugfix_plan(
    bug_description: str = Body(...),
    severity: str = Body("medium"),
    affected_components: List[str] = Body([])
):
    """Create a plan for fixing a bug."""
    try:
        request = PlanningRequest(
            title=f"Fix Bug: {bug_description[:50]}...",
            description=f"Bug Description: {bug_description}\nSeverity: {severity}\nAffected Components: {', '.join(affected_components)}",
            max_timeline_days=7,  # Bugs should be fixed quickly
            preferred_complexity="simple",
            required_skills=["debugging", "testing", "analysis"]
        )
        
        response = await planning_engine.create_plan(request)
        return response
    except Exception as e:
        logger.error(f"❌ Bugfix plan creation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Bugfix plan creation failed: {str(e)}")


@router.post("/templates/refactor", response_model=PlanningResponse)
async def create_refactor_plan(
    component_name: str = Body(...),
    refactor_goals: List[str] = Body(...),
    timeline_days: Optional[int] = Body(None)
):
    """Create a plan for refactoring code."""
    try:
        goals_text = "\n".join([f"- {goal}" for goal in refactor_goals])
        
        request = PlanningRequest(
            title=f"Refactor {component_name}",
            description=f"Refactoring Goals:\n{goals_text}",
            max_timeline_days=timeline_days,
            preferred_complexity="moderate",
            required_skills=["refactoring", "architecture", "testing", "code-review"],
            include_risk_assessment=True  # Refactoring can be risky
        )
        
        response = await planning_engine.create_plan(request)
        return response
    except Exception as e:
        logger.error(f"❌ Refactor plan creation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Refactor plan creation failed: {str(e)}")


# Health check endpoint
@router.get("/health")
async def planning_health_check():
    """Health check for the AI planning system."""
    try:
        is_enabled = planning_engine.is_planning_enabled()
        stats = await planning_engine.get_planning_stats()
        
        return {
            "status": "healthy",
            "enabled": is_enabled,
            "agents_available": True,
            "active_sessions": stats["total_sessions"],
            "timestamp": planning_engine.active_sessions
        }
    except Exception as e:
        logger.error(f"❌ Planning health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "enabled": False,
            "agents_available": False
        }
