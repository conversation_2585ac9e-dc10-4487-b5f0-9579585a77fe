# Environment variables
.env

# Backend data & workspace
/backend/data/
/backend/workspace/
/qdrant_storage/
/ollama_data/

# Node modules & build outputs
/frontend/node_modules/
/frontend/dist/
/frontend/build/

# Python caches
/backend/__pycache__/
/backend/*.pyc
/__pycache__/
**/__pycache__/
**/*.pyc

# Logs
/backend/data/agent.log
*.log

# IDE & OS files
.vscode/
/.idea/
.DS_Store
Thumbs.db

# Docker volumes
docker-volumes/

# Temporary files
*.tmp
*.temp

# Test coverage
coverage/
.coverage
.pytest_cache/

# Virtual environments
venv/
env/
.venv/

# SQLite databases
*.db
*.sqlite
*.sqlite3
