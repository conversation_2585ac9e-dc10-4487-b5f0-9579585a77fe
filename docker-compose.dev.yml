services:
  frontend:
    container_name: ai_coder_frontend_dev
    build: ./frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend
    environment:
      - VITE_BACKEND_URL=http://localhost:8000
    networks:
      - ai_coder_network
    volumes:
      - ./frontend:/app  # Mount frontend for hot reload
      - /app/node_modules  # Exclude node_modules

  backend:
    container_name: ai_coder_backend_dev
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - OLLAMA_HOST=${OLLAMA_HOST:-http://ollama:11434}
      - QDRANT_URL=${QDRANT_URL:-http://qdrant:6333}
      - BACKEND_WORKERS=${BACKEND_WORKERS:-1}  # Single worker for development
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}  # Debug logging
      - PYTHONPATH=/app
    volumes:
      - ./backend:/app  # Mount entire backend directory
      - /app/__pycache__  # Exclude Python cache
    depends_on:
      - qdrant
      - ollama
    networks:
      - ai_coder_network
    # Development command with auto-reload and debug
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload --log-level debug
    # Keep container running even if it crashes
    restart: unless-stopped

  # Ollama service for local embeddings
  ollama:
    container_name: ai_coder_ollama_dev
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ./ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    healthcheck:
      test: ["CMD", "ollama", "list"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - ai_coder_network

  # Self-hosted Qdrant vector database
  qdrant:
    container_name: ai_coder_qdrant_dev
    image: qdrant/qdrant:v1.8.3
    ports:
      - "6333:6333"
    environment:
      - QDRANT__SERVICE__GRPC_PORT=6334
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__LOG_LEVEL=debug  # Debug logging for development
    volumes:
      - ./qdrant_storage:/qdrant/storage
    healthcheck:
      test: ["CMD", "/qdrant/qdrant", "--version"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - ai_coder_network

networks:
  ai_coder_network:
    driver: bridge

volumes:
  backend_data:
  backend_workspace:
  ollama_data:
  qdrant_storage:
