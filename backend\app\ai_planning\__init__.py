"""
AI-Powered Planning System - Phase 8D

Multi-AI plan creation and validation system with:
- Primary planning agent for plan generation
- Secondary validation agent for plan improvement
- Context-aware planning using Code Intelligence Hub
- Risk assessment and mitigation strategies
- Resource estimation and dependency management
- Frontend integration for visual plan management
"""

from .core import PlanningEngine, PlanningSession
from .models import (
    DevelopmentPlan,
    PlanTask,
    PlanValidation,
    PlanRisk,
    PlanResource,
    PlanningRequest,
    PlanningResponse
)
from .primary_planner import PrimaryPlanningAgent
from .validation_agent import PlanValidationAgent
# Risk analyzer and resource estimator are integrated into the planning agents
# from .risk_analyzer import RiskAnalyzer
# from .resource_estimator import ResourceEstimator

__all__ = [
    'PlanningEngine',
    'PlanningSession',
    'DevelopmentPlan',
    'PlanTask',
    'PlanValidation',
    'PlanRisk',
    'PlanResource',
    'PlanningRequest',
    'PlanningResponse',
    'PrimaryPlanningAgent',
    'PlanValidationAgent',
    # 'RiskAnalyzer',
    # 'ResourceEstimator'
]
