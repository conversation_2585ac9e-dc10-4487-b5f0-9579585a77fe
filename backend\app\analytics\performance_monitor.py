"""
⚡ Performance Monitor - Real-Time System Performance Tracking

This module provides comprehensive performance monitoring with:
- Real-time system metrics collection
- Performance alerting and notifications
- Resource utilization tracking
- Bottleneck identification and analysis
- Performance optimization recommendations
- Historical performance trending
"""

import asyncio
import time
from collections import deque
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)


class AlertSeverity(str, Enum):
    """Performance alert severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class MetricType(str, Enum):
    """Types of performance metrics."""
    CPU_USAGE = "cpu_usage"
    MEMORY_USAGE = "memory_usage"
    RESPONSE_TIME = "response_time"
    THROUGHPUT = "throughput"
    ERROR_RATE = "error_rate"
    DISK_IO = "disk_io"
    NETWORK_IO = "network_io"


class PerformanceMetrics(BaseModel):
    """Performance metrics snapshot."""
    
    timestamp: datetime = Field(default_factory=datetime.now, description="Metrics timestamp")
    
    # System metrics
    cpu_usage_percent: float = Field(..., description="CPU usage percentage")
    memory_usage_percent: float = Field(..., description="Memory usage percentage")
    memory_usage_mb: float = Field(..., description="Memory usage in MB")
    
    # Application metrics
    response_time_ms: float = Field(..., description="Average response time in milliseconds")
    requests_per_second: float = Field(..., description="Requests per second")
    error_rate_percent: float = Field(..., description="Error rate percentage")
    
    # Context management metrics
    context_optimizations_per_hour: float = Field(default=0.0, description="Context optimizations per hour")
    auto_compactions_per_hour: float = Field(default=0.0, description="Auto-compactions per hour")
    average_token_reduction: float = Field(default=0.0, description="Average token reduction percentage")
    
    # Resource metrics
    disk_read_mb_per_sec: float = Field(default=0.0, description="Disk read MB/s")
    disk_write_mb_per_sec: float = Field(default=0.0, description="Disk write MB/s")
    network_in_mb_per_sec: float = Field(default=0.0, description="Network in MB/s")
    network_out_mb_per_sec: float = Field(default=0.0, description="Network out MB/s")
    
    # Quality metrics
    ai_success_rate: float = Field(default=100.0, description="AI request success rate")
    user_satisfaction_score: float = Field(default=5.0, description="User satisfaction score (1-5)")


class PerformanceAlert(BaseModel):
    """Performance alert notification."""
    
    alert_id: str = Field(..., description="Unique alert identifier")
    severity: AlertSeverity = Field(..., description="Alert severity")
    metric_type: MetricType = Field(..., description="Metric that triggered alert")
    
    title: str = Field(..., description="Alert title")
    description: str = Field(..., description="Alert description")
    
    # Alert data
    current_value: float = Field(..., description="Current metric value")
    threshold_value: float = Field(..., description="Threshold that was exceeded")
    duration_seconds: float = Field(..., description="How long the condition has persisted")
    
    # Recommendations
    immediate_actions: List[str] = Field(default_factory=list, description="Immediate actions to take")
    root_cause_analysis: List[str] = Field(default_factory=list, description="Potential root causes")
    
    # Metadata
    timestamp: datetime = Field(default_factory=datetime.now, description="Alert timestamp")
    acknowledged: bool = Field(default=False, description="Whether alert has been acknowledged")
    resolved: bool = Field(default=False, description="Whether alert has been resolved")


class PerformanceMonitor:
    """
    ⚡ Revolutionary Performance Monitor
    
    Provides comprehensive real-time performance monitoring with intelligent
    alerting, bottleneck detection, and optimization recommendations.
    """
    
    def __init__(self, collection_interval: int = 30, retention_hours: int = 24):
        self.collection_interval = collection_interval  # seconds
        self.retention_hours = retention_hours
        
        # Metrics storage
        self.metrics_history: deque = deque(maxlen=int(retention_hours * 3600 / collection_interval))
        self.current_metrics: Optional[PerformanceMetrics] = None
        
        # Alert configuration
        self.alert_thresholds = {
            MetricType.CPU_USAGE: {"high": 80.0, "critical": 90.0},
            MetricType.MEMORY_USAGE: {"high": 85.0, "critical": 95.0},
            MetricType.RESPONSE_TIME: {"high": 3000.0, "critical": 5000.0},  # milliseconds
            MetricType.ERROR_RATE: {"high": 5.0, "critical": 10.0},  # percentage
            MetricType.THROUGHPUT: {"low": 10.0, "critical_low": 5.0},  # requests/sec
        }
        
        # Active alerts
        self.active_alerts: Dict[str, PerformanceAlert] = {}
        self.alert_history: deque = deque(maxlen=1000)
        
        # Performance baselines
        self.baselines: Dict[MetricType, float] = {}
        self.baseline_calculation_window = 100  # number of samples
        
        # Monitoring state
        self.monitoring_active = False
        self.monitoring_task: Optional[asyncio.Task] = None
        
        # Performance statistics
        self.performance_stats = {
            "total_alerts": 0,
            "critical_alerts": 0,
            "average_response_time": 0.0,
            "peak_cpu_usage": 0.0,
            "peak_memory_usage": 0.0,
            "uptime_percentage": 100.0
        }
    
    async def start_monitoring(self) -> None:
        """Start performance monitoring."""
        if self.monitoring_active:
            logger.warning("Performance monitoring is already active")
            return
        
        self.monitoring_active = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Performance monitoring started")
    
    async def stop_monitoring(self) -> None:
        """Stop performance monitoring."""
        if not self.monitoring_active:
            return
        
        self.monitoring_active = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Performance monitoring stopped")
    
    async def collect_metrics(self) -> PerformanceMetrics:
        """Collect current performance metrics."""
        try:
            # Collect system metrics
            cpu_usage = await self._get_cpu_usage()
            memory_usage_percent, memory_usage_mb = await self._get_memory_usage()
            
            # Collect application metrics
            response_time = await self._get_average_response_time()
            requests_per_second = await self._get_requests_per_second()
            error_rate = await self._get_error_rate()
            
            # Collect context management metrics
            context_metrics = await self._get_context_metrics()
            
            # Collect resource metrics
            disk_metrics = await self._get_disk_metrics()
            network_metrics = await self._get_network_metrics()
            
            # Collect quality metrics
            ai_success_rate = await self._get_ai_success_rate()
            
            metrics = PerformanceMetrics(
                cpu_usage_percent=cpu_usage,
                memory_usage_percent=memory_usage_percent,
                memory_usage_mb=memory_usage_mb,
                response_time_ms=response_time,
                requests_per_second=requests_per_second,
                error_rate_percent=error_rate,
                context_optimizations_per_hour=context_metrics.get("optimizations_per_hour", 0.0),
                auto_compactions_per_hour=context_metrics.get("compactions_per_hour", 0.0),
                average_token_reduction=context_metrics.get("token_reduction", 0.0),
                disk_read_mb_per_sec=disk_metrics.get("read_mb_per_sec", 0.0),
                disk_write_mb_per_sec=disk_metrics.get("write_mb_per_sec", 0.0),
                network_in_mb_per_sec=network_metrics.get("in_mb_per_sec", 0.0),
                network_out_mb_per_sec=network_metrics.get("out_mb_per_sec", 0.0),
                ai_success_rate=ai_success_rate
            )
            
            # Store metrics
            self.current_metrics = metrics
            self.metrics_history.append(metrics)
            
            # Update baselines
            await self._update_baselines(metrics)
            
            # Check for alerts
            await self._check_alerts(metrics)
            
            # Update statistics
            await self._update_statistics(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to collect performance metrics: {e}")
            # Return default metrics on error
            return PerformanceMetrics(
                cpu_usage_percent=0.0,
                memory_usage_percent=0.0,
                memory_usage_mb=0.0,
                response_time_ms=0.0,
                requests_per_second=0.0,
                error_rate_percent=0.0
            )
    
    async def get_performance_summary(
        self,
        hours: int = 1
    ) -> Dict[str, Any]:
        """Get performance summary for specified period."""
        cutoff = datetime.now() - timedelta(hours=hours)
        recent_metrics = [m for m in self.metrics_history if m.timestamp > cutoff]
        
        if not recent_metrics:
            return {"message": "No performance data available for the specified period"}
        
        # Calculate summary statistics
        cpu_values = [m.cpu_usage_percent for m in recent_metrics]
        memory_values = [m.memory_usage_percent for m in recent_metrics]
        response_times = [m.response_time_ms for m in recent_metrics]
        error_rates = [m.error_rate_percent for m in recent_metrics]
        
        summary = {
            "period_hours": hours,
            "data_points": len(recent_metrics),
            "cpu_usage": {
                "average": sum(cpu_values) / len(cpu_values),
                "peak": max(cpu_values),
                "minimum": min(cpu_values)
            },
            "memory_usage": {
                "average": sum(memory_values) / len(memory_values),
                "peak": max(memory_values),
                "minimum": min(memory_values)
            },
            "response_time": {
                "average": sum(response_times) / len(response_times),
                "peak": max(response_times),
                "minimum": min(response_times)
            },
            "error_rate": {
                "average": sum(error_rates) / len(error_rates),
                "peak": max(error_rates),
                "minimum": min(error_rates)
            },
            "performance_score": await self._calculate_performance_score(recent_metrics),
            "recommendations": await self._generate_performance_recommendations(recent_metrics)
        }
        
        return summary
    
    async def get_active_alerts(self) -> List[PerformanceAlert]:
        """Get all active performance alerts."""
        return list(self.active_alerts.values())
    
    async def acknowledge_alert(self, alert_id: str) -> bool:
        """Acknowledge a performance alert."""
        if alert_id in self.active_alerts:
            self.active_alerts[alert_id].acknowledged = True
            logger.info(f"Alert {alert_id} acknowledged")
            return True
        return False
    
    async def resolve_alert(self, alert_id: str) -> bool:
        """Resolve a performance alert."""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.resolved = True
            alert.acknowledged = True
            
            # Move to history
            self.alert_history.append(alert)
            del self.active_alerts[alert_id]
            
            logger.info(f"Alert {alert_id} resolved")
            return True
        return False
    
    async def get_performance_trends(
        self,
        metric_type: MetricType,
        hours: int = 24
    ) -> Dict[str, Any]:
        """Get performance trends for specific metric."""
        cutoff = datetime.now() - timedelta(hours=hours)
        recent_metrics = [m for m in self.metrics_history if m.timestamp > cutoff]
        
        if not recent_metrics:
            return {"message": "No data available for trend analysis"}
        
        # Extract values based on metric type
        values = []
        timestamps = []
        
        for metrics in recent_metrics:
            timestamps.append(metrics.timestamp)
            
            if metric_type == MetricType.CPU_USAGE:
                values.append(metrics.cpu_usage_percent)
            elif metric_type == MetricType.MEMORY_USAGE:
                values.append(metrics.memory_usage_percent)
            elif metric_type == MetricType.RESPONSE_TIME:
                values.append(metrics.response_time_ms)
            elif metric_type == MetricType.ERROR_RATE:
                values.append(metrics.error_rate_percent)
            elif metric_type == MetricType.THROUGHPUT:
                values.append(metrics.requests_per_second)
        
        # Calculate trend
        trend_direction = "stable"
        if len(values) > 1:
            if values[-1] > values[0] * 1.1:
                trend_direction = "increasing"
            elif values[-1] < values[0] * 0.9:
                trend_direction = "decreasing"
        
        return {
            "metric_type": metric_type.value,
            "period_hours": hours,
            "data_points": len(values),
            "current_value": values[-1] if values else 0,
            "average_value": sum(values) / len(values) if values else 0,
            "trend_direction": trend_direction,
            "values": values,
            "timestamps": [ts.isoformat() for ts in timestamps]
        }
    
    async def _monitoring_loop(self) -> None:
        """Main monitoring loop."""
        while self.monitoring_active:
            try:
                await self.collect_metrics()
                await asyncio.sleep(self.collection_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(self.collection_interval)
    
    async def _get_cpu_usage(self) -> float:
        """Get current CPU usage percentage."""
        try:
            import psutil
            return psutil.cpu_percent(interval=1)
        except ImportError:
            # Fallback if psutil not available
            return 0.0
        except Exception:
            return 0.0
    
    async def _get_memory_usage(self) -> Tuple[float, float]:
        """Get current memory usage (percentage, MB)."""
        try:
            import psutil
            memory = psutil.virtual_memory()
            return memory.percent, memory.used / (1024 * 1024)
        except ImportError:
            return 0.0, 0.0
        except Exception:
            return 0.0, 0.0
    
    async def _get_average_response_time(self) -> float:
        """Get average response time from recent requests."""
        # This would integrate with your usage tracker
        # For now, return a simulated value
        return 1500.0  # milliseconds
    
    async def _get_requests_per_second(self) -> float:
        """Get current requests per second."""
        # This would integrate with your usage tracker
        return 25.0
    
    async def _get_error_rate(self) -> float:
        """Get current error rate percentage."""
        # This would integrate with your usage tracker
        return 2.5
    
    async def _get_context_metrics(self) -> Dict[str, float]:
        """Get context management metrics."""
        # This would integrate with your context management system
        return {
            "optimizations_per_hour": 15.0,
            "compactions_per_hour": 8.0,
            "token_reduction": 45.0
        }
    
    async def _get_disk_metrics(self) -> Dict[str, float]:
        """Get disk I/O metrics."""
        try:
            import psutil
            disk_io = psutil.disk_io_counters()
            if disk_io:
                return {
                    "read_mb_per_sec": disk_io.read_bytes / (1024 * 1024) / self.collection_interval,
                    "write_mb_per_sec": disk_io.write_bytes / (1024 * 1024) / self.collection_interval
                }
        except ImportError:
            pass
        except Exception:
            pass
        
        return {"read_mb_per_sec": 0.0, "write_mb_per_sec": 0.0}
    
    async def _get_network_metrics(self) -> Dict[str, float]:
        """Get network I/O metrics."""
        try:
            import psutil
            net_io = psutil.net_io_counters()
            if net_io:
                return {
                    "in_mb_per_sec": net_io.bytes_recv / (1024 * 1024) / self.collection_interval,
                    "out_mb_per_sec": net_io.bytes_sent / (1024 * 1024) / self.collection_interval
                }
        except ImportError:
            pass
        except Exception:
            pass
        
        return {"in_mb_per_sec": 0.0, "out_mb_per_sec": 0.0}

    async def _get_ai_success_rate(self) -> float:
        """Get AI request success rate."""
        # This would integrate with your usage tracker
        return 94.5

    async def _update_baselines(self, metrics: PerformanceMetrics) -> None:
        """Update performance baselines."""
        if len(self.metrics_history) >= self.baseline_calculation_window:
            recent_metrics = list(self.metrics_history)[-self.baseline_calculation_window:]

            # Calculate baselines
            self.baselines[MetricType.CPU_USAGE] = sum(m.cpu_usage_percent for m in recent_metrics) / len(recent_metrics)
            self.baselines[MetricType.MEMORY_USAGE] = sum(m.memory_usage_percent for m in recent_metrics) / len(recent_metrics)
            self.baselines[MetricType.RESPONSE_TIME] = sum(m.response_time_ms for m in recent_metrics) / len(recent_metrics)
            self.baselines[MetricType.ERROR_RATE] = sum(m.error_rate_percent for m in recent_metrics) / len(recent_metrics)
            self.baselines[MetricType.THROUGHPUT] = sum(m.requests_per_second for m in recent_metrics) / len(recent_metrics)

    async def _check_alerts(self, metrics: PerformanceMetrics) -> None:
        """Check for performance alerts."""
        # CPU usage alerts
        await self._check_metric_alert(
            MetricType.CPU_USAGE,
            metrics.cpu_usage_percent,
            "CPU Usage",
            f"CPU usage is at {metrics.cpu_usage_percent:.1f}%"
        )

        # Memory usage alerts
        await self._check_metric_alert(
            MetricType.MEMORY_USAGE,
            metrics.memory_usage_percent,
            "Memory Usage",
            f"Memory usage is at {metrics.memory_usage_percent:.1f}%"
        )

        # Response time alerts
        await self._check_metric_alert(
            MetricType.RESPONSE_TIME,
            metrics.response_time_ms,
            "Response Time",
            f"Average response time is {metrics.response_time_ms:.0f}ms"
        )

        # Error rate alerts
        await self._check_metric_alert(
            MetricType.ERROR_RATE,
            metrics.error_rate_percent,
            "Error Rate",
            f"Error rate is at {metrics.error_rate_percent:.1f}%"
        )

        # Throughput alerts (low throughput)
        throughput_thresholds = self.alert_thresholds[MetricType.THROUGHPUT]
        if metrics.requests_per_second < throughput_thresholds["critical_low"]:
            await self._create_alert(
                MetricType.THROUGHPUT,
                AlertSeverity.CRITICAL,
                "Critical Low Throughput",
                f"Throughput is critically low at {metrics.requests_per_second:.1f} requests/sec",
                metrics.requests_per_second,
                throughput_thresholds["critical_low"]
            )
        elif metrics.requests_per_second < throughput_thresholds["low"]:
            await self._create_alert(
                MetricType.THROUGHPUT,
                AlertSeverity.HIGH,
                "Low Throughput",
                f"Throughput is low at {metrics.requests_per_second:.1f} requests/sec",
                metrics.requests_per_second,
                throughput_thresholds["low"]
            )

    async def _check_metric_alert(
        self,
        metric_type: MetricType,
        current_value: float,
        title_prefix: str,
        description: str
    ) -> None:
        """Check if a metric exceeds alert thresholds."""
        thresholds = self.alert_thresholds.get(metric_type, {})

        if current_value >= thresholds.get("critical", float('inf')):
            await self._create_alert(
                metric_type,
                AlertSeverity.CRITICAL,
                f"Critical {title_prefix}",
                description,
                current_value,
                thresholds["critical"]
            )
        elif current_value >= thresholds.get("high", float('inf')):
            await self._create_alert(
                metric_type,
                AlertSeverity.HIGH,
                f"High {title_prefix}",
                description,
                current_value,
                thresholds["high"]
            )

    async def _create_alert(
        self,
        metric_type: MetricType,
        severity: AlertSeverity,
        title: str,
        description: str,
        current_value: float,
        threshold_value: float
    ) -> None:
        """Create a new performance alert."""
        alert_key = f"{metric_type.value}_{severity.value}"

        # Check if alert already exists
        if alert_key in self.active_alerts:
            # Update existing alert
            existing_alert = self.active_alerts[alert_key]
            existing_alert.current_value = current_value
            existing_alert.duration_seconds += self.collection_interval
            return

        # Create new alert
        alert_id = f"alert_{int(time.time())}_{metric_type.value}"

        # Generate recommendations based on metric type
        immediate_actions = await self._get_alert_recommendations(metric_type, severity)
        root_causes = await self._get_root_cause_analysis(metric_type)

        alert = PerformanceAlert(
            alert_id=alert_id,
            severity=severity,
            metric_type=metric_type,
            title=title,
            description=description,
            current_value=current_value,
            threshold_value=threshold_value,
            duration_seconds=self.collection_interval,
            immediate_actions=immediate_actions,
            root_cause_analysis=root_causes
        )

        self.active_alerts[alert_key] = alert
        self.performance_stats["total_alerts"] += 1

        if severity == AlertSeverity.CRITICAL:
            self.performance_stats["critical_alerts"] += 1

        logger.warning(f"Performance alert created: {title} - {description}")

    async def _get_alert_recommendations(
        self,
        metric_type: MetricType,
        severity: AlertSeverity
    ) -> List[str]:
        """Get recommendations for performance alerts."""
        recommendations = []

        if metric_type == MetricType.CPU_USAGE:
            recommendations.extend([
                "Enable context auto-compaction to reduce CPU load",
                "Optimize AI request processing",
                "Consider scaling resources horizontally"
            ])
        elif metric_type == MetricType.MEMORY_USAGE:
            recommendations.extend([
                "Enable aggressive context compaction",
                "Clear unused caches and temporary data",
                "Increase memory allocation or optimize usage"
            ])
        elif metric_type == MetricType.RESPONSE_TIME:
            recommendations.extend([
                "Enable context caching for faster responses",
                "Optimize database queries and operations",
                "Implement request prioritization"
            ])
        elif metric_type == MetricType.ERROR_RATE:
            recommendations.extend([
                "Investigate recent error patterns",
                "Implement circuit breakers and retry logic",
                "Review recent code deployments"
            ])
        elif metric_type == MetricType.THROUGHPUT:
            recommendations.extend([
                "Check for system bottlenecks",
                "Optimize request processing pipeline",
                "Scale resources to handle increased load"
            ])

        return recommendations

    async def _get_root_cause_analysis(self, metric_type: MetricType) -> List[str]:
        """Get potential root causes for performance issues."""
        root_causes = []

        if metric_type == MetricType.CPU_USAGE:
            root_causes.extend([
                "High AI request volume",
                "Inefficient context processing",
                "Background task overload",
                "Resource contention"
            ])
        elif metric_type == MetricType.MEMORY_USAGE:
            root_causes.extend([
                "Large context accumulation",
                "Memory leaks in application",
                "Inefficient data structures",
                "Insufficient garbage collection"
            ])
        elif metric_type == MetricType.RESPONSE_TIME:
            root_causes.extend([
                "Database performance issues",
                "Network latency",
                "Inefficient algorithms",
                "Resource starvation"
            ])
        elif metric_type == MetricType.ERROR_RATE:
            root_causes.extend([
                "Recent code changes",
                "External service failures",
                "Configuration issues",
                "Resource exhaustion"
            ])

        return root_causes

    async def _update_statistics(self, metrics: PerformanceMetrics) -> None:
        """Update performance statistics."""
        # Update averages
        if self.metrics_history:
            response_times = [m.response_time_ms for m in self.metrics_history]
            self.performance_stats["average_response_time"] = sum(response_times) / len(response_times)

        # Update peaks
        self.performance_stats["peak_cpu_usage"] = max(
            self.performance_stats["peak_cpu_usage"],
            metrics.cpu_usage_percent
        )

        self.performance_stats["peak_memory_usage"] = max(
            self.performance_stats["peak_memory_usage"],
            metrics.memory_usage_percent
        )

        # Calculate uptime percentage (simplified)
        if self.metrics_history:
            healthy_samples = len([m for m in self.metrics_history if m.error_rate_percent < 5.0])
            self.performance_stats["uptime_percentage"] = (healthy_samples / len(self.metrics_history)) * 100

    async def _calculate_performance_score(self, metrics_list: List[PerformanceMetrics]) -> float:
        """Calculate overall performance score (0-100)."""
        if not metrics_list:
            return 0.0

        # Weight different metrics
        weights = {
            "cpu": 0.25,
            "memory": 0.25,
            "response_time": 0.30,
            "error_rate": 0.20
        }

        # Calculate individual scores
        cpu_scores = [max(0, 100 - m.cpu_usage_percent) for m in metrics_list]
        memory_scores = [max(0, 100 - m.memory_usage_percent) for m in metrics_list]
        response_scores = [max(0, 100 - (m.response_time_ms / 50)) for m in metrics_list]  # 5000ms = 0 score
        error_scores = [max(0, 100 - (m.error_rate_percent * 10)) for m in metrics_list]  # 10% error = 0 score

        # Calculate weighted average
        avg_cpu = sum(cpu_scores) / len(cpu_scores)
        avg_memory = sum(memory_scores) / len(memory_scores)
        avg_response = sum(response_scores) / len(response_scores)
        avg_error = sum(error_scores) / len(error_scores)

        performance_score = (
            avg_cpu * weights["cpu"] +
            avg_memory * weights["memory"] +
            avg_response * weights["response_time"] +
            avg_error * weights["error_rate"]
        )

        return min(100.0, max(0.0, performance_score))

    async def _generate_performance_recommendations(
        self,
        metrics_list: List[PerformanceMetrics]
    ) -> List[str]:
        """Generate performance optimization recommendations."""
        if not metrics_list:
            return []

        recommendations = []

        # Analyze trends
        avg_cpu = sum(m.cpu_usage_percent for m in metrics_list) / len(metrics_list)
        avg_memory = sum(m.memory_usage_percent for m in metrics_list) / len(metrics_list)
        avg_response = sum(m.response_time_ms for m in metrics_list) / len(metrics_list)
        avg_error = sum(m.error_rate_percent for m in metrics_list) / len(metrics_list)

        if avg_cpu > 70:
            recommendations.append("🔥 Enable context auto-compaction to reduce CPU usage")

        if avg_memory > 80:
            recommendations.append("💾 Implement aggressive memory management and cleanup")

        if avg_response > 2000:
            recommendations.append("⚡ Enable context caching for faster response times")

        if avg_error > 3:
            recommendations.append("🛡️ Implement enhanced error handling and retry mechanisms")

        # Context-specific recommendations
        context_optimizations = sum(m.context_optimizations_per_hour for m in metrics_list) / len(metrics_list)
        if context_optimizations < 10:
            recommendations.append("🎯 Increase context optimization frequency for better performance")

        return recommendations[:5]  # Limit to top 5 recommendations

    def get_performance_statistics(self) -> Dict[str, Any]:
        """Get performance monitoring statistics."""
        return {
            **self.performance_stats,
            "monitoring_active": self.monitoring_active,
            "metrics_collected": len(self.metrics_history),
            "active_alerts": len(self.active_alerts),
            "alert_history_count": len(self.alert_history),
            "baselines": self.baselines,
            "collection_interval": self.collection_interval,
            "retention_hours": self.retention_hours
        }


# Global performance monitor instance
_performance_monitor: Optional[PerformanceMonitor] = None


def get_performance_monitor() -> PerformanceMonitor:
    """Get the global performance monitor instance."""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor
