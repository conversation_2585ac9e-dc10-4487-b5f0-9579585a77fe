"""
Semantic Search Engine for Code Intelligence Hub - Phase 8B

Natural language search across indexed codebase using BGE-M3 embeddings.
"""

import logging
from typing import List, Dict, Optional, Any, Union
import re

from .models import SearchQuery, SearchResult, CodeElementType, CodeLanguage

logger = logging.getLogger(__name__)


class SemanticSearch:
    """
    Semantic search engine for code intelligence.
    
    Provides natural language search capabilities across the indexed codebase
    using BGE-M3 embeddings and Qdrant vector database.
    """
    
    def __init__(self, vector_client, embedding_client):
        """Initialize the semantic search engine."""
        self.vector_client = vector_client
        self.embedding_client = embedding_client
        self.collection_name = "code_embeddings_bge_m3_code_intelligence"
        
        # Search enhancement patterns
        self.query_expansions = {
            'auth': ['authentication', 'login', 'password', 'token', 'session', 'user', 'credential'],
            'database': ['db', 'sql', 'query', 'connection', 'model', 'table', 'schema'],
            'api': ['endpoint', 'route', 'handler', 'controller', 'service', 'request', 'response'],
            'test': ['testing', 'unittest', 'spec', 'mock', 'assert', 'verify'],
            'config': ['configuration', 'settings', 'environment', 'env', 'parameter'],
            'error': ['exception', 'error handling', 'try catch', 'failure', 'bug'],
            'security': ['secure', 'encrypt', 'decrypt', 'hash', 'validate', 'sanitize'],
            'performance': ['optimize', 'cache', 'speed', 'efficient', 'fast', 'memory'],
            'ui': ['interface', 'component', 'view', 'template', 'frontend', 'display'],
            'data': ['process', 'transform', 'parse', 'serialize', 'format', 'convert']
        }
        
        logger.info("🔍 Semantic search engine initialized")
    
    async def initialize(self):
        """Initialize the search engine."""
        try:
            # Ensure collection exists
            await self.vector_client.ensure_collection_exists()
            logger.info("✅ Semantic search engine ready")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize semantic search: {e}")
            raise
    
    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """
        Perform semantic search across the codebase.
        
        Args:
            query: SearchQuery object with search parameters
            
        Returns:
            List of SearchResult objects
        """
        try:
            logger.debug(f"🔍 Searching for: {query.query}")
            
            # Expand and enhance query
            enhanced_query = await self._enhance_query(query.query)
            
            # Generate query embedding
            query_embedding = await self.embedding_client.generate_embedding(enhanced_query)
            
            # Search vector database
            search_results = await self.vector_client.search_similar(
                query_embedding=query_embedding,
                top_k=query.limit,
                score_threshold=query.threshold,
                collection_name=self.collection_name
            )
            
            # Convert to SearchResult objects
            results = []
            for result in search_results:
                search_result = await self._create_search_result(result, query)
                if search_result:
                    results.append(search_result)
            
            # Apply filters if specified
            if query.filters:
                results = await self._apply_filters(results, query.filters)
            
            # Sort by relevance and context
            results = await self._rank_results(results, query)
            
            logger.debug(f"📊 Returning {len(results)} search results")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Search failed: {e}")
            return []
    
    async def search_similar_code(self, code_snippet: str, language: Optional[CodeLanguage] = None, limit: int = 10) -> List[SearchResult]:
        """
        Find code similar to the provided snippet.
        
        Args:
            code_snippet: Code snippet to find similar code for
            language: Optional language filter
            limit: Maximum number of results
            
        Returns:
            List of similar code results
        """
        try:
            logger.debug(f"🔍 Searching for similar code (language: {language})")
            
            # Create search query
            query = SearchQuery(
                query=code_snippet,
                query_type="similarity",
                limit=limit,
                threshold=0.6
            )
            
            if language:
                query.filters = {"language": language.value}
            
            return await self.search(query)
            
        except Exception as e:
            logger.error(f"❌ Similar code search failed: {e}")
            return []
    
    async def search_by_function_name(self, function_name: str, limit: int = 10) -> List[SearchResult]:
        """
        Search for functions by name.
        
        Args:
            function_name: Function name to search for
            limit: Maximum number of results
            
        Returns:
            List of function search results
        """
        try:
            query = SearchQuery(
                query=f"function {function_name}",
                query_type="function",
                limit=limit,
                filters={"element_type": CodeElementType.FUNCTION.value}
            )
            
            return await self.search(query)
            
        except Exception as e:
            logger.error(f"❌ Function search failed: {e}")
            return []
    
    async def search_by_class_name(self, class_name: str, limit: int = 10) -> List[SearchResult]:
        """
        Search for classes by name.
        
        Args:
            class_name: Class name to search for
            limit: Maximum number of results
            
        Returns:
            List of class search results
        """
        try:
            query = SearchQuery(
                query=f"class {class_name}",
                query_type="class",
                limit=limit,
                filters={"element_type": CodeElementType.CLASS.value}
            )
            
            return await self.search(query)
            
        except Exception as e:
            logger.error(f"❌ Class search failed: {e}")
            return []
    
    async def search_in_file(self, file_path: str, query: str, limit: int = 10) -> List[SearchResult]:
        """
        Search within a specific file.
        
        Args:
            file_path: Path to the file to search in
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of search results from the file
        """
        try:
            search_query = SearchQuery(
                query=query,
                query_type="file_specific",
                limit=limit,
                filters={"file_path": file_path}
            )
            
            return await self.search(search_query)
            
        except Exception as e:
            logger.error(f"❌ File-specific search failed: {e}")
            return []
    
    async def _enhance_query(self, query: str) -> str:
        """
        Enhance search query with synonyms and related terms.
        
        Args:
            query: Original search query
            
        Returns:
            Enhanced query string
        """
        try:
            enhanced_terms = [query]
            query_lower = query.lower()
            
            # Add expansions for known terms
            for key, expansions in self.query_expansions.items():
                if key in query_lower:
                    enhanced_terms.extend(expansions)
            
            # Add programming-specific enhancements
            if 'function' in query_lower or 'method' in query_lower:
                enhanced_terms.extend(['def', 'function', 'method', 'procedure'])
            
            if 'class' in query_lower:
                enhanced_terms.extend(['class', 'object', 'type', 'struct'])
            
            if 'import' in query_lower:
                enhanced_terms.extend(['import', 'require', 'include', 'use'])
            
            # Remove duplicates and join
            unique_terms = list(set(enhanced_terms))
            enhanced_query = ' '.join(unique_terms)
            
            logger.debug(f"Enhanced query: '{query}' -> '{enhanced_query}'")
            
            return enhanced_query
            
        except Exception as e:
            logger.debug(f"Query enhancement failed: {e}")
            return query
    
    async def _create_search_result(self, vector_result: Dict[str, Any], query: SearchQuery) -> Optional[SearchResult]:
        """
        Create SearchResult object from vector database result.
        
        Args:
            vector_result: Result from vector database
            query: Original search query
            
        Returns:
            SearchResult object or None if creation failed
        """
        try:
            payload = vector_result.get('payload', {})
            score = vector_result.get('score', 0.0)
            
            # Extract metadata
            chunk_id = payload.get('chunk_id', '')
            file_id = payload.get('file_id', '')
            element_type = CodeElementType(payload.get('element_type', 'chunk'))
            start_line = payload.get('start_line', 0)
            end_line = payload.get('end_line', 0)
            function_name = payload.get('function_name')
            class_name = payload.get('class_name')
            
            # Get file path from file_id (this would need to be implemented)
            file_path = await self._get_file_path_from_id(file_id)
            
            # Get content from chunk (this would need to be implemented)
            content = await self._get_chunk_content(chunk_id)
            
            # Detect language from file path
            language = self._detect_language_from_path(file_path)
            
            # Generate context
            context_before, context_after = await self._get_context(file_path, start_line, end_line)
            
            # Create search result
            search_result = SearchResult(
                result_id=chunk_id,
                file_path=file_path,
                content=content,
                score=score,
                element_type=element_type,
                start_line=start_line,
                end_line=end_line,
                context_before=context_before,
                context_after=context_after,
                function_name=function_name,
                class_name=class_name,
                language=language,
                tags=await self._generate_tags(content, element_type)
            )
            
            return search_result
            
        except Exception as e:
            logger.debug(f"Failed to create search result: {e}")
            return None
    
    async def _apply_filters(self, results: List[SearchResult], filters: Dict[str, Any]) -> List[SearchResult]:
        """Apply filters to search results."""
        try:
            filtered_results = []
            
            for result in results:
                include_result = True
                
                # Apply language filter
                if 'language' in filters:
                    if result.language.value != filters['language']:
                        include_result = False
                
                # Apply element type filter
                if 'element_type' in filters:
                    if result.element_type.value != filters['element_type']:
                        include_result = False
                
                # Apply file path filter
                if 'file_path' in filters:
                    if filters['file_path'] not in result.file_path:
                        include_result = False
                
                # Apply function name filter
                if 'function_name' in filters:
                    if result.function_name != filters['function_name']:
                        include_result = False
                
                # Apply class name filter
                if 'class_name' in filters:
                    if result.class_name != filters['class_name']:
                        include_result = False
                
                if include_result:
                    filtered_results.append(result)
            
            return filtered_results
            
        except Exception as e:
            logger.debug(f"Filter application failed: {e}")
            return results
    
    async def _rank_results(self, results: List[SearchResult], query: SearchQuery) -> List[SearchResult]:
        """
        Rank search results by relevance and context.
        
        Args:
            results: List of search results
            query: Original search query
            
        Returns:
            Ranked list of search results
        """
        try:
            # Apply context-based ranking
            for result in results:
                # Boost score for exact matches
                if query.query.lower() in result.content.lower():
                    result.score *= 1.2
                
                # Boost score for function/class matches
                if result.element_type in [CodeElementType.FUNCTION, CodeElementType.CLASS]:
                    result.score *= 1.1
                
                # Boost score for current file context
                if query.current_file and query.current_file in result.file_path:
                    result.score *= 1.15
            
            # Sort by score (descending)
            results.sort(key=lambda x: x.score, reverse=True)
            
            return results
            
        except Exception as e:
            logger.debug(f"Result ranking failed: {e}")
            return results
    
    async def _get_file_path_from_id(self, file_id: str) -> str:
        """Get file path from file ID."""
        # This would need to be implemented to look up file paths
        # For now, return a placeholder
        return f"unknown_file_{file_id}"
    
    async def _get_chunk_content(self, chunk_id: str) -> str:
        """Get chunk content from chunk ID."""
        # This would need to be implemented to retrieve chunk content
        # For now, return a placeholder
        return f"Content for chunk {chunk_id}"
    
    def _detect_language_from_path(self, file_path: str) -> CodeLanguage:
        """Detect language from file path."""
        try:
            extension = file_path.split('.')[-1].lower()
            extension_map = {
                'py': CodeLanguage.PYTHON,
                'js': CodeLanguage.JAVASCRIPT,
                'jsx': CodeLanguage.JAVASCRIPT,
                'ts': CodeLanguage.TYPESCRIPT,
                'tsx': CodeLanguage.TYPESCRIPT,
                'java': CodeLanguage.JAVA,
                'cpp': CodeLanguage.CPP,
                'cc': CodeLanguage.CPP,
                'c': CodeLanguage.CPP,
                'go': CodeLanguage.GO,
                'rs': CodeLanguage.RUST,
                'html': CodeLanguage.HTML,
                'css': CodeLanguage.CSS,
                'sql': CodeLanguage.SQL,
                'md': CodeLanguage.MARKDOWN,
                'json': CodeLanguage.JSON,
                'yaml': CodeLanguage.YAML,
                'yml': CodeLanguage.YAML,
                'sh': CodeLanguage.SHELL,
            }
            
            return extension_map.get(extension, CodeLanguage.UNKNOWN)
            
        except Exception:
            return CodeLanguage.UNKNOWN
    
    async def _get_context(self, file_path: str, start_line: int, end_line: int) -> tuple[str, str]:
        """Get context before and after the matched content."""
        try:
            # This would need to be implemented to read file content
            # For now, return empty context
            return "", ""
            
        except Exception:
            return "", ""
    
    async def _generate_tags(self, content: str, element_type: CodeElementType) -> List[str]:
        """Generate semantic tags for content."""
        try:
            tags = []
            content_lower = content.lower()
            
            # Add element type tag
            tags.append(element_type.value)
            
            # Add language-specific tags
            if 'async' in content_lower or 'await' in content_lower:
                tags.append('async')
            
            if 'test' in content_lower or 'assert' in content_lower:
                tags.append('test')
            
            if 'api' in content_lower or 'endpoint' in content_lower:
                tags.append('api')
            
            if 'database' in content_lower or 'sql' in content_lower:
                tags.append('database')
            
            if 'auth' in content_lower or 'login' in content_lower:
                tags.append('authentication')
            
            return tags
            
        except Exception:
            return []
