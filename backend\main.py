# backend/main.py
import logging
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import socketio

# Import local modules
try:
    from .config import settings
    from .sessions import init_db
    from .llm_client import openrouter_client
    from .ollama_client import ollama_client
    from .qdrant_client import qdrant_client
    from .app.agent.context_manager import context_manager
    # Phase 3 imports
    from .app.agent.file_operations import file_operations
    from .app.agent.git_operations import git_operations
    from .app.agent.code_analyzer import code_analyzer
    from .app.agent.change_tracker import change_tracker
    from .app.agent.repo_context import repo_context
    # Phase 5 imports
    from .utils.lint_test_utils import lint_test_utils
    from .app.agent.task_runner import task_runner
    # Phase 6 imports
    from .utils.screenshot_utils import screenshot_utils
    from .app.agent.vision import vision_analyzer
    # Phase 8D imports
    from .app.ai_planning.core import planning_engine
    # Project Management imports
    from .app.api.projects import router as projects_router
    from .app.api.planning import router as planning_router
    # Enhanced Error Recovery imports (temporarily disabled)
    # from .app.api.error_handling import router as error_handling_router
except ImportError:
    # For direct execution
    from config import settings
    from sessions import init_db
    from llm_client import openrouter_client
    from ollama_client import ollama_client
    from vector_db import qdrant_client
    from app.agent.context_manager import context_manager
    # Phase 3 imports
    from app.agent.file_operations import file_operations
    from app.agent.git_operations import git_operations
    from app.agent.code_analyzer import code_analyzer
    from app.agent.change_tracker import change_tracker
    from app.agent.repo_context import repo_context
    # Phase 5 imports
    from utils.lint_test_utils import lint_test_utils
    from app.agent.task_runner import task_runner
    # Phase 6 imports
    from utils.screenshot_utils import screenshot_utils
    from app.agent.vision import vision_analyzer
    # Phase 8D imports
    from app.ai_planning.core import planning_engine
    # Project Management imports
    from app.api.projects import router as projects_router
    from app.api.planning import router as planning_router
    # Enhanced Error Recovery imports
    # from app.api.error_handling import router as error_handling_router  # Temporarily disabled
    # Session Management imports
    from app.api.sessions import router as sessions_router
    # Enhanced Monitoring imports
    from app.api.monitoring import router as monitoring_router
    # Smart Context Management imports
    from app.api.context_management import router as context_router
    # Analytics & Performance imports
    from app.api.analytics import router as analytics_router
    # Enhanced Projects imports
    from app.api.enhanced_projects import router as enhanced_projects_router
    # Security imports
    from app.api.security import router as security_router

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create Socket.IO server
sio = socketio.AsyncServer(
    async_mode='asgi',
    cors_allowed_origins=settings.cors_origins,
    logger=True,
    engineio_logger=True
)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events."""
    # Startup
    logger.info("Starting AI Coder Agent backend...")
    try:
        # Initialize database
        init_db()
        logger.info("Database initialized")

        # Initialize Qdrant collection
        try:
            collection_created = await qdrant_client.ensure_collection_exists()
            logger.info(f"Qdrant collection initialized: {collection_created}")
        except Exception as e:
            logger.warning(f"Qdrant initialization failed: {e}")

        # Initialize Phase 3 components
        try:
            # Start change tracking for workspace
            workspace_path = getattr(settings, 'workspace_root', '/app/workspace')
            change_tracker.start_watching(workspace_path)
            logger.info(f"Change tracking started for: {workspace_path}")
        except Exception as e:
            logger.warning(f"Change tracker initialization failed: {e}")

        logger.info("Backend startup complete - Phase 3 ready")
        yield
    except Exception as e:
        logger.error(f"Startup failed: {e}")
        raise
    finally:
        # Shutdown
        logger.info("Shutting down AI Coder Agent backend...")

        # Cleanup Phase 3 components
        try:
            change_tracker.cleanup()
            repo_context.cleanup()
            logger.info("Phase 3 components cleaned up")
        except Exception as e:
            logger.error(f"Cleanup failed: {e}")

# Create FastAPI app
app = FastAPI(
    title="AI Coder Agent",
    description="An AI-powered coding assistant that can analyze, modify, and test code repositories",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
try:
    app.include_router(projects_router)
    app.include_router(planning_router)
    # app.include_router(error_handling_router)  # Temporarily disabled
    app.include_router(sessions_router)
    app.include_router(enhanced_projects_router)
    app.include_router(security_router)
    app.include_router(context_router)
    app.include_router(analytics_router)
    app.include_router(monitoring_router)
    logger.info("API routers included successfully (ALL COMPONENTS ENABLED!)")
except Exception as e:
    logger.warning(f"Failed to include API routers: {e}")

# Socket.IO event handlers
@sio.event
async def connect(sid, environ, auth):
    """Handle client connection."""
    logger.info(f"Client connected: {sid}")
    await sio.emit('connected', {'message': 'Connected to AI Coder Agent'}, room=sid)

@sio.event
async def disconnect(sid):
    """Handle client disconnection."""
    logger.info(f"Client disconnected: {sid}")

@sio.event
async def join_session(sid, data):
    """Join a session room for receiving session-specific events."""
    session_id = data.get('session_id')
    if session_id:
        await sio.enter_room(sid, f"session_{session_id}")
        logger.info(f"Client {sid} joined session {session_id}")
        await sio.emit('joined_session', {'session_id': session_id}, room=sid)

@sio.event
async def leave_session(sid, data):
    """Leave a session room."""
    session_id = data.get('session_id')
    if session_id:
        await sio.leave_room(sid, f"session_{session_id}")
        logger.info(f"Client {sid} left session {session_id}")

# Helper function to emit logs to session
async def emit_log(session_id: str, message: str, level: str = "info"):
    """Emit a log message to all clients in a session."""
    await sio.emit('log', {
        'session_id': session_id,
        'message': message,
        'level': level,
        'timestamp': asyncio.get_event_loop().time()
    }, room=f"session_{session_id}")

# Helper function to emit terminal output
async def emit_terminal_output(session_id: str, output: str):
    """Emit terminal output to all clients in a session."""
    await sio.emit('terminal_output', {
        'session_id': session_id,
        'output': output,
        'timestamp': asyncio.get_event_loop().time()
    }, room=f"session_{session_id}")

# Basic API endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "ai-coder-agent"}

@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "AI Coder Agent API",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/api/health")
async def api_health():
    """API health check."""
    return {"status": "healthy", "api": "ready"}

@app.get("/api/config/validate")
async def validate_config():
    """Validate configuration and API keys."""
    validation = settings.validate_api_keys()
    return {
        "status": "success",
        "validation": validation,
        "recommendations": {
            "openrouter": "Set OPENROUTER_API_KEY environment variable" if not validation["openrouter_configured"] else "✓ Configured",
            "ollama": "Ensure Ollama service is running" if not validation["ollama_configured"] else "✓ Configured",
            "qdrant": "Ensure Qdrant service is running (self-hosted)" if not validation["qdrant_configured"] else "✓ Configured"
        }
    }

@app.get("/api/database/health")
async def database_health():
    """Check database connectivity and basic operations."""
    try:
        try:
            from .sessions import create_session, get_session, delete_session
        except ImportError:
            from sessions import create_session, get_session, delete_session

        # Test database operations
        test_session_id = create_session("https://github.com/test/health-check.git", "main")
        retrieved_session = get_session(test_session_id)
        deleted = delete_session(test_session_id)

        if retrieved_session and deleted:
            return {
                "status": "healthy",
                "database": "operational",
                "operations": {
                    "create": "✓ Working",
                    "read": "✓ Working",
                    "delete": "✓ Working"
                }
            }
        else:
            return {
                "status": "degraded",
                "database": "partial_failure",
                "operations": {
                    "create": "✓ Working" if test_session_id else "✗ Failed",
                    "read": "✓ Working" if retrieved_session else "✗ Failed",
                    "delete": "✓ Working" if deleted else "✗ Failed"
                }
            }
    except Exception as e:
        return {
            "status": "unhealthy",
            "database": "failed",
            "error": str(e)
        }

@app.get("/api/services/health")
async def services_health():
    """Check connectivity to all external services."""
    import httpx
    import asyncio

    async def check_service(name: str, url: str, endpoint: str = "") -> dict:
        """Check if a service is reachable."""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{url}{endpoint}")
                return {
                    "name": name,
                    "status": "healthy" if response.status_code == 200 else "unhealthy",
                    "url": url,
                    "response_code": response.status_code,
                    "reachable": True
                }
        except Exception as e:
            return {
                "name": name,
                "status": "unhealthy",
                "url": url,
                "error": str(e),
                "reachable": False
            }

    # Test all services concurrently
    services_to_check = [
        ("Ollama", settings.ollama_host, "/api/tags"),
        ("Qdrant", settings.qdrant_url, "/collections"),
    ]

    service_results = await asyncio.gather(*[
        check_service(name, url, endpoint)
        for name, url, endpoint in services_to_check
    ])

    # Overall health status
    all_healthy = all(result["status"] == "healthy" for result in service_results)

    return {
        "status": "healthy" if all_healthy else "degraded",
        "services": service_results,
        "summary": {
            "total": len(service_results),
            "healthy": sum(1 for r in service_results if r["status"] == "healthy"),
            "unhealthy": sum(1 for r in service_results if r["status"] == "unhealthy")
        }
    }

@app.get("/api/sessions")
async def list_sessions():
    """List all sessions."""
    try:
        try:
            from .sessions import list_sessions
        except ImportError:
            from sessions import list_sessions

        sessions = list_sessions()
        return {
            "status": "success",
            "sessions": sessions,
            "count": len(sessions)
        }
    except Exception as e:
        logger.error(f"Failed to list sessions: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

@app.post("/api/sessions")
async def create_new_session(data: dict = None):
    """Create a new session."""
    try:
        try:
            from .sessions import create_session
        except ImportError:
            from sessions import create_session

        # Extract data from request body if provided
        repo_url = ""
        branch = "main"
        if data:
            repo_url = data.get("repo_url", "")
            branch = data.get("branch", "main")

        session_id = create_session(repo_url, branch)
        return {
            "status": "success",
            "session_id": session_id,
            "repo_url": repo_url,
            "branch": branch
        }
    except Exception as e:
        logger.error(f"Failed to create session: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

@app.get("/api/sessions/{session_id}")
async def get_session_info(session_id: str):
    """Get session information."""
    try:
        try:
            from .sessions import get_session
        except ImportError:
            from sessions import get_session

        session = get_session(session_id)
        if session:
            return {
                "status": "success",
                "session": session
            }
        else:
            return {
                "status": "error",
                "error": "Session not found"
            }
    except Exception as e:
        logger.error(f"Failed to get session {session_id}: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

@app.delete("/api/sessions/{session_id}")
async def delete_session_endpoint(session_id: str):
    """Delete a session."""
    try:
        try:
            from .sessions import delete_session
        except ImportError:
            from sessions import delete_session

        success = delete_session(session_id)
        if success:
            return {
                "status": "success",
                "message": f"Session {session_id} deleted"
            }
        else:
            return {
                "status": "error",
                "error": "Failed to delete session"
            }
    except Exception as e:
        logger.error(f"Failed to delete session {session_id}: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

# ═══════════════════════════════════════════════════════════════════════════════
# PHASE 2 TEST ENDPOINTS
# ═══════════════════════════════════════════════════════════════════════════════

@app.post("/api/test/llm")
async def test_llm_endpoint():
    """Test OpenRouter LLM client functionality."""
    try:
        # Test basic chat completion
        messages = [
            {"role": "user", "content": "Say 'Hello from OpenRouter!' in exactly those words."}
        ]

        response = await openrouter_client.chat_completion(messages, max_tokens=50)

        # Test health check
        health = await openrouter_client.health_check()

        return {
            "status": "success",
            "test_response": response,
            "health_check": health,
            "client_info": {
                "base_url": openrouter_client.base_url,
                "code_model": openrouter_client.code_model,
                "vision_model": openrouter_client.vision_model
            }
        }

    except Exception as e:
        logger.error(f"LLM test failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "client_info": {
                "base_url": openrouter_client.base_url,
                "api_key_configured": bool(openrouter_client.api_key)
            }
        }

@app.post("/api/test/embeddings")
async def test_embeddings_endpoint():
    """Test Ollama embedding client functionality."""
    try:
        # Test single text embedding
        test_text = "This is a test sentence for embedding generation."
        embedding = await ollama_client.embed_text(test_text)

        # Test batch embedding
        test_texts = [
            "First test sentence.",
            "Second test sentence.",
            "Third test sentence."
        ]
        batch_embeddings = await ollama_client.embed_batch(test_texts, batch_size=2)

        # Test health check
        health = await ollama_client.health_check()

        return {
            "status": "success",
            "single_embedding": {
                "text": test_text,
                "embedding_length": len(embedding),
                "expected_dimensions": ollama_client.embedding_dim,
                "dimensions_match": len(embedding) == ollama_client.embedding_dim
            },
            "batch_embedding": {
                "texts_count": len(test_texts),
                "embeddings_count": len(batch_embeddings),
                "all_same_dimensions": all(len(emb) == ollama_client.embedding_dim for emb in batch_embeddings)
            },
            "health_check": health,
            "client_info": {
                "host": ollama_client.host,
                "embedding_model": ollama_client.embedding_model,
                "expected_dimensions": ollama_client.embedding_dim
            }
        }

    except Exception as e:
        logger.error(f"Embeddings test failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "client_info": {
                "host": ollama_client.host,
                "embedding_model": ollama_client.embedding_model
            }
        }

@app.post("/api/test/vector-search")
async def test_vector_search_endpoint():
    """Test Qdrant vector database client functionality."""
    try:
        # Test collection creation
        logger.info("Testing collection creation...")
        collection_created = await qdrant_client.ensure_collection_exists()
        logger.info(f"Collection creation result: {collection_created}")

        # Test embedding upsert
        test_embeddings = [
            [0.1] * qdrant_client.embedding_dim,  # Dummy embedding 1
            [0.2] * qdrant_client.embedding_dim,  # Dummy embedding 2
            [0.3] * qdrant_client.embedding_dim   # Dummy embedding 3
        ]

        test_metadata = [
            {"text": "First test document", "type": "test"},
            {"text": "Second test document", "type": "test"},
            {"text": "Third test document", "type": "test"}
        ]

        logger.info("Testing embedding upsert...")
        upserted_ids = await qdrant_client.upsert_embeddings(test_embeddings, test_metadata)
        logger.info(f"Upsert result: {len(upserted_ids)} IDs returned")

        # Test similarity search
        query_embedding = [0.15] * qdrant_client.embedding_dim  # Similar to first embedding
        search_results = await qdrant_client.search_similar(query_embedding, top_k=2)

        # Test health check
        health = await qdrant_client.health_check()

        # Clean up test data
        if upserted_ids:
            qdrant_client.delete_points(upserted_ids)

        return {
            "status": "success",
            "collection_created": collection_created,
            "upsert_test": {
                "embeddings_count": len(test_embeddings),
                "upserted_ids": upserted_ids,
                "upsert_successful": len(upserted_ids) == len(test_embeddings)
            },
            "search_test": {
                "query_dimensions": len(query_embedding),
                "results_count": len(search_results),
                "results": search_results
            },
            "health_check": health,
            "client_info": {
                "url": qdrant_client.url,
                "collection_name": qdrant_client.collection_name,
                "embedding_dimensions": qdrant_client.embedding_dim
            }
        }

    except Exception as e:
        logger.error(f"Vector search test failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "client_info": {
                "url": qdrant_client.url,
                "collection_name": qdrant_client.collection_name
            }
        }

@app.post("/api/test/context-manager")
async def test_context_manager_endpoint():
    """Test context management system functionality."""
    try:
        # Test token counting
        test_text = "This is a test sentence for token counting."
        token_count = context_manager.count_tokens(test_text)

        # Test message token counting
        test_messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello, how are you?"},
            {"role": "assistant", "content": "I'm doing well, thank you!"}
        ]
        messages_token_count = context_manager.count_messages_tokens(test_messages)

        # Test system prompt building
        system_prompt = context_manager.build_system_prompt("Testing context management")

        # Test summarization trigger logic
        should_summarize = context_manager.should_summarize(test_messages)

        # Test context preparation
        prepared_context = await context_manager.prepare_context(
            test_messages,
            task_context="Test task context",
            auto_summarize=False  # Don't actually summarize for test
        )

        return {
            "status": "success",
            "token_counting": {
                "test_text": test_text,
                "token_count": token_count,
                "text_length": len(test_text)
            },
            "message_token_counting": {
                "messages_count": len(test_messages),
                "total_tokens": messages_token_count
            },
            "system_prompt": {
                "prompt_length": len(system_prompt),
                "contains_task_context": "Testing context management" in system_prompt
            },
            "summarization_logic": {
                "should_summarize": should_summarize,
                "max_context_tokens": context_manager.max_context_tokens,
                "trigger_ratio": context_manager.summarization_trigger_ratio
            },
            "context_preparation": {
                "input_messages": len(test_messages),
                "prepared_messages": len(prepared_context),
                "has_system_message": prepared_context[0]["role"] == "system" if prepared_context else False
            }
        }

    except Exception as e:
        logger.error(f"Context manager test failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "context_manager_info": {
                "max_context_tokens": context_manager.max_context_tokens,
                "summarization_trigger_ratio": context_manager.summarization_trigger_ratio
            }
        }

# ═══════════════════════════════════════════════════════════════════════════════
# PHASE 3 ENDPOINTS - File Operations, Git, Code Analysis, Change Tracking
# ═══════════════════════════════════════════════════════════════════════════════

@app.post("/api/files/read")
async def read_file_endpoint(data: dict):
    """Read file content with encoding detection."""
    try:
        file_path = data.get("file_path")
        encoding = data.get("encoding")

        if not file_path:
            return {"status": "error", "error": "file_path is required"}

        result = await file_operations.read_file(file_path, encoding)
        return result

    except Exception as e:
        logger.error(f"File read failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/files/write")
async def write_file_endpoint(data: dict):
    """Write file content with atomic operations."""
    try:
        file_path = data.get("file_path")
        content = data.get("content", "")
        encoding = data.get("encoding", "utf-8")
        create_dirs = data.get("create_dirs", True)

        if not file_path:
            return {"status": "error", "error": "file_path is required"}

        result = await file_operations.write_file(file_path, content, encoding, create_dirs)
        return result

    except Exception as e:
        logger.error(f"File write failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/files/create")
async def create_file_endpoint(data: dict):
    """Create new file with conflict resolution."""
    try:
        file_path = data.get("file_path")
        content = data.get("content", "")
        overwrite = data.get("overwrite", False)

        if not file_path:
            return {"status": "error", "error": "file_path is required"}

        result = await file_operations.create_file(file_path, content, overwrite)
        return result

    except Exception as e:
        logger.error(f"File create failed: {e}")
        return {"status": "error", "error": str(e)}

@app.delete("/api/files/delete")
async def delete_file_endpoint(data: dict):
    """Delete file with safety checks and backup options."""
    try:
        file_path = data.get("file_path")
        create_backup = data.get("create_backup", True)

        if not file_path:
            return {"status": "error", "error": "file_path is required"}

        result = await file_operations.delete_file(file_path, create_backup)
        return result

    except Exception as e:
        logger.error(f"File delete failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/files/list")
async def list_directory_endpoint(data: dict):
    """List directory contents with filtering options."""
    try:
        directory_path = data.get("directory_path", ".")
        recursive = data.get("recursive", False)
        include_hidden = data.get("include_hidden", False)
        filter_extensions = data.get("filter_extensions")

        result = await file_operations.list_directory(
            directory_path, recursive, include_hidden, filter_extensions
        )
        return result

    except Exception as e:
        logger.error(f"Directory listing failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/files/info")
async def get_file_info_endpoint(data: dict):
    """Get detailed file/directory metadata."""
    try:
        file_path = data.get("file_path")

        if not file_path:
            return {"status": "error", "error": "file_path is required"}

        result = await file_operations.get_file_info(file_path)
        return result

    except Exception as e:
        logger.error(f"File info failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/git/status")
async def git_status_endpoint(data: dict):
    """Get git repository status."""
    try:
        repo_path = data.get("repo_path", ".")

        result = await git_operations.get_git_status(repo_path)
        return result

    except Exception as e:
        logger.error(f"Git status failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/git/diff")
async def git_diff_endpoint(data: dict):
    """Get git diff with file-specific and commit-range options."""
    try:
        repo_path = data.get("repo_path", ".")
        file_path = data.get("file_path")
        staged = data.get("staged", False)
        commit_range = data.get("commit_range")

        result = await git_operations.get_git_diff(repo_path, file_path, staged, commit_range)
        return result

    except Exception as e:
        logger.error(f"Git diff failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/git/commit")
async def git_commit_endpoint(data: dict):
    """Commit changes with message validation and staging."""
    try:
        repo_path = data.get("repo_path", ".")
        message = data.get("message")
        files = data.get("files")
        add_all = data.get("add_all", False)

        if not message:
            return {"status": "error", "error": "commit message is required"}

        result = await git_operations.git_commit(repo_path, message, files, add_all)
        return result

    except Exception as e:
        logger.error(f"Git commit failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/git/branch")
async def git_branch_endpoint(data: dict):
    """Git branch operations (create, switch, list, delete)."""
    try:
        repo_path = data.get("repo_path", ".")
        operation = data.get("operation")  # 'create', 'switch', 'list', 'delete'
        branch_name = data.get("branch_name")
        force = data.get("force", False)

        if not operation:
            return {"status": "error", "error": "operation is required"}

        result = await git_operations.git_branch_operations(repo_path, operation, branch_name, force)
        return result

    except Exception as e:
        logger.error(f"Git branch operation failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/git/history")
async def git_history_endpoint(data: dict):
    """Get commit history with filtering and pagination."""
    try:
        repo_path = data.get("repo_path", ".")
        max_count = data.get("max_count", 50)
        branch = data.get("branch")
        file_path = data.get("file_path")

        result = await git_operations.get_commit_history(repo_path, max_count, branch, file_path)
        return result

    except Exception as e:
        logger.error(f"Git history failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/git/blame")
async def git_blame_endpoint(data: dict):
    """Get line-by-line authorship information for a file."""
    try:
        repo_path = data.get("repo_path", ".")
        file_path = data.get("file_path")

        if not file_path:
            return {"status": "error", "error": "file_path is required"}

        result = await git_operations.get_file_blame(repo_path, file_path)
        return result

    except Exception as e:
        logger.error(f"Git blame failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/git/stash")
async def git_stash_endpoint(data: dict):
    """Git stash operations for temporary changes."""
    try:
        repo_path = data.get("repo_path", ".")
        operation = data.get("operation")  # 'save', 'pop', 'list', 'drop', 'apply'
        message = data.get("message")
        stash_index = data.get("stash_index")

        if not operation:
            return {"status": "error", "error": "operation is required"}

        result = await git_operations.git_stash_operations(repo_path, operation, message, stash_index)
        return result

    except Exception as e:
        logger.error(f"Git stash operation failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/code/parse")
async def parse_code_structure_endpoint(data: dict):
    """Parse code structure for functions, classes, imports, etc."""
    try:
        file_path = data.get("file_path")

        if not file_path:
            return {"status": "error", "error": "file_path is required"}

        result = await code_analyzer.parse_code_structure(file_path)
        return result

    except Exception as e:
        logger.error(f"Code parsing failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/code/complexity")
async def analyze_code_complexity_endpoint(data: dict):
    """Analyze code complexity metrics."""
    try:
        file_path = data.get("file_path")

        if not file_path:
            return {"status": "error", "error": "file_path is required"}

        result = await code_analyzer.analyze_code_complexity(file_path)
        return result

    except Exception as e:
        logger.error(f"Code complexity analysis failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/code/embeddings")
async def generate_code_embeddings_endpoint(data: dict):
    """Generate semantic embeddings for code using BGE-M3."""
    try:
        file_path = data.get("file_path")
        chunk_size = data.get("chunk_size", 1000)

        if not file_path:
            return {"status": "error", "error": "file_path is required"}

        result = await code_analyzer.generate_code_embeddings(file_path, chunk_size)
        return result

    except Exception as e:
        logger.error(f"Code embedding generation failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/code/index")
async def index_code_file_endpoint(data: dict):
    """Index a code file with structure analysis and embeddings."""
    try:
        file_path = data.get("file_path")

        if not file_path:
            return {"status": "error", "error": "file_path is required"}

        result = await code_analyzer.index_code_file(file_path)
        return result

    except Exception as e:
        logger.error(f"Code indexing failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/code/search")
async def search_code_semantically_endpoint(data: dict):
    """Search code semantically using embeddings."""
    try:
        query = data.get("query")
        limit = data.get("limit", 10)
        language_filter = data.get("language_filter")

        if not query:
            return {"status": "error", "error": "query is required"}

        result = await code_analyzer.search_code_semantically(query, limit, language_filter)
        return result

    except Exception as e:
        logger.error(f"Code search failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/code/dependencies")
async def analyze_project_dependencies_endpoint(data: dict):
    """Analyze project-wide dependencies and relationships."""
    try:
        project_path = data.get("project_path", ".")

        result = await code_analyzer.analyze_project_dependencies(project_path)
        return result

    except Exception as e:
        logger.error(f"Dependency analysis failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/changes/start-watching")
async def start_watching_endpoint(data: dict):
    """Start watching a directory for file changes."""
    try:
        path = data.get("path")

        result = change_tracker.start_watching(path)
        return result

    except Exception as e:
        logger.error(f"Start watching failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/changes/stop-watching")
async def stop_watching_endpoint(data: dict):
    """Stop watching a directory for file changes."""
    try:
        path = data.get("path")

        result = change_tracker.stop_watching(path)
        return result

    except Exception as e:
        logger.error(f"Stop watching failed: {e}")
        return {"status": "error", "error": str(e)}

@app.get("/api/changes/recent")
async def get_recent_changes_endpoint():
    """Get recent file changes."""
    try:
        result = await change_tracker.get_recent_changes()
        return result

    except Exception as e:
        logger.error(f"Get recent changes failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/changes/manual-scan")
async def trigger_manual_scan_endpoint(data: dict):
    """Trigger a manual scan for changes."""
    try:
        path = data.get("path")

        result = await change_tracker.trigger_manual_scan(path)
        return result

    except Exception as e:
        logger.error(f"Manual scan failed: {e}")
        return {"status": "error", "error": str(e)}

@app.get("/api/changes/statistics")
async def get_change_statistics_endpoint():
    """Get change tracking statistics."""
    try:
        result = change_tracker.get_statistics()
        return result

    except Exception as e:
        logger.error(f"Get statistics failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/repo/analyze-structure")
async def analyze_project_structure_endpoint(data: dict):
    """Analyze complete project structure."""
    try:
        project_path = data.get("project_path")

        result = await repo_context.analyze_project_structure(project_path)
        return result

    except Exception as e:
        logger.error(f"Project structure analysis failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/repo/generate-summary")
async def generate_codebase_summary_endpoint(data: dict):
    """Generate comprehensive codebase summary for LLM context."""
    try:
        project_path = data.get("project_path")
        include_code_samples = data.get("include_code_samples", True)
        max_files = data.get("max_files")

        result = await repo_context.generate_codebase_summary(
            project_path, include_code_samples, max_files
        )
        return result

    except Exception as e:
        logger.error(f"Codebase summary generation failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/repo/search-relevant")
async def search_relevant_files_endpoint(data: dict):
    """Search for files relevant to a query using semantic search."""
    try:
        query = data.get("query")
        project_path = data.get("project_path")
        limit = data.get("limit", 10)

        if not query:
            return {"status": "error", "error": "query is required"}

        result = await repo_context.search_relevant_files(query, project_path, limit)
        return result

    except Exception as e:
        logger.error(f"Relevant files search failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/repo/context-for-task")
async def get_context_for_task_endpoint(data: dict):
    """Get comprehensive context for a specific task."""
    try:
        task_description = data.get("task_description")
        project_path = data.get("project_path")
        include_git_status = data.get("include_git_status", True)

        if not task_description:
            return {"status": "error", "error": "task_description is required"}

        result = await repo_context.get_context_for_task(
            task_description, project_path, include_git_status
        )
        return result

    except Exception as e:
        logger.error(f"Task context generation failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/test/phase3-integration")
async def test_phase3_integration_endpoint():
    """Test Phase 3 integration and all components."""
    try:
        test_results = {}

        # Test file operations
        try:
            test_file = "/tmp/test_phase3.txt"
            write_result = await file_operations.write_file(test_file, "Phase 3 test content")
            read_result = await file_operations.read_file(test_file)
            delete_result = await file_operations.delete_file(test_file, create_backup=False)

            test_results["file_operations"] = {
                "status": "success",
                "write": write_result.get("status") == "success",
                "read": read_result.get("status") == "success",
                "delete": delete_result.get("status") == "success"
            }
        except Exception as e:
            test_results["file_operations"] = {"status": "error", "error": str(e)}

        # Test git operations
        try:
            git_status = await git_operations.get_git_status(".")
            test_results["git_operations"] = {
                "status": "success",
                "git_status": git_status.get("status") == "success"
            }
        except Exception as e:
            test_results["git_operations"] = {"status": "error", "error": str(e)}

        # Test code analyzer
        try:
            # Test with this file
            parse_result = await code_analyzer.parse_code_structure(__file__)
            test_results["code_analyzer"] = {
                "status": "success",
                "parse": parse_result.get("status") == "success"
            }
        except Exception as e:
            test_results["code_analyzer"] = {"status": "error", "error": str(e)}

        # Test change tracker
        try:
            stats = change_tracker.get_statistics()
            test_results["change_tracker"] = {
                "status": "success",
                "statistics": stats.get("status") == "success"
            }
        except Exception as e:
            test_results["change_tracker"] = {"status": "error", "error": str(e)}

        # Test repo context
        try:
            structure = await repo_context.analyze_project_structure(".")
            test_results["repo_context"] = {
                "status": "success",
                "structure_analysis": structure.get("status") == "success"
            }
        except Exception as e:
            test_results["repo_context"] = {"status": "error", "error": str(e)}

        # Overall status
        all_success = all(
            result.get("status") == "success"
            for result in test_results.values()
        )

        return {
            "status": "success" if all_success else "partial",
            "phase3_integration": test_results,
            "summary": {
                "total_components": len(test_results),
                "successful": sum(1 for r in test_results.values() if r.get("status") == "success"),
                "failed": sum(1 for r in test_results.values() if r.get("status") == "error")
            }
        }

    except Exception as e:
        logger.error(f"Phase 3 integration test failed: {e}")
        return {"status": "error", "error": str(e)}

# ═══════════════════════════════════════════════════════════════════════════════
# PHASE 5 TEST ENDPOINTS
# ═══════════════════════════════════════════════════════════════════════════════

@app.post("/api/test/lint-test-utils")
async def test_lint_test_utilities_endpoint():
    """Test lint and test utilities functionality."""
    try:
        # Import Phase 5 modules
        try:
            from .utils.lint_test_utils import lint_test_utils
        except ImportError:
            from utils.lint_test_utils import lint_test_utils

        test_results = {}

        # Test configuration loading
        try:
            config_result = await lint_test_utils.load_project_config(".")
            test_results["config_loading"] = {
                "status": "success",
                "config_loaded": config_result.get("status") == "success"
            }
        except Exception as e:
            test_results["config_loading"] = {"status": "error", "error": str(e)}

        # Test Python linting (if available)
        try:
            lint_result = await lint_test_utils.run_python_linting(".")
            test_results["python_linting"] = {
                "status": "success",
                "linting_executed": lint_result.get("status") in ["success", "skipped"]
            }
        except Exception as e:
            test_results["python_linting"] = {"status": "error", "error": str(e)}

        # Test Python testing (if available)
        try:
            test_result = await lint_test_utils.run_python_tests(".")
            test_results["python_testing"] = {
                "status": "success",
                "testing_executed": test_result.get("status") in ["success", "skipped"]
            }
        except Exception as e:
            test_results["python_testing"] = {"status": "error", "error": str(e)}

        # Test full check
        try:
            full_result = await lint_test_utils.run_full_check(".")
            test_results["full_check"] = {
                "status": "success",
                "check_completed": full_result.get("status") == "success"
            }
        except Exception as e:
            test_results["full_check"] = {"status": "error", "error": str(e)}

        # Overall status
        all_success = all(
            result.get("status") == "success"
            for result in test_results.values()
        )

        return {
            "status": "success" if all_success else "partial",
            "lint_test_utils": test_results,
            "summary": {
                "total_tests": len(test_results),
                "successful": sum(1 for r in test_results.values() if r.get("status") == "success"),
                "failed": sum(1 for r in test_results.values() if r.get("status") == "error")
            }
        }

    except Exception as e:
        logger.error(f"Lint/test utilities test failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/test/task-runner")
async def test_task_runner_endpoint():
    """Test core task runner functionality."""
    try:
        # Import Phase 5 modules
        try:
            from .app.agent.task_runner import task_runner
        except ImportError:
            from app.agent.task_runner import task_runner

        test_results = {}

        # Test task runner initialization
        try:
            stats = task_runner.execution_stats
            test_results["initialization"] = {
                "status": "success",
                "stats_available": isinstance(stats, dict)
            }
        except Exception as e:
            test_results["initialization"] = {"status": "error", "error": str(e)}

        # Test tool availability
        try:
            available_tools = list(task_runner.available_tools.keys())
            test_results["tools"] = {
                "status": "success",
                "tool_count": len(available_tools),
                "tools_available": len(available_tools) > 0
            }
        except Exception as e:
            test_results["tools"] = {"status": "error", "error": str(e)}

        # Test context generation
        try:
            context_result = await task_runner.generate_task_context(".", "Test task")
            test_results["context_generation"] = {
                "status": "success",
                "context_generated": context_result.get("status") == "success"
            }
        except Exception as e:
            test_results["context_generation"] = {"status": "error", "error": str(e)}

        # Test LLM response parsing (with mock data)
        try:
            mock_prompt = "Test prompt"
            # This will likely fail due to API limits, but we test the structure
            parse_result = await task_runner.parse_llm_response(mock_prompt, "test-task")
            test_results["llm_parsing"] = {
                "status": "success",
                "parsing_attempted": parse_result.get("status") in ["success", "error"]
            }
        except Exception as e:
            test_results["llm_parsing"] = {"status": "error", "error": str(e)}

        # Overall status
        all_success = all(
            result.get("status") == "success"
            for result in test_results.values()
        )

        return {
            "status": "success" if all_success else "partial",
            "task_runner": test_results,
            "summary": {
                "total_tests": len(test_results),
                "successful": sum(1 for r in test_results.values() if r.get("status") == "success"),
                "failed": sum(1 for r in test_results.values() if r.get("status") == "error")
            }
        }

    except Exception as e:
        logger.error(f"Task runner test failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/test/enhanced-context")
async def test_enhanced_context_endpoint():
    """Test enhanced memory and context retrieval."""
    try:
        # Import enhanced context manager
        try:
            from .app.agent.context_manager import context_manager
        except ImportError:
            from app.agent.context_manager import context_manager

        test_results = {}

        # Test semantic search integration
        try:
            search_result = await context_manager.integrate_semantic_search(
                "test query", ".", max_results=3
            )
            test_results["semantic_search"] = {
                "status": "success",
                "search_executed": search_result.get("status") in ["success", "skipped"]
            }
        except Exception as e:
            test_results["semantic_search"] = {"status": "error", "error": str(e)}

        # Test task context building
        try:
            context_result = await context_manager.build_task_context(".", "Test task")
            test_results["task_context"] = {
                "status": "success",
                "context_built": context_result.get("status") == "success"
            }
        except Exception as e:
            test_results["task_context"] = {"status": "error", "error": str(e)}

        # Test relevant code retrieval
        try:
            code_result = await context_manager.retrieve_relevant_code("test", ".")
            test_results["code_retrieval"] = {
                "status": "success",
                "retrieval_executed": code_result.get("status") in ["success", "skipped"]
            }
        except Exception as e:
            test_results["code_retrieval"] = {"status": "error", "error": str(e)}

        # Test conversation memory
        try:
            test_messages = [
                {"role": "system", "content": "Test system message"},
                {"role": "user", "content": "Test user message"},
                {"role": "assistant", "content": "Test assistant message"}
            ]
            memory_result = await context_manager.maintain_conversation_memory(
                test_messages, "test-task"
            )
            test_results["conversation_memory"] = {
                "status": "success",
                "memory_maintained": isinstance(memory_result, list)
            }
        except Exception as e:
            test_results["conversation_memory"] = {"status": "error", "error": str(e)}

        # Test context optimization
        try:
            test_messages = [
                {"role": "system", "content": "Test system message"},
                {"role": "user", "content": "Test user message"}
            ]
            optimized = context_manager.optimize_context_window(test_messages)
            test_results["context_optimization"] = {
                "status": "success",
                "optimization_executed": isinstance(optimized, list)
            }
        except Exception as e:
            test_results["context_optimization"] = {"status": "error", "error": str(e)}

        # Overall status
        all_success = all(
            result.get("status") == "success"
            for result in test_results.values()
        )

        return {
            "status": "success" if all_success else "partial",
            "enhanced_context": test_results,
            "summary": {
                "total_tests": len(test_results),
                "successful": sum(1 for r in test_results.values() if r.get("status") == "success"),
                "failed": sum(1 for r in test_results.values() if r.get("status") == "error")
            }
        }

    except Exception as e:
        logger.error(f"Enhanced context test failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/test/phase5-integration")
async def test_phase5_integration_endpoint():
    """Test Phase 5 integration and all components."""
    try:
        # Import all Phase 5 modules
        try:
            from .utils.lint_test_utils import lint_test_utils
            from .app.agent.task_runner import task_runner
            from .app.agent.context_manager import context_manager
        except ImportError:
            from utils.lint_test_utils import lint_test_utils
            from app.agent.task_runner import task_runner
            from app.agent.context_manager import context_manager

        test_results = {}

        # Test lint/test utilities
        try:
            config_result = await lint_test_utils.load_project_config(".")
            test_results["lint_test_utils"] = {
                "status": "success",
                "module_loaded": True,
                "config_loading": config_result.get("status") == "success"
            }
        except Exception as e:
            test_results["lint_test_utils"] = {"status": "error", "error": str(e)}

        # Test task runner
        try:
            stats = task_runner.execution_stats
            available_tools = list(task_runner.available_tools.keys())
            test_results["task_runner"] = {
                "status": "success",
                "module_loaded": True,
                "stats_available": isinstance(stats, dict),
                "tools_count": len(available_tools)
            }
        except Exception as e:
            test_results["task_runner"] = {"status": "error", "error": str(e)}

        # Test enhanced context manager
        try:
            test_messages = [{"role": "user", "content": "test"}]
            token_count = context_manager.count_messages_tokens(test_messages)
            test_results["enhanced_context"] = {
                "status": "success",
                "module_loaded": True,
                "token_counting": token_count > 0
            }
        except Exception as e:
            test_results["enhanced_context"] = {"status": "error", "error": str(e)}

        # Test integration between components
        try:
            # Test task context generation
            context_result = await task_runner.generate_task_context(".", "Integration test")

            # Test enhanced context building
            enhanced_context = await context_manager.build_task_context(".", "Integration test")

            test_results["component_integration"] = {
                "status": "success",
                "task_context": context_result.get("status") == "success",
                "enhanced_context": enhanced_context.get("status") == "success"
            }
        except Exception as e:
            test_results["component_integration"] = {"status": "error", "error": str(e)}

        # Test Phase 3 + Phase 5 integration
        try:
            # Test that Phase 5 can use Phase 3 modules
            from .app.agent.file_operations import file_operations
            from .app.agent.code_analyzer import code_analyzer

            # Test file operations through task runner
            file_result = await task_runner._tool_read_file(__file__)

            test_results["phase3_integration"] = {
                "status": "success",
                "file_operations": file_result.get("status") == "success",
                "modules_accessible": True
            }
        except Exception as e:
            test_results["phase3_integration"] = {"status": "error", "error": str(e)}

        # Overall status
        all_success = all(
            result.get("status") == "success"
            for result in test_results.values()
        )

        return {
            "status": "success" if all_success else "partial",
            "phase5_integration": test_results,
            "summary": {
                "total_components": len(test_results),
                "successful": sum(1 for r in test_results.values() if r.get("status") == "success"),
                "failed": sum(1 for r in test_results.values() if r.get("status") == "error")
            },
            "phase5_status": "COMPLETE" if all_success else "PARTIAL"
        }

    except Exception as e:
        logger.error(f"Phase 5 integration test failed: {e}")
        return {"status": "error", "error": str(e)}

# ═══════════════════════════════════════════════════════════════════════════════
# PHASE 6 TEST ENDPOINTS
# ═══════════════════════════════════════════════════════════════════════════════

@app.post("/api/test/screenshot-utils")
async def test_screenshot_utilities_endpoint():
    """Test screenshot utilities functionality."""
    try:
        # Import Phase 6 modules
        try:
            from .utils.screenshot_utils import screenshot_utils
        except ImportError:
            from utils.screenshot_utils import screenshot_utils

        test_results = {}

        # Test local server detection
        try:
            port = await screenshot_utils.detect_local_server_port(".")
            test_results["server_detection"] = {
                "status": "success",
                "port_detected": port is not None,
                "port": port
            }
        except Exception as e:
            test_results["server_detection"] = {"status": "error", "error": str(e)}

        # Test screenshot settings
        try:
            settings = screenshot_utils.default_settings
            test_results["settings"] = {
                "status": "success",
                "settings_available": isinstance(settings, dict),
                "settings_count": len(settings)
            }
        except Exception as e:
            test_results["settings"] = {"status": "error", "error": str(e)}

        # Test path validation
        try:
            validated_path = screenshot_utils._validate_path(".")
            test_results["path_validation"] = {
                "status": "success",
                "validation_working": validated_path is not None
            }
        except Exception as e:
            test_results["path_validation"] = {"status": "error", "error": str(e)}

        # Overall status
        all_success = all(
            result.get("status") == "success"
            for result in test_results.values()
        )

        return {
            "status": "success" if all_success else "partial",
            "screenshot_utils": test_results,
            "summary": {
                "total_tests": len(test_results),
                "successful": sum(1 for r in test_results.values() if r.get("status") == "success"),
                "failed": sum(1 for r in test_results.values() if r.get("status") == "error")
            }
        }

    except Exception as e:
        logger.error(f"Screenshot utilities test failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/test/vision-analysis")
async def test_vision_analysis_endpoint():
    """Test vision analysis functionality."""
    try:
        # Import Phase 6 modules
        try:
            from .app.agent.vision import vision_analyzer
        except ImportError:
            from app.agent.vision import vision_analyzer

        test_results = {}

        # Test vision analyzer initialization
        try:
            prompts = vision_analyzer.analysis_prompts
            test_results["initialization"] = {
                "status": "success",
                "prompts_available": len(prompts) > 0,
                "prompt_types": list(prompts.keys())
            }
        except Exception as e:
            test_results["initialization"] = {"status": "error", "error": str(e)}

        # Test path validation
        try:
            validated_path = vision_analyzer._validate_path(".")
            test_results["path_validation"] = {
                "status": "success",
                "validation_working": validated_path is not None
            }
        except Exception as e:
            test_results["path_validation"] = {"status": "error", "error": str(e)}

        # Test analysis summary generation
        try:
            mock_results = {
                "test_analysis": {
                    "status": "success",
                    "analysis": {
                        "analysis": {
                            "overall_score": 8,
                            "issues": [{"severity": "low", "description": "Test issue"}]
                        }
                    }
                }
            }
            summary = vision_analyzer._generate_analysis_summary(mock_results)
            test_results["summary_generation"] = {
                "status": "success",
                "summary_generated": isinstance(summary, dict)
            }
        except Exception as e:
            test_results["summary_generation"] = {"status": "error", "error": str(e)}

        # Overall status
        all_success = all(
            result.get("status") == "success"
            for result in test_results.values()
        )

        return {
            "status": "success" if all_success else "partial",
            "vision_analysis": test_results,
            "summary": {
                "total_tests": len(test_results),
                "successful": sum(1 for r in test_results.values() if r.get("status") == "success"),
                "failed": sum(1 for r in test_results.values() if r.get("status") == "error")
            }
        }

    except Exception as e:
        logger.error(f"Vision analysis test failed: {e}")
        return {"status": "error", "error": str(e)}

@app.post("/api/test/phase6-integration")
async def test_phase6_integration_endpoint():
    """Test Phase 6 integration and all components."""
    try:
        # Import all Phase 6 modules
        try:
            from .utils.screenshot_utils import screenshot_utils
            from .app.agent.vision import vision_analyzer
        except ImportError:
            from utils.screenshot_utils import screenshot_utils
            from app.agent.vision import vision_analyzer

        test_results = {}

        # Test screenshot utilities
        try:
            port = await screenshot_utils.detect_local_server_port(".")
            test_results["screenshot_utils"] = {
                "status": "success",
                "module_loaded": True,
                "server_detection": port is not None
            }
        except Exception as e:
            test_results["screenshot_utils"] = {"status": "error", "error": str(e)}

        # Test vision analyzer
        try:
            prompts = vision_analyzer.analysis_prompts
            test_results["vision_analyzer"] = {
                "status": "success",
                "module_loaded": True,
                "prompts_count": len(prompts)
            }
        except Exception as e:
            test_results["vision_analyzer"] = {"status": "error", "error": str(e)}

        # Test integration between components
        try:
            # Test that vision analyzer can work with screenshot utils
            # This is a basic integration test
            screenshot_settings = screenshot_utils.default_settings
            analysis_prompts = vision_analyzer.analysis_prompts

            test_results["component_integration"] = {
                "status": "success",
                "screenshot_settings": isinstance(screenshot_settings, dict),
                "analysis_prompts": isinstance(analysis_prompts, dict),
                "integration_ready": True
            }
        except Exception as e:
            test_results["component_integration"] = {"status": "error", "error": str(e)}

        # Test Phase 2 + Phase 6 integration (LLM client)
        try:
            # Test that vision analyzer can access LLM client
            from .llm_client import openrouter_client

            test_results["llm_integration"] = {
                "status": "success",
                "llm_client_accessible": True,
                "vision_model": openrouter_client.vision_model
            }
        except Exception as e:
            test_results["llm_integration"] = {"status": "error", "error": str(e)}

        # Overall status
        all_success = all(
            result.get("status") == "success"
            for result in test_results.values()
        )

        return {
            "status": "success" if all_success else "partial",
            "phase6_integration": test_results,
            "summary": {
                "total_components": len(test_results),
                "successful": sum(1 for r in test_results.values() if r.get("status") == "success"),
                "failed": sum(1 for r in test_results.values() if r.get("status") == "error")
            },
            "phase6_status": "COMPLETE" if all_success else "PARTIAL"
        }

    except Exception as e:
        logger.error(f"Phase 6 integration test failed: {e}")
        return {"status": "error", "error": str(e)}

# ═══════════════════════════════════════════════════════════════════════════════
# PHASE 8D - AI PLANNING ENDPOINTS
# ═══════════════════════════════════════════════════════════════════════════════

@app.post("/api/planning/create")
async def create_development_plan_endpoint(request: dict):
    """Create a comprehensive development plan using AI."""
    try:
        from .app.ai_planning.models import PlanningRequest

        # Convert dict to PlanningRequest
        planning_request = PlanningRequest(**request)

        # Create the plan
        response = await planning_engine.create_plan(planning_request)

        return {
            "status": "success",
            "plan_id": response.plan.plan_id,
            "title": response.plan.title,
            "total_tasks": len(response.plan.tasks),
            "estimated_hours": response.plan.estimated_total_hours,
            "confidence_score": response.confidence_score,
            "validation_status": response.plan.validation_status,
            "planning_duration": response.planning_duration_seconds,
            "response": response.dict()
        }

    except Exception as e:
        logger.error(f"❌ Plan creation failed: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

@app.get("/api/planning/status")
async def get_planning_status_endpoint():
    """Get current AI planning system status."""
    try:
        stats = await planning_engine.get_planning_stats()
        return {
            "status": "operational" if planning_engine.is_planning_enabled() else "disabled",
            "enabled": planning_engine.is_planning_enabled(),
            "stats": stats,
            "agents": {
                "primary_planner": planning_engine.primary_planner.agent_name,
                "validation_agent": planning_engine.validation_agent.agent_name
            }
        }
    except Exception as e:
        logger.error(f"❌ Failed to get planning status: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

@app.post("/api/planning/toggle")
async def toggle_ai_planning_endpoint(data: dict):
    """Toggle AI planning system on/off."""
    try:
        enabled = data.get("enabled", True)
        planning_engine.toggle_planning(enabled)
        status = "enabled" if enabled else "disabled"
        logger.info(f"🎯 AI Planning {status} via API")
        return {
            "status": "success",
            "message": f"AI Planning {status}",
            "enabled": enabled
        }
    except Exception as e:
        logger.error(f"❌ Failed to toggle planning: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

@app.get("/api/planning/sessions")
async def list_planning_sessions_endpoint():
    """List all planning sessions."""
    try:
        sessions = await planning_engine.list_sessions()
        return {
            "status": "success",
            "sessions": [session.dict() for session in sessions],
            "count": len(sessions)
        }
    except Exception as e:
        logger.error(f"❌ Failed to list sessions: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

@app.get("/api/planning/plans/{plan_id}")
async def get_development_plan_endpoint(plan_id: str):
    """Get a specific development plan by ID."""
    try:
        # Search through all sessions for the plan
        sessions = await planning_engine.list_sessions()
        for session in sessions:
            for plan in session.plans:
                if plan.plan_id == plan_id:
                    return {
                        "status": "success",
                        "plan": plan.dict()
                    }

        return {
            "status": "error",
            "error": "Plan not found"
        }
    except Exception as e:
        logger.error(f"❌ Failed to get plan: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

@app.post("/api/planning/templates/feature")
async def create_feature_plan_endpoint(data: dict):
    """Create a plan for implementing a new feature."""
    try:
        from .app.ai_planning.models import PlanningRequest

        feature_name = data.get("feature_name", "")
        description = data.get("description", "")
        complexity = data.get("complexity", "moderate")
        timeline_days = data.get("timeline_days")

        request = PlanningRequest(
            title=f"Implement {feature_name}",
            description=description,
            max_timeline_days=timeline_days,
            preferred_complexity=complexity,
            required_skills=["programming", "testing", "documentation"]
        )

        response = await planning_engine.create_plan(request)
        return {
            "status": "success",
            "plan_id": response.plan.plan_id,
            "feature_name": feature_name,
            "response": response.dict()
        }
    except Exception as e:
        logger.error(f"❌ Feature plan creation failed: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

@app.get("/api/planning/health")
async def planning_health_check_endpoint():
    """Health check for the AI planning system."""
    try:
        is_enabled = planning_engine.is_planning_enabled()
        stats = await planning_engine.get_planning_stats()

        return {
            "status": "healthy",
            "enabled": is_enabled,
            "agents_available": True,
            "active_sessions": stats["total_sessions"]
        }
    except Exception as e:
        logger.error(f"❌ Planning health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "enabled": False,
            "agents_available": False
        }

# Error handlers
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )

# Mount Socket.IO app
socket_app = socketio.ASGIApp(sio, app)

# Export the socket app as the main app
app = socket_app

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level=settings.log_level.lower()
    )
