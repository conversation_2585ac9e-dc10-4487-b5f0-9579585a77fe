interactions:
- request:
    body: '{"messages": [{"role": "user", "content": [{"text": "What is the capital of France?"}]}], "system": [], "inferenceConfig":
      {"stopSequences": ["Paris"]}}'
    headers:
      amz-sdk-invocation-id:
      - !!binary |
        YzVkZjljOTMtMDQ1Zi00NWE0LWJhY2YtMDAwMjdjYTg1NmRl
      amz-sdk-request:
      - !!binary |
        YXR0ZW1wdD0x
      content-length:
      - '152'
      content-type:
      - !!binary |
        YXBwbGljYXRpb24vanNvbg==
    method: POST
    uri: https://bedrock-runtime.us-east-1.amazonaws.com/model/us.amazon.nova-micro-v1%3A0/converse
  response:
    headers:
      connection:
      - keep-alive
      content-length:
      - '209'
      content-type:
      - application/json
    parsed_body:
      metrics:
        latencyMs: 179
      output:
        message:
          content:
          - text: The capital of France is Paris
          role: assistant
      stopReason: end_turn
      usage:
        inputTokens: 7
        outputTokens: 6
        totalTokens: 13
    status:
      code: 200
      message: OK
version: 1
