"""
⚡ Cache Optimizer - Revolutionary Intelligent Caching System

This module provides advanced cache optimization with:
- Multi-level caching strategies (L1, L2, L3)
- Intelligent cache warming and preloading
- Adaptive cache sizing and eviction
- Cache hit rate optimization
- Distributed cache coordination
- Performance-aware cache management
"""

import asyncio
import hashlib
import time
from collections import defaultdict, OrderedDict
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)


class CacheStrategy(str, Enum):
    """Cache optimization strategies."""
    LRU = "lru"                    # Least Recently Used
    LFU = "lfu"                    # Least Frequently Used
    ARC = "arc"                    # Adaptive Replacement Cache
    CLOCK = "clock"                # Clock algorithm
    RANDOM = "random"              # Random replacement
    OPTIMAL = "optimal"            # Optimal (predictive)


class CacheLevel(str, Enum):
    """Cache levels for multi-tier caching."""
    L1_MEMORY = "l1_memory"        # In-memory cache (fastest)
    L2_REDIS = "l2_redis"          # Redis cache (fast)
    L3_DISK = "l3_disk"            # Disk cache (slower but persistent)


class CacheMetrics(BaseModel):
    """Cache performance metrics."""
    
    cache_level: CacheLevel = Field(..., description="Cache level")
    
    # Hit/Miss metrics
    total_requests: int = Field(default=0, description="Total cache requests")
    cache_hits: int = Field(default=0, description="Cache hits")
    cache_misses: int = Field(default=0, description="Cache misses")
    hit_rate: float = Field(default=0.0, description="Cache hit rate percentage")
    
    # Performance metrics
    average_hit_time_ms: float = Field(default=0.0, description="Average hit time in milliseconds")
    average_miss_time_ms: float = Field(default=0.0, description="Average miss time in milliseconds")
    
    # Storage metrics
    total_size_mb: float = Field(default=0.0, description="Total cache size in MB")
    used_size_mb: float = Field(default=0.0, description="Used cache size in MB")
    utilization_percent: float = Field(default=0.0, description="Cache utilization percentage")
    
    # Entry metrics
    total_entries: int = Field(default=0, description="Total cache entries")
    hot_entries: int = Field(default=0, description="Hot cache entries")
    cold_entries: int = Field(default=0, description="Cold cache entries")
    
    # Eviction metrics
    evictions: int = Field(default=0, description="Total evictions")
    eviction_rate: float = Field(default=0.0, description="Eviction rate per hour")
    
    # Timestamp
    timestamp: datetime = Field(default_factory=datetime.now, description="Metrics timestamp")


class CacheEntry(BaseModel):
    """Cache entry with optimization metadata."""
    
    key: str = Field(..., description="Cache key")
    value: Any = Field(..., description="Cached value")
    
    # Access patterns
    access_count: int = Field(default=1, description="Number of accesses")
    last_accessed: datetime = Field(default_factory=datetime.now, description="Last access time")
    created_at: datetime = Field(default_factory=datetime.now, description="Creation time")
    
    # Performance data
    hit_time_ms: float = Field(default=0.0, description="Average hit time")
    generation_time_ms: float = Field(default=0.0, description="Time to generate value")
    
    # Optimization hints
    priority: float = Field(default=1.0, description="Cache priority (0-1)")
    predicted_next_access: Optional[datetime] = Field(None, description="Predicted next access")
    access_pattern: str = Field(default="unknown", description="Access pattern type")
    
    # Size and TTL
    size_bytes: int = Field(default=0, description="Entry size in bytes")
    ttl_seconds: Optional[int] = Field(None, description="Time to live in seconds")
    expires_at: Optional[datetime] = Field(None, description="Expiration time")
    
    def is_expired(self) -> bool:
        """Check if entry is expired."""
        if self.expires_at is None:
            return False
        return datetime.now() > self.expires_at
    
    def is_hot(self, threshold_hours: int = 1) -> bool:
        """Check if entry is hot (recently accessed)."""
        return datetime.now() - self.last_accessed < timedelta(hours=threshold_hours)
    
    def calculate_priority(self) -> float:
        """Calculate dynamic priority based on access patterns."""
        # Recency factor
        hours_since_access = (datetime.now() - self.last_accessed).total_seconds() / 3600
        recency_factor = max(0, 1 - hours_since_access / 24)  # Decay over 24 hours
        
        # Frequency factor
        frequency_factor = min(1.0, self.access_count / 100)  # Normalize to 100 accesses
        
        # Performance factor
        performance_factor = max(0, 1 - self.generation_time_ms / 5000)  # Penalty for slow generation
        
        # Combined priority
        return (recency_factor * 0.4 + frequency_factor * 0.4 + performance_factor * 0.2)


class CacheOptimizer:
    """
    ⚡ Revolutionary Cache Optimizer
    
    Provides intelligent multi-level caching with adaptive optimization,
    predictive preloading, and performance-aware cache management.
    """
    
    def __init__(self, max_memory_mb: int = 512):
        self.max_memory_mb = max_memory_mb
        
        # Multi-level cache storage
        self.l1_cache: OrderedDict[str, CacheEntry] = OrderedDict()  # Memory cache
        self.l2_cache: Dict[str, CacheEntry] = {}  # Redis simulation
        self.l3_cache: Dict[str, CacheEntry] = {}  # Disk simulation
        
        # Cache configuration
        self.l1_max_size_mb = max_memory_mb * 0.6  # 60% for L1
        self.l2_max_size_mb = max_memory_mb * 2.0  # 2x for L2
        self.l3_max_size_mb = max_memory_mb * 10.0  # 10x for L3
        
        # Optimization strategies
        self.l1_strategy = CacheStrategy.ARC
        self.l2_strategy = CacheStrategy.LFU
        self.l3_strategy = CacheStrategy.LRU
        
        # Performance tracking
        self.metrics: Dict[CacheLevel, CacheMetrics] = {
            CacheLevel.L1_MEMORY: CacheMetrics(cache_level=CacheLevel.L1_MEMORY),
            CacheLevel.L2_REDIS: CacheMetrics(cache_level=CacheLevel.L2_REDIS),
            CacheLevel.L3_DISK: CacheMetrics(cache_level=CacheLevel.L3_DISK)
        }
        
        # Access pattern analysis
        self.access_patterns: Dict[str, List[datetime]] = defaultdict(list)
        self.hot_keys: Set[str] = set()
        self.prediction_models: Dict[str, Any] = {}
        
        # Optimization state
        self.optimization_active = False
        self.optimization_task: Optional[asyncio.Task] = None
        self.warming_queue: asyncio.Queue = asyncio.Queue()
        
        # Statistics
        self.optimization_stats = {
            "total_optimizations": 0,
            "cache_promotions": 0,
            "cache_demotions": 0,
            "preload_hits": 0,
            "optimization_time_saved_ms": 0.0
        }
    
    async def get(self, key: str, default: Any = None) -> Tuple[Any, CacheLevel]:
        """Get value from multi-level cache with optimization."""
        start_time = time.time()
        
        # Try L1 cache first
        if key in self.l1_cache:
            entry = self.l1_cache[key]
            if not entry.is_expired():
                await self._record_hit(CacheLevel.L1_MEMORY, start_time)
                await self._update_access_pattern(key, entry)
                self.l1_cache.move_to_end(key)  # LRU update
                return entry.value, CacheLevel.L1_MEMORY
            else:
                del self.l1_cache[key]
        
        # Try L2 cache
        if key in self.l2_cache:
            entry = self.l2_cache[key]
            if not entry.is_expired():
                await self._record_hit(CacheLevel.L2_REDIS, start_time)
                await self._update_access_pattern(key, entry)
                # Promote to L1 if hot
                if entry.is_hot():
                    await self._promote_to_l1(key, entry)
                return entry.value, CacheLevel.L2_REDIS
            else:
                del self.l2_cache[key]
        
        # Try L3 cache
        if key in self.l3_cache:
            entry = self.l3_cache[key]
            if not entry.is_expired():
                await self._record_hit(CacheLevel.L3_DISK, start_time)
                await self._update_access_pattern(key, entry)
                # Promote to L2 if frequently accessed
                if entry.access_count > 5:
                    await self._promote_to_l2(key, entry)
                return entry.value, CacheLevel.L3_DISK
            else:
                del self.l3_cache[key]
        
        # Cache miss
        await self._record_miss(start_time)
        return default, None
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl_seconds: Optional[int] = None,
        priority: float = 1.0,
        generation_time_ms: float = 0.0
    ) -> bool:
        """Set value in optimal cache level."""
        try:
            # Calculate entry size
            size_bytes = await self._calculate_size(value)
            
            # Create cache entry
            entry = CacheEntry(
                key=key,
                value=value,
                priority=priority,
                generation_time_ms=generation_time_ms,
                size_bytes=size_bytes,
                ttl_seconds=ttl_seconds,
                expires_at=datetime.now() + timedelta(seconds=ttl_seconds) if ttl_seconds else None
            )
            
            # Determine optimal cache level
            optimal_level = await self._determine_optimal_level(key, entry)
            
            # Store in optimal level
            if optimal_level == CacheLevel.L1_MEMORY:
                await self._set_l1(key, entry)
            elif optimal_level == CacheLevel.L2_REDIS:
                await self._set_l2(key, entry)
            else:
                await self._set_l3(key, entry)
            
            # Update access patterns
            await self._update_access_pattern(key, entry)
            
            # Schedule preloading if predictable
            await self._schedule_preloading(key, entry)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to set cache entry {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from all cache levels."""
        deleted = False
        
        if key in self.l1_cache:
            del self.l1_cache[key]
            deleted = True
        
        if key in self.l2_cache:
            del self.l2_cache[key]
            deleted = True
        
        if key in self.l3_cache:
            del self.l3_cache[key]
            deleted = True
        
        # Clean up access patterns
        if key in self.access_patterns:
            del self.access_patterns[key]
        
        self.hot_keys.discard(key)
        
        return deleted
    
    async def optimize_cache(self) -> Dict[str, Any]:
        """Perform comprehensive cache optimization."""
        optimization_start = time.time()
        
        optimization_results = {
            "l1_optimizations": await self._optimize_l1_cache(),
            "l2_optimizations": await self._optimize_l2_cache(),
            "l3_optimizations": await self._optimize_l3_cache(),
            "cross_level_optimizations": await self._optimize_cross_level(),
            "preloading_optimizations": await self._optimize_preloading(),
            "optimization_time_ms": (time.time() - optimization_start) * 1000
        }
        
        # Update statistics
        self.optimization_stats["total_optimizations"] += 1
        self.optimization_stats["optimization_time_saved_ms"] += optimization_results["optimization_time_ms"]
        
        return optimization_results
    
    async def start_optimization(self) -> None:
        """Start continuous cache optimization."""
        if self.optimization_active:
            return
        
        self.optimization_active = True
        self.optimization_task = asyncio.create_task(self._optimization_loop())
        logger.info("Cache optimization started")
    
    async def stop_optimization(self) -> None:
        """Stop cache optimization."""
        if not self.optimization_active:
            return
        
        self.optimization_active = False
        if self.optimization_task:
            self.optimization_task.cancel()
            try:
                await self.optimization_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Cache optimization stopped")
    
    async def get_cache_metrics(self) -> Dict[CacheLevel, CacheMetrics]:
        """Get comprehensive cache metrics."""
        # Update current metrics
        await self._update_metrics()
        return self.metrics.copy()
    
    async def warm_cache(self, keys: List[str], warm_function: callable) -> Dict[str, bool]:
        """Warm cache with specified keys."""
        results = {}
        
        for key in keys:
            try:
                # Check if already cached
                value, level = await self.get(key)
                if level is not None:
                    results[key] = True
                    continue
                
                # Generate value
                start_time = time.time()
                value = await warm_function(key)
                generation_time = (time.time() - start_time) * 1000
                
                # Cache the value
                success = await self.set(key, value, generation_time_ms=generation_time)
                results[key] = success
                
            except Exception as e:
                logger.error(f"Failed to warm cache for key {key}: {e}")
                results[key] = False
        
        return results
    
    async def predict_cache_needs(self, hours_ahead: int = 1) -> List[str]:
        """Predict cache needs for future time period."""
        predictions = []
        
        # Analyze access patterns
        for key, access_times in self.access_patterns.items():
            if len(access_times) < 3:
                continue
            
            # Simple pattern prediction
            intervals = []
            for i in range(1, len(access_times)):
                interval = (access_times[i] - access_times[i-1]).total_seconds()
                intervals.append(interval)
            
            if intervals:
                avg_interval = sum(intervals) / len(intervals)
                last_access = access_times[-1]
                predicted_next = last_access + timedelta(seconds=avg_interval)
                
                # If predicted access is within forecast window
                if predicted_next <= datetime.now() + timedelta(hours=hours_ahead):
                    predictions.append(key)
        
        return predictions
    
    # Internal optimization methods
    async def _determine_optimal_level(self, key: str, entry: CacheEntry) -> CacheLevel:
        """Determine optimal cache level for entry."""
        # High priority or frequently accessed -> L1
        if entry.priority > 0.8 or entry.access_count > 10:
            return CacheLevel.L1_MEMORY
        
        # Medium priority or moderate access -> L2
        if entry.priority > 0.5 or entry.access_count > 3:
            return CacheLevel.L2_REDIS
        
        # Default to L3
        return CacheLevel.L3_DISK
    
    async def _set_l1(self, key: str, entry: CacheEntry) -> None:
        """Set entry in L1 cache with eviction if needed."""
        # Check size limits
        current_size = await self._calculate_l1_size()
        if current_size + entry.size_bytes > self.l1_max_size_mb * 1024 * 1024:
            await self._evict_l1_entries(entry.size_bytes)
        
        self.l1_cache[key] = entry
    
    async def _set_l2(self, key: str, entry: CacheEntry) -> None:
        """Set entry in L2 cache."""
        self.l2_cache[key] = entry
    
    async def _set_l3(self, key: str, entry: CacheEntry) -> None:
        """Set entry in L3 cache."""
        self.l3_cache[key] = entry
    
    async def _evict_l1_entries(self, needed_bytes: int) -> None:
        """Evict L1 entries to make space."""
        evicted_bytes = 0
        
        # Use ARC strategy for L1
        if self.l1_strategy == CacheStrategy.ARC:
            # Evict least recently used with low priority
            keys_to_evict = []
            for key, entry in self.l1_cache.items():
                if not entry.is_hot() and entry.priority < 0.5:
                    keys_to_evict.append(key)
                    evicted_bytes += entry.size_bytes
                    if evicted_bytes >= needed_bytes:
                        break
            
            for key in keys_to_evict:
                entry = self.l1_cache[key]
                # Demote to L2
                await self._demote_to_l2(key, entry)
                del self.l1_cache[key]
                self.optimization_stats["cache_demotions"] += 1
    
    async def _promote_to_l1(self, key: str, entry: CacheEntry) -> None:
        """Promote entry from L2 to L1."""
        await self._set_l1(key, entry)
        if key in self.l2_cache:
            del self.l2_cache[key]
        self.optimization_stats["cache_promotions"] += 1
    
    async def _promote_to_l2(self, key: str, entry: CacheEntry) -> None:
        """Promote entry from L3 to L2."""
        await self._set_l2(key, entry)
        if key in self.l3_cache:
            del self.l3_cache[key]
        self.optimization_stats["cache_promotions"] += 1
    
    async def _demote_to_l2(self, key: str, entry: CacheEntry) -> None:
        """Demote entry from L1 to L2."""
        await self._set_l2(key, entry)
    
    async def _update_access_pattern(self, key: str, entry: CacheEntry) -> None:
        """Update access pattern for key."""
        entry.access_count += 1
        entry.last_accessed = datetime.now()
        
        # Update access history
        self.access_patterns[key].append(datetime.now())
        
        # Keep only recent access history
        cutoff = datetime.now() - timedelta(hours=24)
        self.access_patterns[key] = [
            access_time for access_time in self.access_patterns[key]
            if access_time > cutoff
        ]
        
        # Update hot keys
        if entry.is_hot():
            self.hot_keys.add(key)
        else:
            self.hot_keys.discard(key)
    
    async def _schedule_preloading(self, key: str, entry: CacheEntry) -> None:
        """Schedule predictive preloading."""
        # Analyze access pattern
        if len(self.access_patterns[key]) >= 3:
            # Predict next access time
            access_times = self.access_patterns[key]
            intervals = []
            for i in range(1, len(access_times)):
                interval = (access_times[i] - access_times[i-1]).total_seconds()
                intervals.append(interval)
            
            if intervals:
                avg_interval = sum(intervals) / len(intervals)
                predicted_next = datetime.now() + timedelta(seconds=avg_interval)
                entry.predicted_next_access = predicted_next
    
    async def _calculate_size(self, value: Any) -> int:
        """Calculate approximate size of value in bytes."""
        try:
            import sys
            return sys.getsizeof(value)
        except:
            # Fallback estimation
            if isinstance(value, str):
                return len(value.encode('utf-8'))
            elif isinstance(value, (int, float)):
                return 8
            elif isinstance(value, (list, tuple)):
                return sum(self._calculate_size(item) for item in value)
            elif isinstance(value, dict):
                return sum(self._calculate_size(k) + self._calculate_size(v) for k, v in value.items())
            else:
                return 1024  # Default 1KB
    
    async def _calculate_l1_size(self) -> int:
        """Calculate current L1 cache size in bytes."""
        return sum(entry.size_bytes for entry in self.l1_cache.values())
    
    async def _record_hit(self, level: CacheLevel, start_time: float) -> None:
        """Record cache hit metrics."""
        hit_time = (time.time() - start_time) * 1000
        metrics = self.metrics[level]
        
        metrics.total_requests += 1
        metrics.cache_hits += 1
        metrics.hit_rate = (metrics.cache_hits / metrics.total_requests) * 100
        
        # Update average hit time
        if metrics.cache_hits == 1:
            metrics.average_hit_time_ms = hit_time
        else:
            metrics.average_hit_time_ms = (
                (metrics.average_hit_time_ms * (metrics.cache_hits - 1) + hit_time) / metrics.cache_hits
            )
    
    async def _record_miss(self, start_time: float) -> None:
        """Record cache miss metrics."""
        miss_time = (time.time() - start_time) * 1000
        
        for metrics in self.metrics.values():
            metrics.total_requests += 1
            metrics.cache_misses += 1
            metrics.hit_rate = (metrics.cache_hits / metrics.total_requests) * 100 if metrics.total_requests > 0 else 0
            
            # Update average miss time
            if metrics.cache_misses == 1:
                metrics.average_miss_time_ms = miss_time
            else:
                metrics.average_miss_time_ms = (
                    (metrics.average_miss_time_ms * (metrics.cache_misses - 1) + miss_time) / metrics.cache_misses
                )
    
    async def _update_metrics(self) -> None:
        """Update cache metrics."""
        # L1 metrics
        l1_metrics = self.metrics[CacheLevel.L1_MEMORY]
        l1_metrics.total_entries = len(self.l1_cache)
        l1_metrics.hot_entries = len([e for e in self.l1_cache.values() if e.is_hot()])
        l1_metrics.cold_entries = l1_metrics.total_entries - l1_metrics.hot_entries
        l1_metrics.used_size_mb = await self._calculate_l1_size() / (1024 * 1024)
        l1_metrics.total_size_mb = self.l1_max_size_mb
        l1_metrics.utilization_percent = (l1_metrics.used_size_mb / l1_metrics.total_size_mb) * 100
        
        # L2 metrics
        l2_metrics = self.metrics[CacheLevel.L2_REDIS]
        l2_metrics.total_entries = len(self.l2_cache)
        l2_metrics.hot_entries = len([e for e in self.l2_cache.values() if e.is_hot()])
        l2_metrics.cold_entries = l2_metrics.total_entries - l2_metrics.hot_entries
        
        # L3 metrics
        l3_metrics = self.metrics[CacheLevel.L3_DISK]
        l3_metrics.total_entries = len(self.l3_cache)
        l3_metrics.hot_entries = len([e for e in self.l3_cache.values() if e.is_hot()])
        l3_metrics.cold_entries = l3_metrics.total_entries - l3_metrics.hot_entries
    
    async def _optimization_loop(self) -> None:
        """Continuous optimization loop."""
        while self.optimization_active:
            try:
                await self.optimize_cache()
                await asyncio.sleep(300)  # Optimize every 5 minutes
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cache optimization loop: {e}")
                await asyncio.sleep(60)
    
    async def _optimize_l1_cache(self) -> Dict[str, Any]:
        """Optimize L1 cache."""
        return {"optimizations_applied": ["priority_reordering", "hot_key_promotion"]}
    
    async def _optimize_l2_cache(self) -> Dict[str, Any]:
        """Optimize L2 cache."""
        return {"optimizations_applied": ["frequency_analysis", "cold_key_eviction"]}
    
    async def _optimize_l3_cache(self) -> Dict[str, Any]:
        """Optimize L3 cache."""
        return {"optimizations_applied": ["expired_entry_cleanup", "size_optimization"]}
    
    async def _optimize_cross_level(self) -> Dict[str, Any]:
        """Optimize across cache levels."""
        return {"optimizations_applied": ["intelligent_promotion", "predictive_demotion"]}
    
    async def _optimize_preloading(self) -> Dict[str, Any]:
        """Optimize cache preloading."""
        return {"optimizations_applied": ["pattern_prediction", "proactive_warming"]}
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """Get cache optimization statistics."""
        return self.optimization_stats.copy()


# Global cache optimizer instance
_cache_optimizer: Optional[CacheOptimizer] = None


def get_cache_optimizer() -> CacheOptimizer:
    """Get the global cache optimizer instance."""
    global _cache_optimizer
    if _cache_optimizer is None:
        _cache_optimizer = CacheOptimizer()
    return _cache_optimizer
