#!/usr/bin/env python3
"""
Project Management System Test

Tests the complete project isolation system including:
- Project creation and management
- Project-scoped workspaces
- Project-specific embeddings collections
- Project-aware file operations
- Project API endpoints
"""

import asyncio
import json
import shutil
import tempfile
from pathlib import Path
from typing import Dict, Any

try:
    import httpx
except ImportError:
    print("❌ httpx not available. Install with: pip install httpx")
    exit(1)

try:
    from qdrant_client import QdrantClient
except ImportError:
    print("❌ qdrant-client not available. Install with: pip install qdrant-client")
    QdrantClient = None

# Test configuration
TEST_CONFIG = {
    "base_url": "http://localhost:8000",
    "test_projects": [
        {
            "name": "Test Python Project",
            "slug": "test-python-project",
            "description": "A test Python project for validation",
            "language": "python",
            "framework": "fastapi"
        },
        {
            "name": "Test JavaScript Project", 
            "slug": "test-js-project",
            "description": "A test JavaScript project for validation",
            "language": "javascript",
            "framework": "react"
        }
    ]
}


class ProjectManagementTester:
    """Comprehensive project management system tester."""
    
    def __init__(self):
        self.base_url = TEST_CONFIG["base_url"]
        self.test_projects = TEST_CONFIG["test_projects"]
        self.created_projects = []
        self.test_results = {
            "project_creation": [],
            "project_isolation": [],
            "file_operations": [],
            "embeddings": [],
            "api_endpoints": []
        }
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all project management tests."""
        print("🏗️ Starting Project Management System Tests...")
        
        try:
            # Test 1: Project Creation
            await self.test_project_creation()
            
            # Test 2: Project Isolation
            await self.test_project_isolation()
            
            # Test 3: File Operations
            await self.test_project_file_operations()
            
            # Test 4: Embeddings Isolation
            await self.test_embeddings_isolation()
            
            # Test 5: API Endpoints
            await self.test_api_endpoints()
            
            # Generate summary
            return self.generate_test_summary()
            
        except Exception as e:
            print(f"❌ Test suite failed: {e}")
            return {"status": "failed", "error": str(e)}
        finally:
            # Cleanup
            await self.cleanup_test_projects()
    
    async def test_project_creation(self):
        """Test project creation functionality."""
        print("\n📋 Testing Project Creation...")
        
        async with httpx.AsyncClient() as client:
            for project_data in self.test_projects:
                try:
                    # Create project via API
                    response = await client.post(
                        f"{self.base_url}/api/projects/create",
                        json=project_data,
                        timeout=30.0
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        if result["status"] == "success":
                            self.created_projects.append(project_data["slug"])
                            self.test_results["project_creation"].append({
                                "project": project_data["slug"],
                                "status": "success",
                                "project_id": result["project"]["project_id"]
                            })
                            print(f"  ✅ Created project: {project_data['name']}")
                        else:
                            self.test_results["project_creation"].append({
                                "project": project_data["slug"],
                                "status": "failed",
                                "error": result.get("error", "Unknown error")
                            })
                            print(f"  ❌ Failed to create project: {project_data['name']}")
                    else:
                        self.test_results["project_creation"].append({
                            "project": project_data["slug"],
                            "status": "failed",
                            "error": f"HTTP {response.status_code}"
                        })
                        print(f"  ❌ HTTP error creating project: {project_data['name']}")
                        
                except Exception as e:
                    self.test_results["project_creation"].append({
                        "project": project_data["slug"],
                        "status": "failed",
                        "error": str(e)
                    })
                    print(f"  ❌ Exception creating project {project_data['name']}: {e}")
    
    async def test_project_isolation(self):
        """Test that projects are properly isolated."""
        print("\n🔒 Testing Project Isolation...")
        
        async with httpx.AsyncClient() as client:
            for project_slug in self.created_projects:
                try:
                    # Get project workspace info
                    response = await client.get(
                        f"{self.base_url}/api/projects/{project_slug}/workspace/info",
                        timeout=30.0
                    )
                    
                    if response.status_code == 200:
                        workspace_info = response.json()
                        
                        # Check workspace isolation
                        workspace_path = workspace_info["workspace_path"]
                        embeddings_collection = workspace_info["embeddings_collection"]
                        
                        isolation_check = {
                            "project": project_slug,
                            "workspace_isolated": project_slug in workspace_path,
                            "embeddings_isolated": project_slug in embeddings_collection,
                            "workspace_exists": workspace_info["workspace_exists"],
                            "config_exists": workspace_info["config_exists"]
                        }
                        
                        self.test_results["project_isolation"].append(isolation_check)
                        
                        if all([
                            isolation_check["workspace_isolated"],
                            isolation_check["embeddings_isolated"],
                            isolation_check["workspace_exists"]
                        ]):
                            print(f"  ✅ Project isolation verified: {project_slug}")
                        else:
                            print(f"  ❌ Project isolation failed: {project_slug}")
                    else:
                        print(f"  ❌ Failed to get workspace info for: {project_slug}")
                        
                except Exception as e:
                    print(f"  ❌ Exception testing isolation for {project_slug}: {e}")
    
    async def test_project_file_operations(self):
        """Test project-scoped file operations."""
        print("\n📁 Testing Project File Operations...")
        
        async with httpx.AsyncClient() as client:
            for project_slug in self.created_projects:
                try:
                    # Activate project
                    await client.post(f"{self.base_url}/api/projects/{project_slug}/activate")
                    
                    # Test file operations would go here
                    # For now, just verify project activation
                    response = await client.get(f"{self.base_url}/api/projects/current/status")
                    
                    if response.status_code == 200:
                        status = response.json()
                        if status["status"] == "active_project" and status["project"]["slug"] == project_slug:
                            self.test_results["file_operations"].append({
                                "project": project_slug,
                                "activation": "success"
                            })
                            print(f"  ✅ Project activation verified: {project_slug}")
                        else:
                            print(f"  ❌ Project activation failed: {project_slug}")
                    
                except Exception as e:
                    print(f"  ❌ Exception testing file operations for {project_slug}: {e}")
    
    async def test_embeddings_isolation(self):
        """Test that embeddings are isolated per project."""
        print("\n🔍 Testing Embeddings Isolation...")

        if QdrantClient is None:
            print("  ⚠️ Qdrant client not available, skipping embeddings test")
            return

        try:
            # Connect to Qdrant to verify collections
            qdrant_client = QdrantClient(url="http://localhost:6333")
            collections = qdrant_client.get_collections()
            collection_names = [col.name for col in collections.collections]

            for project_slug in self.created_projects:
                expected_collection = f"project_{project_slug}_embeddings"
                collection_exists = expected_collection in collection_names

                self.test_results["embeddings"].append({
                    "project": project_slug,
                    "collection_name": expected_collection,
                    "collection_exists": collection_exists
                })

                if collection_exists:
                    print(f"  ✅ Embeddings collection verified: {expected_collection}")
                else:
                    print(f"  ❌ Embeddings collection missing: {expected_collection}")

        except Exception as e:
            print(f"  ❌ Exception testing embeddings isolation: {e}")
            # Add error to results for non-critical failure
            for project_slug in self.created_projects:
                self.test_results["embeddings"].append({
                    "project": project_slug,
                    "collection_name": f"project_{project_slug}_embeddings",
                    "collection_exists": False,
                    "error": str(e)
                })
    
    async def test_api_endpoints(self):
        """Test all project API endpoints."""
        print("\n🌐 Testing API Endpoints...")
        
        async with httpx.AsyncClient() as client:
            endpoints_to_test = [
                ("GET", "/api/projects/list", "list_projects"),
                ("GET", "/api/projects/health", "system_health"),
                ("GET", "/api/projects/current/status", "current_status")
            ]
            
            for method, endpoint, _ in endpoints_to_test:
                try:
                    if method == "GET":
                        response = await client.get(f"{self.base_url}{endpoint}")
                    
                    success = response.status_code == 200
                    self.test_results["api_endpoints"].append({
                        "endpoint": endpoint,
                        "method": method,
                        "status_code": response.status_code,
                        "success": success
                    })
                    
                    if success:
                        print(f"  ✅ API endpoint working: {method} {endpoint}")
                    else:
                        print(f"  ❌ API endpoint failed: {method} {endpoint} ({response.status_code})")
                        
                except Exception as e:
                    print(f"  ❌ Exception testing {endpoint}: {e}")
    
    async def cleanup_test_projects(self):
        """Clean up test projects."""
        print("\n🧹 Cleaning up test projects...")
        
        async with httpx.AsyncClient() as client:
            for project_slug in self.created_projects:
                try:
                    # Archive project (safer than delete)
                    response = await client.post(f"{self.base_url}/api/projects/{project_slug}/archive")
                    if response.status_code == 200:
                        print(f"  ✅ Archived test project: {project_slug}")
                    else:
                        print(f"  ❌ Failed to archive test project: {project_slug}")
                except Exception as e:
                    print(f"  ❌ Exception archiving {project_slug}: {e}")
    
    def generate_test_summary(self) -> Dict[str, Any]:
        """Generate comprehensive test summary."""
        summary = {
            "status": "completed",
            "timestamp": asyncio.get_event_loop().time(),
            "test_categories": {},
            "overall_success": True
        }
        
        for category, results in self.test_results.items():
            if results:
                successful = sum(1 for r in results if r.get("status") == "success" or r.get("success", False))
                total = len(results)
                success_rate = (successful / total) * 100 if total > 0 else 0
                
                summary["test_categories"][category] = {
                    "total_tests": total,
                    "successful": successful,
                    "failed": total - successful,
                    "success_rate": success_rate,
                    "details": results
                }
                
                if success_rate < 100:
                    summary["overall_success"] = False
        
        return summary


async def main():
    """Main test execution function."""
    print("🚀 Project Management System Test Suite")
    print("=" * 50)
    
    tester = ProjectManagementTester()
    results = await tester.run_all_tests()
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    if results["status"] == "completed":
        print(f"Overall Success: {'✅ PASS' if results['overall_success'] else '❌ FAIL'}")
        print()
        
        for category, stats in results["test_categories"].items():
            print(f"{category.replace('_', ' ').title()}:")
            print(f"  Total: {stats['total_tests']}")
            print(f"  Successful: {stats['successful']}")
            print(f"  Failed: {stats['failed']}")
            print(f"  Success Rate: {stats['success_rate']:.1f}%")
            print()
    else:
        print(f"❌ Test suite failed: {results.get('error', 'Unknown error')}")
    
    # Save detailed results
    results_file = Path("project_management_test_results.json")
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"📄 Detailed results saved to: {results_file}")
    
    return results["overall_success"] if results["status"] == "completed" else False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
