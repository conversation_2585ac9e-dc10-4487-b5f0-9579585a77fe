#!/usr/bin/env python3
"""
Test Multimodal Integration (Phase 7C.2)

This script tests the vision analysis capabilities and multimodal integration
with the Pydantic AI system.
"""

import asyncio
import logging
import sys
import os
import tempfile
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.pydantic_ai.agents import vision_agent
from app.pydantic_ai.dependencies import create_vision_dependencies
from app.pydantic_ai.tools import get_vision_tools, get_tool_summary

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def create_test_images():
    """Create test images for vision analysis."""
    print("🖼️  Creating test images...")
    
    # Create temporary directory
    temp_dir = Path(tempfile.mkdtemp(prefix="deepnexus_vision_"))
    print(f"📁 Test images directory: {temp_dir}")
    
    # Create a simple UI mockup image
    ui_image = Image.new('RGB', (800, 600), color='white')
    draw = ImageDraw.Draw(ui_image)
    
    # Draw a simple UI layout
    # Header
    draw.rectangle([0, 0, 800, 80], fill='#2196F3')
    draw.text((20, 30), "DeepNexus AI Dashboard", fill='white')
    
    # Navigation
    draw.rectangle([0, 80, 200, 600], fill='#f5f5f5')
    draw.text((20, 100), "Navigation", fill='black')
    draw.text((20, 130), "• Dashboard", fill='black')
    draw.text((20, 150), "• Projects", fill='black')
    draw.text((20, 170), "• Settings", fill='black')
    
    # Main content
    draw.rectangle([220, 100, 780, 300], fill='#ffffff', outline='#ddd')
    draw.text((240, 120), "Main Content Area", fill='black')
    draw.text((240, 150), "This is a sample UI for testing", fill='gray')
    
    # Button
    draw.rectangle([240, 200, 340, 240], fill='#4CAF50')
    draw.text((260, 215), "Click Me", fill='white')
    
    # Save UI image
    ui_image_path = temp_dir / "ui_mockup.png"
    ui_image.save(ui_image_path)
    
    # Create a second image with a "bug" (overlapping elements)
    bug_image = ui_image.copy()
    bug_draw = ImageDraw.Draw(bug_image)
    
    # Add overlapping red box (simulating a bug)
    bug_draw.rectangle([300, 180, 500, 280], fill='red', outline='darkred')
    bug_draw.text((320, 220), "ERROR OVERLAY", fill='white')
    
    bug_image_path = temp_dir / "ui_with_bug.png"
    bug_image.save(bug_image_path)
    
    # Create a simple component image (just a button)
    component_image = Image.new('RGB', (200, 100), color='white')
    comp_draw = ImageDraw.Draw(component_image)
    comp_draw.rectangle([20, 20, 180, 80], fill='#2196F3', outline='#1976D2')
    comp_draw.text((60, 45), "Submit", fill='white')
    
    component_image_path = temp_dir / "button_component.png"
    component_image.save(component_image_path)
    
    return {
        "temp_dir": temp_dir,
        "ui_mockup": str(ui_image_path),
        "ui_with_bug": str(bug_image_path),
        "button_component": str(component_image_path)
    }


async def test_vision_tools_registry():
    """Test that vision tools are properly registered."""
    print("\n🧪 Testing Vision Tools Registry...")
    
    try:
        # Get tool summary
        summary = get_tool_summary()
        
        print(f"📊 Tool Summary: {summary}")
        
        # Check vision tools
        vision_tools = get_vision_tools()
        vision_tool_names = [tool.function.__name__ for tool in vision_tools]
        
        expected_vision_tools = [
            'analyze_screenshot',
            'analyze_ui_component',
            'compare_screenshots',
            'generate_visual_report'
        ]
        
        print(f"🔧 Vision tools found: {vision_tool_names}")
        
        missing_tools = [tool for tool in expected_vision_tools if tool not in vision_tool_names]
        if missing_tools:
            print(f"❌ Missing vision tools: {missing_tools}")
            return False
        
        print(f"✅ All {len(expected_vision_tools)} vision tools registered successfully")
        return True
        
    except Exception as e:
        print(f"❌ Vision tools registry error: {e}")
        return False


async def test_screenshot_analysis(test_images):
    """Test screenshot analysis functionality."""
    print("\n🧪 Testing Screenshot Analysis...")
    
    try:
        # Create vision dependencies
        deps = await create_vision_dependencies(workspace_root=str(test_images["temp_dir"]))
        
        # Test general analysis
        result = await vision_agent.run(
            f"Please analyze this screenshot for overall UI/UX quality: {test_images['ui_mockup']}",
            deps=deps
        )
        print(f"✅ General analysis: {result.output[:200]}...")
        
        # Test bug detection
        result = await vision_agent.run(
            f"Please analyze this screenshot for visual bugs and issues: {test_images['ui_with_bug']}",
            deps=deps
        )
        print(f"✅ Bug detection: {result.output[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Screenshot analysis error: {e}")
        return False


async def test_ui_component_analysis(test_images):
    """Test UI component analysis."""
    print("\n🧪 Testing UI Component Analysis...")
    
    try:
        # Create vision dependencies
        deps = await create_vision_dependencies(workspace_root=str(test_images["temp_dir"]))
        
        # Test button component analysis
        result = await vision_agent.run(
            f"Please analyze this button component for design and usability: {test_images['button_component']}",
            deps=deps
        )
        print(f"✅ Component analysis: {result.output[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ UI component analysis error: {e}")
        return False


async def test_screenshot_comparison(test_images):
    """Test screenshot comparison functionality."""
    print("\n🧪 Testing Screenshot Comparison...")
    
    try:
        # Create vision dependencies
        deps = await create_vision_dependencies(workspace_root=str(test_images["temp_dir"]))
        
        # Test comparison between normal and buggy UI
        result = await vision_agent.run(
            f"Please compare these two screenshots and identify the differences: before={test_images['ui_mockup']}, after={test_images['ui_with_bug']}",
            deps=deps
        )
        print(f"✅ Screenshot comparison: {result.output[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Screenshot comparison error: {e}")
        return False


async def test_multimodal_workflow():
    """Test a complete multimodal workflow."""
    print("\n🧪 Testing Complete Multimodal Workflow...")
    
    try:
        test_images = await create_test_images()
        
        # Create vision dependencies
        deps = await create_vision_dependencies(workspace_root=str(test_images["temp_dir"]))
        
        # Complex multimodal workflow
        result = await vision_agent.run(
            f"""Please perform a comprehensive visual analysis workflow:
            
            1. Analyze the UI mockup: {test_images['ui_mockup']}
            2. Analyze the buggy version: {test_images['ui_with_bug']}
            3. Compare the two images to identify issues
            4. Analyze the button component: {test_images['button_component']}
            5. Generate a summary report with recommendations
            
            Use multiple vision analysis tools as needed.""",
            deps=deps
        )
        
        print(f"✅ Multimodal workflow: {result.output[:300]}...")
        
        # Cleanup
        import shutil
        shutil.rmtree(test_images["temp_dir"])
        
        return True
        
    except Exception as e:
        print(f"❌ Multimodal workflow error: {e}")
        return False


async def test_vision_agent_capabilities():
    """Test the vision agent's capabilities without tools."""
    print("\n🧪 Testing Vision Agent Capabilities...")
    
    try:
        from app.pydantic_ai.agents import simple_vision_agent
        
        # Test basic vision understanding
        result = await simple_vision_agent.run(
            "Explain what computer vision and UI analysis can help with in software development."
        )
        print(f"✅ Vision capabilities: {result.output[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Vision agent capabilities error: {e}")
        return False


async def main():
    """Run all multimodal integration tests."""
    print("🚀 Testing Multimodal Integration (Phase 7C.2)")
    print("=" * 70)
    
    # Create test images first
    test_images = await create_test_images()
    
    tests = [
        ("Vision Tools Registry", test_vision_tools_registry),
        ("Screenshot Analysis", lambda: test_screenshot_analysis(test_images)),
        ("UI Component Analysis", lambda: test_ui_component_analysis(test_images)),
        ("Screenshot Comparison", lambda: test_screenshot_comparison(test_images)),
        ("Vision Agent Capabilities", test_vision_agent_capabilities),
        ("Complete Multimodal Workflow", test_multimodal_workflow),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            success = await test_func()
            results.append((test_name, success))
            status = "✅ PASSED" if success else "❌ FAILED"
            print(f"{status}: {test_name}")
        except Exception as e:
            print(f"❌ FAILED: {test_name} - {e}")
            results.append((test_name, False))
    
    # Cleanup test images
    try:
        import shutil
        shutil.rmtree(test_images["temp_dir"])
        print(f"🧹 Cleaned up test images: {test_images['temp_dir']}")
    except:
        pass
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 Multimodal Integration Test Results:")
    print("=" * 70)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status}: {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed >= total - 1:  # Allow 1 failure for network issues
        print("🎉 Multimodal integration is working excellently!")
        print("\n🖼️  Phase 7C.2 Features Active:")
        print("  ✅ Vision analysis tools (4 tools)")
        print("  ✅ Screenshot analysis")
        print("  ✅ UI component analysis")
        print("  ✅ Screenshot comparison")
        print("  ✅ Visual report generation")
        print("  ✅ Multimodal workflows")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
