"""
AI Planning Tools - Phase 8D

Pydantic AI tools for the AI-powered planning system.
Allows agents to create, validate, and manage development plans.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field

from ...ai_planning.core import planning_engine
from ...ai_planning.models import PlanningRequest, TaskComplexity, TaskPriority

logger = logging.getLogger(__name__)


class CreatePlanRequest(BaseModel):
    """Request model for creating a development plan."""
    title: str = Field(..., description="Plan title")
    description: str = Field(..., description="Detailed plan description")
    max_timeline_days: Optional[int] = Field(None, description="Maximum timeline in days")
    required_skills: List[str] = Field(default_factory=list, description="Required skills")
    preferred_complexity: Optional[str] = Field(None, description="Preferred complexity level")
    include_risk_assessment: bool = Field(True, description="Include risk assessment")
    include_resource_estimation: bool = Field(True, description="Include resource estimation")


class PlanUpdateRequest(BaseModel):
    """Request model for updating a plan."""
    plan_id: str = Field(..., description="Plan ID to update")
    updates: Dict[str, Any] = Field(..., description="Updates to apply")


class PlanValidationRequest(BaseModel):
    """Request model for plan validation."""
    plan_id: str = Field(..., description="Plan ID to validate")


class PlanningStatusResponse(BaseModel):
    """Response model for planning status."""
    enabled: bool
    total_sessions: int
    total_plans: int
    agents_available: bool


async def create_development_plan(request: CreatePlanRequest) -> Dict[str, Any]:
    """
    Create a comprehensive development plan using AI.
    
    This tool uses a multi-AI approach to create detailed development plans
    with tasks, timelines, risks, and resource estimates.
    """
    try:
        logger.info(f"🎯 Creating development plan: {request.title}")
        
        # Convert to internal request format
        planning_request = PlanningRequest(
            title=request.title,
            description=request.description,
            max_timeline_days=request.max_timeline_days,
            required_skills=request.required_skills,
            preferred_complexity=TaskComplexity(request.preferred_complexity) if request.preferred_complexity else None,
            include_risk_assessment=request.include_risk_assessment,
            include_resource_estimation=request.include_resource_estimation
        )
        
        # Create the plan
        response = await planning_engine.create_plan(planning_request)
        
        return {
            "success": True,
            "plan_id": response.plan.plan_id,
            "title": response.plan.title,
            "total_tasks": len(response.plan.tasks),
            "estimated_hours": response.plan.estimated_total_hours,
            "confidence_score": response.confidence_score,
            "validation_status": response.plan.validation_status,
            "planning_duration": response.planning_duration_seconds,
            "recommendations": response.recommendations[:3],  # Top 3 recommendations
            "message": f"Successfully created development plan with {len(response.plan.tasks)} tasks"
        }
        
    except Exception as e:
        logger.error(f"❌ Plan creation failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to create development plan"
        }


async def validate_development_plan(request: PlanValidationRequest) -> Dict[str, Any]:
    """
    Validate an existing development plan using AI analysis.
    
    This tool uses a secondary AI agent to analyze and provide feedback
    on an existing development plan.
    """
    try:
        logger.info(f"🔍 Validating plan: {request.plan_id}")
        
        # Find the plan
        sessions = await planning_engine.list_sessions()
        plan = None
        for session in sessions:
            for p in session.plans:
                if p.plan_id == request.plan_id:
                    plan = p
                    break
            if plan:
                break
        
        if not plan:
            return {
                "success": False,
                "error": "Plan not found",
                "message": f"No plan found with ID: {request.plan_id}"
            }
        
        # Validate the plan
        validation = await planning_engine.validation_agent.validate_plan(plan)
        plan.validations.append(validation)
        
        return {
            "success": True,
            "validation_id": validation.validation_id,
            "overall_score": validation.overall_score,
            "feasibility_score": validation.feasibility_score,
            "completeness_score": validation.completeness_score,
            "strengths": validation.strengths[:3],  # Top 3 strengths
            "improvements": validation.improvements[:3],  # Top 3 improvements
            "timeline_realistic": validation.timeline_realistic,
            "risk_count": len(validation.identified_risks),
            "message": f"Plan validation completed with overall score: {validation.overall_score}/10"
        }
        
    except Exception as e:
        logger.error(f"❌ Plan validation failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to validate development plan"
        }


async def get_planning_status() -> Dict[str, Any]:
    """
    Get the current status of the AI planning system.
    
    Returns information about whether planning is enabled, statistics,
    and system health.
    """
    try:
        logger.info("📊 Getting planning system status")
        
        stats = await planning_engine.get_planning_stats()
        
        return {
            "success": True,
            "enabled": stats["planning_enabled"],
            "total_sessions": stats["total_sessions"],
            "total_plans": stats["total_plans"],
            "total_requests": stats["total_requests"],
            "average_scores": stats["average_scores"],
            "primary_planner": stats["primary_planner"],
            "validation_agent": stats["validation_agent"],
            "agents_available": True,
            "message": f"Planning system is {'enabled' if stats['planning_enabled'] else 'disabled'}"
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get planning status: {e}")
        return {
            "success": False,
            "error": str(e),
            "enabled": False,
            "agents_available": False,
            "message": "Failed to get planning system status"
        }


async def toggle_ai_planning(enabled: bool) -> Dict[str, Any]:
    """
    Toggle the AI planning system on or off.
    
    This tool allows agents to enable or disable the AI planning feature.
    When disabled, all planning requests will be rejected.
    """
    try:
        logger.info(f"🎯 Toggling AI planning: {'enabled' if enabled else 'disabled'}")
        
        planning_engine.toggle_planning(enabled)
        
        return {
            "success": True,
            "enabled": enabled,
            "message": f"AI Planning {'enabled' if enabled else 'disabled'} successfully"
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to toggle planning: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to toggle AI planning"
        }


async def list_development_plans(user_id: Optional[str] = None) -> Dict[str, Any]:
    """
    List all development plans, optionally filtered by user.
    
    Returns a summary of all plans in the system with key information.
    """
    try:
        logger.info(f"📋 Listing development plans for user: {user_id or 'all'}")
        
        sessions = await planning_engine.list_sessions(user_id)
        
        plans_summary = []
        for session in sessions:
            for plan in session.plans:
                plans_summary.append({
                    "plan_id": plan.plan_id,
                    "title": plan.title,
                    "status": plan.execution_status,
                    "progress": plan.progress_percentage,
                    "total_tasks": len(plan.tasks),
                    "estimated_hours": plan.estimated_total_hours,
                    "created_at": plan.created_at.isoformat(),
                    "validation_status": plan.validation_status,
                    "priority": plan.priority
                })
        
        return {
            "success": True,
            "total_plans": len(plans_summary),
            "plans": plans_summary[:10],  # Limit to 10 for tool response
            "message": f"Found {len(plans_summary)} development plans"
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to list plans: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to list development plans"
        }


async def get_plan_details(plan_id: str) -> Dict[str, Any]:
    """
    Get detailed information about a specific development plan.
    
    Returns comprehensive plan information including tasks, risks, and validation.
    """
    try:
        logger.info(f"📋 Getting plan details: {plan_id}")
        
        # Find the plan
        sessions = await planning_engine.list_sessions()
        plan = None
        for session in sessions:
            for p in session.plans:
                if p.plan_id == plan_id:
                    plan = p
                    break
            if plan:
                break
        
        if not plan:
            return {
                "success": False,
                "error": "Plan not found",
                "message": f"No plan found with ID: {plan_id}"
            }
        
        # Get task summary
        tasks_summary = []
        for task in plan.tasks[:5]:  # Limit to 5 tasks for tool response
            tasks_summary.append({
                "task_id": task.task_id,
                "title": task.title,
                "status": task.status,
                "priority": task.priority,
                "complexity": task.complexity,
                "estimated_hours": task.estimated_hours,
                "dependencies": len(task.dependencies)
            })
        
        # Get risk summary
        risks_summary = []
        for risk in plan.risks[:3]:  # Limit to 3 risks
            risks_summary.append({
                "title": risk.title,
                "level": risk.level,
                "probability": risk.probability,
                "impact": risk.impact,
                "category": risk.category
            })
        
        # Get latest validation
        latest_validation = None
        if plan.validations:
            val = plan.validations[-1]
            latest_validation = {
                "overall_score": val.overall_score,
                "feasibility_score": val.feasibility_score,
                "completeness_score": val.completeness_score,
                "timeline_realistic": val.timeline_realistic
            }
        
        return {
            "success": True,
            "plan_id": plan.plan_id,
            "title": plan.title,
            "description": plan.description,
            "status": plan.execution_status,
            "progress": plan.progress_percentage,
            "total_tasks": len(plan.tasks),
            "completed_tasks": len(plan.get_completed_tasks()),
            "estimated_hours": plan.estimated_total_hours,
            "objectives": plan.objectives[:3],  # Top 3 objectives
            "tasks": tasks_summary,
            "risks": risks_summary,
            "validation": latest_validation,
            "created_at": plan.created_at.isoformat(),
            "message": f"Retrieved details for plan: {plan.title}"
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get plan details: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to get plan details"
        }


async def create_feature_plan(
    feature_name: str,
    description: str,
    complexity: str = "moderate",
    timeline_days: Optional[int] = None
) -> Dict[str, Any]:
    """
    Create a development plan specifically for implementing a new feature.
    
    This is a specialized planning tool for feature development with
    predefined templates and best practices.
    """
    try:
        logger.info(f"🚀 Creating feature plan: {feature_name}")
        
        # Create feature-specific planning request
        request = CreatePlanRequest(
            title=f"Implement {feature_name}",
            description=f"Feature: {feature_name}\n\nDescription:\n{description}",
            max_timeline_days=timeline_days,
            preferred_complexity=complexity,
            required_skills=["programming", "testing", "documentation", "ui-design"],
            include_risk_assessment=True,
            include_resource_estimation=True
        )
        
        # Use the main planning function
        result = await create_development_plan(request)
        
        if result["success"]:
            result["message"] = f"Successfully created feature plan for '{feature_name}'"
            result["feature_name"] = feature_name
        
        return result
        
    except Exception as e:
        logger.error(f"❌ Feature plan creation failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": f"Failed to create feature plan for '{feature_name}'"
        }


# Tool registry for Pydantic AI
PLANNING_TOOLS = {
    "create_development_plan": create_development_plan,
    "validate_development_plan": validate_development_plan,
    "get_planning_status": get_planning_status,
    "toggle_ai_planning": toggle_ai_planning,
    "list_development_plans": list_development_plans,
    "get_plan_details": get_plan_details,
    "create_feature_plan": create_feature_plan
}
