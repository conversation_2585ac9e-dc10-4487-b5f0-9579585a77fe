import React, { useState, useEffect } from 'react';

interface ServiceHealth {
  service_name: string;
  status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
  response_time: number;
  uptime: number;
  last_check: Date;
  error_count: number;
  memory_usage: number;
  cpu_usage: number;
  dependencies: string[];
}

interface APIEndpoint {
  endpoint: string;
  method: string;
  avg_response_time: number;
  success_rate: number;
  requests_per_minute: number;
  error_rate: number;
  last_error?: string;
  status_code_distribution: { [key: string]: number };
}

interface SystemAlert {
  id: string;
  type: 'error' | 'warning' | 'info' | 'critical';
  title: string;
  description: string;
  service: string;
  timestamp: Date;
  resolved: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface AgentActivity {
  agent_id: string;
  agent_type: string;
  status: 'active' | 'idle' | 'busy' | 'error';
  current_task?: string;
  tasks_completed: number;
  avg_task_duration: number;
  last_activity: Date;
  success_rate: number;
}

export const SystemMonitoring: React.FC = () => {
  const [services, setServices] = useState<ServiceHealth[]>([]);
  const [endpoints, setEndpoints] = useState<APIEndpoint[]>([]);
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);
  const [agents, setAgents] = useState<AgentActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [selectedService, setSelectedService] = useState<string | null>(null);

  useEffect(() => {
    loadMonitoringData();
    if (autoRefresh) {
      const interval = setInterval(loadMonitoringData, 15000); // Refresh every 15 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const loadMonitoringData = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        loadServiceHealth(),
        loadAPIEndpoints(),
        loadSystemAlerts(),
        loadAgentActivity()
      ]);
    } catch (error) {
      console.error('Failed to load monitoring data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadServiceHealth = async () => {
    try {
      const response = await fetch('/api/monitoring/health');
      if (response.ok) {
        const data = await response.json();
        setServices(data.services || []);
      } else {
        // Mock data for development
        setServices([
          {
            service_name: 'DeepNexus API',
            status: 'healthy',
            response_time: 145,
            uptime: 99.8,
            last_check: new Date(),
            error_count: 2,
            memory_usage: 45.2,
            cpu_usage: 23.7,
            dependencies: ['Database', 'Redis', 'OpenRouter']
          },
          {
            service_name: 'Database',
            status: 'healthy',
            response_time: 12,
            uptime: 99.9,
            last_check: new Date(),
            error_count: 0,
            memory_usage: 67.8,
            cpu_usage: 15.3,
            dependencies: []
          },
          {
            service_name: 'Qdrant Vector DB',
            status: 'healthy',
            response_time: 89,
            uptime: 99.7,
            last_check: new Date(),
            error_count: 1,
            memory_usage: 78.4,
            cpu_usage: 34.2,
            dependencies: []
          },
          {
            service_name: 'OpenRouter API',
            status: 'degraded',
            response_time: 2340,
            uptime: 97.2,
            last_check: new Date(),
            error_count: 15,
            memory_usage: 0,
            cpu_usage: 0,
            dependencies: []
          },
          {
            service_name: 'BGE-M3 Embeddings',
            status: 'healthy',
            response_time: 567,
            uptime: 99.5,
            last_check: new Date(),
            error_count: 3,
            memory_usage: 89.1,
            cpu_usage: 56.8,
            dependencies: ['Qdrant Vector DB']
          }
        ]);
      }
    } catch (error) {
      console.error('Failed to load service health:', error);
    }
  };

  const loadAPIEndpoints = async () => {
    try {
      const response = await fetch('/api/monitoring/endpoints');
      if (response.ok) {
        const data = await response.json();
        setEndpoints(data.endpoints || []);
      } else {
        // Mock data
        setEndpoints([
          {
            endpoint: '/api/test/llm',
            method: 'POST',
            avg_response_time: 1245,
            success_rate: 97.8,
            requests_per_minute: 45,
            error_rate: 2.2,
            last_error: '502 Bad Gateway',
            status_code_distribution: { '200': 978, '502': 22, '429': 8 }
          },
          {
            endpoint: '/api/projects/create',
            method: 'POST',
            avg_response_time: 234,
            success_rate: 99.1,
            requests_per_minute: 12,
            error_rate: 0.9,
            status_code_distribution: { '200': 234, '400': 2, '500': 1 }
          },
          {
            endpoint: '/api/sessions',
            method: 'GET',
            avg_response_time: 89,
            success_rate: 99.8,
            requests_per_minute: 67,
            error_rate: 0.2,
            status_code_distribution: { '200': 1456, '404': 3 }
          },
          {
            endpoint: '/api/context/optimize',
            method: 'POST',
            avg_response_time: 3456,
            success_rate: 94.5,
            requests_per_minute: 8,
            error_rate: 5.5,
            last_error: 'Context too large',
            status_code_distribution: { '200': 189, '413': 11, '500': 2 }
          }
        ]);
      }
    } catch (error) {
      console.error('Failed to load API endpoints:', error);
    }
  };

  const loadSystemAlerts = async () => {
    try {
      const response = await fetch('/api/monitoring/alerts');
      if (response.ok) {
        const data = await response.json();
        setAlerts(data.alerts || []);
      } else {
        // Mock alerts
        setAlerts([
          {
            id: '1',
            type: 'warning',
            title: 'High Response Time',
            description: 'OpenRouter API response time exceeded 2s threshold',
            service: 'OpenRouter API',
            timestamp: new Date(Date.now() - 5 * 60 * 1000),
            resolved: false,
            severity: 'medium'
          },
          {
            id: '2',
            type: 'error',
            title: 'Memory Usage High',
            description: 'BGE-M3 Embeddings service memory usage at 89%',
            service: 'BGE-M3 Embeddings',
            timestamp: new Date(Date.now() - 15 * 60 * 1000),
            resolved: false,
            severity: 'high'
          },
          {
            id: '3',
            type: 'info',
            title: 'Service Restart',
            description: 'Qdrant Vector DB service restarted successfully',
            service: 'Qdrant Vector DB',
            timestamp: new Date(Date.now() - 30 * 60 * 1000),
            resolved: true,
            severity: 'low'
          }
        ]);
      }
    } catch (error) {
      console.error('Failed to load system alerts:', error);
    }
  };

  const loadAgentActivity = async () => {
    try {
      const response = await fetch('/api/monitoring/agents');
      if (response.ok) {
        const data = await response.json();
        setAgents(data.agents || []);
      } else {
        // Mock agent data
        setAgents([
          {
            agent_id: 'coding-agent-1',
            agent_type: 'Coding Assistant',
            status: 'active',
            current_task: 'Analyzing React component structure',
            tasks_completed: 156,
            avg_task_duration: 45.2,
            last_activity: new Date(Date.now() - 2 * 60 * 1000),
            success_rate: 94.8
          },
          {
            agent_id: 'planning-agent-1',
            agent_type: 'AI Planner',
            status: 'busy',
            current_task: 'Generating development roadmap',
            tasks_completed: 89,
            avg_task_duration: 120.5,
            last_activity: new Date(Date.now() - 1 * 60 * 1000),
            success_rate: 97.2
          },
          {
            agent_id: 'context-agent-1',
            agent_type: 'Context Optimizer',
            status: 'idle',
            tasks_completed: 234,
            avg_task_duration: 23.8,
            last_activity: new Date(Date.now() - 10 * 60 * 1000),
            success_rate: 91.5
          }
        ]);
      }
    } catch (error) {
      console.error('Failed to load agent activity:', error);
    }
  };

  const resolveAlert = async (alertId: string) => {
    try {
      const response = await fetch(`/api/monitoring/alerts/${alertId}/resolve`, {
        method: 'POST'
      });
      if (response.ok) {
        setAlerts(prev => prev.map(alert => 
          alert.id === alertId ? { ...alert, resolved: true } : alert
        ));
      }
    } catch (error) {
      console.error('Failed to resolve alert:', error);
    }
  };

  const restartService = async (serviceName: string) => {
    try {
      const response = await fetch(`/api/monitoring/services/${serviceName}/restart`, {
        method: 'POST'
      });
      if (response.ok) {
        console.log(`Service ${serviceName} restart initiated`);
        // Refresh data after restart
        setTimeout(loadMonitoringData, 2000);
      }
    } catch (error) {
      console.error('Failed to restart service:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-growth-accent bg-growth-accent/10 border-growth-accent';
      case 'degraded': return 'text-bloom-highlight bg-bloom-highlight/10 border-bloom-highlight';
      case 'unhealthy': return 'text-crimson-alert bg-crimson-alert/10 border-crimson-alert';
      case 'unknown': return 'text-earth-base/60 bg-earth-base/10 border-earth-base/30';
      default: return 'text-earth-base bg-mist-gradient border-earth-base/20';
    }
  };

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'critical': return 'border-crimson-alert bg-crimson-alert/10';
      case 'error': return 'border-crimson-alert bg-crimson-alert/10';
      case 'warning': return 'border-amber-data bg-amber-data/10';
      case 'info': return 'border-bloom-highlight bg-bloom-highlight/10';
      default: return 'border-earth-base/20 bg-mist-gradient';
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'critical': return '🚨';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'info': return 'ℹ️';
      default: return '📋';
    }
  };

  const getAgentStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-growth-accent';
      case 'busy': return 'text-bloom-highlight';
      case 'idle': return 'text-earth-base/60';
      case 'error': return 'text-crimson-alert';
      default: return 'text-earth-base';
    }
  };

  const healthyServices = services.filter(s => s.status === 'healthy').length;
  const totalServices = services.length;
  const activeAlerts = alerts.filter(a => !a.resolved).length;
  const activeAgents = agents.filter(a => a.status === 'active' || a.status === 'busy').length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-h2 text-forest-primary">🏥 System Health Monitor</h1>
          <p className="text-earth-base/70">
            🔒 <span className="font-medium">Admin Dashboard</span> - Real-time system monitoring and alerts
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={`px-4 py-2 rounded-lg transition-all hover-bloom ${
              autoRefresh 
                ? 'bg-growth-accent text-crystal-clear' 
                : 'border border-growth-accent/20 text-earth-base hover:bg-growth-accent/5'
            }`}
          >
            {autoRefresh ? '🔄 Auto' : '⏸️ Manual'}
          </button>
          
          <button
            onClick={loadMonitoringData}
            disabled={isLoading}
            className="px-6 py-3 bg-growth-gradient text-crystal-clear rounded-lg font-medium hover-bloom transition-all disabled:opacity-50"
          >
            {isLoading ? '🔄 Loading...' : '🔄 Refresh'}
          </button>
        </div>
      </div>

      {/* System Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-crystal-clear rounded-lg p-6 border border-growth-accent/20 hover-bloom">
          <div className="flex items-center justify-between mb-2">
            <span className="text-3xl">🏥</span>
            <span className={`text-sm font-medium ${
              healthyServices === totalServices ? 'text-growth-accent' : 'text-crimson-alert'
            }`}>
              {healthyServices}/{totalServices}
            </span>
          </div>
          <div>
            <p className="text-sm text-earth-base/60">System Health</p>
            <p className={`text-xl font-bold ${
              healthyServices === totalServices ? 'text-growth-accent' : 'text-crimson-alert'
            }`}>
              {totalServices > 0 ? ((healthyServices / totalServices) * 100).toFixed(1) : 0}%
            </p>
          </div>
        </div>

        <div className="bg-crystal-clear rounded-lg p-6 border border-growth-accent/20 hover-bloom">
          <div className="flex items-center justify-between mb-2">
            <span className="text-3xl">🚨</span>
            <span className={`text-sm font-medium ${
              activeAlerts === 0 ? 'text-growth-accent' : 'text-crimson-alert'
            }`}>
              {activeAlerts}
            </span>
          </div>
          <div>
            <p className="text-sm text-earth-base/60">Active Alerts</p>
            <p className={`text-xl font-bold ${
              activeAlerts === 0 ? 'text-growth-accent' : 'text-crimson-alert'
            }`}>
              {activeAlerts === 0 ? 'All Clear' : `${activeAlerts} Issues`}
            </p>
          </div>
        </div>

        <div className="bg-crystal-clear rounded-lg p-6 border border-growth-accent/20 hover-bloom">
          <div className="flex items-center justify-between mb-2">
            <span className="text-3xl">🤖</span>
            <span className="text-sm font-medium text-growth-accent">
              {activeAgents}/{agents.length}
            </span>
          </div>
          <div>
            <p className="text-sm text-earth-base/60">Active Agents</p>
            <p className="text-xl font-bold text-growth-accent">
              {agents.length > 0 ? ((activeAgents / agents.length) * 100).toFixed(1) : 0}%
            </p>
          </div>
        </div>

        <div className="bg-crystal-clear rounded-lg p-6 border border-growth-accent/20 hover-bloom">
          <div className="flex items-center justify-between mb-2">
            <span className="text-3xl">⚡</span>
            <span className="text-sm font-medium text-bloom-highlight">
              {endpoints.length > 0 ? endpoints.reduce((sum, e) => sum + e.avg_response_time, 0) / endpoints.length : 0}ms
            </span>
          </div>
          <div>
            <p className="text-sm text-earth-base/60">Avg Response</p>
            <p className="text-xl font-bold text-bloom-highlight">
              {endpoints.length > 0 ? 
                `${Math.round(endpoints.reduce((sum, e) => sum + e.avg_response_time, 0) / endpoints.length)}ms` : 
                'N/A'
              }
            </p>
          </div>
        </div>
      </div>

      {/* Service Health Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {services.map((service) => (
          <div
            key={service.service_name}
            className={`p-6 rounded-lg border-2 transition-all hover-bloom cursor-pointer ${getStatusColor(service.status)}`}
            onClick={() => setSelectedService(selectedService === service.service_name ? null : service.service_name)}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-forest-primary">{service.service_name}</h3>
              <div className="flex items-center space-x-2">
                <span className={`px-2 py-1 rounded text-xs font-medium capitalize ${getStatusColor(service.status)}`}>
                  {service.status}
                </span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    restartService(service.service_name);
                  }}
                  className="p-1 hover:bg-growth-accent/10 rounded transition-all"
                  title="Restart Service"
                >
                  🔄
                </button>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <div className="text-xs text-earth-base/60">Response Time</div>
                <div className={`font-bold ${
                  service.response_time < 500 ? 'text-growth-accent' :
                  service.response_time < 1000 ? 'text-bloom-highlight' :
                  'text-crimson-alert'
                }`}>
                  {service.response_time}ms
                </div>
              </div>
              <div>
                <div className="text-xs text-earth-base/60">Uptime</div>
                <div className={`font-bold ${
                  service.uptime >= 99 ? 'text-growth-accent' :
                  service.uptime >= 95 ? 'text-bloom-highlight' :
                  'text-crimson-alert'
                }`}>
                  {service.uptime.toFixed(1)}%
                </div>
              </div>
              <div>
                <div className="text-xs text-earth-base/60">Memory</div>
                <div className={`font-bold ${
                  service.memory_usage < 70 ? 'text-growth-accent' :
                  service.memory_usage < 85 ? 'text-bloom-highlight' :
                  'text-crimson-alert'
                }`}>
                  {service.memory_usage.toFixed(1)}%
                </div>
              </div>
              <div>
                <div className="text-xs text-earth-base/60">CPU</div>
                <div className={`font-bold ${
                  service.cpu_usage < 70 ? 'text-growth-accent' :
                  service.cpu_usage < 85 ? 'text-bloom-highlight' :
                  'text-crimson-alert'
                }`}>
                  {service.cpu_usage.toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between text-xs text-earth-base/60">
              <span>Errors: {service.error_count}</span>
              <span>Last check: {service.last_check.toLocaleTimeString()}</span>
            </div>

            {selectedService === service.service_name && service.dependencies.length > 0 && (
              <div className="mt-4 pt-4 border-t border-growth-accent/20">
                <div className="text-xs text-earth-base/60 mb-2">Dependencies</div>
                <div className="flex flex-wrap gap-1">
                  {service.dependencies.map((dep) => (
                    <span key={dep} className="text-xs bg-mist-gradient px-2 py-1 rounded">
                      {dep}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* API Endpoints Performance */}
      <div className="bg-crystal-clear rounded-lg p-6 border border-growth-accent/20">
        <h3 className="text-lg font-semibold text-forest-primary mb-4">🔌 API Endpoints Performance</h3>
        <div className="space-y-4">
          {endpoints.map((endpoint) => (
            <div key={`${endpoint.method}-${endpoint.endpoint}`} className="p-4 bg-mist-gradient rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    endpoint.method === 'GET' ? 'bg-growth-accent text-crystal-clear' :
                    endpoint.method === 'POST' ? 'bg-bloom-highlight text-earth-base' :
                    endpoint.method === 'PUT' ? 'bg-amber-data text-crystal-clear' :
                    'bg-crimson-alert text-crystal-clear'
                  }`}>
                    {endpoint.method}
                  </span>
                  <span className="font-mono text-sm text-earth-base">{endpoint.endpoint}</span>
                </div>
                <div className="flex items-center space-x-4 text-sm">
                  <span className={`${
                    endpoint.success_rate >= 99 ? 'text-growth-accent' :
                    endpoint.success_rate >= 95 ? 'text-bloom-highlight' :
                    'text-crimson-alert'
                  }`}>
                    {endpoint.success_rate.toFixed(1)}% success
                  </span>
                  <span className="text-earth-base/70">
                    {endpoint.requests_per_minute} req/min
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <div className="text-xs text-earth-base/60">Avg Response</div>
                  <div className={`font-bold ${
                    endpoint.avg_response_time < 500 ? 'text-growth-accent' :
                    endpoint.avg_response_time < 1000 ? 'text-bloom-highlight' :
                    'text-crimson-alert'
                  }`}>
                    {endpoint.avg_response_time}ms
                  </div>
                </div>
                <div>
                  <div className="text-xs text-earth-base/60">Error Rate</div>
                  <div className={`font-bold ${
                    endpoint.error_rate < 1 ? 'text-growth-accent' :
                    endpoint.error_rate < 5 ? 'text-bloom-highlight' :
                    'text-crimson-alert'
                  }`}>
                    {endpoint.error_rate.toFixed(1)}%
                  </div>
                </div>
                <div>
                  <div className="text-xs text-earth-base/60">Status Codes</div>
                  <div className="flex space-x-1">
                    {Object.entries(endpoint.status_code_distribution).map(([code, count]) => (
                      <span key={code} className={`text-xs px-1 rounded ${
                        code.startsWith('2') ? 'bg-growth-accent/20 text-growth-accent' :
                        code.startsWith('4') ? 'bg-amber-data/20 text-amber-data' :
                        'bg-crimson-alert/20 text-crimson-alert'
                      }`}>
                        {code}:{count}
                      </span>
                    ))}
                  </div>
                </div>
                <div>
                  <div className="text-xs text-earth-base/60">Last Error</div>
                  <div className="text-xs text-crimson-alert truncate">
                    {endpoint.last_error || 'None'}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* System Alerts */}
      <div className="bg-crystal-clear rounded-lg p-6 border border-growth-accent/20">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-forest-primary">🚨 System Alerts</h3>
          <span className="text-sm text-earth-base/70">
            {activeAlerts} active, {alerts.filter(a => a.resolved).length} resolved
          </span>
        </div>

        <div className="space-y-3">
          {alerts.map((alert) => (
            <div
              key={alert.id}
              className={`p-4 rounded-lg border-2 transition-all hover-bloom ${getAlertColor(alert.type)} ${
                alert.resolved ? 'opacity-60' : ''
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <span className="text-2xl">{getAlertIcon(alert.type)}</span>
                  <div>
                    <h4 className="font-semibold text-forest-primary">{alert.title}</h4>
                    <p className="text-sm text-earth-base/80 mt-1">{alert.description}</p>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-earth-base/60">
                      <span>Service: {alert.service}</span>
                      <span>Severity: {alert.severity}</span>
                      <span>{alert.timestamp.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {alert.resolved ? (
                    <span className="px-2 py-1 bg-growth-accent/20 text-growth-accent rounded text-xs">
                      ✅ Resolved
                    </span>
                  ) : (
                    <button
                      onClick={() => resolveAlert(alert.id)}
                      className="px-3 py-1 bg-growth-gradient text-crystal-clear rounded text-xs hover-bloom transition-all"
                    >
                      Resolve
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {alerts.length === 0 && (
          <div className="text-center py-8">
            <div className="text-4xl mb-2">✅</div>
            <p className="text-earth-base/60">No system alerts</p>
          </div>
        )}
      </div>

      {/* Agent Activity */}
      <div className="bg-crystal-clear rounded-lg p-6 border border-growth-accent/20">
        <h3 className="text-lg font-semibold text-forest-primary mb-4">🤖 Agent Activity Monitor</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {agents.map((agent) => (
            <div key={agent.agent_id} className="p-4 bg-mist-gradient rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-semibold text-forest-primary">{agent.agent_type}</h4>
                <span className={`px-2 py-1 rounded text-xs font-medium capitalize ${
                  agent.status === 'active' ? 'bg-growth-accent/20 text-growth-accent' :
                  agent.status === 'busy' ? 'bg-bloom-highlight/20 text-bloom-highlight' :
                  agent.status === 'idle' ? 'bg-earth-base/20 text-earth-base/70' :
                  'bg-crimson-alert/20 text-crimson-alert'
                }`}>
                  {agent.status}
                </span>
              </div>

              {agent.current_task && (
                <div className="mb-3">
                  <div className="text-xs text-earth-base/60">Current Task</div>
                  <div className="text-sm text-earth-base line-clamp-2">{agent.current_task}</div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <div className="text-xs text-earth-base/60">Tasks Done</div>
                  <div className="font-bold text-growth-accent">{agent.tasks_completed}</div>
                </div>
                <div>
                  <div className="text-xs text-earth-base/60">Success Rate</div>
                  <div className={`font-bold ${
                    agent.success_rate >= 95 ? 'text-growth-accent' :
                    agent.success_rate >= 85 ? 'text-bloom-highlight' :
                    'text-crimson-alert'
                  }`}>
                    {agent.success_rate.toFixed(1)}%
                  </div>
                </div>
                <div>
                  <div className="text-xs text-earth-base/60">Avg Duration</div>
                  <div className="font-bold text-earth-base">{agent.avg_task_duration.toFixed(1)}s</div>
                </div>
                <div>
                  <div className="text-xs text-earth-base/60">Last Activity</div>
                  <div className="font-bold text-earth-base/70">
                    {Math.round((Date.now() - agent.last_activity.getTime()) / 60000)}m ago
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {isLoading && (
        <div className="fixed inset-0 bg-shadow-deep/20 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-crystal-clear rounded-xl p-8 animate-bloom">
            <div className="flex items-center space-x-3">
              <div className="w-4 h-4 bg-growth-accent rounded-full animate-bounce"></div>
              <div className="w-4 h-4 bg-growth-accent rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
              <div className="w-4 h-4 bg-growth-accent rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
              <span className="text-forest-primary font-medium">Loading monitoring data...</span>
            </div>
          </div>
        )}
      )}
    </div>
  );
};
