#!/usr/bin/env python3
"""
Test script to verify the basic setup is working.
"""
import sys
import os
sys.path.append(os.path.dirname(__file__))

def test_imports():
    """Test that all basic imports work."""
    try:
        from config import settings
        print("✓ Config import successful")
        print(f"  - OpenRouter API key configured: {'Yes' if settings.openrouter_api_key != 'your_openrouter_api_key_here' else 'No (using default)'}")
        print(f"  - Code model: {settings.code_model}")
        print(f"  - Max token limit: {settings.max_token_limit}")
        
        from sessions import init_db, create_session, get_session
        print("✓ Sessions import successful")
        
        # Test database initialization
        init_db()
        print("✓ Database initialization successful")
        
        # Test session creation
        session_id = create_session("https://github.com/test/repo.git", "main")
        print(f"✓ Session creation successful: {session_id}")
        
        # Test session retrieval
        session = get_session(session_id)
        if session:
            print("✓ Session retrieval successful")
            print(f"  - Session ID: {session['session_id']}")
            print(f"  - Repo URL: {session['repo_url']}")
            print(f"  - Status: {session['status']}")
        else:
            print("✗ Session retrieval failed")
            
        return True
    except Exception as e:
        print(f"✗ Import test failed: {e}")
        return False

def test_fastapi_imports():
    """Test FastAPI related imports."""
    try:
        import fastapi
        import socketio
        import uvicorn
        print("✓ FastAPI and Socket.IO imports successful")
        print(f"  - FastAPI version: {fastapi.__version__}")
        return True
    except Exception as e:
        print(f"✗ FastAPI imports failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=== AI Coder Agent Setup Test ===\n")
    
    tests = [
        ("Basic imports", test_imports),
        ("FastAPI imports", test_fastapi_imports),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"Running {test_name}...")
        if test_func():
            passed += 1
        print()
    
    print(f"=== Test Results: {passed}/{total} passed ===")
    
    if passed == total:
        print("🎉 All tests passed! Setup looks good.")
        return 0
    else:
        print("❌ Some tests failed. Check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
