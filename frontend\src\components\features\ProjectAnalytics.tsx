import React, { useState, useEffect } from 'react';

interface AnalyticsData {
  project_id: string;
  complexity_analysis: {
    overall_score: number;
    file_complexity: { file: string; score: number }[];
    function_complexity: { function: string; score: number }[];
    suggestions: string[];
  };
  performance_metrics: {
    build_time: number;
    bundle_size: number;
    test_coverage: number;
    dependencies_count: number;
    outdated_dependencies: number;
  };
  code_quality: {
    maintainability_index: number;
    technical_debt_ratio: number;
    code_smells: number;
    duplicated_lines: number;
  };
  ai_insights: {
    optimization_opportunities: string[];
    refactoring_suggestions: string[];
    security_recommendations: string[];
    performance_tips: string[];
  };
}

interface ProjectAnalyticsProps {
  projectId: string;
  onClose: () => void;
}

export const ProjectAnalytics: React.FC<ProjectAnalyticsProps> = ({
  projectId,
  onClose
}) => {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  useEffect(() => {
    loadAnalytics();
  }, [projectId]);

  const loadAnalytics = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/projects/${projectId}/analyze`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        const data = await response.json();
        setAnalytics(data.analysis);
      } else {
        // Mock data for development
        setAnalytics({
          project_id: projectId,
          complexity_analysis: {
            overall_score: 7.2,
            file_complexity: [
              { file: 'src/components/ProjectDashboard.tsx', score: 8.5 },
              { file: 'src/components/ChatInterface.tsx', score: 6.8 },
              { file: 'src/utils/api.ts', score: 4.2 }
            ],
            function_complexity: [
              { function: 'renderProjectCard', score: 9.1 },
              { function: 'loadProjects', score: 5.5 },
              { function: 'filterAndSort', score: 3.8 }
            ],
            suggestions: [
              'Consider breaking down renderProjectCard into smaller components',
              'Add error boundaries for better error handling',
              'Implement memoization for expensive calculations'
            ]
          },
          performance_metrics: {
            build_time: 12.5,
            bundle_size: 2.8,
            test_coverage: 85,
            dependencies_count: 42,
            outdated_dependencies: 3
          },
          code_quality: {
            maintainability_index: 78,
            technical_debt_ratio: 12.5,
            code_smells: 8,
            duplicated_lines: 156
          },
          ai_insights: {
            optimization_opportunities: [
              '🚀 Implement code splitting to reduce initial bundle size',
              '⚡ Add lazy loading for heavy components',
              '🔄 Use React.memo for frequently re-rendered components'
            ],
            refactoring_suggestions: [
              '🏗️ Extract custom hooks from complex components',
              '📦 Create a centralized state management solution',
              '🎯 Implement proper TypeScript strict mode'
            ],
            security_recommendations: [
              '🔒 Add input validation for all user inputs',
              '🛡️ Implement proper authentication checks',
              '🔐 Use environment variables for sensitive data'
            ],
            performance_tips: [
              '⚡ Optimize image loading with next-gen formats',
              '🗜️ Enable gzip compression on the server',
              '📊 Implement performance monitoring'
            ]
          }
        });
      }
    } catch (error) {
      console.error('Failed to load analytics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const runOptimization = async () => {
    setIsAnalyzing(true);
    try {
      const response = await fetch(`/api/projects/${projectId}/optimize`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Optimization completed:', data);
        // Reload analytics to show updated data
        await loadAnalytics();
      }
    } catch (error) {
      console.error('Failed to run optimization:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getScoreColor = (score: number, reverse = false) => {
    if (reverse) {
      if (score <= 3) return 'text-growth-accent';
      if (score <= 7) return 'text-bloom-highlight';
      return 'text-crimson-alert';
    } else {
      if (score >= 8) return 'text-growth-accent';
      if (score >= 6) return 'text-bloom-highlight';
      return 'text-crimson-alert';
    }
  };

  const getScoreBackground = (score: number, reverse = false) => {
    if (reverse) {
      if (score <= 3) return 'bg-growth-gradient';
      if (score <= 7) return 'bg-gradient-to-r from-bloom-highlight to-golden-success';
      return 'bg-gradient-to-r from-crimson-alert to-amber-data';
    } else {
      if (score >= 8) return 'bg-growth-gradient';
      if (score >= 6) return 'bg-gradient-to-r from-bloom-highlight to-golden-success';
      return 'bg-gradient-to-r from-crimson-alert to-amber-data';
    }
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-shadow-deep/50 backdrop-blur-sm z-50 flex items-center justify-center">
        <div className="bg-crystal-clear rounded-xl p-8 animate-bloom">
          <div className="flex items-center space-x-3">
            <div className="w-4 h-4 bg-growth-accent rounded-full animate-bounce"></div>
            <div className="w-4 h-4 bg-growth-accent rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
            <div className="w-4 h-4 bg-growth-accent rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
            <span className="text-forest-primary font-medium">Analyzing your project...</span>
          </div>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="fixed inset-0 bg-shadow-deep/50 backdrop-blur-sm z-50 flex items-center justify-center">
        <div className="bg-crystal-clear rounded-xl p-8 text-center">
          <div className="text-4xl mb-4">❌</div>
          <h3 className="text-xl font-semibold text-forest-primary mb-2">Analysis Failed</h3>
          <p className="text-earth-base/70 mb-4">Unable to analyze the project</p>
          <button
            onClick={onClose}
            className="px-6 py-2 bg-growth-gradient text-crystal-clear rounded-lg hover-bloom"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-shadow-deep/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-crystal-clear rounded-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto animate-bloom">
        
        {/* Header */}
        <div className="p-6 border-b border-growth-accent/20">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-h2 text-forest-primary">📊 Project Analytics</h2>
              <p className="text-earth-base/70">Deep insights into your project's health</p>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={runOptimization}
                disabled={isAnalyzing}
                className="px-4 py-2 bg-growth-gradient text-crystal-clear rounded-lg hover-bloom transition-all disabled:opacity-50"
              >
                {isAnalyzing ? '🔄 Optimizing...' : '⚡ Optimize'}
              </button>
              <button
                onClick={onClose}
                className="p-2 hover:bg-growth-accent/10 rounded-lg transition-all hover-bloom"
              >
                ✕
              </button>
            </div>
          </div>
        </div>

        <div className="p-6 space-y-6">
          
          {/* Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-mist-gradient rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-earth-base/70">Complexity</span>
                <span className="text-2xl">🧩</span>
              </div>
              <div className={`text-2xl font-bold ${getScoreColor(analytics.complexity_analysis.overall_score)}`}>
                {analytics.complexity_analysis.overall_score}/10
              </div>
              <div className="w-full bg-frost-light rounded-full h-2 mt-2">
                <div 
                  className={`h-2 rounded-full ${getScoreBackground(analytics.complexity_analysis.overall_score)}`}
                  style={{ width: `${(analytics.complexity_analysis.overall_score / 10) * 100}%` }}
                />
              </div>
            </div>

            <div className="bg-mist-gradient rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-earth-base/70">Maintainability</span>
                <span className="text-2xl">🛠️</span>
              </div>
              <div className={`text-2xl font-bold ${getScoreColor(analytics.code_quality.maintainability_index / 10)}`}>
                {analytics.code_quality.maintainability_index}%
              </div>
              <div className="w-full bg-frost-light rounded-full h-2 mt-2">
                <div 
                  className={`h-2 rounded-full ${getScoreBackground(analytics.code_quality.maintainability_index / 10)}`}
                  style={{ width: `${analytics.code_quality.maintainability_index}%` }}
                />
              </div>
            </div>

            <div className="bg-mist-gradient rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-earth-base/70">Test Coverage</span>
                <span className="text-2xl">🧪</span>
              </div>
              <div className={`text-2xl font-bold ${getScoreColor(analytics.performance_metrics.test_coverage / 10)}`}>
                {analytics.performance_metrics.test_coverage}%
              </div>
              <div className="w-full bg-frost-light rounded-full h-2 mt-2">
                <div 
                  className={`h-2 rounded-full ${getScoreBackground(analytics.performance_metrics.test_coverage / 10)}`}
                  style={{ width: `${analytics.performance_metrics.test_coverage}%` }}
                />
              </div>
            </div>

            <div className="bg-mist-gradient rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-earth-base/70">Tech Debt</span>
                <span className="text-2xl">⚠️</span>
              </div>
              <div className={`text-2xl font-bold ${getScoreColor(analytics.code_quality.technical_debt_ratio, true)}`}>
                {analytics.code_quality.technical_debt_ratio}%
              </div>
              <div className="w-full bg-frost-light rounded-full h-2 mt-2">
                <div 
                  className={`h-2 rounded-full ${getScoreBackground(analytics.code_quality.technical_debt_ratio, true)}`}
                  style={{ width: `${analytics.code_quality.technical_debt_ratio}%` }}
                />
              </div>
            </div>
          </div>

          {/* Performance Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-crystal-clear border border-growth-accent/20 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-forest-primary mb-4">⚡ Performance Metrics</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-earth-base/70">Build Time:</span>
                  <span className="font-medium">{analytics.performance_metrics.build_time}s</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-earth-base/70">Bundle Size:</span>
                  <span className="font-medium">{analytics.performance_metrics.bundle_size}MB</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-earth-base/70">Dependencies:</span>
                  <span className="font-medium">{analytics.performance_metrics.dependencies_count}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-earth-base/70">Outdated Deps:</span>
                  <span className={`font-medium ${analytics.performance_metrics.outdated_dependencies > 0 ? 'text-crimson-alert' : 'text-growth-accent'}`}>
                    {analytics.performance_metrics.outdated_dependencies}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-crystal-clear border border-growth-accent/20 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-forest-primary mb-4">🔍 Code Quality</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-earth-base/70">Code Smells:</span>
                  <span className={`font-medium ${getScoreColor(analytics.code_quality.code_smells, true)}`}>
                    {analytics.code_quality.code_smells}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-earth-base/70">Duplicated Lines:</span>
                  <span className={`font-medium ${getScoreColor(analytics.code_quality.duplicated_lines / 100, true)}`}>
                    {analytics.code_quality.duplicated_lines}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-earth-base/70">Maintainability:</span>
                  <span className={`font-medium ${getScoreColor(analytics.code_quality.maintainability_index / 10)}`}>
                    {analytics.code_quality.maintainability_index}/100
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-earth-base/70">Technical Debt:</span>
                  <span className={`font-medium ${getScoreColor(analytics.code_quality.technical_debt_ratio, true)}`}>
                    {analytics.code_quality.technical_debt_ratio}%
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* AI Insights */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-crystal-clear border border-growth-accent/20 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-forest-primary mb-4">🚀 Optimization Opportunities</h3>
              <div className="space-y-2">
                {analytics.ai_insights.optimization_opportunities.map((opportunity, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <span className="text-growth-accent mt-1">•</span>
                    <span className="text-sm text-earth-base">{opportunity}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-crystal-clear border border-growth-accent/20 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-forest-primary mb-4">🔒 Security Recommendations</h3>
              <div className="space-y-2">
                {analytics.ai_insights.security_recommendations.map((recommendation, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <span className="text-crimson-alert mt-1">•</span>
                    <span className="text-sm text-earth-base">{recommendation}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Complex Files */}
          <div className="bg-crystal-clear border border-growth-accent/20 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-forest-primary mb-4">📁 Most Complex Files</h3>
            <div className="space-y-3">
              {analytics.complexity_analysis.file_complexity.map((file, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm font-mono text-earth-base truncate">{file.file}</span>
                  <div className="flex items-center space-x-2">
                    <span className={`text-sm font-medium ${getScoreColor(file.score)}`}>
                      {file.score}/10
                    </span>
                    <div className="w-16 bg-frost-light rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${getScoreBackground(file.score)}`}
                        style={{ width: `${(file.score / 10) * 100}%` }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Suggestions */}
          <div className="bg-mist-gradient rounded-lg p-6">
            <h3 className="text-lg font-semibold text-forest-primary mb-4">💡 AI Suggestions</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-earth-base mb-2">🏗️ Refactoring</h4>
                <div className="space-y-1">
                  {analytics.ai_insights.refactoring_suggestions.map((suggestion, index) => (
                    <div key={index} className="text-sm text-earth-base/80">{suggestion}</div>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="font-medium text-earth-base mb-2">⚡ Performance</h4>
                <div className="space-y-1">
                  {analytics.ai_insights.performance_tips.map((tip, index) => (
                    <div key={index} className="text-sm text-earth-base/80">{tip}</div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
