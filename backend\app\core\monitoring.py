"""
Enhanced Monitoring and Observability Module

Provides comprehensive monitoring capabilities including:
- Performance metrics collection
- Error tracking and analysis
- System health monitoring
- Custom span creation
- Real-time dashboards
"""

import asyncio
import logging
import time
import psutil
from typing import Dict, Any, Optional, List, Callable
from functools import wraps
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta

try:
    import logfire
    LOGFIRE_AVAILABLE = True
except ImportError:
    LOGFIRE_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics data structure."""
    operation_name: str
    duration: float
    success: bool
    timestamp: datetime
    tags: Dict[str, str] = field(default_factory=dict)
    memory_usage: Optional[float] = None
    cpu_usage: Optional[float] = None


class EnhancedMetricsCollector:
    """Advanced metrics collection with real-time analysis."""
    
    def __init__(self, max_history: int = 10000):
        self.max_history = max_history
        self.metrics = deque(maxlen=max_history)
        self.counters = defaultdict(int)
        self.gauges = defaultdict(float)
        self.histograms = defaultdict(list)
        
        # Performance tracking
        self.operation_stats = defaultdict(lambda: {
            'count': 0,
            'total_duration': 0.0,
            'success_count': 0,
            'error_count': 0,
            'avg_duration': 0.0,
            'min_duration': float('inf'),
            'max_duration': 0.0,
            'recent_durations': deque(maxlen=100)
        })
        
        # System metrics
        self.system_metrics = deque(maxlen=1000)
        self._start_system_monitoring()
    
    def record_performance(self, metrics: PerformanceMetrics) -> None:
        """Record performance metrics."""
        self.metrics.append(metrics)
        
        # Update operation statistics
        stats = self.operation_stats[metrics.operation_name]
        stats['count'] += 1
        stats['total_duration'] += metrics.duration
        stats['recent_durations'].append(metrics.duration)
        
        if metrics.success:
            stats['success_count'] += 1
        else:
            stats['error_count'] += 1
        
        # Update duration statistics
        stats['avg_duration'] = stats['total_duration'] / stats['count']
        stats['min_duration'] = min(stats['min_duration'], metrics.duration)
        stats['max_duration'] = max(stats['max_duration'], metrics.duration)
        
        # Log to Logfire if available
        if LOGFIRE_AVAILABLE:
            logfire.info(
                f"Performance: {metrics.operation_name}",
                operation=metrics.operation_name,
                duration=metrics.duration,
                success=metrics.success,
                tags=metrics.tags,
                memory_usage=metrics.memory_usage,
                cpu_usage=metrics.cpu_usage
            )
    
    def increment_counter(self, name: str, value: int = 1, tags: Optional[Dict[str, str]] = None) -> None:
        """Increment a counter metric."""
        self.counters[name] += value
        
        if LOGFIRE_AVAILABLE:
            logfire.info(f"Counter: {name}", counter=name, value=value, tags=tags or {})
    
    def set_gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Set a gauge metric."""
        self.gauges[name] = value
        
        if LOGFIRE_AVAILABLE:
            logfire.info(f"Gauge: {name}", gauge=name, value=value, tags=tags or {})
    
    def record_histogram(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record a histogram value."""
        self.histograms[name].append(value)
        if len(self.histograms[name]) > 1000:
            self.histograms[name].pop(0)
        
        if LOGFIRE_AVAILABLE:
            logfire.info(f"Histogram: {name}", histogram=name, value=value, tags=tags or {})
    
    def get_operation_summary(self, operation_name: str) -> Dict[str, Any]:
        """Get summary statistics for an operation."""
        stats = self.operation_stats[operation_name]
        
        if stats['count'] == 0:
            return {'error': 'No data available'}
        
        recent_durations = list(stats['recent_durations'])
        
        summary = {
            'operation_name': operation_name,
            'total_calls': stats['count'],
            'success_rate': (stats['success_count'] / stats['count']) * 100,
            'error_rate': (stats['error_count'] / stats['count']) * 100,
            'avg_duration': stats['avg_duration'],
            'min_duration': stats['min_duration'],
            'max_duration': stats['max_duration']
        }
        
        # Calculate percentiles from recent data
        if recent_durations:
            sorted_durations = sorted(recent_durations)
            n = len(sorted_durations)
            summary.update({
                'p50_duration': sorted_durations[int(n * 0.5)],
                'p95_duration': sorted_durations[int(n * 0.95)],
                'p99_duration': sorted_durations[int(n * 0.99)]
            })
        
        return summary
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get current system health metrics."""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            health = {
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'memory_available': memory.available / (1024**3),  # GB
                'disk_usage': disk.percent,
                'disk_free': disk.free / (1024**3),  # GB
                'load_average': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None,
                'active_connections': len(psutil.net_connections()),
                'timestamp': datetime.now().isoformat()
            }
            
            # Store system metrics
            self.system_metrics.append(health)
            
            return health
            
        except Exception as e:
            logger.error(f"Failed to get system health: {e}")
            return {'error': str(e)}
    
    def get_performance_trends(self, operation_name: str, hours: int = 24) -> Dict[str, Any]:
        """Get performance trends for an operation."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        recent_metrics = [
            m for m in self.metrics
            if m.operation_name == operation_name and m.timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return {'error': 'No recent data available'}
        
        # Group by hour
        hourly_stats = defaultdict(lambda: {'count': 0, 'durations': [], 'errors': 0})
        
        for metric in recent_metrics:
            hour_key = metric.timestamp.replace(minute=0, second=0, microsecond=0)
            hourly_stats[hour_key]['count'] += 1
            hourly_stats[hour_key]['durations'].append(metric.duration)
            if not metric.success:
                hourly_stats[hour_key]['errors'] += 1
        
        # Calculate trends
        trends = []
        for hour, stats in sorted(hourly_stats.items()):
            avg_duration = sum(stats['durations']) / len(stats['durations'])
            error_rate = (stats['errors'] / stats['count']) * 100
            
            trends.append({
                'hour': hour.isoformat(),
                'call_count': stats['count'],
                'avg_duration': avg_duration,
                'error_rate': error_rate
            })
        
        return {
            'operation_name': operation_name,
            'time_range_hours': hours,
            'trends': trends
        }
    
    def _start_system_monitoring(self) -> None:
        """Start background system monitoring."""
        async def monitor_loop():
            while True:
                try:
                    health = self.get_system_health()
                    
                    # Set gauge metrics
                    self.set_gauge('system.cpu_usage', health.get('cpu_usage', 0))
                    self.set_gauge('system.memory_usage', health.get('memory_usage', 0))
                    self.set_gauge('system.disk_usage', health.get('disk_usage', 0))
                    
                    await asyncio.sleep(30)  # Monitor every 30 seconds
                    
                except Exception as e:
                    logger.error(f"System monitoring failed: {e}")
                    await asyncio.sleep(60)  # Wait longer on error
        
        # Start monitoring task
        asyncio.create_task(monitor_loop())


class HealthChecker:
    """Comprehensive health checking system."""
    
    def __init__(self):
        self.health_checks = {}
        self.last_results = {}
    
    def register_check(self, name: str, check_func: Callable[[], Dict[str, Any]]) -> None:
        """Register a health check function."""
        self.health_checks[name] = check_func
        logger.info(f"Registered health check: {name}")
    
    async def run_all_checks(self) -> Dict[str, Any]:
        """Run all registered health checks."""
        results = {}
        overall_healthy = True
        
        for name, check_func in self.health_checks.items():
            try:
                start_time = time.time()
                
                if asyncio.iscoroutinefunction(check_func):
                    result = await check_func()
                else:
                    result = check_func()
                
                duration = time.time() - start_time
                
                result['check_duration'] = duration
                result['timestamp'] = datetime.now().isoformat()
                
                results[name] = result
                
                if not result.get('healthy', True):
                    overall_healthy = False
                
            except Exception as e:
                logger.error(f"Health check {name} failed: {e}")
                results[name] = {
                    'healthy': False,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
                overall_healthy = False
        
        self.last_results = results
        
        summary = {
            'overall_healthy': overall_healthy,
            'checks': results,
            'total_checks': len(self.health_checks),
            'healthy_checks': sum(1 for r in results.values() if r.get('healthy', False)),
            'timestamp': datetime.now().isoformat()
        }
        
        if LOGFIRE_AVAILABLE:
            logfire.info(
                "Health check completed",
                overall_healthy=overall_healthy,
                total_checks=len(self.health_checks),
                healthy_checks=summary['healthy_checks']
            )
        
        return summary
    
    def get_last_results(self) -> Dict[str, Any]:
        """Get the last health check results."""
        return self.last_results


# Performance monitoring decorator
def monitor_performance(operation_name: str, tags: Optional[Dict[str, str]] = None):
    """Decorator to monitor function performance."""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            start_memory = None
            start_cpu = None
            
            try:
                # Get initial system metrics
                process = psutil.Process()
                start_memory = process.memory_percent()
                start_cpu = process.cpu_percent()
            except:
                pass
            
            success = True
            error = None
            
            try:
                if LOGFIRE_AVAILABLE:
                    with logfire.span(f"operation.{operation_name}", **tags or {}):
                        result = await func(*args, **kwargs)
                else:
                    result = await func(*args, **kwargs)
                
                return result
                
            except Exception as e:
                success = False
                error = e
                raise
            finally:
                duration = time.time() - start_time
                
                # Calculate resource usage
                memory_usage = None
                cpu_usage = None
                try:
                    if start_memory is not None:
                        memory_usage = process.memory_percent() - start_memory
                    if start_cpu is not None:
                        cpu_usage = process.cpu_percent() - start_cpu
                except:
                    pass
                
                # Record metrics
                metrics = PerformanceMetrics(
                    operation_name=operation_name,
                    duration=duration,
                    success=success,
                    timestamp=datetime.now(),
                    tags=tags or {},
                    memory_usage=memory_usage,
                    cpu_usage=cpu_usage
                )
                
                metrics_collector.record_performance(metrics)
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            
            try:
                if LOGFIRE_AVAILABLE:
                    with logfire.span(f"operation.{operation_name}", **tags or {}):
                        result = func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                return result
                
            except Exception as e:
                success = False
                raise
            finally:
                duration = time.time() - start_time
                
                metrics = PerformanceMetrics(
                    operation_name=operation_name,
                    duration=duration,
                    success=success,
                    timestamp=datetime.now(),
                    tags=tags or {}
                )
                
                metrics_collector.record_performance(metrics)
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator


# Global instances
metrics_collector = EnhancedMetricsCollector()
health_checker = HealthChecker()

# Register basic health checks
def basic_system_check():
    """Basic system health check."""
    try:
        health = metrics_collector.get_system_health()
        
        healthy = (
            health.get('cpu_usage', 0) < 90 and
            health.get('memory_usage', 0) < 90 and
            health.get('disk_usage', 0) < 90
        )
        
        return {
            'healthy': healthy,
            'cpu_usage': health.get('cpu_usage'),
            'memory_usage': health.get('memory_usage'),
            'disk_usage': health.get('disk_usage')
        }
    except Exception as e:
        return {'healthy': False, 'error': str(e)}

health_checker.register_check('system', basic_system_check)


# Factory function for getting system monitor
def get_system_monitor():
    """Get the global system monitor instance."""
    return {
        'metrics_collector': metrics_collector,
        'health_checker': health_checker,
        'monitor_performance': monitor_performance
    }
