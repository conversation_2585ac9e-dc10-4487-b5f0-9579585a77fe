import React, { useState, useEffect } from 'react';

interface UsageMetrics {
  total_users: number;
  active_sessions: number;
  requests_per_minute: number;
  avg_response_time: number;
  error_rate: number;
  uptime_percentage: number;
  peak_concurrent_users: number;
  total_tokens_processed: number;
}

interface FeatureUsage {
  feature_name: string;
  usage_count: number;
  unique_users: number;
  avg_session_duration: number;
  success_rate: number;
  growth_rate: number;
}

interface UserJourney {
  step: string;
  users_count: number;
  conversion_rate: number;
  avg_time_spent: number;
  drop_off_rate: number;
}

interface PerformanceMetric {
  timestamp: Date;
  cpu_usage: number;
  memory_usage: number;
  response_time: number;
  requests_per_second: number;
  error_count: number;
}

interface Insight {
  id: string;
  type: 'optimization' | 'warning' | 'success' | 'trend';
  title: string;
  description: string;
  impact: 'low' | 'medium' | 'high' | 'critical';
  action_required: boolean;
  timestamp: Date;
}

export const AnalyticsDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<UsageMetrics>({
    total_users: 0,
    active_sessions: 0,
    requests_per_minute: 0,
    avg_response_time: 0,
    error_rate: 0,
    uptime_percentage: 0,
    peak_concurrent_users: 0,
    total_tokens_processed: 0
  });
  
  const [featureUsage, setFeatureUsage] = useState<FeatureUsage[]>([]);
  const [userJourney, setUserJourney] = useState<UserJourney[]>([]);
  const [performanceData, setPerformanceData] = useState<PerformanceMetric[]>([]);
  const [insights, setInsights] = useState<Insight[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h');
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    loadAnalyticsData();
    if (autoRefresh) {
      const interval = setInterval(loadAnalyticsData, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [timeRange, autoRefresh]);

  const loadAnalyticsData = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        loadUsageMetrics(),
        loadFeatureUsage(),
        loadUserJourney(),
        loadPerformanceData(),
        loadInsights()
      ]);
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadUsageMetrics = async () => {
    try {
      const response = await fetch('/api/analytics/usage-metrics');
      if (response.ok) {
        const data = await response.json();
        setMetrics(data);
      } else {
        // Mock data for development
        setMetrics({
          total_users: 1247,
          active_sessions: 23,
          requests_per_minute: 156,
          avg_response_time: 245,
          error_rate: 0.8,
          uptime_percentage: 99.7,
          peak_concurrent_users: 45,
          total_tokens_processed: 2847392
        });
      }
    } catch (error) {
      console.error('Failed to load usage metrics:', error);
    }
  };

  const loadFeatureUsage = async () => {
    try {
      const response = await fetch('/api/analytics/feature-usage');
      if (response.ok) {
        const data = await response.json();
        setFeatureUsage(data.features || []);
      } else {
        // Mock data
        setFeatureUsage([
          {
            feature_name: 'AI Chat',
            usage_count: 8934,
            unique_users: 456,
            avg_session_duration: 12.5,
            success_rate: 97.2,
            growth_rate: 15.3
          },
          {
            feature_name: 'Project Creation',
            usage_count: 2341,
            unique_users: 234,
            avg_session_duration: 8.7,
            success_rate: 94.8,
            growth_rate: 22.1
          },
          {
            feature_name: 'Code Analysis',
            usage_count: 5672,
            unique_users: 345,
            avg_session_duration: 6.3,
            success_rate: 91.5,
            growth_rate: 8.9
          },
          {
            feature_name: 'Context Optimization',
            usage_count: 1876,
            unique_users: 123,
            avg_session_duration: 4.2,
            success_rate: 89.3,
            growth_rate: 31.7
          }
        ]);
      }
    } catch (error) {
      console.error('Failed to load feature usage:', error);
    }
  };

  const loadUserJourney = async () => {
    try {
      const response = await fetch('/api/analytics/user-journey/aggregate');
      if (response.ok) {
        const data = await response.json();
        setUserJourney(data.journey || []);
      } else {
        // Mock data
        setUserJourney([
          { step: 'Landing', users_count: 1000, conversion_rate: 100, avg_time_spent: 45, drop_off_rate: 0 },
          { step: 'Registration', users_count: 780, conversion_rate: 78, avg_time_spent: 120, drop_off_rate: 22 },
          { step: 'First Chat', users_count: 650, conversion_rate: 83.3, avg_time_spent: 300, drop_off_rate: 16.7 },
          { step: 'Project Creation', users_count: 520, conversion_rate: 80, avg_time_spent: 480, drop_off_rate: 20 },
          { step: 'Advanced Features', users_count: 390, conversion_rate: 75, avg_time_spent: 720, drop_off_rate: 25 },
          { step: 'Power User', users_count: 290, conversion_rate: 74.4, avg_time_spent: 1200, drop_off_rate: 25.6 }
        ]);
      }
    } catch (error) {
      console.error('Failed to load user journey:', error);
    }
  };

  const loadPerformanceData = async () => {
    try {
      const response = await fetch('/api/analytics/performance/metrics');
      if (response.ok) {
        const data = await response.json();
        setPerformanceData(data.metrics || []);
      } else {
        // Mock performance data
        const now = new Date();
        const mockData: PerformanceMetric[] = [];
        for (let i = 23; i >= 0; i--) {
          mockData.push({
            timestamp: new Date(now.getTime() - i * 60 * 60 * 1000),
            cpu_usage: 20 + Math.random() * 60,
            memory_usage: 30 + Math.random() * 50,
            response_time: 200 + Math.random() * 300,
            requests_per_second: 10 + Math.random() * 40,
            error_count: Math.floor(Math.random() * 5)
          });
        }
        setPerformanceData(mockData);
      }
    } catch (error) {
      console.error('Failed to load performance data:', error);
    }
  };

  const loadInsights = async () => {
    try {
      const response = await fetch('/api/analytics/insights');
      if (response.ok) {
        const data = await response.json();
        setInsights(data.insights || []);
      } else {
        // Mock insights
        setInsights([
          {
            id: '1',
            type: 'optimization',
            title: 'Context Optimization Opportunity',
            description: 'Users are experiencing 40% token reduction with context optimization. Consider promoting this feature.',
            impact: 'medium',
            action_required: false,
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
          },
          {
            id: '2',
            type: 'trend',
            title: 'AI Planning Usage Surge',
            description: 'AI Planning feature usage increased by 31.7% this week. Server capacity may need scaling.',
            impact: 'high',
            action_required: true,
            timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000)
          },
          {
            id: '3',
            type: 'success',
            title: 'High User Satisfaction',
            description: 'Chat interface maintains 97.2% success rate with excellent user feedback.',
            impact: 'low',
            action_required: false,
            timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000)
          }
        ]);
      }
    } catch (error) {
      console.error('Failed to load insights:', error);
    }
  };

  const generateReport = async () => {
    try {
      const response = await fetch('/api/analytics/generate-report', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          time_range: timeRange,
          include_charts: true,
          format: 'pdf'
        })
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `analytics_report_${timeRange}.pdf`;
        a.click();
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Failed to generate report:', error);
    }
  };

  const getMetricColor = (value: number, threshold: { good: number; warning: number }) => {
    if (value >= threshold.good) return 'text-growth-accent';
    if (value >= threshold.warning) return 'text-bloom-highlight';
    return 'text-crimson-alert';
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'optimization': return '⚡';
      case 'warning': return '⚠️';
      case 'success': return '✅';
      case 'trend': return '📈';
      default: return '💡';
    }
  };

  const getInsightColor = (impact: string) => {
    switch (impact) {
      case 'critical': return 'border-crimson-alert bg-crimson-alert/10';
      case 'high': return 'border-amber-data bg-amber-data/10';
      case 'medium': return 'border-bloom-highlight bg-bloom-highlight/10';
      case 'low': return 'border-growth-accent bg-growth-accent/10';
      default: return 'border-earth-base/20 bg-mist-gradient';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-h2 text-forest-primary">📊 Analytics Forest</h1>
          <p className="text-earth-base/70">
            🔒 <span className="font-medium">Admin Dashboard</span> - Deep insights into system performance and usage
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-3 py-2 border border-growth-accent/20 rounded-lg bg-mist-gradient focus:outline-none focus:ring-2 focus:ring-growth-accent/50"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
          
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={`px-4 py-2 rounded-lg transition-all hover-bloom ${
              autoRefresh 
                ? 'bg-growth-accent text-crystal-clear' 
                : 'border border-growth-accent/20 text-earth-base hover:bg-growth-accent/5'
            }`}
          >
            {autoRefresh ? '🔄 Auto' : '⏸️ Manual'}
          </button>
          
          <button
            onClick={generateReport}
            className="px-4 py-2 border border-growth-accent/20 rounded-lg text-earth-base hover:bg-growth-accent/5 transition-all hover-bloom"
          >
            📄 Export Report
          </button>
          
          <button
            onClick={loadAnalyticsData}
            disabled={isLoading}
            className="px-6 py-3 bg-growth-gradient text-crystal-clear rounded-lg font-medium hover-bloom transition-all disabled:opacity-50"
          >
            {isLoading ? '🔄 Loading...' : '🔄 Refresh'}
          </button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-8 gap-4">
        {[
          { 
            label: 'Total Users', 
            value: metrics.total_users.toLocaleString(), 
            icon: '👥', 
            color: 'text-forest-primary',
            change: '+12.5%'
          },
          { 
            label: 'Active Sessions', 
            value: metrics.active_sessions, 
            icon: '🟢', 
            color: getMetricColor(metrics.active_sessions, { good: 20, warning: 10 }),
            change: '+5.2%'
          },
          { 
            label: 'Requests/min', 
            value: metrics.requests_per_minute, 
            icon: '⚡', 
            color: getMetricColor(metrics.requests_per_minute, { good: 100, warning: 50 }),
            change: '+8.7%'
          },
          { 
            label: 'Response Time', 
            value: `${metrics.avg_response_time}ms`, 
            icon: '⏱️', 
            color: getMetricColor(500 - metrics.avg_response_time, { good: 250, warning: 150 }),
            change: '-3.1%'
          },
          { 
            label: 'Error Rate', 
            value: `${metrics.error_rate}%`, 
            icon: '❌', 
            color: getMetricColor(10 - metrics.error_rate, { good: 9, warning: 7 }),
            change: '-0.3%'
          },
          { 
            label: 'Uptime', 
            value: `${metrics.uptime_percentage}%`, 
            icon: '🚀', 
            color: getMetricColor(metrics.uptime_percentage, { good: 99, warning: 95 }),
            change: '+0.1%'
          },
          { 
            label: 'Peak Users', 
            value: metrics.peak_concurrent_users, 
            icon: '📈', 
            color: 'text-bloom-highlight',
            change: '+15.3%'
          },
          { 
            label: 'Tokens Processed', 
            value: `${(metrics.total_tokens_processed / 1000000).toFixed(1)}M`, 
            icon: '🔤', 
            color: 'text-golden-success',
            change: '+23.8%'
          }
        ].map((metric) => (
          <div key={metric.label} className="bg-crystal-clear rounded-lg p-4 border border-growth-accent/20 hover-bloom">
            <div className="flex items-center justify-between mb-2">
              <span className="text-2xl">{metric.icon}</span>
              <span className={`text-xs font-medium ${
                metric.change.startsWith('+') ? 'text-growth-accent' : 'text-crimson-alert'
              }`}>
                {metric.change}
              </span>
            </div>
            <div>
              <p className="text-xs text-earth-base/60 mb-1">{metric.label}</p>
              <p className={`text-lg font-bold ${metric.color}`}>{metric.value}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-crystal-clear rounded-lg p-6 border border-growth-accent/20">
          <h3 className="text-lg font-semibold text-forest-primary mb-4">📈 Performance Trends</h3>
          <div className="h-64 flex items-end justify-between space-x-1">
            {performanceData.slice(-24).map((data, index) => (
              <div key={index} className="flex-1 flex flex-col items-center space-y-1">
                <div className="w-full bg-frost-light rounded-t">
                  <div 
                    className="bg-growth-gradient rounded-t transition-all duration-500"
                    style={{ height: `${(data.cpu_usage / 100) * 100}px` }}
                    title={`CPU: ${data.cpu_usage.toFixed(1)}%`}
                  />
                </div>
                <div className="w-full bg-frost-light">
                  <div 
                    className="bg-bloom-highlight rounded transition-all duration-500"
                    style={{ height: `${(data.memory_usage / 100) * 80}px` }}
                    title={`Memory: ${data.memory_usage.toFixed(1)}%`}
                  />
                </div>
                <span className="text-xs text-earth-base/60 transform -rotate-45">
                  {data.timestamp.getHours()}:00
                </span>
              </div>
            ))}
          </div>
          <div className="flex items-center justify-center space-x-4 mt-4 text-sm">
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 bg-growth-gradient rounded"></div>
              <span className="text-earth-base/70">CPU Usage</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 bg-bloom-highlight rounded"></div>
              <span className="text-earth-base/70">Memory Usage</span>
            </div>
          </div>
        </div>

        <div className="bg-crystal-clear rounded-lg p-6 border border-growth-accent/20">
          <h3 className="text-lg font-semibold text-forest-primary mb-4">🎯 Feature Usage</h3>
          <div className="space-y-4">
            {featureUsage.map((feature, index) => (
              <div key={feature.feature_name} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-earth-base">{feature.feature_name}</span>
                  <div className="flex items-center space-x-2 text-xs text-earth-base/60">
                    <span>{feature.usage_count.toLocaleString()} uses</span>
                    <span className={`font-medium ${
                      feature.growth_rate > 0 ? 'text-growth-accent' : 'text-crimson-alert'
                    }`}>
                      {feature.growth_rate > 0 ? '+' : ''}{feature.growth_rate.toFixed(1)}%
                    </span>
                  </div>
                </div>
                <div className="w-full bg-frost-light rounded-full h-2">
                  <div 
                    className="bg-growth-gradient h-2 rounded-full transition-all duration-1000"
                    style={{ width: `${(feature.usage_count / Math.max(...featureUsage.map(f => f.usage_count))) * 100}%` }}
                  />
                </div>
                <div className="flex items-center justify-between text-xs text-earth-base/60">
                  <span>{feature.unique_users} users</span>
                  <span>{feature.success_rate.toFixed(1)}% success</span>
                  <span>{feature.avg_session_duration.toFixed(1)}m avg</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* User Journey Funnel */}
      <div className="bg-crystal-clear rounded-lg p-6 border border-growth-accent/20">
        <h3 className="text-lg font-semibold text-forest-primary mb-4">🚀 User Journey Funnel</h3>
        <div className="flex items-end justify-between space-x-2">
          {userJourney.map((step, index) => (
            <div key={step.step} className="flex-1 text-center">
              <div className="relative">
                <div 
                  className={`mx-auto rounded-t-lg transition-all duration-1000 ${
                    index === 0 ? 'bg-forest-primary' :
                    index === 1 ? 'bg-growth-accent' :
                    index === 2 ? 'bg-bloom-highlight' :
                    index === 3 ? 'bg-golden-success' :
                    index === 4 ? 'bg-amber-data' :
                    'bg-crimson-alert'
                  }`}
                  style={{ 
                    height: `${(step.users_count / userJourney[0].users_count) * 200}px`,
                    width: `${Math.max(60 - index * 8, 20)}%`
                  }}
                />
                <div className="absolute top-2 left-1/2 transform -translate-x-1/2 text-crystal-clear text-sm font-bold">
                  {step.users_count}
                </div>
              </div>
              <div className="mt-3 space-y-1">
                <div className="font-medium text-earth-base text-sm">{step.step}</div>
                <div className="text-xs text-earth-base/60">
                  {step.conversion_rate.toFixed(1)}% conversion
                </div>
                <div className="text-xs text-earth-base/60">
                  {step.avg_time_spent}s avg time
                </div>
                {step.drop_off_rate > 0 && (
                  <div className="text-xs text-crimson-alert">
                    {step.drop_off_rate.toFixed(1)}% drop-off
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* AI Insights */}
      <div className="bg-crystal-clear rounded-lg p-6 border border-growth-accent/20">
        <h3 className="text-lg font-semibold text-forest-primary mb-4">🤖 AI Insights & Recommendations</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {insights.map((insight) => (
            <div
              key={insight.id}
              className={`p-4 rounded-lg border-2 transition-all hover-bloom ${getInsightColor(insight.impact)}`}
            >
              <div className="flex items-start justify-between mb-2">
                <span className="text-2xl">{getInsightIcon(insight.type)}</span>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 rounded text-xs font-medium capitalize ${
                    insight.impact === 'critical' ? 'bg-crimson-alert text-crystal-clear' :
                    insight.impact === 'high' ? 'bg-amber-data text-crystal-clear' :
                    insight.impact === 'medium' ? 'bg-bloom-highlight text-earth-base' :
                    'bg-growth-accent text-crystal-clear'
                  }`}>
                    {insight.impact}
                  </span>
                  {insight.action_required && (
                    <span className="w-2 h-2 bg-crimson-alert rounded-full animate-breathe" title="Action Required" />
                  )}
                </div>
              </div>
              <h4 className="font-semibold text-forest-primary mb-2">{insight.title}</h4>
              <p className="text-sm text-earth-base/80 mb-3">{insight.description}</p>
              <div className="flex items-center justify-between text-xs text-earth-base/60">
                <span>{insight.timestamp.toLocaleString()}</span>
                {insight.action_required && (
                  <button className="px-2 py-1 bg-growth-gradient text-crystal-clear rounded text-xs hover-bloom transition-all">
                    Take Action
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {isLoading && (
        <div className="fixed inset-0 bg-shadow-deep/20 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-crystal-clear rounded-xl p-8 animate-bloom">
            <div className="flex items-center space-x-3">
              <div className="w-4 h-4 bg-growth-accent rounded-full animate-bounce"></div>
              <div className="w-4 h-4 bg-growth-accent rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
              <div className="w-4 h-4 bg-growth-accent rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
              <span className="text-forest-primary font-medium">Loading analytics data...</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
