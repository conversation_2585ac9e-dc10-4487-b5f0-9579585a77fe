"""
Relationship Mapper for Code Intelligence Hub - Phase 8B

Maps code relationships and dependencies to create intelligent code graphs.
"""

import logging
import re
from typing import List, Dict, Optional, Any, Set, Tuple
from collections import defaultdict

from .models import (
    CodeFile, RelationshipGraph, CodeRelationship, RelationshipType,
    CodeLanguage, CodeElementType
)

logger = logging.getLogger(__name__)


class RelationshipMapper:
    """
    Code relationship mapper for building dependency graphs.
    
    Analyzes code files to identify relationships between functions, classes,
    modules, and files to create intelligent code dependency graphs.
    """
    
    def __init__(self):
        """Initialize the relationship mapper."""
        self.relationship_cache: Dict[str, RelationshipGraph] = {}
        
        logger.info("🔗 Relationship mapper initialized")
    
    async def initialize(self):
        """Initialize the relationship mapper."""
        try:
            logger.info("✅ Relationship mapper ready")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize relationship mapper: {e}")
            raise
    
    async def build_graph(self, code_files: List[CodeFile]) -> RelationshipGraph:
        """
        Build a comprehensive relationship graph from code files.
        
        Args:
            code_files: List of CodeFile objects to analyze
            
        Returns:
            RelationshipGraph with all relationships
        """
        try:
            logger.info(f"🔗 Building relationship graph for {len(code_files)} files")
            
            graph = RelationshipGraph()
            
            # Add all files as nodes
            for code_file in code_files:
                await self._add_file_node(graph, code_file)
            
            # Analyze relationships between files
            for code_file in code_files:
                await self._analyze_file_relationships(graph, code_file, code_files)
            
            # Analyze internal relationships within files
            for code_file in code_files:
                await self._analyze_internal_relationships(graph, code_file)
            
            # Calculate relationship strengths
            await self._calculate_relationship_strengths(graph)
            
            logger.info(f"✅ Built relationship graph: {len(graph.nodes)} nodes, {len(graph.edges)} edges")
            
            return graph
            
        except Exception as e:
            logger.error(f"❌ Failed to build relationship graph: {e}")
            return RelationshipGraph()
    
    async def get_file_relationships(self, code_file: CodeFile) -> RelationshipGraph:
        """
        Get relationship graph for a specific file.
        
        Args:
            code_file: CodeFile to analyze
            
        Returns:
            RelationshipGraph for the file
        """
        try:
            # Check cache first
            if code_file.file_id in self.relationship_cache:
                return self.relationship_cache[code_file.file_id]
            
            graph = RelationshipGraph()
            
            # Add file node
            await self._add_file_node(graph, code_file)
            
            # Analyze internal relationships
            await self._analyze_internal_relationships(graph, code_file)
            
            # Cache result
            self.relationship_cache[code_file.file_id] = graph
            
            return graph
            
        except Exception as e:
            logger.error(f"❌ Failed to get file relationships: {e}")
            return RelationshipGraph()
    
    async def find_dependencies(self, file_path: str, code_files: List[CodeFile]) -> List[str]:
        """
        Find all dependencies of a specific file.
        
        Args:
            file_path: Path to the file
            code_files: List of all code files
            
        Returns:
            List of file paths that this file depends on
        """
        try:
            # Find the target file
            target_file = None
            for code_file in code_files:
                if code_file.path == file_path:
                    target_file = code_file
                    break
            
            if not target_file:
                return []
            
            dependencies = []
            
            # Analyze imports and dependencies
            for dependency in target_file.dependencies:
                # Find matching files
                for code_file in code_files:
                    if self._matches_dependency(dependency, code_file):
                        dependencies.append(code_file.path)
            
            return list(set(dependencies))
            
        except Exception as e:
            logger.error(f"❌ Failed to find dependencies: {e}")
            return []
    
    async def find_dependents(self, file_path: str, code_files: List[CodeFile]) -> List[str]:
        """
        Find all files that depend on a specific file.
        
        Args:
            file_path: Path to the file
            code_files: List of all code files
            
        Returns:
            List of file paths that depend on this file
        """
        try:
            dependents = []
            
            # Find the target file
            target_file = None
            for code_file in code_files:
                if code_file.path == file_path:
                    target_file = code_file
                    break
            
            if not target_file:
                return []
            
            # Check which files import or depend on this file
            for code_file in code_files:
                if code_file.file_id == target_file.file_id:
                    continue
                
                # Check if this file depends on the target
                for dependency in code_file.dependencies:
                    if self._matches_dependency(dependency, target_file):
                        dependents.append(code_file.path)
                        break
            
            return list(set(dependents))
            
        except Exception as e:
            logger.error(f"❌ Failed to find dependents: {e}")
            return []
    
    async def analyze_impact(self, file_path: str, code_files: List[CodeFile]) -> Dict[str, Any]:
        """
        Analyze the impact of changes to a specific file.
        
        Args:
            file_path: Path to the file
            code_files: List of all code files
            
        Returns:
            Impact analysis results
        """
        try:
            dependencies = await self.find_dependencies(file_path, code_files)
            dependents = await self.find_dependents(file_path, code_files)
            
            # Calculate impact score
            impact_score = len(dependents) * 2 + len(dependencies)
            
            # Categorize impact level
            if impact_score > 20:
                impact_level = "high"
            elif impact_score > 10:
                impact_level = "medium"
            elif impact_score > 5:
                impact_level = "low"
            else:
                impact_level = "minimal"
            
            return {
                'file_path': file_path,
                'dependencies': dependencies,
                'dependents': dependents,
                'impact_score': impact_score,
                'impact_level': impact_level,
                'total_affected_files': len(dependencies) + len(dependents)
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to analyze impact: {e}")
            return {
                'file_path': file_path,
                'error': str(e)
            }
    
    async def _add_file_node(self, graph: RelationshipGraph, code_file: CodeFile):
        """Add a file node to the graph."""
        try:
            node_data = {
                'type': 'file',
                'path': code_file.path,
                'language': code_file.language.value,
                'size': code_file.size,
                'functions': code_file.functions,
                'classes': code_file.classes,
                'imports': code_file.imports,
                'dependencies': code_file.dependencies
            }
            
            graph.add_node(code_file.file_id, node_data)
            
            # Add function nodes
            for func_name in code_file.functions:
                func_id = f"{code_file.file_id}_func_{func_name}"
                func_data = {
                    'type': 'function',
                    'name': func_name,
                    'file_id': code_file.file_id,
                    'file_path': code_file.path
                }
                graph.add_node(func_id, func_data)
                
                # Add relationship from file to function
                relationship = CodeRelationship(
                    source_id=code_file.file_id,
                    target_id=func_id,
                    relationship_type=RelationshipType.DEFINES
                )
                graph.add_edge(relationship)
            
            # Add class nodes
            for class_name in code_file.classes:
                class_id = f"{code_file.file_id}_class_{class_name}"
                class_data = {
                    'type': 'class',
                    'name': class_name,
                    'file_id': code_file.file_id,
                    'file_path': code_file.path
                }
                graph.add_node(class_id, class_data)
                
                # Add relationship from file to class
                relationship = CodeRelationship(
                    source_id=code_file.file_id,
                    target_id=class_id,
                    relationship_type=RelationshipType.DEFINES
                )
                graph.add_edge(relationship)
            
        except Exception as e:
            logger.debug(f"Failed to add file node: {e}")
    
    async def _analyze_file_relationships(self, graph: RelationshipGraph, code_file: CodeFile, all_files: List[CodeFile]):
        """Analyze relationships between files."""
        try:
            # Analyze import relationships
            for dependency in code_file.dependencies:
                # Find matching files
                for other_file in all_files:
                    if other_file.file_id == code_file.file_id:
                        continue
                    
                    if self._matches_dependency(dependency, other_file):
                        relationship = CodeRelationship(
                            source_id=code_file.file_id,
                            target_id=other_file.file_id,
                            relationship_type=RelationshipType.IMPORTS,
                            metadata={'dependency': dependency}
                        )
                        graph.add_edge(relationship)
            
        except Exception as e:
            logger.debug(f"Failed to analyze file relationships: {e}")
    
    async def _analyze_internal_relationships(self, graph: RelationshipGraph, code_file: CodeFile):
        """Analyze relationships within a file."""
        try:
            # This would analyze function calls, class inheritance, etc.
            # For now, we'll implement basic patterns
            
            if code_file.language == CodeLanguage.PYTHON:
                await self._analyze_python_internal_relationships(graph, code_file)
            elif code_file.language in [CodeLanguage.JAVASCRIPT, CodeLanguage.TYPESCRIPT]:
                await self._analyze_javascript_internal_relationships(graph, code_file)
            
        except Exception as e:
            logger.debug(f"Failed to analyze internal relationships: {e}")
    
    async def _analyze_python_internal_relationships(self, graph: RelationshipGraph, code_file: CodeFile):
        """Analyze internal relationships in Python files."""
        try:
            # Read file content
            with open(code_file.absolute_path, 'r', encoding=code_file.encoding, errors='ignore') as f:
                content = f.read()
            
            # Find function calls
            for func_name in code_file.functions:
                func_id = f"{code_file.file_id}_func_{func_name}"
                
                # Look for calls to other functions in the same file
                for other_func in code_file.functions:
                    if func_name != other_func:
                        # Simple pattern matching for function calls
                        pattern = rf'\b{other_func}\s*\('
                        if re.search(pattern, content):
                            other_func_id = f"{code_file.file_id}_func_{other_func}"
                            relationship = CodeRelationship(
                                source_id=func_id,
                                target_id=other_func_id,
                                relationship_type=RelationshipType.CALLS
                            )
                            graph.add_edge(relationship)
            
            # Find class inheritance
            class_pattern = r'class\s+(\w+)\s*\(\s*(\w+)\s*\)'
            matches = re.finditer(class_pattern, content)
            for match in matches:
                child_class = match.group(1)
                parent_class = match.group(2)
                
                if child_class in code_file.classes:
                    child_id = f"{code_file.file_id}_class_{child_class}"
                    
                    # Check if parent class is in the same file
                    if parent_class in code_file.classes:
                        parent_id = f"{code_file.file_id}_class_{parent_class}"
                        relationship = CodeRelationship(
                            source_id=child_id,
                            target_id=parent_id,
                            relationship_type=RelationshipType.INHERITS
                        )
                        graph.add_edge(relationship)
            
        except Exception as e:
            logger.debug(f"Failed to analyze Python internal relationships: {e}")
    
    async def _analyze_javascript_internal_relationships(self, graph: RelationshipGraph, code_file: CodeFile):
        """Analyze internal relationships in JavaScript/TypeScript files."""
        try:
            # Read file content
            with open(code_file.absolute_path, 'r', encoding=code_file.encoding, errors='ignore') as f:
                content = f.read()
            
            # Find function calls (simplified)
            for func_name in code_file.functions:
                func_id = f"{code_file.file_id}_func_{func_name}"
                
                # Look for calls to other functions
                for other_func in code_file.functions:
                    if func_name != other_func:
                        pattern = rf'\b{other_func}\s*\('
                        if re.search(pattern, content):
                            other_func_id = f"{code_file.file_id}_func_{other_func}"
                            relationship = CodeRelationship(
                                source_id=func_id,
                                target_id=other_func_id,
                                relationship_type=RelationshipType.CALLS
                            )
                            graph.add_edge(relationship)
            
            # Find class inheritance (ES6 classes)
            class_pattern = r'class\s+(\w+)\s+extends\s+(\w+)'
            matches = re.finditer(class_pattern, content)
            for match in matches:
                child_class = match.group(1)
                parent_class = match.group(2)
                
                if child_class in code_file.classes:
                    child_id = f"{code_file.file_id}_class_{child_class}"
                    
                    if parent_class in code_file.classes:
                        parent_id = f"{code_file.file_id}_class_{parent_class}"
                        relationship = CodeRelationship(
                            source_id=child_id,
                            target_id=parent_id,
                            relationship_type=RelationshipType.INHERITS
                        )
                        graph.add_edge(relationship)
            
        except Exception as e:
            logger.debug(f"Failed to analyze JavaScript internal relationships: {e}")
    
    async def _calculate_relationship_strengths(self, graph: RelationshipGraph):
        """Calculate relationship strengths based on usage patterns."""
        try:
            # Count relationships between nodes
            relationship_counts = defaultdict(int)
            
            for edge in graph.edges:
                key = (edge.source_id, edge.target_id, edge.relationship_type)
                relationship_counts[key] += 1
            
            # Update relationship strengths
            for edge in graph.edges:
                key = (edge.source_id, edge.target_id, edge.relationship_type)
                count = relationship_counts[key]
                
                # Calculate strength based on count and relationship type
                base_strength = {
                    RelationshipType.IMPORTS: 1.0,
                    RelationshipType.CALLS: 0.8,
                    RelationshipType.INHERITS: 1.2,
                    RelationshipType.IMPLEMENTS: 1.1,
                    RelationshipType.USES: 0.6,
                    RelationshipType.DEFINES: 1.0,
                    RelationshipType.REFERENCES: 0.4
                }.get(edge.relationship_type, 0.5)
                
                edge.strength = min(base_strength * count, 2.0)  # Cap at 2.0
            
        except Exception as e:
            logger.debug(f"Failed to calculate relationship strengths: {e}")
    
    def _matches_dependency(self, dependency: str, code_file: CodeFile) -> bool:
        """Check if a dependency matches a code file."""
        try:
            # Simple matching logic
            file_name = code_file.path.split('/')[-1]
            file_name_no_ext = file_name.rsplit('.', 1)[0]
            
            # Check various matching patterns
            if dependency == file_name_no_ext:
                return True
            
            if dependency in code_file.path:
                return True
            
            # Check for module-style imports
            if '.' in dependency:
                parts = dependency.split('.')
                if any(part in code_file.path for part in parts):
                    return True
            
            return False
            
        except Exception:
            return False
