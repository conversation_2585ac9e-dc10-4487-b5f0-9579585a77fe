#!/usr/bin/env python3
"""
Core Task Runner for AI Coder Agent - Phase 5
Provides AI-driven task execution with lint/test cycles and git integration.

Features:
- Main execution loop with error handling
- LLM JSON parsing with retry logic
- Tool execution with Phase 3 integration
- Lint/test iteration cycles
- Commit operations on success
- Task context generation using Phase 3 capabilities
"""

import asyncio
import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from concurrent.futures import ThreadPoolExecutor
import uuid

# Import Phase 3 modules
from app.agent.file_operations import file_operations
from app.agent.git_operations import git_operations
from app.agent.code_analyzer import code_analyzer
from app.agent.change_tracker import change_tracker
from app.agent.repo_context import repo_context
from app.agent.context_manager import context_manager

# Import Phase 2 clients
from llm_client import openrouter_client
from utils.lint_test_utils import lint_test_utils

logger = logging.getLogger(__name__)

class TaskRunner:
    """Core task runner for AI-driven coding workflows."""
    
    def __init__(self, workspace_root: Union[str, Path] = "/app/workspace"):
        self.workspace_root = Path(workspace_root)
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Task execution state
        self.current_task = None
        self.task_history = []
        self.execution_stats = {
            'tasks_completed': 0,
            'tasks_failed': 0,
            'total_execution_time': 0,
            'lint_test_cycles': 0
        }
        
        # Available tools for LLM
        self.available_tools = {
            'read_file': self._tool_read_file,
            'write_file': self._tool_write_file,
            'create_file': self._tool_create_file,
            'delete_file': self._tool_delete_file,
            'list_directory': self._tool_list_directory,
            'git_status': self._tool_git_status,
            'git_diff': self._tool_git_diff,
            'git_commit': self._tool_git_commit,
            'run_linting': self._tool_run_linting,
            'run_tests': self._tool_run_tests,
            'search_code': self._tool_search_code,
            'analyze_code': self._tool_analyze_code,
            'get_project_context': self._tool_get_project_context
        }
    
    def _validate_path(self, path: Union[str, Path]) -> Path:
        """Validate and resolve path within workspace."""
        path = Path(path).resolve()
        
        # Ensure path is within workspace
        try:
            path.relative_to(self.workspace_root.resolve())
        except ValueError:
            raise ValueError(f"Path {path} is outside workspace {self.workspace_root}")
        
        return path
    
    async def execute_task(self, task_description: str, 
                          project_path: Union[str, Path],
                          max_iterations: int = 3,
                          timeout_seconds: int = 300) -> Dict[str, Any]:
        """
        Execute a coding task with AI-driven workflow.
        
        Args:
            task_description: Description of the task to execute
            project_path: Path to the project directory
            max_iterations: Maximum lint/test/fix iterations
            timeout_seconds: Maximum execution time
            
        Returns:
            Dict with task execution results
        """
        task_id = str(uuid.uuid4())
        start_time = time.time()
        
        try:
            project_path = self._validate_path(project_path)
            
            # Initialize task
            task = {
                'id': task_id,
                'description': task_description,
                'project_path': str(project_path),
                'start_time': start_time,
                'status': 'running',
                'iterations': 0,
                'max_iterations': max_iterations,
                'timeout_seconds': timeout_seconds,
                'changes_made': [],
                'lint_test_results': [],
                'error_log': []
            }
            
            self.current_task = task
            logger.info(f"Starting task {task_id}: {task_description}")
            
            # Generate initial context
            context_result = await self.generate_task_context(project_path, task_description)
            if context_result['status'] != 'success':
                raise Exception(f"Failed to generate task context: {context_result.get('error')}")
            
            task_context = context_result['context']
            
            # Main execution loop
            for iteration in range(max_iterations):
                task['iterations'] = iteration + 1
                
                # Check timeout
                if time.time() - start_time > timeout_seconds:
                    raise TimeoutError(f"Task execution timed out after {timeout_seconds} seconds")
                
                logger.info(f"Task {task_id} iteration {iteration + 1}/{max_iterations}")
                
                # Generate LLM prompt
                prompt = self._build_task_prompt(task_description, task_context, task, iteration)
                
                # Get LLM response with tool calls
                llm_response = await self.parse_llm_response(prompt, task_id)
                if llm_response['status'] != 'success':
                    task['error_log'].append(f"LLM response error: {llm_response.get('error')}")
                    continue
                
                # Execute tool calls
                tool_results = await self.execute_tool_calls(llm_response['tool_calls'], task_id)
                task['changes_made'].extend(tool_results.get('changes', []))
                
                # Run lint/test cycle
                lint_test_result = await self.run_lint_test_cycle(project_path, task_id)
                task['lint_test_results'].append(lint_test_result)
                self.execution_stats['lint_test_cycles'] += 1
                
                # Check if task is complete
                if lint_test_result.get('overall_passed', False):
                    logger.info(f"Task {task_id} completed successfully after {iteration + 1} iterations")
                    task['status'] = 'completed'
                    
                    # Commit changes if successful
                    commit_result = await self.commit_changes(project_path, task_description, task_id)
                    task['commit_result'] = commit_result
                    
                    break
                else:
                    # Update context with lint/test feedback
                    task_context = await self._update_context_with_feedback(
                        task_context, lint_test_result, iteration
                    )
            
            # Final status
            if task['status'] == 'running':
                task['status'] = 'failed'
                task['error_log'].append(f"Task failed after {max_iterations} iterations")
            
            # Update statistics
            execution_time = time.time() - start_time
            task['execution_time'] = execution_time
            self.execution_stats['total_execution_time'] += execution_time
            
            if task['status'] == 'completed':
                self.execution_stats['tasks_completed'] += 1
            else:
                self.execution_stats['tasks_failed'] += 1
            
            # Add to history
            self.task_history.append(task)
            self.current_task = None
            
            return {
                'status': 'success',
                'task': task,
                'execution_time': execution_time
            }
            
        except Exception as e:
            logger.error(f"Task execution failed for {task_id}: {e}")
            
            # Update task status
            if self.current_task:
                self.current_task['status'] = 'error'
                self.current_task['error'] = str(e)
                self.current_task['execution_time'] = time.time() - start_time
                self.task_history.append(self.current_task)
                self.execution_stats['tasks_failed'] += 1
            
            self.current_task = None
            
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__,
                'task_id': task_id,
                'execution_time': time.time() - start_time
            }
    
    def _build_task_prompt(self, task_description: str, context: Dict[str, Any], 
                          task: Dict[str, Any], iteration: int) -> str:
        """Build prompt for LLM with task context and available tools."""
        
        tools_description = "\n".join([
            f"- {name}: {func.__doc__.split('.')[0] if func.__doc__ else 'No description'}"
            for name, func in self.available_tools.items()
        ])
        
        previous_results = ""
        if task['lint_test_results']:
            latest_result = task['lint_test_results'][-1]
            if not latest_result.get('overall_passed', False):
                previous_results = f"""
Previous iteration results:
- Linting passed: {latest_result.get('linting', {}).get('passed', False)}
- Tests passed: {latest_result.get('testing', {}).get('passed', False)}
- Issues found: {latest_result.get('linting', {}).get('issues_count', 0)} linting issues
- Failed tests: {latest_result.get('testing', {}).get('tests_failed', 0)}

Please fix these issues in this iteration.
"""
        
        prompt = f"""You are an AI coding assistant. Execute the following task:

TASK: {task_description}

PROJECT CONTEXT:
{json.dumps(context, indent=2)}

AVAILABLE TOOLS:
{tools_description}

{previous_results}

INSTRUCTIONS:
1. Analyze the project structure and existing code
2. Make necessary changes to complete the task
3. Ensure code quality and follow best practices
4. Use appropriate tools to read, modify, and test code
5. Return your response as JSON with tool calls

RESPONSE FORMAT:
{{
    "analysis": "Your analysis of what needs to be done",
    "tool_calls": [
        {{
            "tool": "tool_name",
            "args": {{
                "arg1": "value1",
                "arg2": "value2"
            }},
            "reasoning": "Why you're using this tool"
        }}
    ],
    "expected_outcome": "What you expect to achieve"
}}

Iteration: {iteration + 1}/{task['max_iterations']}
"""
        
        return prompt

    async def parse_llm_response(self, prompt: str, task_id: str,
                                max_retries: int = 3) -> Dict[str, Any]:
        """
        Parse LLM response with JSON validation and retry logic.

        Args:
            prompt: Prompt to send to LLM
            task_id: Task ID for logging
            max_retries: Maximum retry attempts

        Returns:
            Dict with parsed response
        """
        try:
            for attempt in range(max_retries):
                try:
                    # Get LLM response
                    response = await openrouter_client.chat_completion(
                        messages=[{"role": "user", "content": prompt}],
                        model="deepseek/deepseek-r1-0528:free",
                        temperature=0.2
                    )

                    if response['status'] != 'success':
                        raise Exception(f"LLM API error: {response.get('error')}")

                    content = response['content']

                    # Try to parse JSON
                    try:
                        parsed_response = json.loads(content)

                        # Validate required fields
                        if 'tool_calls' not in parsed_response:
                            raise ValueError("Missing 'tool_calls' field in response")

                        if not isinstance(parsed_response['tool_calls'], list):
                            raise ValueError("'tool_calls' must be a list")

                        return {
                            'status': 'success',
                            'tool_calls': parsed_response['tool_calls'],
                            'analysis': parsed_response.get('analysis', ''),
                            'expected_outcome': parsed_response.get('expected_outcome', ''),
                            'raw_response': content,
                            'attempt': attempt + 1
                        }

                    except json.JSONDecodeError as e:
                        logger.warning(f"Task {task_id} attempt {attempt + 1}: Invalid JSON: {e}")
                        if attempt == max_retries - 1:
                            raise

                        # Retry with clarification
                        prompt += f"\n\nPREVIOUS ATTEMPT FAILED: Invalid JSON format. Please ensure your response is valid JSON."
                        continue

                except Exception as e:
                    logger.error(f"Task {task_id} attempt {attempt + 1}: LLM error: {e}")
                    if attempt == max_retries - 1:
                        raise

                    await asyncio.sleep(1)  # Brief delay before retry

            return {
                'status': 'error',
                'error': f'Failed to get valid LLM response after {max_retries} attempts',
                'error_type': 'LLMParsingError'
            }

        except Exception as e:
            logger.error(f"Failed to parse LLM response for task {task_id}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def execute_tool_calls(self, tool_calls: List[Dict[str, Any]],
                                task_id: str) -> Dict[str, Any]:
        """
        Execute tool calls with Phase 3 integration.

        Args:
            tool_calls: List of tool calls from LLM
            task_id: Task ID for logging

        Returns:
            Dict with execution results
        """
        try:
            results = []
            changes = []

            for i, tool_call in enumerate(tool_calls):
                try:
                    tool_name = tool_call.get('tool')
                    tool_args = tool_call.get('args', {})
                    reasoning = tool_call.get('reasoning', '')

                    if tool_name not in self.available_tools:
                        results.append({
                            'tool': tool_name,
                            'status': 'error',
                            'error': f'Unknown tool: {tool_name}',
                            'reasoning': reasoning
                        })
                        continue

                    logger.info(f"Task {task_id} executing tool {tool_name}: {reasoning}")

                    # Execute tool
                    tool_func = self.available_tools[tool_name]
                    result = await tool_func(**tool_args)

                    results.append({
                        'tool': tool_name,
                        'args': tool_args,
                        'reasoning': reasoning,
                        'result': result,
                        'status': result.get('status', 'unknown')
                    })

                    # Track changes
                    if tool_name in ['write_file', 'create_file', 'delete_file']:
                        changes.append({
                            'tool': tool_name,
                            'file': tool_args.get('file_path', 'unknown'),
                            'timestamp': time.time()
                        })

                except Exception as e:
                    logger.error(f"Task {task_id} tool {tool_name} failed: {e}")
                    results.append({
                        'tool': tool_name,
                        'status': 'error',
                        'error': str(e),
                        'error_type': type(e).__name__,
                        'reasoning': reasoning
                    })

            return {
                'status': 'success',
                'results': results,
                'changes': changes,
                'tools_executed': len(tool_calls)
            }

        except Exception as e:
            logger.error(f"Failed to execute tool calls for task {task_id}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def run_lint_test_cycle(self, project_path: Path, task_id: str) -> Dict[str, Any]:
        """
        Run lint/test cycle for iterative improvement.

        Args:
            project_path: Path to project directory
            task_id: Task ID for logging

        Returns:
            Dict with lint/test results
        """
        try:
            logger.info(f"Task {task_id} running lint/test cycle")

            # Run full check (linting and testing)
            result = await lint_test_utils.run_full_check(project_path)

            if result['status'] != 'success':
                return result

            # Add summary for LLM consumption
            result['summary'] = {
                'overall_passed': result.get('overall_passed', False),
                'linting_passed': result.get('linting', {}).get('passed', False),
                'testing_passed': result.get('testing', {}).get('passed', False),
                'issues_to_fix': []
            }

            # Collect issues for LLM
            if not result['summary']['linting_passed']:
                linting_issues = result.get('linting', {}).get('issues', [])
                for issue in linting_issues[:5]:  # Limit to top 5 issues
                    result['summary']['issues_to_fix'].append({
                        'type': 'linting',
                        'file': issue.get('file', 'unknown'),
                        'line': issue.get('line', 0),
                        'message': issue.get('message', 'Unknown issue')
                    })

            if not result['summary']['testing_passed']:
                failed_tests = result.get('testing', {}).get('failed_tests', [])
                for test in failed_tests[:3]:  # Limit to top 3 failed tests
                    result['summary']['issues_to_fix'].append({
                        'type': 'testing',
                        'test': test,
                        'message': f'Test failed: {test}'
                    })

            return result

        except Exception as e:
            logger.error(f"Failed to run lint/test cycle for task {task_id}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def commit_changes(self, project_path: Path, task_description: str,
                           task_id: str) -> Dict[str, Any]:
        """
        Commit changes on successful task completion.

        Args:
            project_path: Path to project directory
            task_description: Description of the task
            task_id: Task ID for logging

        Returns:
            Dict with commit results
        """
        try:
            logger.info(f"Task {task_id} committing changes")

            # Check git status
            status_result = await git_operations.get_git_status(project_path)
            if status_result['status'] != 'success':
                return {
                    'status': 'skipped',
                    'reason': 'Not a git repository or git error',
                    'error': status_result.get('error')
                }

            # Check if there are changes to commit
            if not status_result.get('modified_files') and not status_result.get('untracked_files'):
                return {
                    'status': 'skipped',
                    'reason': 'No changes to commit'
                }

            # Create commit message
            commit_message = f"AI Agent: {task_description}\n\nTask ID: {task_id}\nGenerated by AI Coder Agent Phase 5"

            # Commit changes
            commit_result = await git_operations.git_commit(
                project_path,
                message=commit_message,
                add_all=True
            )

            return commit_result

        except Exception as e:
            logger.error(f"Failed to commit changes for task {task_id}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def generate_task_context(self, project_path: Path,
                                  task_description: str) -> Dict[str, Any]:
        """
        Generate task context using Phase 3 capabilities.

        Args:
            project_path: Path to project directory
            task_description: Description of the task

        Returns:
            Dict with task context
        """
        try:
            # Get project structure and context
            context_result = await repo_context.generate_project_summary(project_path)
            if context_result['status'] != 'success':
                return context_result

            # Get task-specific context
            task_context_result = await repo_context.generate_task_context(
                project_path, task_description
            )

            if task_context_result['status'] != 'success':
                return task_context_result

            # Combine contexts
            combined_context = {
                'project_summary': context_result.get('summary', {}),
                'task_context': task_context_result.get('context', {}),
                'relevant_files': task_context_result.get('relevant_files', []),
                'project_structure': context_result.get('structure', {}),
                'dependencies': context_result.get('dependencies', [])
            }

            return {
                'status': 'success',
                'context': combined_context
            }

        except Exception as e:
            logger.error(f"Failed to generate task context: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def _update_context_with_feedback(self, context: Dict[str, Any],
                                          lint_test_result: Dict[str, Any],
                                          iteration: int) -> Dict[str, Any]:
        """Update context with lint/test feedback for next iteration."""
        try:
            # Add feedback to context
            context['feedback'] = {
                'iteration': iteration,
                'lint_test_result': lint_test_result.get('summary', {}),
                'issues_to_fix': lint_test_result.get('summary', {}).get('issues_to_fix', [])
            }

            return context

        except Exception as e:
            logger.error(f"Failed to update context with feedback: {e}")
            return context

    # Tool implementations for LLM
    async def _tool_read_file(self, file_path: str) -> Dict[str, Any]:
        """Read file content."""
        return await file_operations.read_file(file_path)

    async def _tool_write_file(self, file_path: str, content: str,
                              create_backup: bool = True) -> Dict[str, Any]:
        """Write file content."""
        return await file_operations.write_file(file_path, content, create_backup)

    async def _tool_create_file(self, file_path: str, content: str = "") -> Dict[str, Any]:
        """Create new file."""
        return await file_operations.create_file(file_path, content)

    async def _tool_delete_file(self, file_path: str, create_backup: bool = True) -> Dict[str, Any]:
        """Delete file."""
        return await file_operations.delete_file(file_path, create_backup)

    async def _tool_list_directory(self, directory_path: str,
                                  recursive: bool = False) -> Dict[str, Any]:
        """List directory contents."""
        return await file_operations.list_directory(directory_path, recursive)

    async def _tool_git_status(self, repo_path: str) -> Dict[str, Any]:
        """Get git status."""
        return await git_operations.get_git_status(repo_path)

    async def _tool_git_diff(self, repo_path: str, file_path: str = None) -> Dict[str, Any]:
        """Get git diff."""
        return await git_operations.get_git_diff(repo_path, file_path)

    async def _tool_git_commit(self, repo_path: str, message: str,
                              add_all: bool = True) -> Dict[str, Any]:
        """Commit changes."""
        return await git_operations.git_commit(repo_path, message, add_all)

    async def _tool_run_linting(self, project_path: str, language: str = "auto") -> Dict[str, Any]:
        """Run linting."""
        if language == "python":
            return await lint_test_utils.run_python_linting(project_path)
        elif language == "javascript":
            return await lint_test_utils.run_javascript_linting(project_path)
        else:
            result = await lint_test_utils.run_full_check(project_path, language)
            return result.get('linting', {})

    async def _tool_run_tests(self, project_path: str, language: str = "auto") -> Dict[str, Any]:
        """Run tests."""
        if language == "python":
            return await lint_test_utils.run_python_tests(project_path)
        elif language == "javascript":
            return await lint_test_utils.run_javascript_tests(project_path)
        else:
            result = await lint_test_utils.run_full_check(project_path, language)
            return result.get('testing', {})

    async def _tool_search_code(self, query: str, project_path: str) -> Dict[str, Any]:
        """Search code using semantic search."""
        return await code_analyzer.search_similar_code(query, project_path)

    async def _tool_analyze_code(self, file_path: str) -> Dict[str, Any]:
        """Analyze code structure and complexity."""
        return await code_analyzer.parse_code_structure(file_path)

    async def _tool_get_project_context(self, project_path: str) -> Dict[str, Any]:
        """Get project context and structure."""
        return await repo_context.generate_project_summary(project_path)


# Global instance
task_runner = TaskRunner()
