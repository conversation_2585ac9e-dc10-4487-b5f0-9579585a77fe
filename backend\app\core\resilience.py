"""
Resilience and Error Handling Module

Provides advanced error handling, retry mechanisms, and system resilience:
- Circuit breakers with adaptive thresholds
- Intelligent retry strategies
- Graceful degradation
- Error aggregation and analysis
- Health monitoring and recovery
"""

import asyncio
import logging
import time
import random
from typing import Dict, Any, Optional, List, Callable, Union
from functools import wraps
from enum import Enum
from dataclasses import dataclass, field
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


@dataclass
class ErrorMetrics:
    """Error metrics for analysis."""
    total_errors: int = 0
    error_rate: float = 0.0
    last_error_time: float = 0.0
    error_types: Dict[str, int] = field(default_factory=dict)
    recent_errors: deque = field(default_factory=lambda: deque(maxlen=100))


class AdaptiveCircuitBreaker:
    """Advanced circuit breaker with adaptive thresholds."""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        success_threshold: int = 3,
        adaptive: bool = True
    ):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.success_threshold = success_threshold
        self.adaptive = adaptive
        
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = 0.0
        self.last_state_change = time.time()
        
        # Adaptive parameters
        self.base_failure_threshold = failure_threshold
        self.error_history = deque(maxlen=1000)
        
    def is_request_allowed(self) -> bool:
        """Check if request should be allowed."""
        current_time = time.time()
        
        if self.state == CircuitState.CLOSED:
            return True
        elif self.state == CircuitState.OPEN:
            if current_time - self.last_failure_time >= self.recovery_timeout:
                self._transition_to_half_open()
                return True
            return False
        else:  # HALF_OPEN
            return True
    
    def record_success(self) -> None:
        """Record a successful operation."""
        if self.state == CircuitState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.success_threshold:
                self._transition_to_closed()
        elif self.state == CircuitState.CLOSED:
            # Reset failure count on success
            self.failure_count = max(0, self.failure_count - 1)
        
        # Update adaptive threshold
        if self.adaptive:
            self._update_adaptive_threshold()
    
    def record_failure(self, error_type: str = "unknown") -> None:
        """Record a failed operation."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        self.error_history.append({
            'timestamp': self.last_failure_time,
            'error_type': error_type
        })
        
        if self.state == CircuitState.CLOSED:
            if self.failure_count >= self.failure_threshold:
                self._transition_to_open()
        elif self.state == CircuitState.HALF_OPEN:
            self._transition_to_open()
        
        # Update adaptive threshold
        if self.adaptive:
            self._update_adaptive_threshold()
    
    def _transition_to_open(self) -> None:
        """Transition to OPEN state."""
        self.state = CircuitState.OPEN
        self.last_state_change = time.time()
        logger.warning(f"Circuit breaker opened after {self.failure_count} failures")
    
    def _transition_to_half_open(self) -> None:
        """Transition to HALF_OPEN state."""
        self.state = CircuitState.HALF_OPEN
        self.success_count = 0
        self.last_state_change = time.time()
        logger.info("Circuit breaker transitioned to half-open")
    
    def _transition_to_closed(self) -> None:
        """Transition to CLOSED state."""
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_state_change = time.time()
        logger.info("Circuit breaker closed - service recovered")
    
    def _update_adaptive_threshold(self) -> None:
        """Update failure threshold based on error patterns."""
        if len(self.error_history) < 10:
            return
        
        # Calculate recent error rate
        recent_window = 300  # 5 minutes
        current_time = time.time()
        recent_errors = [
            e for e in self.error_history
            if current_time - e['timestamp'] <= recent_window
        ]
        
        if len(recent_errors) > 0:
            error_rate = len(recent_errors) / recent_window
            
            # Adjust threshold based on error rate
            if error_rate > 0.1:  # High error rate
                self.failure_threshold = max(2, self.base_failure_threshold - 2)
            elif error_rate < 0.01:  # Low error rate
                self.failure_threshold = min(10, self.base_failure_threshold + 2)
            else:
                self.failure_threshold = self.base_failure_threshold
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get circuit breaker metrics."""
        return {
            'state': self.state.value,
            'failure_count': self.failure_count,
            'success_count': self.success_count,
            'failure_threshold': self.failure_threshold,
            'last_failure_time': self.last_failure_time,
            'time_since_last_failure': time.time() - self.last_failure_time,
            'error_history_size': len(self.error_history)
        }


class RetryStrategy:
    """Intelligent retry strategy with multiple algorithms."""
    
    @staticmethod
    def exponential_backoff(
        attempt: int,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        jitter: bool = True
    ) -> float:
        """Exponential backoff with optional jitter."""
        delay = min(base_delay * (2 ** attempt), max_delay)
        
        if jitter:
            # Add ±25% jitter
            jitter_range = delay * 0.25
            delay += random.uniform(-jitter_range, jitter_range)
        
        return max(0, delay)
    
    @staticmethod
    def linear_backoff(
        attempt: int,
        base_delay: float = 1.0,
        increment: float = 1.0,
        max_delay: float = 60.0
    ) -> float:
        """Linear backoff strategy."""
        return min(base_delay + (attempt * increment), max_delay)
    
    @staticmethod
    def fibonacci_backoff(
        attempt: int,
        base_delay: float = 1.0,
        max_delay: float = 60.0
    ) -> float:
        """Fibonacci backoff strategy."""
        def fibonacci(n):
            if n <= 1:
                return 1
            a, b = 1, 1
            for _ in range(2, n + 1):
                a, b = b, a + b
            return b
        
        return min(base_delay * fibonacci(attempt), max_delay)


class ErrorAggregator:
    """Aggregate and analyze errors for insights."""
    
    def __init__(self, window_size: int = 1000):
        self.window_size = window_size
        self.errors = deque(maxlen=window_size)
        self.error_counts = defaultdict(int)
        self.error_patterns = defaultdict(list)
    
    def record_error(
        self,
        error: Exception,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        """Record an error with context."""
        error_info = {
            'timestamp': time.time(),
            'type': type(error).__name__,
            'message': str(error),
            'context': context or {}
        }
        
        self.errors.append(error_info)
        self.error_counts[error_info['type']] += 1
        
        # Detect patterns
        self._detect_patterns(error_info)
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of recent errors."""
        if not self.errors:
            return {'total_errors': 0}
        
        recent_window = 300  # 5 minutes
        current_time = time.time()
        recent_errors = [
            e for e in self.errors
            if current_time - e['timestamp'] <= recent_window
        ]
        
        error_types = defaultdict(int)
        for error in recent_errors:
            error_types[error['type']] += 1
        
        return {
            'total_errors': len(self.errors),
            'recent_errors': len(recent_errors),
            'error_rate': len(recent_errors) / recent_window,
            'most_common_errors': dict(sorted(
                error_types.items(),
                key=lambda x: x[1],
                reverse=True
            )[:5]),
            'patterns_detected': len(self.error_patterns)
        }
    
    def _detect_patterns(self, error_info: Dict[str, Any]) -> None:
        """Detect error patterns."""
        error_type = error_info['type']
        
        # Simple pattern detection: same error type in short time window
        recent_same_type = [
            e for e in self.errors
            if (e['type'] == error_type and
                error_info['timestamp'] - e['timestamp'] <= 60)
        ]
        
        if len(recent_same_type) >= 3:
            pattern_key = f"burst_{error_type}"
            if pattern_key not in self.error_patterns:
                self.error_patterns[pattern_key] = recent_same_type
                logger.warning(f"Error pattern detected: {pattern_key}")


class GracefulDegradation:
    """Manage graceful degradation of services."""
    
    def __init__(self):
        self.service_health = defaultdict(lambda: {'healthy': True, 'last_check': 0})
        self.fallback_strategies = {}
    
    def register_fallback(
        self,
        service_name: str,
        fallback_func: Callable
    ) -> None:
        """Register a fallback strategy for a service."""
        self.fallback_strategies[service_name] = fallback_func
        logger.info(f"Registered fallback for service: {service_name}")
    
    def mark_service_unhealthy(self, service_name: str) -> None:
        """Mark a service as unhealthy."""
        self.service_health[service_name]['healthy'] = False
        self.service_health[service_name]['last_check'] = time.time()
        logger.warning(f"Service marked unhealthy: {service_name}")
    
    def mark_service_healthy(self, service_name: str) -> None:
        """Mark a service as healthy."""
        self.service_health[service_name]['healthy'] = True
        self.service_health[service_name]['last_check'] = time.time()
        logger.info(f"Service marked healthy: {service_name}")
    
    async def execute_with_fallback(
        self,
        service_name: str,
        primary_func: Callable,
        *args,
        **kwargs
    ) -> Any:
        """Execute function with fallback if service is unhealthy."""
        if self.service_health[service_name]['healthy']:
            try:
                return await primary_func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Primary service {service_name} failed: {e}")
                self.mark_service_unhealthy(service_name)
        
        # Use fallback
        fallback_func = self.fallback_strategies.get(service_name)
        if fallback_func:
            logger.info(f"Using fallback for service: {service_name}")
            return await fallback_func(*args, **kwargs)
        else:
            raise Exception(f"Service {service_name} unavailable and no fallback configured")


# Decorators for resilience
def circuit_breaker(
    failure_threshold: int = 5,
    recovery_timeout: float = 60.0,
    adaptive: bool = True
):
    """Decorator to add circuit breaker to functions."""
    def decorator(func):
        breaker = AdaptiveCircuitBreaker(
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout,
            adaptive=adaptive
        )
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if not breaker.is_request_allowed():
                raise Exception("Circuit breaker is open")
            
            try:
                result = await func(*args, **kwargs)
                breaker.record_success()
                return result
            except Exception as e:
                breaker.record_failure(type(e).__name__)
                raise
        
        wrapper._circuit_breaker = breaker
        return wrapper
    return decorator


def retry_with_backoff(
    max_attempts: int = 3,
    strategy: str = "exponential",
    base_delay: float = 1.0,
    max_delay: float = 60.0
):
    """Decorator to add retry logic with backoff."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if attempt == max_attempts - 1:
                        break
                    
                    # Calculate delay
                    if strategy == "exponential":
                        delay = RetryStrategy.exponential_backoff(
                            attempt, base_delay, max_delay
                        )
                    elif strategy == "linear":
                        delay = RetryStrategy.linear_backoff(
                            attempt, base_delay, max_delay=max_delay
                        )
                    elif strategy == "fibonacci":
                        delay = RetryStrategy.fibonacci_backoff(
                            attempt, base_delay, max_delay
                        )
                    else:
                        delay = base_delay
                    
                    logger.warning(
                        f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                        f"Retrying in {delay:.2f}s"
                    )
                    await asyncio.sleep(delay)
            
            raise last_exception
        
        return wrapper
    return decorator


# Global instances
error_aggregator = ErrorAggregator()
graceful_degradation = GracefulDegradation()
