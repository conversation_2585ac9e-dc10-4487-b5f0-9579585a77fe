interactions:
- request:
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '685'
      content-type:
      - application/json
      host:
      - generativelanguage.googleapis.com
    method: POST
    parsed_body:
      contents:
      - parts:
        - text: What is the temperature of the capital of France?
        role: user
      generationConfig: {}
      systemInstruction:
        parts:
        - text: You are a helpful chatbot.
        role: user
      tools:
      - functionDeclarations:
        - description: Get the capital of a country.
          name: get_capital
          parameters:
            properties:
              country:
                description: The country name.
                type: STRING
            required:
            - country
            type: OBJECT
        - description: Get the temperature in a city.
          name: get_temperature
          parameters:
            properties:
              city:
                description: The city name.
                type: STRING
            required:
            - city
            type: OBJECT
    uri: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:streamGenerateContent?alt=sse
  response:
    body:
      string: "data: {\"candidates\": [{\"content\": {\"parts\": [{\"functionCall\": {\"name\": \"get_capital\",\"args\":
        {\"country\": \"France\"}}}],\"role\": \"model\"},\"finishReason\": \"STOP\"}],\"usageMetadata\": {\"promptTokenCount\":
        52,\"candidatesTokenCount\": 5,\"totalTokenCount\": 57,\"promptTokensDetails\": [{\"modality\": \"TEXT\",\"tokenCount\":
        52}],\"candidatesTokensDetails\": [{\"modality\": \"TEXT\",\"tokenCount\": 5}]},\"modelVersion\": \"gemini-2.0-flash\"}\r\n\r\n"
    headers:
      alt-svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      content-disposition:
      - attachment
      content-type:
      - text/event-stream
      server-timing:
      - gfet4t7; dur=586
      transfer-encoding:
      - chunked
      vary:
      - Origin
      - X-Origin
      - Referer
    status:
      code: 200
      message: OK
- request:
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1002'
      content-type:
      - application/json
      host:
      - generativelanguage.googleapis.com
    method: POST
    parsed_body:
      contents:
      - parts:
        - text: What is the temperature of the capital of France?
        role: user
      - parts:
        - functionCall:
            args:
              country: France
            id: pyd_ai_f9d7afaf87104cf88c261926103e7898
            name: get_capital
        role: model
      - parts:
        - functionResponse:
            id: pyd_ai_f9d7afaf87104cf88c261926103e7898
            name: get_capital
            response:
              return_value: Paris
        role: user
      generationConfig: {}
      systemInstruction:
        parts:
        - text: You are a helpful chatbot.
        role: user
      tools:
      - functionDeclarations:
        - description: Get the capital of a country.
          name: get_capital
          parameters:
            properties:
              country:
                description: The country name.
                type: STRING
            required:
            - country
            type: OBJECT
        - description: Get the temperature in a city.
          name: get_temperature
          parameters:
            properties:
              city:
                description: The city name.
                type: STRING
            required:
            - city
            type: OBJECT
    uri: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:streamGenerateContent?alt=sse
  response:
    body:
      string: "data: {\"candidates\": [{\"content\": {\"parts\": [{\"functionCall\": {\"name\": \"get_temperature\",\"args\":
        {\"city\": \"Paris\"}}}],\"role\": \"model\"},\"finishReason\": \"STOP\"}],\"usageMetadata\": {\"promptTokenCount\":
        64,\"candidatesTokenCount\": 5,\"totalTokenCount\": 69,\"promptTokensDetails\": [{\"modality\": \"TEXT\",\"tokenCount\":
        64}],\"candidatesTokensDetails\": [{\"modality\": \"TEXT\",\"tokenCount\": 5}]},\"modelVersion\": \"gemini-2.0-flash\"}\r\n\r\n"
    headers:
      alt-svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      content-disposition:
      - attachment
      content-type:
      - text/event-stream
      server-timing:
      - gfet4t7; dur=488
      transfer-encoding:
      - chunked
      vary:
      - Origin
      - X-Origin
      - Referer
    status:
      code: 200
      message: OK
- request:
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1327'
      content-type:
      - application/json
      host:
      - generativelanguage.googleapis.com
    method: POST
    parsed_body:
      contents:
      - parts:
        - text: What is the temperature of the capital of France?
        role: user
      - parts:
        - functionCall:
            args:
              country: France
            id: pyd_ai_f9d7afaf87104cf88c261926103e7898
            name: get_capital
        role: model
      - parts:
        - functionResponse:
            id: pyd_ai_f9d7afaf87104cf88c261926103e7898
            name: get_capital
            response:
              return_value: Paris
        role: user
      - parts:
        - functionCall:
            args:
              city: Paris
            id: pyd_ai_c289a95a512d462b9cd2bc9c7b24f07b
            name: get_temperature
        role: model
      - parts:
        - functionResponse:
            id: pyd_ai_c289a95a512d462b9cd2bc9c7b24f07b
            name: get_temperature
            response:
              return_value: 30°C
        role: user
      generationConfig: {}
      systemInstruction:
        parts:
        - text: You are a helpful chatbot.
        role: user
      tools:
      - functionDeclarations:
        - description: Get the capital of a country.
          name: get_capital
          parameters:
            properties:
              country:
                description: The country name.
                type: STRING
            required:
            - country
            type: OBJECT
        - description: Get the temperature in a city.
          name: get_temperature
          parameters:
            properties:
              city:
                description: The city name.
                type: STRING
            required:
            - city
            type: OBJECT
    uri: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:streamGenerateContent?alt=sse
  response:
    body:
      string: "data: {\"candidates\": [{\"content\": {\"parts\": [{\"text\": \"The temperature in\"}],\"role\": \"model\"}}],\"usageMetadata\":
        {\"promptTokenCount\": 169,\"totalTokenCount\": 169,\"promptTokensDetails\": [{\"modality\": \"TEXT\",\"tokenCount\":
        169}]},\"modelVersion\": \"gemini-2.0-flash\"}\r\n\r\ndata: {\"candidates\": [{\"content\": {\"parts\": [{\"text\":
        \" Paris is 30°C.\\n\"}],\"role\": \"model\"},\"finishReason\": \"STOP\"}],\"usageMetadata\": {\"promptTokenCount\":
        79,\"candidatesTokenCount\": 12,\"totalTokenCount\": 91,\"promptTokensDetails\": [{\"modality\": \"TEXT\",\"tokenCount\":
        79}],\"candidatesTokensDetails\": [{\"modality\": \"TEXT\",\"tokenCount\": 12}]},\"modelVersion\": \"gemini-2.0-flash\"}\r\n\r\n"
    headers:
      alt-svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      content-disposition:
      - attachment
      content-type:
      - text/event-stream
      server-timing:
      - gfet4t7; dur=383
      transfer-encoding:
      - chunked
      vary:
      - Origin
      - X-Origin
      - Referer
    status:
      code: 200
      message: OK
version: 1
