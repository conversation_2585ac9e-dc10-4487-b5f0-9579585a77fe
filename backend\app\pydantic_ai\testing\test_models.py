"""
Test Models for Pydantic AI Testing

This module provides TestModel and FunctionModel implementations for
comprehensive testing of AI agents without making real API calls.
"""

import json
import logging
import uuid
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Callable, Union
from datetime import datetime, timezone

from pydantic_ai.models.test import TestModel
from pydantic_ai.models.function import FunctionModel, AgentInfo
from pydantic_ai.messages import (
    ModelMessage, ModelResponse, TextPart, ToolCallPart,
    ModelRequest, UserPromptPart, SystemPromptPart
)
from pydantic_ai.usage import Usage

logger = logging.getLogger(__name__)


@dataclass
class TestModelConfig:
    """Configuration for TestModel instances."""
    default_response: str = "Test response"
    tool_call_responses: Dict[str, Any] = None
    simulate_errors: bool = False
    response_delay: float = 0.0
    usage_tracking: bool = True


@dataclass 
class FunctionModelConfig:
    """Configuration for FunctionModel instances."""
    response_patterns: Dict[str, str] = None
    tool_call_patterns: Dict[str, Dict[str, Any]] = None
    simulate_reasoning: bool = True
    include_usage: bool = True


def create_test_model(config: Optional[TestModelConfig] = None) -> TestModel:
    """
    Create a configured TestModel for testing.
    
    Args:
        config: Optional configuration for the test model
        
    Returns:
        Configured TestModel instance
    """
    if config is None:
        config = TestModelConfig()
    
    # Create test model with default responses
    test_model = TestModel()
    
    # Configure default responses
    if config.tool_call_responses:
        for tool_name, response in config.tool_call_responses.items():
            # TestModel will automatically handle tool calls
            pass
    
    logger.info(f"🧪 Created TestModel with config: {config}")
    return test_model


def create_function_model(
    config: Optional[FunctionModelConfig] = None,
    custom_function: Optional[Callable] = None
) -> FunctionModel:
    """
    Create a configured FunctionModel for advanced testing.
    
    Args:
        config: Optional configuration for the function model
        custom_function: Optional custom function to use
        
    Returns:
        Configured FunctionModel instance
    """
    if config is None:
        config = FunctionModelConfig()
    
    if custom_function:
        return FunctionModel(custom_function)
    
    # Create default function based on config
    async def default_model_function(
        messages: List[ModelMessage], 
        info: AgentInfo
    ) -> ModelResponse:
        """Default model function with configurable responses."""
        
        # Extract the latest user message
        user_message = None
        for message in reversed(messages):
            if isinstance(message, ModelRequest):
                for part in message.parts:
                    if isinstance(part, UserPromptPart):
                        user_message = part.content
                        break
                if user_message:
                    break
        
        if not user_message:
            user_message = "No user message found"
        
        # Check for tool calling patterns
        if config.tool_call_patterns and info.function_tools:
            for pattern, tool_args in config.tool_call_patterns.items():
                if pattern.lower() in user_message.lower():
                    # Find matching tool
                    for tool in info.function_tools:
                        if tool.name in tool_args:
                            tool_call_id = f"test_{uuid.uuid4().hex[:8]}"
                            return ModelResponse(
                                parts=[ToolCallPart(
                                    tool_name=tool.name,
                                    args=tool_args[tool.name],
                                    tool_call_id=tool_call_id
                                )],
                                model_name="function-test",
                                timestamp=datetime.now(timezone.utc),
                                usage=Usage(requests=1) if config.include_usage else None
                            )
        
        # Check for response patterns
        response_text = "Test response"
        if config.response_patterns:
            for pattern, response in config.response_patterns.items():
                if pattern.lower() in user_message.lower():
                    response_text = response
                    break
        
        # Simulate reasoning if enabled
        if config.simulate_reasoning:
            if "analyze" in user_message.lower():
                response_text = f"Analysis of '{user_message}': This appears to be a request for analysis. Based on the input, I would recommend reviewing the code structure and identifying potential improvements."
            elif "read" in user_message.lower() and "file" in user_message.lower():
                response_text = f"File reading request detected. I would use the read_file tool to access the specified file content."
            elif "git" in user_message.lower():
                response_text = f"Git operation detected. I would use appropriate git tools to handle this request."
        
        return ModelResponse(
            parts=[TextPart(content=response_text)],
            model_name="function-test",
            timestamp=datetime.now(timezone.utc),
            usage=Usage(requests=1) if config.include_usage else None
        )
    
    logger.info(f"🧪 Created FunctionModel with config: {config}")
    return FunctionModel(default_model_function)


def create_coding_test_model() -> FunctionModel:
    """Create a specialized test model for coding tasks."""
    
    config = FunctionModelConfig(
        response_patterns={
            "hello": "Hello! I'm ready to help with coding tasks.",
            "analyze": "I'll analyze the code structure and provide insights.",
            "read": "I'll read the specified file content.",
            "write": "I'll write content to the specified file.",
            "git": "I'll perform the requested git operation."
        },
        tool_call_patterns={
            "read file": {
                "read_file": {"file_path": "test.py"}
            },
            "analyze code": {
                "analyze_code": {"file_path": "test.py", "analysis_type": "structure"}
            },
            "git status": {
                "git_status": {}
            },
            "write file": {
                "write_file": {"file_path": "test.py", "content": "# Test content"}
            }
        },
        simulate_reasoning=True,
        include_usage=True
    )
    
    return create_function_model(config)


def create_vision_test_model() -> FunctionModel:
    """Create a specialized test model for vision tasks."""
    
    config = FunctionModelConfig(
        response_patterns={
            "analyze image": "I can see this is a user interface with several elements including buttons and text.",
            "accessibility": "I've identified several accessibility issues including low contrast text and small button sizes.",
            "screenshot": "This screenshot shows a web interface with navigation, content areas, and interactive elements."
        },
        tool_call_patterns={
            "analyze screenshot": {
                "analyze_screenshot": {
                    "image_path": "test_screenshot.png",
                    "analysis_type": "comprehensive"
                }
            },
            "accessibility audit": {
                "accessibility_audit": {
                    "image_path": "test_ui.png",
                    "wcag_level": "AA"
                }
            },
            "detect ui elements": {
                "detect_ui_elements": {
                    "image_path": "test_interface.png"
                }
            }
        },
        simulate_reasoning=True,
        include_usage=True
    )
    
    return create_function_model(config)


def create_workflow_test_model() -> FunctionModel:
    """Create a specialized test model for workflow tasks."""
    
    async def workflow_model_function(
        messages: List[ModelMessage], 
        info: AgentInfo
    ) -> ModelResponse:
        """Model function that simulates complex workflow responses."""
        
        # Extract user message
        user_message = ""
        for message in reversed(messages):
            if isinstance(message, ModelRequest):
                for part in message.parts:
                    if isinstance(part, UserPromptPart):
                        user_message = part.content
                        break
                if user_message:
                    break
        
        # Simulate workflow coordination
        if "workflow" in user_message.lower():
            if info.function_tools:
                # Call multiple tools in sequence
                tool_calls = []
                for i, tool in enumerate(info.function_tools[:3]):  # Limit to 3 tools
                    tool_call_id = f"workflow_{uuid.uuid4().hex[:8]}"
                    args = {}
                    
                    # Provide realistic args based on tool name
                    if "read" in tool.name:
                        args = {"file_path": f"step_{i+1}.py"}
                    elif "analyze" in tool.name:
                        args = {"file_path": f"step_{i+1}.py", "analysis_type": "structure"}
                    elif "git" in tool.name:
                        args = {}
                    
                    tool_calls.append(ToolCallPart(
                        tool_name=tool.name,
                        args=args,
                        tool_call_id=tool_call_id
                    ))
                
                if tool_calls:
                    return ModelResponse(
                        parts=tool_calls,
                        model_name="workflow-test",
                        timestamp=datetime.now(timezone.utc),
                        usage=Usage(requests=1)
                    )
        
        # Default response
        return ModelResponse(
            parts=[TextPart(content="Workflow coordination complete. All steps executed successfully.")],
            model_name="workflow-test", 
            timestamp=datetime.now(timezone.utc),
            usage=Usage(requests=1)
        )
    
    return FunctionModel(workflow_model_function)


# Export utility functions
__all__ = [
    'TestModelConfig',
    'FunctionModelConfig',
    'create_test_model',
    'create_function_model',
    'create_coding_test_model',
    'create_vision_test_model',
    'create_workflow_test_model'
]
