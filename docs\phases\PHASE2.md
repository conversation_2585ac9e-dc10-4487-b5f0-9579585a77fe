# 🚀 **Phase 2 Implementation Instructions for AI Coder Agent**

## 📋 **Current Status**
Phase 1 in @IMPLEMENTATION_PLAN.md is **COMPLETE** ✅. All Docker containers are running, backend API is functional, and all tests pass. I stopped the containers though so you will need to rerun them if needed.

## 🎯 **Phase 2 Objective**
Implement **External Service Integration** as defined in `IMPLEMENTATION_PLAN.md`:
- OpenRouter LLM Client
- Ollama Embedding Client  
- Qdrant Vector Database Client
- Context Management System

## 🔧 **Prerequisites Check**
Before starting, verify Phase 1 completion:
```bash
# 1. Ensure all containers are running
docker-compose ps

# 2. Run Phase 1 completion test
python test_phase1_complete.py

# Expected: "🎉 Phase 1 is COMPLETE! All requirements satisfied."
```

## 📝 **Phase 2 Implementation Tasks**

### **Task 2.1: OpenRouter LLM Client**

1. **Create `backend/llm_client.py`**:
```python
# Required features from IMPLEMENTATION_PLAN.md:
# - chat_completion() method with retry logic
# - vision_analyze() method for image analysis  
# - summarize_text() method
# - Handle rate limiting (429 errors) and server errors (5xx)
# - Use models from config.py:
#   - code_model: "deepseek/deepseek-r1-0528:free"
#   - vision_model: "google/gemini-2.0-flash-exp:free"
```

2. **Implementation Requirements**:
   - Use `httpx` for async HTTP requests (already in requirements.txt)
   - Implement exponential backoff for retries
   - Handle OpenRouter API response format
   - Add comprehensive error handling and logging
   - Use settings from `backend/config.py`

3. **Test the implementation**:
```bash
# Add to backend/test_llm_client.py
# Test with simple prompts and verify responses
```

### **Task 2.1b: Ollama Embedding Client**

1. **Create `backend/ollama_client.py`**:
```python
# Required features from IMPLEMENTATION_PLAN.md:
# - embed_text() method using local Ollama models
# - embed_batch() method for multiple texts
# - Model management (pull, list, check availability)
# - Handle connection errors and model loading
# - Use embedding_model: "nomic-embed-text" from config.py
```

2. **Implementation Requirements**:
   - Use `ollama` Python client (already in requirements.txt)
   - Connect to `http://ollama:11434` (Docker service name)
   - Implement batch processing for efficiency
   - Add model auto-download if not available
   - Verify embedding dimensions match config (768)

3. **Test the implementation**:
```bash
# Test with sample texts and verify embedding dimensions
```

### **Task 2.2: Qdrant Vector Database Client**

1. **Create `backend/qdrant_client.py`**:
```python
# Required features from IMPLEMENTATION_PLAN.md:
# - Collection creation with proper vector dimensions
# - upsert_embeddings() with validation
# - search_similar() with error handling
# - Connect to self-hosted Qdrant at http://qdrant:6333
```

2. **Implementation Requirements**:
   - Use `qdrant-client` (already in requirements.txt)
   - Create collection with 768 dimensions (matching nomic-embed-text)
   - Implement vector upsert with metadata
   - Add similarity search with configurable top_k
   - Handle connection errors gracefully

3. **Test the implementation**:
```bash
# Test connection, collection creation, and vector operations
```

### **Task 2.3: Context Management System**

1. **Create `backend/app/agent/context_manager.py`**:
```python
# Required features from IMPLEMENTATION_PLAN.md:
# - Token counting with tiktoken
# - Prompt building with system instructions
# - Summarization trigger logic
# - Post-summarization token validation
```

2. **Implementation Requirements**:
   - Use `tiktoken` for accurate token counting (already in requirements.txt)
   - Implement context window management for deepseek model
   - Add summarization when approaching token limits
   - Build prompts with proper system instructions
   - Handle context overflow gracefully

## 🧪 **Testing Strategy**

### **Create Phase 2 Test Suite**

1. **Create `test_phase2_complete.py`**:
```python
#!/usr/bin/env python3
"""
Comprehensive Phase 2 Completion Test for AI Coder Agent
Tests all Phase 2 requirements from IMPLEMENTATION_PLAN.md
"""

def test_openrouter_client():
    """Test OpenRouter LLM client functionality."""
    # Test chat completion
    # Test vision analysis (if possible without real API key)
    # Test error handling and retries

def test_ollama_client():
    """Test Ollama embedding client."""
    # Test text embedding
    # Test batch embedding
    # Test model management
    # Verify embedding dimensions

def test_qdrant_client():
    """Test Qdrant vector database client."""
    # Test collection creation
    # Test vector upsert
    # Test similarity search
    # Test error handling

def test_context_manager():
    """Test context management system."""
    # Test token counting
    # Test prompt building
    # Test summarization logic

def test_integration():
    """Test integration between all components."""
    # Test full workflow: text → embedding → storage → retrieval
```

2. **Add API endpoints for testing**:
```python
# Add to backend/main.py:
@app.post("/api/test/llm")
async def test_llm_endpoint():
    """Test LLM functionality."""

@app.post("/api/test/embeddings") 
async def test_embeddings_endpoint():
    """Test embedding generation."""

@app.post("/api/test/vector-search")
async def test_vector_search_endpoint():
    """Test vector search."""
```

## 🔄 **Development Workflow**

### **Step-by-Step Process**

1. **Setup Phase**:
```bash
# Ensure Phase 1 is complete
python test_phase1_complete.py

# Check current directory structure
ls -la backend/
```

2. **Implementation Phase**:
```bash
# For each component:
# 1. Create the Python file
# 2. Implement required methods
# 3. Add to backend/main.py imports
# 4. Test individually

# Example for LLM client:
# Create backend/llm_client.py
# Test: python -c "from backend.llm_client import OpenRouterClient; print('Import successful')"
```

3. **Integration Phase**:
```bash
# Rebuild backend container after each major component
docker-compose build backend
docker-compose up -d backend

# Test endpoints
curl http://localhost:8000/api/test/llm
curl http://localhost:8000/api/test/embeddings
```

4. **Validation Phase**:
```bash
# Run comprehensive Phase 2 test
python test_phase2_complete.py

# Expected output: "🎉 Phase 2 is COMPLETE! All requirements satisfied."
```

## 📁 **Expected File Structure After Phase 2**

```
backend/
├── app/
│   └── agent/
│       └── context_manager.py     # NEW
├── llm_client.py                  # NEW
├── ollama_client.py               # NEW  
├── qdrant_client.py               # NEW
├── main.py                        # UPDATED with new endpoints
├── config.py                      # EXISTING
└── sessions.py                    # EXISTING

# Root directory:
├── test_phase2_complete.py        # NEW
└── IMPLEMENTATION_PLAN.md         # UPDATED with Phase 2 checkmarks
```

## ⚙️ **Configuration Notes**

### **Environment Variables** (already configured):
- `OPENROUTER_API_KEY`: For LLM API calls
- `OLLAMA_HOST`: `http://ollama:11434` 
- `QDRANT_URL`: `http://qdrant:6333`

### **Model Configuration** (already set in config.py):
- **Code Model**: `deepseek/deepseek-r1-0528:free`
- **Vision Model**: `google/gemini-2.0-flash-exp:free`  
- **Embedding Model**: `nomic-embed-text` (Ollama)
- **Embedding Dimensions**: `768`

## 🚨 **Important Implementation Notes**

1. **Error Handling**: All clients must handle network errors, API errors, and service unavailability gracefully

2. **Async/Await**: Use async methods where appropriate, especially for HTTP calls

3. **Logging**: Add comprehensive logging using the existing logger setup

4. **Docker Integration**: All services communicate via Docker service names, not localhost

5. **Testing**: Test each component individually before integration

6. **Memory Management**: Be mindful of embedding storage and context window limits

## ✅ **Success Criteria**

Phase 2 is complete when:
- [ ] All 4 client classes are implemented and functional
- [ ] Integration tests pass
- [ ] `test_phase2_complete.py` shows 100% pass rate
- [ ] All endpoints respond correctly
- [ ] Docker containers remain stable
- [ ] No breaking changes to Phase 1 functionality

## 🎯 **Final Validation Command**

```bash
# This should be the final test:
python test_phase2_complete.py && echo "✅ PHASE 2 COMPLETE - READY FOR PHASE 3"
```

---

**Next AI: Follow these instructions step-by-step to implement Phase 2. Start with Task 2.1 (OpenRouter LLM Client) and work through each component systematically. Test thoroughly at each step before proceeding.**
