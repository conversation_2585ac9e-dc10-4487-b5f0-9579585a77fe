interactions:
- request:
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '187'
      content-type:
      - application/json
      host:
      - api.groq.com
      xProxy-Limit-IDs:
      - monthly_budget
    method: POST
    parsed_body:
      messages:
      - content: hello
        role: user
      model: llama-3.3-70b-versatile
      n: 1
      stream: false
    uri: https://api.groq.com/openai/v1/chat/completions
  response:
    headers:
      alt-svc:
      - h3=":443"; ma=86400
      cache-control:
      - private, max-age=0, no-store, no-cache, must-revalidate
      connection:
      - keep-alive
      content-length:
      - '570'
      content-type:
      - application/json
      transfer-encoding:
      - chunked
      vary:
      - Origin, Accept-Encoding
    parsed_body:
      choices:
      - finish_reason: stop
        index: 0
        logprobs: null
        message:
          content: Hello! How can I assist you today?
          role: assistant
      created: 1744043573
      id: chatcmpl-7586b6a9-fb4b-4ec7-86a0-59f0a77844cf
      model: llama-3.3-70b-versatile
      object: chat.completion
      system_fingerprint: fp_72a5dc99ee
      usage:
        completion_time: 0.029090909
        completion_tokens: 8
        prompt_time: 0.002665957
        prompt_tokens: 48
        queue_time: 0.100731848
        total_time: 0.031756866
        total_tokens: 56
      usage_breakdown:
        models: null
      x_groq:
        id: req_01jr8hj0hzeq9b86xqb5dn7wqs
    status:
      code: 200
      message: OK
version: 1
