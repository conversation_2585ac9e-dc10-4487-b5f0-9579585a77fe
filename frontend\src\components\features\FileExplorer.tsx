import React, { useState, useEffect } from 'react';

interface FileItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  modified?: Date;
  children?: FileItem[];
  isExpanded?: boolean;
  isLoading?: boolean;
}

interface FileExplorerProps {
  onFileSelect?: (file: FileItem) => void;
  onFileOpen?: (file: FileItem) => void;
  currentProject?: string;
}

export const FileExplorer: React.FC<FileExplorerProps> = ({
  onFileSelect,
  onFileOpen,
  currentProject
}) => {
  const [fileTree, setFileTree] = useState<FileItem[]>([]);
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPath] = useState('.'); // setCurrentPath removed - not used yet
  const [searchQuery, setSearchQuery] = useState('');
  const [showHidden, setShowHidden] = useState(false);

  useEffect(() => {
    loadDirectory(currentPath);
  }, [currentPath, currentProject]);

  const loadDirectory = async (path: string = '.') => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/files/list', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          path: path,
          include_hidden: showHidden,
          recursive: false
        }),
      });

      if (response.ok) {
        const data = await response.json();
        const files: FileItem[] = (data.files || []).map((file: any) => ({
          name: file.name,
          path: file.path,
          type: file.type,
          size: file.size,
          modified: file.modified ? new Date(file.modified) : undefined,
          isExpanded: false
        }));

        // Sort: directories first, then files, both alphabetically
        files.sort((a, b) => {
          if (a.type !== b.type) {
            return a.type === 'directory' ? -1 : 1;
          }
          return a.name.localeCompare(b.name);
        });

        setFileTree(files);
      } else {
        console.error('Failed to load directory:', response.statusText);
        setFileTree([]);
      }
    } catch (error) {
      console.error('Failed to load directory:', error);
      setFileTree([]);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleDirectory = async (item: FileItem, index: number) => {
    if (item.type !== 'directory') return;

    const newTree = [...fileTree];
    const targetItem = newTree[index];

    if (targetItem.isExpanded) {
      // Collapse
      targetItem.isExpanded = false;
      targetItem.children = undefined;
    } else {
      // Expand
      targetItem.isLoading = true;
      setFileTree(newTree);

      try {
        const response = await fetch('/api/files/list', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            path: item.path,
            include_hidden: showHidden,
            recursive: false
          }),
        });

        if (response.ok) {
          const data = await response.json();
          const children: FileItem[] = (data.files || []).map((file: any) => ({
            name: file.name,
            path: file.path,
            type: file.type,
            size: file.size,
            modified: file.modified ? new Date(file.modified) : undefined,
            isExpanded: false
          }));

          children.sort((a, b) => {
            if (a.type !== b.type) {
              return a.type === 'directory' ? -1 : 1;
            }
            return a.name.localeCompare(b.name);
          });

          targetItem.children = children;
          targetItem.isExpanded = true;
        }
      } catch (error) {
        console.error('Failed to load directory contents:', error);
      } finally {
        targetItem.isLoading = false;
        setFileTree([...newTree]);
      }
    }

    setFileTree(newTree);
  };

  const handleFileClick = (item: FileItem) => {
    setSelectedFile(item.path);
    onFileSelect?.(item);
  };

  const handleFileDoubleClick = (item: FileItem) => {
    if (item.type === 'file') {
      onFileOpen?.(item);
    }
  };

  const getFileIcon = (item: FileItem) => {
    if (item.type === 'directory') {
      return item.isExpanded ? '📂' : '📁';
    }

    const ext = item.name.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'js':
      case 'jsx':
        return '🟨';
      case 'ts':
      case 'tsx':
        return '🔷';
      case 'py':
        return '🐍';
      case 'html':
        return '🌐';
      case 'css':
      case 'scss':
        return '🎨';
      case 'json':
        return '📋';
      case 'md':
        return '📝';
      case 'yml':
      case 'yaml':
        return '⚙️';
      case 'docker':
      case 'dockerfile':
        return '🐳';
      case 'git':
        return '🔀';
      default:
        return '📄';
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const renderFileItem = (item: FileItem, index: number, level: number = 0) => {
    const isSelected = selectedFile === item.path;
    const isHidden = item.name.startsWith('.');

    if (searchQuery && !item.name.toLowerCase().includes(searchQuery.toLowerCase())) {
      return null;
    }

    return (
      <div key={item.path}>
        <div
          className={`flex items-center space-x-2 px-2 py-1 cursor-pointer hover:bg-growth-accent/10 transition-all hover-bloom ${
            isSelected ? 'bg-growth-accent/20 border-l-2 border-growth-accent' : ''
          } ${isHidden ? 'opacity-60' : ''}`}
          style={{ paddingLeft: `${level * 16 + 8}px` }}
          onClick={() => handleFileClick(item)}
          onDoubleClick={() => handleFileDoubleClick(item)}
        >
          {item.type === 'directory' && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                toggleDirectory(item, index);
              }}
              className="w-4 h-4 flex items-center justify-center hover:bg-growth-accent/20 rounded"
            >
              {item.isLoading ? (
                <div className="w-2 h-2 bg-growth-accent rounded-full animate-bounce" />
              ) : (
                <span className={`text-xs transition-transform duration-200 ${
                  item.isExpanded ? 'rotate-90' : ''
                }`}>
                  ▶
                </span>
              )}
            </button>
          )}
          
          <span className="text-lg">{getFileIcon(item)}</span>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <span className={`text-sm truncate ${
                item.type === 'directory' ? 'font-medium text-forest-primary' : 'text-earth-base'
              }`}>
                {item.name}
              </span>
              
              {item.type === 'file' && item.size && (
                <span className="text-xs text-earth-base/60 ml-2">
                  {formatFileSize(item.size)}
                </span>
              )}
            </div>
            
            {item.modified && (
              <div className="text-xs text-earth-base/50">
                {item.modified.toLocaleDateString()}
              </div>
            )}
          </div>
        </div>

        {/* Render children if expanded */}
        {item.isExpanded && item.children && (
          <div>
            {item.children.map((child, childIndex) => 
              renderFileItem(child, childIndex, level + 1)
            )}
          </div>
        )}
      </div>
    );
  };

  const filteredFiles = searchQuery 
    ? fileTree.filter(item => 
        item.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : fileTree;

  return (
    <div className="h-full flex flex-col bg-crystal-clear">
      {/* Header */}
      <div className="p-4 border-b border-growth-accent/20">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-forest-primary">📁 File Explorer</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowHidden(!showHidden)}
              className={`p-1 rounded text-xs transition-all hover-bloom ${
                showHidden ? 'bg-growth-accent text-crystal-clear' : 'bg-mist-gradient text-earth-base'
              }`}
              title="Toggle hidden files"
            >
              👁️
            </button>
            <button
              onClick={() => loadDirectory(currentPath)}
              className="p-1 rounded text-xs bg-mist-gradient text-earth-base hover-bloom transition-all"
              title="Refresh"
            >
              🔄
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="relative">
          <input
            type="text"
            placeholder="Search files..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full px-3 py-2 text-sm border border-growth-accent/20 rounded-lg bg-mist-gradient focus:outline-none focus:ring-2 focus:ring-growth-accent/50"
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery('')}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-earth-base/60 hover:text-earth-base"
            >
              ✕
            </button>
          )}
        </div>

        {/* Current Path */}
        <div className="mt-2 text-xs text-earth-base/60 font-mono">
          📍 {currentPath}
        </div>
      </div>

      {/* File Tree */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-growth-accent rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-growth-accent rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
              <div className="w-2 h-2 bg-growth-accent rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
              <span className="text-sm text-earth-base/60 ml-2">Loading files...</span>
            </div>
          </div>
        ) : filteredFiles.length > 0 ? (
          <div className="py-2">
            {filteredFiles.map((item, index) => renderFileItem(item, index))}
          </div>
        ) : (
          <div className="flex items-center justify-center h-32 text-earth-base/60">
            <div className="text-center">
              <div className="text-2xl mb-2">📂</div>
              <div className="text-sm">
                {searchQuery ? 'No files match your search' : 'No files found'}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-3 border-t border-growth-accent/20 bg-mist-gradient">
        <div className="flex items-center justify-between text-xs text-earth-base/60">
          <span>{filteredFiles.length} items</span>
          <span>
            {selectedFile ? `Selected: ${selectedFile.split('/').pop()}` : 'No selection'}
          </span>
        </div>
      </div>
    </div>
  );
};
