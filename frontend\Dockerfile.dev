# 🎂🚀 DeepNexus Frontend Development Dockerfile
# Fast iteration with volume mounting - no rebuilds needed!
FROM node:18-alpine

WORKDIR /app

# Install build dependencies for Alpine (minimal set)
RUN apk add --no-cache curl

# Copy package files first for better Docker layer caching
COPY package*.json ./

# Install dependencies (including dev dependencies)
# Use npm ci for faster, reliable, reproducible builds
RUN npm ci

# The source code will be mounted as a volume, so we don't copy it here
# This allows for hot reloading without rebuilding the container

# Expose port 3000 for Vite dev server
EXPOSE 3000

# Health check for development
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000 || exit 1

# Default command - can be overridden in docker-compose
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "3000"]
