"""
📊 Relevance Scorer - Score context relevance dynamically

This module provides dynamic relevance scoring with:
- Multi-dimensional relevance analysis
- Temporal relevance decay
- Content type prioritization
- User interaction patterns
- Semantic similarity scoring
"""

import math
import re
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)


class ScoringCriteria(str, Enum):
    """Relevance scoring criteria."""
    TEMPORAL = "temporal"              # Time-based relevance
    SEMANTIC = "semantic"              # Content similarity
    INTERACTION = "interaction"        # User interaction patterns
    CONTENT_TYPE = "content_type"      # Type-based priority
    REFERENCE = "reference"            # Cross-reference importance
    DECISION = "decision"              # Decision-making relevance


class RelevanceScore(BaseModel):
    """Comprehensive relevance score breakdown."""
    
    message_id: str = Field(..., description="Message identifier")
    overall_score: float = Field(..., description="Overall relevance score (0-1)")
    
    # Individual scoring dimensions
    temporal_score: float = Field(..., description="Temporal relevance score")
    semantic_score: float = Field(..., description="Semantic relevance score")
    interaction_score: float = Field(..., description="Interaction-based score")
    content_type_score: float = Field(..., description="Content type priority score")
    reference_score: float = Field(..., description="Reference importance score")
    decision_score: float = Field(..., description="Decision relevance score")
    
    # Scoring metadata
    content_type: str = Field(..., description="Detected content type")
    key_terms: List[str] = Field(default_factory=list, description="Extracted key terms")
    references: List[str] = Field(default_factory=list, description="Found references")
    timestamp: datetime = Field(default_factory=datetime.now, description="Scoring timestamp")
    
    # Scoring weights used
    weights: Dict[str, float] = Field(default_factory=dict, description="Weights used in scoring")


class RelevanceScorer:
    """
    📊 Advanced Relevance Scorer
    
    Dynamically scores context relevance using multiple dimensions
    to optimize context management and compression decisions.
    """
    
    def __init__(self):
        # Default scoring weights
        self.default_weights = {
            ScoringCriteria.TEMPORAL: 0.20,        # 20% - recency matters
            ScoringCriteria.SEMANTIC: 0.25,        # 25% - content similarity
            ScoringCriteria.INTERACTION: 0.15,     # 15% - user engagement
            ScoringCriteria.CONTENT_TYPE: 0.20,    # 20% - content importance
            ScoringCriteria.REFERENCE: 0.10,       # 10% - cross-references
            ScoringCriteria.DECISION: 0.10,        # 10% - decision impact
        }
        
        # Content type priorities
        self.content_type_priorities = {
            "code_block": 1.0,           # Highest priority
            "error_message": 0.9,        # Very high priority
            "decision": 0.9,             # Very high priority
            "file_reference": 0.8,       # High priority
            "user_request": 0.8,         # High priority
            "system_response": 0.6,      # Medium priority
            "explanation": 0.5,          # Medium priority
            "general_chat": 0.3,         # Low priority
            "greeting": 0.2,             # Very low priority
        }
        
        # Temporal decay parameters
        self.temporal_decay_hours = 24   # Hours for full decay
        self.temporal_half_life = 6      # Hours for 50% decay
        
        # Semantic similarity cache
        self.similarity_cache: Dict[str, float] = {}
        
        # Interaction tracking
        self.interaction_patterns: Dict[str, Dict[str, Any]] = {}
        
        # Reference graph
        self.reference_graph: Dict[str, Set[str]] = {}
    
    def score_message_relevance(
        self,
        message_content: str,
        message_timestamp: datetime,
        context_messages: List[Dict[str, Any]],
        current_topic: Optional[str] = None,
        user_focus: Optional[List[str]] = None,
        custom_weights: Optional[Dict[ScoringCriteria, float]] = None
    ) -> RelevanceScore:
        """Score message relevance using multiple criteria."""
        message_id = self._generate_message_id(message_content, message_timestamp)
        
        # Use custom weights or defaults
        weights = custom_weights or self.default_weights
        
        # Detect content type
        content_type = self._detect_content_type(message_content)
        
        # Extract key elements
        key_terms = self._extract_key_terms(message_content)
        references = self._extract_references(message_content)
        
        # Calculate individual scores
        temporal_score = self._calculate_temporal_score(message_timestamp)
        semantic_score = self._calculate_semantic_score(message_content, context_messages, current_topic)
        interaction_score = self._calculate_interaction_score(message_content, user_focus)
        content_type_score = self._calculate_content_type_score(content_type)
        reference_score = self._calculate_reference_score(references, context_messages)
        decision_score = self._calculate_decision_score(message_content)
        
        # Calculate weighted overall score
        overall_score = (
            temporal_score * weights[ScoringCriteria.TEMPORAL] +
            semantic_score * weights[ScoringCriteria.SEMANTIC] +
            interaction_score * weights[ScoringCriteria.INTERACTION] +
            content_type_score * weights[ScoringCriteria.CONTENT_TYPE] +
            reference_score * weights[ScoringCriteria.REFERENCE] +
            decision_score * weights[ScoringCriteria.DECISION]
        )
        
        # Update reference graph
        self._update_reference_graph(message_id, references)
        
        return RelevanceScore(
            message_id=message_id,
            overall_score=min(overall_score, 1.0),  # Cap at 1.0
            temporal_score=temporal_score,
            semantic_score=semantic_score,
            interaction_score=interaction_score,
            content_type_score=content_type_score,
            reference_score=reference_score,
            decision_score=decision_score,
            content_type=content_type,
            key_terms=key_terms,
            references=references,
            weights=weights
        )
    
    def score_multiple_messages(
        self,
        messages: List[Dict[str, Any]],
        current_topic: Optional[str] = None,
        user_focus: Optional[List[str]] = None,
        custom_weights: Optional[Dict[ScoringCriteria, float]] = None
    ) -> List[RelevanceScore]:
        """Score relevance for multiple messages."""
        scores = []
        
        for message in messages:
            content = message.get("content", "")
            timestamp = message.get("timestamp", datetime.now())
            
            if isinstance(timestamp, str):
                timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            
            score = self.score_message_relevance(
                content, timestamp, messages, current_topic, user_focus, custom_weights
            )
            scores.append(score)
        
        return scores
    
    def _calculate_temporal_score(self, message_timestamp: datetime) -> float:
        """Calculate temporal relevance score with exponential decay."""
        now = datetime.now()
        
        # Handle timezone-naive timestamps
        if message_timestamp.tzinfo is None:
            message_timestamp = message_timestamp.replace(tzinfo=now.tzinfo)
        if now.tzinfo is None:
            now = now.replace(tzinfo=message_timestamp.tzinfo)
        
        time_diff = now - message_timestamp
        hours_old = time_diff.total_seconds() / 3600
        
        # Exponential decay with half-life
        decay_factor = math.exp(-hours_old * math.log(2) / self.temporal_half_life)
        
        # Ensure minimum score for very recent messages
        if hours_old < 1:
            return 1.0
        
        return max(decay_factor, 0.1)  # Minimum 10% relevance
    
    def _calculate_semantic_score(
        self,
        message_content: str,
        context_messages: List[Dict[str, Any]],
        current_topic: Optional[str] = None
    ) -> float:
        """Calculate semantic similarity score."""
        if not context_messages and not current_topic:
            return 0.5  # Neutral score
        
        # Extract key terms from message
        message_terms = set(self._extract_key_terms(message_content))
        
        # Calculate similarity with current topic
        topic_similarity = 0.0
        if current_topic:
            topic_terms = set(self._extract_key_terms(current_topic))
            if topic_terms and message_terms:
                intersection = message_terms.intersection(topic_terms)
                union = message_terms.union(topic_terms)
                topic_similarity = len(intersection) / len(union)
        
        # Calculate similarity with recent context
        context_similarity = 0.0
        if context_messages:
            recent_messages = context_messages[-5:]  # Last 5 messages
            context_terms = set()
            
            for msg in recent_messages:
                content = msg.get("content", "")
                context_terms.update(self._extract_key_terms(content))
            
            if context_terms and message_terms:
                intersection = message_terms.intersection(context_terms)
                union = message_terms.union(context_terms)
                context_similarity = len(intersection) / len(union)
        
        # Combine similarities
        return max(topic_similarity, context_similarity)
    
    def _calculate_interaction_score(
        self,
        message_content: str,
        user_focus: Optional[List[str]] = None
    ) -> float:
        """Calculate interaction-based relevance score."""
        score = 0.5  # Base score
        
        # Check for user focus terms
        if user_focus:
            message_lower = message_content.lower()
            focus_matches = sum(1 for term in user_focus if term.lower() in message_lower)
            if focus_matches > 0:
                score += 0.3 * min(focus_matches / len(user_focus), 1.0)
        
        # Check for interactive elements
        interactive_patterns = [
            r'\?',                    # Questions
            r'\b(please|help|how)\b', # Requests
            r'\b(error|issue|problem)\b', # Problems
            r'\b(thanks|thank you)\b', # Acknowledgments
        ]
        
        for pattern in interactive_patterns:
            if re.search(pattern, message_content, re.IGNORECASE):
                score += 0.1
        
        return min(score, 1.0)
    
    def _calculate_content_type_score(self, content_type: str) -> float:
        """Calculate content type priority score."""
        return self.content_type_priorities.get(content_type, 0.5)
    
    def _calculate_reference_score(
        self,
        references: List[str],
        context_messages: List[Dict[str, Any]]
    ) -> float:
        """Calculate reference importance score."""
        if not references:
            return 0.3  # Low score for no references
        
        # Count how many references appear in context
        context_content = " ".join(msg.get("content", "") for msg in context_messages[-10:])
        
        reference_matches = 0
        for ref in references:
            if ref in context_content:
                reference_matches += 1
        
        # Score based on reference density and matches
        reference_density = len(references) / 10  # Normalize by expected max
        match_ratio = reference_matches / len(references) if references else 0
        
        return min(reference_density * 0.5 + match_ratio * 0.5, 1.0)
    
    def _calculate_decision_score(self, message_content: str) -> float:
        """Calculate decision-making relevance score."""
        decision_patterns = [
            r'\b(decided|chosen|selected|implemented)\b',
            r'\b(will use|going with|opted for)\b',
            r'\b(approach|solution|strategy)\b',
            r'\b(final|conclusion|result)\b',
        ]
        
        decision_count = 0
        for pattern in decision_patterns:
            matches = len(re.findall(pattern, message_content, re.IGNORECASE))
            decision_count += matches
        
        # Normalize decision score
        return min(decision_count * 0.2, 1.0)
    
    def _detect_content_type(self, content: str) -> str:
        """Detect the type of content in the message."""
        content_lower = content.lower()
        
        # Check for code blocks
        if re.search(r'```[\s\S]*?```|`[^`]+`', content):
            return "code_block"
        
        # Check for error messages
        if re.search(r'\b(error|exception|traceback|failed)\b', content_lower):
            return "error_message"
        
        # Check for decisions
        if re.search(r'\b(decided|chosen|implemented|approach)\b', content_lower):
            return "decision"
        
        # Check for file references
        if re.search(r'/[^\s]+\.[a-zA-Z]+|[a-zA-Z0-9_]+\.[a-zA-Z]+', content):
            return "file_reference"
        
        # Check for questions (user requests)
        if re.search(r'\?|how to|can you|please|help', content_lower):
            return "user_request"
        
        # Check for explanations
        if re.search(r'\b(because|since|therefore|thus|explanation)\b', content_lower):
            return "explanation"
        
        # Check for greetings
        if re.search(r'\b(hello|hi|hey|good morning|good afternoon)\b', content_lower):
            return "greeting"
        
        # Default to general chat
        return "general_chat"
    
    def _extract_key_terms(self, content: str) -> List[str]:
        """Extract key terms from content."""
        # Remove code blocks for term extraction
        content_no_code = re.sub(r'```[\s\S]*?```', '', content)
        content_no_code = re.sub(r'`[^`]+`', '', content_no_code)
        
        # Extract meaningful terms (3+ characters, alphanumeric)
        terms = re.findall(r'\b[a-zA-Z][a-zA-Z0-9_]{2,}\b', content_no_code)
        
        # Filter out common words
        common_words = {
            'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had',
            'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his',
            'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy',
            'did', 'man', 'men', 'put', 'say', 'she', 'too', 'use', 'way', 'will'
        }
        
        filtered_terms = [term for term in terms if term.lower() not in common_words]
        
        # Return unique terms, limited to top 10
        return list(set(filtered_terms))[:10]
    
    def _extract_references(self, content: str) -> List[str]:
        """Extract references (files, URLs, etc.) from content."""
        references = []
        
        # Extract file paths
        file_patterns = [
            r'/[^\s]+\.[a-zA-Z]+',           # Unix-style paths
            r'[a-zA-Z]:\\[^\s]+\.[a-zA-Z]+', # Windows-style paths
            r'[a-zA-Z0-9_]+\.[a-zA-Z]+',     # Simple filenames
        ]
        
        for pattern in file_patterns:
            matches = re.findall(pattern, content)
            references.extend(matches)
        
        # Extract URLs
        url_pattern = r'https?://[^\s]+'
        urls = re.findall(url_pattern, content)
        references.extend(urls)
        
        # Extract function/method references
        function_pattern = r'\b[a-zA-Z_][a-zA-Z0-9_]*\([^)]*\)'
        functions = re.findall(function_pattern, content)
        references.extend(functions)
        
        return list(set(references))  # Remove duplicates
    
    def _generate_message_id(self, content: str, timestamp: datetime) -> str:
        """Generate unique message ID."""
        import hashlib
        content_hash = hashlib.md5(content.encode()).hexdigest()[:8]
        timestamp_str = timestamp.strftime("%Y%m%d_%H%M%S")
        return f"msg_{timestamp_str}_{content_hash}"
    
    def _update_reference_graph(self, message_id: str, references: List[str]) -> None:
        """Update the reference graph for cross-reference analysis."""
        if message_id not in self.reference_graph:
            self.reference_graph[message_id] = set()
        
        self.reference_graph[message_id].update(references)
    
    def get_relevance_statistics(self) -> Dict[str, Any]:
        """Get relevance scoring statistics."""
        return {
            "total_messages_scored": len(self.reference_graph),
            "total_references": sum(len(refs) for refs in self.reference_graph.values()),
            "average_references_per_message": (
                sum(len(refs) for refs in self.reference_graph.values()) / len(self.reference_graph)
                if self.reference_graph else 0
            ),
            "cache_size": len(self.similarity_cache),
            "content_type_priorities": self.content_type_priorities.copy(),
            "default_weights": self.default_weights.copy()
        }


# Global relevance scorer instance
_relevance_scorer: Optional[RelevanceScorer] = None


def get_relevance_scorer() -> RelevanceScorer:
    """Get the global relevance scorer instance."""
    global _relevance_scorer
    if _relevance_scorer is None:
        _relevance_scorer = RelevanceScorer()
    return _relevance_scorer
