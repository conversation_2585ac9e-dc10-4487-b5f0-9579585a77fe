"""
Autonomous Continue Mode - Phase 8C
Progressive Development Loops with Intelligent Error Handling

This module provides autonomous development capabilities that allow the AI agent
to continue working on tasks through multiple iterations, learning from errors
and adapting its approach for better results.

Features:
- Configurable loop management with safety mechanisms
- Intelligent error pattern recognition and resolution
- Context adaptation based on error patterns and progress
- Real-time progress monitoring and reporting
- Integration with Code Intelligence Hub for enhanced context
- Circuit breaker patterns for infinite loop prevention
"""

from .core import AutonomousContinueEngine
from .models import (
    ContinueSession,
    LoopState,
    ErrorPattern,
    ProgressReport,
    ContinueConfig,
    SafetyLimits
)
from .loop_manager import LoopManager
from .error_intelligence import ErrorIntelligenceSystem
from .context_adapter import ContextAdaptationEngine

__all__ = [
    'AutonomousContinueEngine',
    'ContinueSession',
    'LoopState',
    'ErrorPattern',
    'ProgressReport',
    'ContinueConfig',
    'SafetyLimits',
    'LoopManager',
    'ErrorIntelligenceSystem',
    'ContextAdaptationEngine'
]

__version__ = "8C.1.0"
__author__ = "DeepNexus AI Team"
