"""
📊 Analytics API Endpoints - Revolutionary Usage Analytics & Insights

This module provides comprehensive analytics API endpoints:
- Real-time usage tracking and metrics
- Advanced analytics reports and insights
- Performance monitoring and alerting
- User behavior analysis and patterns
- Predictive analytics and forecasting
- Interactive dashboards and visualizations
"""

from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, List, Optional
from fastapi import APIRouter, HTTPException, Query, BackgroundTasks
from pydantic import BaseModel, Field
import logging

from ..analytics import (
    get_usage_tracker,
    get_analytics_engine,
    get_performance_monitor,
    EventType,
    InsightType,
    AlertSeverity,
    MetricType
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/analytics", tags=["analytics"])

# Request/Response Models
class TrackEventRequest(BaseModel):
    """Request model for tracking events."""
    event_type: EventType = Field(..., description="Type of event to track")
    user_id: Optional[str] = Field(None, description="User identifier")
    session_id: Optional[str] = Field(None, description="Session identifier")
    project_id: Optional[str] = Field(None, description="Project identifier")
    event_data: Optional[Dict[str, Any]] = Field(None, description="Event-specific data")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    duration_ms: Optional[float] = Field(None, description="Event duration in milliseconds")
    success: bool = Field(default=True, description="Whether event was successful")
    error_message: Optional[str] = Field(None, description="Error message if failed")


class AnalyticsReportRequest(BaseModel):
    """Request model for analytics reports."""
    report_type: str = Field(default="comprehensive", description="Type of report")
    start_time: Optional[datetime] = Field(None, description="Report start time")
    end_time: Optional[datetime] = Field(None, description="Report end time")
    include_predictions: bool = Field(default=True, description="Include predictive analytics")
    include_recommendations: bool = Field(default=True, description="Include recommendations")


# Usage Tracking Endpoints
@router.post("/track-event")
async def track_event(request: TrackEventRequest):
    """Track a usage event with comprehensive metadata."""
    try:
        usage_tracker = get_usage_tracker()
        
        event_id = await usage_tracker.track_event(
            event_type=request.event_type,
            user_id=request.user_id,
            session_id=request.session_id,
            project_id=request.project_id,
            event_data=request.event_data,
            metadata=request.metadata,
            duration_ms=request.duration_ms,
            success=request.success,
            error_message=request.error_message
        )
        
        return {
            "event_tracked": True,
            "event_id": event_id,
            "event_type": request.event_type.value,
            "message": f"✅ Event {request.event_type.value} tracked successfully",
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Event tracking failed: {e}")
        raise HTTPException(status_code=500, detail=f"Event tracking failed: {str(e)}")


@router.get("/usage-metrics")
async def get_usage_metrics(
    start_time: Optional[datetime] = Query(None, description="Start time for metrics"),
    end_time: Optional[datetime] = Query(None, description="End time for metrics"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    project_id: Optional[str] = Query(None, description="Filter by project ID")
):
    """Get comprehensive usage metrics for specified period."""
    try:
        usage_tracker = get_usage_tracker()
        
        metrics = await usage_tracker.get_usage_metrics(
            start_time=start_time,
            end_time=end_time,
            user_id=user_id,
            project_id=project_id
        )
        
        return {
            "usage_metrics": metrics,
            "period": {
                "start_time": metrics.start_time,
                "end_time": metrics.end_time,
                "duration_hours": metrics.duration_hours
            },
            "summary": {
                "total_users": metrics.total_users,
                "ai_success_rate": f"{metrics.ai_success_rate:.1f}%",
                "average_response_time": f"{metrics.average_response_time:.0f}ms",
                "error_rate": f"{metrics.error_rate:.1f}%"
            },
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get usage metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get usage metrics: {str(e)}")


@router.get("/real-time-stats")
async def get_real_time_stats():
    """Get real-time system statistics and activity."""
    try:
        usage_tracker = get_usage_tracker()
        stats = await usage_tracker.get_real_time_stats()
        
        return {
            "real_time_stats": stats,
            "status": "operational",
            "message": f"📊 System is {stats.get('system_health', 'unknown')} with {stats.get('active_users', 0)} active users",
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get real-time stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get real-time stats: {str(e)}")


@router.get("/feature-usage")
async def get_feature_usage_stats():
    """Get feature usage statistics and adoption metrics."""
    try:
        usage_tracker = get_usage_tracker()
        feature_stats = await usage_tracker.get_feature_usage_stats()
        
        return {
            "feature_usage": feature_stats,
            "insights": {
                "most_popular_feature": feature_stats["top_features"][0][0] if feature_stats["top_features"] else "None",
                "feature_diversity": len(feature_stats["feature_usage_distribution"]),
                "adoption_rate": "high" if len(feature_stats["feature_usage_distribution"]) > 5 else "medium"
            },
            "recommendations": await _generate_feature_recommendations(feature_stats),
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get feature usage stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get feature usage stats: {str(e)}")


@router.get("/user-journey/{user_id}")
async def get_user_journey(
    user_id: str,
    limit: int = Query(100, description="Maximum number of events to return")
):
    """Get user journey events and behavior analysis."""
    try:
        usage_tracker = get_usage_tracker()
        analytics_engine = get_analytics_engine()
        
        # Get user journey
        journey = await usage_tracker.get_user_journey(user_id, limit)
        
        # Analyze behavior
        behavior_analysis = await analytics_engine.analyze_user_behavior(user_id)
        
        return {
            "user_id": user_id,
            "journey_events": journey,
            "behavior_analysis": behavior_analysis,
            "journey_summary": {
                "total_events": len(journey),
                "event_types": list(set(event.event_type for event in journey)),
                "time_span": (journey[-1].timestamp - journey[0].timestamp).total_seconds() / 3600 if len(journey) > 1 else 0
            },
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get user journey: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get user journey: {str(e)}")


# Analytics & Insights Endpoints
@router.post("/generate-report")
async def generate_analytics_report(request: AnalyticsReportRequest):
    """🧠 Generate comprehensive analytics report with AI-powered insights."""
    try:
        analytics_engine = get_analytics_engine()
        
        report = await analytics_engine.generate_comprehensive_report(
            start_time=request.start_time,
            end_time=request.end_time,
            report_type=request.report_type
        )
        
        return {
            "analytics_report": report,
            "executive_summary": report.executive_summary,
            "key_insights": len(report.insights),
            "high_priority_items": len(report.high_priority_insights),
            "immediate_actions": report.immediate_actions,
            "user_message": f"📊 Analytics report generated! Found {len(report.high_priority_insights)} high-priority insights and {len(report.immediate_actions)} immediate action items.",
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Analytics report generation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Analytics report generation failed: {str(e)}")


@router.get("/insights")
async def get_insights(
    insight_type: Optional[InsightType] = Query(None, description="Filter by insight type"),
    priority: Optional[str] = Query(None, description="Filter by priority (low/medium/high/critical)"),
    days: int = Query(7, description="Number of days to analyze")
):
    """Get AI-powered insights and recommendations."""
    try:
        analytics_engine = get_analytics_engine()
        
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        
        # Generate insights
        insights = await analytics_engine._generate_all_insights(start_time, end_time)
        
        # Apply filters
        if insight_type:
            insights = [i for i in insights if i.insight_type == insight_type]
        
        if priority:
            insights = [i for i in insights if i.priority == priority]
        
        # Sort by priority and impact
        priority_order = {"critical": 4, "high": 3, "medium": 2, "low": 1}
        insights.sort(key=lambda x: (priority_order.get(x.priority, 0), x.impact_score), reverse=True)
        
        return {
            "insights": insights,
            "summary": {
                "total_insights": len(insights),
                "critical_insights": len([i for i in insights if i.priority == "critical"]),
                "high_priority_insights": len([i for i in insights if i.priority == "high"]),
                "insight_types": list(set(i.insight_type for i in insights))
            },
            "recommendations": [rec for insight in insights[:5] for rec in insight.recommendations],
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get insights: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get insights: {str(e)}")


@router.get("/behavior-analysis")
async def analyze_user_behavior(
    user_id: Optional[str] = Query(None, description="Specific user ID to analyze"),
    days: int = Query(30, description="Number of days to analyze")
):
    """Analyze user behavior patterns and trends."""
    try:
        analytics_engine = get_analytics_engine()
        
        behavior_analysis = await analytics_engine.analyze_user_behavior(user_id, days)
        
        return {
            "behavior_analysis": behavior_analysis,
            "analysis_period_days": days,
            "user_scope": "individual" if user_id else "aggregate",
            "insights": await _generate_behavior_insights(behavior_analysis),
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Behavior analysis failed: {e}")
        raise HTTPException(status_code=500, detail=f"Behavior analysis failed: {str(e)}")


@router.get("/anomaly-detection")
async def detect_anomalies(
    metric_name: str = Query(..., description="Metric to analyze (response_time, error_rate, user_activity)"),
    lookback_days: int = Query(7, description="Number of days to analyze")
):
    """Detect anomalies in system metrics."""
    try:
        analytics_engine = get_analytics_engine()
        
        anomalies = await analytics_engine.detect_anomalies(metric_name, lookback_days)
        
        return {
            "metric_name": metric_name,
            "lookback_days": lookback_days,
            "anomalies_detected": len(anomalies),
            "anomalies": anomalies,
            "severity_summary": await _analyze_anomaly_severity(anomalies),
            "recommendations": await _generate_anomaly_recommendations(anomalies),
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Anomaly detection failed: {e}")
        raise HTTPException(status_code=500, detail=f"Anomaly detection failed: {str(e)}")


@router.get("/predictions")
async def get_predictions(
    forecast_hours: int = Query(24, description="Number of hours to forecast")
):
    """Get predictive analytics and system load forecasts."""
    try:
        analytics_engine = get_analytics_engine()
        
        predictions = await analytics_engine.predict_system_load(forecast_hours)
        
        return {
            "predictions": predictions,
            "forecast_period_hours": forecast_hours,
            "confidence_level": predictions.get("confidence_level", 0.75),
            "key_predictions": {
                "user_activity": predictions.get("predicted_user_activity", {}),
                "ai_requests": predictions.get("predicted_ai_requests", {}),
                "resource_usage": predictions.get("predicted_resource_usage", {})
            },
            "planning_recommendations": predictions.get("recommendations", []),
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Predictions generation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Predictions generation failed: {str(e)}")


# Performance Monitoring Endpoints
@router.post("/performance/start-monitoring")
async def start_performance_monitoring(background_tasks: BackgroundTasks):
    """Start real-time performance monitoring."""
    try:
        performance_monitor = get_performance_monitor()
        
        # Start monitoring in background
        background_tasks.add_task(performance_monitor.start_monitoring)
        
        return {
            "monitoring_started": True,
            "message": "⚡ Performance monitoring started successfully",
            "collection_interval": performance_monitor.collection_interval,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to start performance monitoring: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start performance monitoring: {str(e)}")


@router.post("/performance/stop-monitoring")
async def stop_performance_monitoring():
    """Stop performance monitoring."""
    try:
        performance_monitor = get_performance_monitor()
        await performance_monitor.stop_monitoring()
        
        return {
            "monitoring_stopped": True,
            "message": "⚡ Performance monitoring stopped",
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to stop performance monitoring: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to stop performance monitoring: {str(e)}")


@router.get("/performance/metrics")
async def get_performance_metrics():
    """Get current performance metrics."""
    try:
        performance_monitor = get_performance_monitor()
        metrics = await performance_monitor.collect_metrics()
        
        return {
            "performance_metrics": metrics,
            "system_status": await _determine_system_status(metrics),
            "health_score": await _calculate_health_score(metrics),
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get performance metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get performance metrics: {str(e)}")


@router.get("/performance/summary")
async def get_performance_summary(
    hours: int = Query(1, description="Number of hours to summarize")
):
    """Get performance summary for specified period."""
    try:
        performance_monitor = get_performance_monitor()
        summary = await performance_monitor.get_performance_summary(hours)
        
        return {
            "performance_summary": summary,
            "period_hours": hours,
            "overall_health": await _assess_overall_health(summary),
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get performance summary: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get performance summary: {str(e)}")


@router.get("/performance/alerts")
async def get_performance_alerts():
    """Get active performance alerts."""
    try:
        performance_monitor = get_performance_monitor()
        alerts = await performance_monitor.get_active_alerts()
        
        return {
            "active_alerts": alerts,
            "alert_count": len(alerts),
            "critical_alerts": len([a for a in alerts if a.severity == AlertSeverity.CRITICAL]),
            "alert_summary": await _summarize_alerts(alerts),
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get performance alerts: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get performance alerts: {str(e)}")


@router.post("/performance/alerts/{alert_id}/acknowledge")
async def acknowledge_alert(alert_id: str):
    """Acknowledge a performance alert."""
    try:
        performance_monitor = get_performance_monitor()
        success = await performance_monitor.acknowledge_alert(alert_id)
        
        if success:
            return {
                "alert_acknowledged": True,
                "alert_id": alert_id,
                "message": f"✅ Alert {alert_id} acknowledged",
                "timestamp": datetime.now()
            }
        else:
            raise HTTPException(status_code=404, detail="Alert not found")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to acknowledge alert: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to acknowledge alert: {str(e)}")


@router.post("/performance/alerts/{alert_id}/resolve")
async def resolve_alert(alert_id: str):
    """Resolve a performance alert."""
    try:
        performance_monitor = get_performance_monitor()
        success = await performance_monitor.resolve_alert(alert_id)
        
        if success:
            return {
                "alert_resolved": True,
                "alert_id": alert_id,
                "message": f"✅ Alert {alert_id} resolved",
                "timestamp": datetime.now()
            }
        else:
            raise HTTPException(status_code=404, detail="Alert not found")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to resolve alert: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to resolve alert: {str(e)}")


@router.get("/performance/trends/{metric_type}")
async def get_performance_trends(
    metric_type: MetricType,
    hours: int = Query(24, description="Number of hours for trend analysis")
):
    """Get performance trends for specific metric."""
    try:
        performance_monitor = get_performance_monitor()
        trends = await performance_monitor.get_performance_trends(metric_type, hours)
        
        return {
            "performance_trends": trends,
            "metric_type": metric_type.value,
            "analysis_period_hours": hours,
            "trend_insights": await _analyze_trend_insights(trends),
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get performance trends: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get performance trends: {str(e)}")


# System Health and Overview
@router.get("/health")
async def get_analytics_system_health():
    """Get overall analytics system health and status."""
    try:
        usage_tracker = get_usage_tracker()
        analytics_engine = get_analytics_engine()
        performance_monitor = get_performance_monitor()
        
        # Get stats from all components
        real_time_stats = await usage_tracker.get_real_time_stats()
        performance_stats = performance_monitor.get_performance_statistics()
        
        return {
            "healthy": True,
            "system_status": "operational",
            "components": {
                "usage_tracker": {
                    "status": "healthy",
                    "events_tracked": real_time_stats.get("total_events_tracked", 0),
                    "active_users": real_time_stats.get("active_users", 0)
                },
                "analytics_engine": {
                    "status": "healthy",
                    "insights_generated": "operational"
                },
                "performance_monitor": {
                    "status": "healthy",
                    "monitoring_active": performance_stats["monitoring_active"],
                    "metrics_collected": performance_stats["metrics_collected"]
                }
            },
            "overall_metrics": {
                "system_health": real_time_stats.get("system_health", "unknown"),
                "active_sessions": real_time_stats.get("active_sessions", 0),
                "events_per_minute": real_time_stats.get("events_per_minute", 0)
            },
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Analytics system health check failed: {e}")
        return {
            "healthy": False,
            "error": str(e),
            "timestamp": datetime.now()
        }


# Helper Functions
async def _generate_feature_recommendations(feature_stats: Dict[str, Any]) -> List[str]:
    """Generate feature usage recommendations."""
    recommendations = []

    if feature_stats["unique_features_used"] < 5:
        recommendations.append("🎯 Implement feature discovery tutorials to increase adoption")

    if feature_stats["total_feature_uses"] < 100:
        recommendations.append("📊 Add contextual feature suggestions to boost usage")

    if len(feature_stats["recently_used_features"]) < 3:
        recommendations.append("⚡ Improve feature visibility and accessibility")

    return recommendations


async def _generate_behavior_insights(behavior_analysis: Dict[str, Any]) -> List[str]:
    """Generate behavior analysis insights."""
    insights = []

    if "user_engagement" in behavior_analysis:
        engagement = behavior_analysis["user_engagement"]
        if engagement == "high":
            insights.append("🎯 Users show high engagement patterns")
        elif engagement == "medium":
            insights.append("📊 User engagement is moderate with room for improvement")
        else:
            insights.append("⚠️ Low user engagement detected - investigate user experience")

    if "feature_adoption" in behavior_analysis:
        adoption = behavior_analysis["feature_adoption"]
        if adoption == "low":
            insights.append("🔍 Feature adoption is low - consider onboarding improvements")

    return insights


async def _analyze_anomaly_severity(anomalies: List[Dict[str, Any]]) -> Dict[str, int]:
    """Analyze anomaly severity distribution."""
    severity_counts = {"low": 0, "medium": 0, "high": 0, "critical": 0}

    for anomaly in anomalies:
        severity = anomaly.get("severity", "medium")
        if severity in severity_counts:
            severity_counts[severity] += 1

    return severity_counts


async def _generate_anomaly_recommendations(anomalies: List[Dict[str, Any]]) -> List[str]:
    """Generate recommendations based on detected anomalies."""
    recommendations = []

    if any(a.get("severity") == "critical" for a in anomalies):
        recommendations.append("🚨 Critical anomalies detected - immediate investigation required")

    if len(anomalies) > 5:
        recommendations.append("📊 Multiple anomalies detected - review system stability")

    if any(a.get("metric") == "response_time" for a in anomalies):
        recommendations.append("⚡ Response time anomalies - optimize performance bottlenecks")

    return recommendations


async def _determine_system_status(metrics: Any) -> str:
    """Determine system status based on performance metrics."""
    if metrics.cpu_usage_percent > 90 or metrics.memory_usage_percent > 95:
        return "critical"
    elif metrics.cpu_usage_percent > 80 or metrics.memory_usage_percent > 85:
        return "warning"
    elif metrics.error_rate_percent > 10:
        return "degraded"
    else:
        return "healthy"


async def _calculate_health_score(metrics: Any) -> float:
    """Calculate overall health score (0-100)."""
    cpu_score = max(0, 100 - metrics.cpu_usage_percent)
    memory_score = max(0, 100 - metrics.memory_usage_percent)
    response_score = max(0, 100 - (metrics.response_time_ms / 50))  # 5000ms = 0 score
    error_score = max(0, 100 - (metrics.error_rate_percent * 10))  # 10% error = 0 score

    # Weighted average
    health_score = (cpu_score * 0.25 + memory_score * 0.25 +
                   response_score * 0.30 + error_score * 0.20)

    return min(100.0, max(0.0, health_score))


async def _assess_overall_health(summary: Dict[str, Any]) -> str:
    """Assess overall health from performance summary."""
    if "performance_score" in summary:
        score = summary["performance_score"]
        if score >= 90:
            return "excellent"
        elif score >= 75:
            return "good"
        elif score >= 60:
            return "fair"
        else:
            return "poor"

    return "unknown"


async def _summarize_alerts(alerts: List[Any]) -> Dict[str, Any]:
    """Summarize active alerts."""
    if not alerts:
        return {"message": "No active alerts"}

    severity_counts = {"low": 0, "medium": 0, "high": 0, "critical": 0}
    metric_types = {}

    for alert in alerts:
        severity = alert.severity.value if hasattr(alert.severity, 'value') else str(alert.severity)
        if severity in severity_counts:
            severity_counts[severity] += 1

        metric_type = alert.metric_type.value if hasattr(alert.metric_type, 'value') else str(alert.metric_type)
        metric_types[metric_type] = metric_types.get(metric_type, 0) + 1

    return {
        "severity_distribution": severity_counts,
        "metric_distribution": metric_types,
        "most_common_metric": max(metric_types.items(), key=lambda x: x[1])[0] if metric_types else None,
        "requires_immediate_attention": severity_counts["critical"] > 0 or severity_counts["high"] > 2
    }


async def _analyze_trend_insights(trends: Dict[str, Any]) -> List[str]:
    """Analyze trend data and generate insights."""
    insights = []

    trend_direction = trends.get("trend_direction", "stable")

    if trend_direction == "increasing":
        insights.append("📈 Metric is trending upward - monitor for potential issues")
    elif trend_direction == "decreasing":
        insights.append("📉 Metric is trending downward - investigate potential improvements")
    else:
        insights.append("📊 Metric is stable - system performing consistently")

    current_value = trends.get("current_value", 0)
    average_value = trends.get("average_value", 0)

    if current_value > average_value * 1.2:
        insights.append("⚠️ Current value significantly above average")
    elif current_value < average_value * 0.8:
        insights.append("✅ Current value below average - good performance")

    return insights
