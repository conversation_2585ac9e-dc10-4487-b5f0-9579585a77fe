import React, { useState, useEffect } from 'react';

interface ProjectTemplate {
  id: string;
  name: string;
  description: string;
  type: string;
  icon: string;
  features: string[];
  ai_suggestions: string[];
  estimated_setup_time: string;
}

interface CreateProjectData {
  name: string;
  description: string;
  type: string;
  template_id?: string;
  ai_features: {
    auto_planning: boolean;
    code_analysis: boolean;
    optimization: boolean;
    testing: boolean;
    documentation: boolean;
  };
  config: {
    language?: string;
    framework?: string;
    database?: string;
    deployment?: string;
  };
  repository: {
    initialize_git: boolean;
    create_readme: boolean;
    add_gitignore: boolean;
    license?: string;
  };
}

interface WizardProps {
  isOpen: boolean;
  onClose: () => void;
  onProjectCreated: (project: any) => void;
}

export const ProjectCreationWizard: React.FC<WizardProps> = ({
  isOpen,
  onClose,
  onProjectCreated
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [templates, setTemplates] = useState<ProjectTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);
  
  const [projectData, setProjectData] = useState<CreateProjectData>({
    name: '',
    description: '',
    type: 'web_app',
    ai_features: {
      auto_planning: true,
      code_analysis: true,
      optimization: false,
      testing: true,
      documentation: true
    },
    config: {},
    repository: {
      initialize_git: true,
      create_readme: true,
      add_gitignore: true,
      license: 'MIT'
    }
  });

  useEffect(() => {
    if (isOpen) {
      loadTemplates();
      setCurrentStep(1);
    }
  }, [isOpen]);

  const loadTemplates = async () => {
    try {
      const response = await fetch('/api/projects/templates/');
      if (response.ok) {
        const data = await response.json();
        setTemplates(data.templates || []);
      }
    } catch (error) {
      console.error('Failed to load templates:', error);
      // Mock templates
      setTemplates([
        {
          id: 'react-app',
          name: 'React Web App',
          description: 'Modern React application with TypeScript and Vite',
          type: 'web_app',
          icon: '⚛️',
          features: ['TypeScript', 'Vite', 'Tailwind CSS', 'React Router'],
          ai_suggestions: ['Add state management', 'Implement testing', 'Setup CI/CD'],
          estimated_setup_time: '5 minutes'
        },
        {
          id: 'fastapi-backend',
          name: 'FastAPI Backend',
          description: 'High-performance API with automatic documentation',
          type: 'api',
          icon: '🚀',
          features: ['FastAPI', 'SQLAlchemy', 'Pydantic', 'Docker'],
          ai_suggestions: ['Add authentication', 'Implement caching', 'Setup monitoring'],
          estimated_setup_time: '8 minutes'
        },
        {
          id: 'data-science',
          name: 'Data Science Project',
          description: 'Jupyter notebooks with ML libraries and visualization',
          type: 'data_science',
          icon: '📊',
          features: ['Jupyter', 'Pandas', 'Scikit-learn', 'Matplotlib'],
          ai_suggestions: ['Add data validation', 'Implement model versioning', 'Create dashboards'],
          estimated_setup_time: '6 minutes'
        }
      ]);
    }
  };

  const generateAISuggestions = async (projectName: string, description: string) => {
    try {
      const response = await fetch('/api/test/llm', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: `Generate 3 creative suggestions for a project named "${projectName}" with description: "${description}". Focus on features, architecture, or improvements.`,
          context: { type: 'project_suggestions' }
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        const suggestions = data.response.split('\n').filter((s: string) => s.trim()).slice(0, 3);
        setAiSuggestions(suggestions);
      }
    } catch (error) {
      console.error('Failed to generate AI suggestions:', error);
      setAiSuggestions([
        '🤖 Add intelligent code completion',
        '🔍 Implement automated testing',
        '📊 Include performance monitoring'
      ]);
    }
  };

  const createProject = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/projects/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(projectData)
      });

      if (response.ok) {
        const data = await response.json();
        onProjectCreated(data.project);
        onClose();
        setCurrentStep(1);
        setProjectData({
          name: '',
          description: '',
          type: 'web_app',
          ai_features: {
            auto_planning: true,
            code_analysis: true,
            optimization: false,
            testing: true,
            documentation: true
          },
          config: {},
          repository: {
            initialize_git: true,
            create_readme: true,
            add_gitignore: true,
            license: 'MIT'
          }
        });
      } else {
        throw new Error('Failed to create project');
      }
    } catch (error) {
      console.error('Failed to create project:', error);
      alert('Failed to create project. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const nextStep = () => {
    if (currentStep === 1 && projectData.name && projectData.description) {
      generateAISuggestions(projectData.name, projectData.description);
    }
    setCurrentStep(prev => Math.min(prev + 1, 4));
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const selectTemplate = (template: ProjectTemplate) => {
    setProjectData(prev => ({
      ...prev,
      template_id: template.id,
      type: template.type,
      config: {
        ...prev.config,
        framework: template.features[0]
      }
    }));
    nextStep();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-shadow-deep/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-crystal-clear rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto animate-bloom">
        
        {/* Header */}
        <div className="p-6 border-b border-growth-accent/20">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-h2 text-forest-primary">🌱 Plant a New Project</h2>
              <p className="text-earth-base/70">Let's grow something amazing together</p>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-growth-accent/10 rounded-lg transition-all hover-bloom"
            >
              ✕
            </button>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center mt-6 space-x-4">
            {[1, 2, 3, 4].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all ${
                  step <= currentStep 
                    ? 'bg-growth-gradient text-crystal-clear' 
                    : 'bg-mist-overlay text-earth-base/60'
                }`}>
                  {step < currentStep ? '✓' : step}
                </div>
                {step < 4 && (
                  <div className={`w-12 h-1 mx-2 rounded-full transition-all ${
                    step < currentStep ? 'bg-growth-accent' : 'bg-mist-overlay'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <div className="p-6">
          
          {/* Step 1: Basic Info */}
          {currentStep === 1 && (
            <div className="space-y-6 animate-bloom">
              <div>
                <h3 className="text-xl font-semibold text-forest-primary mb-4">🌿 Project Basics</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-earth-base mb-2">Project Name</label>
                    <input
                      type="text"
                      value={projectData.name}
                      onChange={(e) => setProjectData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="My Awesome Project"
                      className="w-full px-4 py-3 border border-growth-accent/20 rounded-lg bg-mist-gradient focus:outline-none focus:ring-2 focus:ring-growth-accent/50"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-earth-base mb-2">Description</label>
                    <textarea
                      value={projectData.description}
                      onChange={(e) => setProjectData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Describe what your project will do..."
                      rows={4}
                      className="w-full px-4 py-3 border border-growth-accent/20 rounded-lg bg-mist-gradient focus:outline-none focus:ring-2 focus:ring-growth-accent/50 resize-none"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-earth-base mb-2">Project Type</label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {[
                        { value: 'web_app', label: 'Web App', icon: '🌐' },
                        { value: 'mobile_app', label: 'Mobile App', icon: '📱' },
                        { value: 'api', label: 'API/Backend', icon: '🔌' },
                        { value: 'library', label: 'Library', icon: '📚' },
                        { value: 'data_science', label: 'Data Science', icon: '📊' },
                        { value: 'other', label: 'Other', icon: '💻' }
                      ].map((type) => (
                        <button
                          key={type.value}
                          onClick={() => setProjectData(prev => ({ ...prev, type: type.value }))}
                          className={`p-3 rounded-lg border transition-all hover-bloom ${
                            projectData.type === type.value
                              ? 'border-growth-accent bg-growth-accent/10 text-growth-accent'
                              : 'border-growth-accent/20 bg-mist-gradient text-earth-base hover:border-growth-accent/40'
                          }`}
                        >
                          <div className="text-lg mb-1">{type.icon}</div>
                          <div className="text-sm font-medium">{type.label}</div>
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Template Selection */}
          {currentStep === 2 && (
            <div className="space-y-6 animate-bloom">
              <div>
                <h3 className="text-xl font-semibold text-forest-primary mb-4">🎯 Choose Your Foundation</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <button
                    onClick={() => nextStep()}
                    className="p-6 border-2 border-dashed border-growth-accent/40 rounded-lg hover:border-growth-accent hover:bg-growth-accent/5 transition-all hover-bloom text-left"
                  >
                    <div className="text-3xl mb-3">🌱</div>
                    <h4 className="font-semibold text-forest-primary mb-2">Start from Scratch</h4>
                    <p className="text-sm text-earth-base/70">Create a completely custom project with your own setup</p>
                  </button>

                  {templates.map((template) => (
                    <button
                      key={template.id}
                      onClick={() => selectTemplate(template)}
                      className="p-6 border border-growth-accent/20 rounded-lg hover:border-growth-accent hover:bg-growth-accent/5 transition-all hover-bloom text-left"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <span className="text-2xl">{template.icon}</span>
                        <span className="text-xs text-earth-base/60 bg-mist-overlay px-2 py-1 rounded">
                          {template.estimated_setup_time}
                        </span>
                      </div>
                      <h4 className="font-semibold text-forest-primary mb-2">{template.name}</h4>
                      <p className="text-sm text-earth-base/70 mb-3">{template.description}</p>
                      <div className="flex flex-wrap gap-1">
                        {template.features.slice(0, 3).map((feature) => (
                          <span key={feature} className="text-xs bg-growth-accent/10 text-growth-accent px-2 py-1 rounded">
                            {feature}
                          </span>
                        ))}
                        {template.features.length > 3 && (
                          <span className="text-xs text-earth-base/60">+{template.features.length - 3} more</span>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Step 3: AI Features */}
          {currentStep === 3 && (
            <div className="space-y-6 animate-bloom">
              <div>
                <h3 className="text-xl font-semibold text-forest-primary mb-4">🤖 AI Superpowers</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  {Object.entries(projectData.ai_features).map(([feature, enabled]) => (
                    <label key={feature} className="flex items-center p-4 border border-growth-accent/20 rounded-lg hover:bg-growth-accent/5 transition-all cursor-pointer">
                      <input
                        type="checkbox"
                        checked={enabled}
                        onChange={(e) => setProjectData(prev => ({
                          ...prev,
                          ai_features: { ...prev.ai_features, [feature]: e.target.checked }
                        }))}
                        className="sr-only"
                      />
                      <div className={`w-5 h-5 rounded border-2 mr-3 flex items-center justify-center transition-all ${
                        enabled ? 'bg-growth-accent border-growth-accent' : 'border-growth-accent/40'
                      }`}>
                        {enabled && <span className="text-crystal-clear text-xs">✓</span>}
                      </div>
                      <div>
                        <div className="font-medium text-earth-base capitalize">
                          {feature.replace('_', ' ')}
                        </div>
                        <div className="text-sm text-earth-base/60">
                          {feature === 'auto_planning' && 'Automatically generate development plans'}
                          {feature === 'code_analysis' && 'Real-time code quality analysis'}
                          {feature === 'optimization' && 'Performance optimization suggestions'}
                          {feature === 'testing' && 'Automated test generation'}
                          {feature === 'documentation' && 'Auto-generate documentation'}
                        </div>
                      </div>
                    </label>
                  ))}
                </div>

                {/* AI Suggestions */}
                {aiSuggestions.length > 0 && (
                  <div className="bg-mist-gradient rounded-lg p-4">
                    <h4 className="font-semibold text-forest-primary mb-3">💡 AI Suggestions for Your Project</h4>
                    <div className="space-y-2">
                      {aiSuggestions.map((suggestion, index) => (
                        <div key={index} className="flex items-start space-x-2">
                          <span className="text-growth-accent mt-1">•</span>
                          <span className="text-sm text-earth-base">{suggestion}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Step 4: Final Configuration */}
          {currentStep === 4 && (
            <div className="space-y-6 animate-bloom">
              <div>
                <h3 className="text-xl font-semibold text-forest-primary mb-4">⚙️ Final Configuration</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Repository Settings */}
                  <div className="space-y-4">
                    <h4 className="font-medium text-earth-base">Repository Setup</h4>
                    {Object.entries(projectData.repository).map(([key, value]) => {
                      if (key === 'license') return null;
                      return (
                        <label key={key} className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            checked={value as boolean}
                            onChange={(e) => setProjectData(prev => ({
                              ...prev,
                              repository: { ...prev.repository, [key]: e.target.checked }
                            }))}
                            className="w-4 h-4 text-growth-accent border-growth-accent/40 rounded focus:ring-growth-accent/50"
                          />
                          <span className="text-sm text-earth-base capitalize">
                            {key.replace('_', ' ')}
                          </span>
                        </label>
                      );
                    })}
                    
                    <div>
                      <label className="block text-sm font-medium text-earth-base mb-2">License</label>
                      <select
                        value={projectData.repository.license}
                        onChange={(e) => setProjectData(prev => ({
                          ...prev,
                          repository: { ...prev.repository, license: e.target.value }
                        }))}
                        className="w-full px-3 py-2 border border-growth-accent/20 rounded-lg bg-mist-gradient focus:outline-none focus:ring-2 focus:ring-growth-accent/50"
                      >
                        <option value="MIT">MIT</option>
                        <option value="Apache-2.0">Apache 2.0</option>
                        <option value="GPL-3.0">GPL 3.0</option>
                        <option value="BSD-3-Clause">BSD 3-Clause</option>
                        <option value="none">No License</option>
                      </select>
                    </div>
                  </div>

                  {/* Project Summary */}
                  <div className="bg-mist-gradient rounded-lg p-4">
                    <h4 className="font-medium text-forest-primary mb-3">📋 Project Summary</h4>
                    <div className="space-y-2 text-sm">
                      <div><strong>Name:</strong> {projectData.name}</div>
                      <div><strong>Type:</strong> {projectData.type.replace('_', ' ')}</div>
                      <div><strong>AI Features:</strong> {Object.values(projectData.ai_features).filter(Boolean).length}/5 enabled</div>
                      <div><strong>Template:</strong> {projectData.template_id || 'Custom'}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-growth-accent/20 flex items-center justify-between">
          <button
            onClick={prevStep}
            disabled={currentStep === 1}
            className="px-4 py-2 text-earth-base hover:text-growth-accent transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            ← Previous
          </button>

          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-6 py-2 border border-growth-accent/20 rounded-lg text-earth-base hover:bg-growth-accent/5 transition-all"
            >
              Cancel
            </button>
            
            {currentStep < 4 ? (
              <button
                onClick={nextStep}
                disabled={currentStep === 1 && (!projectData.name || !projectData.description)}
                className="px-6 py-2 bg-growth-gradient text-crystal-clear rounded-lg hover-bloom transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next →
              </button>
            ) : (
              <button
                onClick={createProject}
                disabled={isLoading}
                className="px-6 py-2 bg-growth-gradient text-crystal-clear rounded-lg hover-bloom transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? '🌱 Planting...' : '🚀 Create Project'}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
