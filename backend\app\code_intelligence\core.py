"""
Code Intelligence Hub Core - Phase 8B
Main orchestrator for the Code Intelligence system.

This module coordinates all code intelligence operations including indexing,
searching, and relationship analysis.
"""

import asyncio
import logging
import time
from typing import List, Dict, Optional, Any, Union
from pathlib import Path
from datetime import datetime

from .models import (
    CodeFile, CodeChunk, SearchQuery, SearchResult, 
    RelationshipGraph, IndexingStats, CodeLanguage
)
from .file_scanner import FileScanner
from .code_analyzer import CodeAnalyzer
from .semantic_search import SemanticSearch
from .relationship_mapper import RelationshipMapper

# Import existing infrastructure
try:
    from ...vector_db import QdrantVectorClient
    from ...ollama_client import OllamaEmbeddingClient
    from ...config import settings
except ImportError:
    try:
        from ..vector_db import <PERSON>drantVectorClient
        from ..ollama_client import OllamaEmbeddingClient
        from ..config import settings
    except ImportError:
        from vector_db import QdrantVectorClient
        from ollama_client import OllamaEmbeddingClient
        from config import settings

logger = logging.getLogger(__name__)


class CodeIntelligenceHub:
    """
    Main Code Intelligence Hub orchestrator.
    
    Coordinates universal file indexing, semantic search, and code relationship
    analysis for the DeepNexus AI development platform.
    """
    
    def __init__(self, project_slug: Optional[str] = None):
        """Initialize the Code Intelligence Hub with project awareness."""
        # Import project manager here to avoid circular imports
        try:
            from ..core.project_manager import project_manager
        except ImportError:
            from backend.app.core.project_manager import project_manager

        # Set up project-aware workspace and collection
        if project_slug:
            project = None
            for p in project_manager.active_projects.values():
                if p.slug == project_slug:
                    project = p
                    break

            if project:
                self.workspace_root = project.get_workspace_path(str(project_manager.base_workspace))
                self.collection_name = project.get_embeddings_collection_name()
                self.project = project
            else:
                # Fallback to default
                self.workspace_root = Path(settings.workspace_root)
                self.collection_name = "code_embeddings_bge_m3_code_intelligence"
                self.project = None
        elif project_manager and project_manager.current_project:
            # Use current project
            self.workspace_root = project_manager.get_project_workspace_path()
            self.collection_name = project_manager.current_project.get_embeddings_collection_name()
            self.project = project_manager.current_project
        else:
            # Fallback to default
            self.workspace_root = Path(settings.workspace_root)
            self.collection_name = "code_embeddings_bge_m3_code_intelligence"
            self.project = None
        
        # Initialize components
        self.vector_client = QdrantVectorClient()
        self.embedding_client = OllamaEmbeddingClient()
        self.file_scanner = FileScanner(self.workspace_root)
        self.code_analyzer = CodeAnalyzer()
        self.semantic_search = SemanticSearch(self.vector_client, self.embedding_client)
        self.relationship_mapper = RelationshipMapper()
        
        # State management
        self.indexed_files: Dict[str, CodeFile] = {}
        self.indexing_stats = IndexingStats()
        self.is_indexing = False
        
        logger.info(f"🧠 Code Intelligence Hub initialized for workspace: {self.workspace_root}")
    
    async def initialize(self) -> Dict[str, Any]:
        """
        Initialize the Code Intelligence Hub and ensure vector collection exists.
        
        Returns:
            Initialization status and statistics
        """
        try:
            logger.info("🚀 Initializing Code Intelligence Hub...")
            
            # Ensure vector collection exists
            collection_created = await self.vector_client.ensure_collection_exists()
            
            # Initialize components
            await self.semantic_search.initialize()
            await self.relationship_mapper.initialize()
            
            # Load existing index if available
            await self._load_existing_index()
            
            logger.info("✅ Code Intelligence Hub initialized successfully")
            
            return {
                'status': 'success',
                'collection_created': collection_created,
                'workspace_root': str(self.workspace_root),
                'indexed_files_count': len(self.indexed_files),
                'components_initialized': True
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Code Intelligence Hub: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def index_workspace(self, 
                            force_reindex: bool = False,
                            include_patterns: Optional[List[str]] = None,
                            exclude_patterns: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Index the entire workspace for code intelligence.
        
        Args:
            force_reindex: Whether to reindex all files regardless of changes
            include_patterns: File patterns to include (e.g., ['*.py', '*.js'])
            exclude_patterns: File patterns to exclude (e.g., ['node_modules/*'])
            
        Returns:
            Indexing results and statistics
        """
        if self.is_indexing:
            return {
                'status': 'error',
                'error': 'Indexing already in progress'
            }
        
        try:
            self.is_indexing = True
            start_time = time.time()
            
            logger.info("🔍 Starting workspace indexing...")
            
            # Reset stats for new indexing run
            self.indexing_stats = IndexingStats()
            
            # Scan for files
            files_to_index = await self.file_scanner.scan_workspace(
                include_patterns=include_patterns,
                exclude_patterns=exclude_patterns
            )
            
            logger.info(f"📁 Found {len(files_to_index)} files to process")
            
            # Process files in batches for better performance
            batch_size = 10
            for i in range(0, len(files_to_index), batch_size):
                batch = files_to_index[i:i + batch_size]
                await self._process_file_batch(batch, force_reindex)
                
                # Log progress
                progress = min(i + batch_size, len(files_to_index))
                logger.info(f"📊 Progress: {progress}/{len(files_to_index)} files processed")
            
            # Build relationship graph
            logger.info("🔗 Building code relationship graph...")
            relationship_graph = await self.relationship_mapper.build_graph(
                list(self.indexed_files.values())
            )
            
            # Calculate final statistics
            total_time = time.time() - start_time
            self.indexing_stats.indexing_time = total_time
            
            logger.info(f"✅ Workspace indexing completed in {total_time:.2f}s")
            
            return {
                'status': 'success',
                'stats': self.indexing_stats.model_dump(),
                'relationship_graph_nodes': len(relationship_graph.nodes),
                'relationship_graph_edges': len(relationship_graph.edges),
                'total_time': total_time
            }
            
        except Exception as e:
            logger.error(f"❌ Workspace indexing failed: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'partial_stats': self.indexing_stats.model_dump()
            }
        finally:
            self.is_indexing = False
    
    async def index_file(self, file_path: Union[str, Path], force_reindex: bool = False) -> Dict[str, Any]:
        """
        Index a single file for code intelligence.
        
        Args:
            file_path: Path to the file to index
            force_reindex: Whether to reindex even if file hasn't changed
            
        Returns:
            Indexing result for the file
        """
        try:
            start_time = time.time()
            file_path = Path(file_path)
            
            logger.debug(f"🔍 Indexing file: {file_path}")
            
            # Check if file needs indexing
            if not force_reindex and await self._is_file_up_to_date(file_path):
                logger.debug(f"⏭️ File up to date, skipping: {file_path}")
                return {
                    'status': 'skipped',
                    'reason': 'File up to date',
                    'file_path': str(file_path)
                }
            
            # Analyze file
            file_info = await self.file_scanner.analyze_file(file_path)
            if not file_info:
                return {
                    'status': 'error',
                    'error': 'Failed to analyze file',
                    'file_path': str(file_path)
                }
            
            # Create CodeFile object
            code_file = CodeFile(**file_info)
            
            # Analyze code structure
            analysis_result = await self.code_analyzer.analyze_file(code_file)
            if analysis_result['status'] == 'success':
                # Update code file with analysis results
                code_file.functions = analysis_result.get('functions', [])
                code_file.classes = analysis_result.get('classes', [])
                code_file.imports = analysis_result.get('imports', [])
                code_file.dependencies = analysis_result.get('dependencies', [])
            
            # Generate chunks and embeddings
            chunks = await self.code_analyzer.create_chunks(code_file)
            embedding_results = await self._generate_embeddings(chunks)
            
            # Update statistics
            code_file.chunk_count = len(chunks)
            code_file.embedding_count = len(embedding_results)
            code_file.indexed_time = datetime.now()
            
            # Store in index
            self.indexed_files[code_file.file_id] = code_file
            
            processing_time = time.time() - start_time
            self.indexing_stats.add_file(code_file.language, True, processing_time)
            
            logger.debug(f"✅ File indexed successfully: {file_path} ({processing_time:.2f}s)")
            
            return {
                'status': 'success',
                'file_id': code_file.file_id,
                'chunks_created': len(chunks),
                'embeddings_generated': len(embedding_results),
                'processing_time': processing_time
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to index file {file_path}: {e}")
            processing_time = time.time() - start_time
            
            # Try to determine language for stats
            try:
                language = self.file_scanner._detect_language(file_path)
            except:
                language = CodeLanguage.UNKNOWN
            
            self.indexing_stats.add_file(language, False, processing_time)
            self.indexing_stats.errors.append(f"{file_path}: {str(e)}")
            
            return {
                'status': 'error',
                'error': str(e),
                'file_path': str(file_path),
                'processing_time': processing_time
            }
    
    async def search(self, query: Union[str, SearchQuery]) -> List[SearchResult]:
        """
        Perform semantic search across the indexed codebase.
        
        Args:
            query: Search query string or SearchQuery object
            
        Returns:
            List of search results
        """
        try:
            # Convert string to SearchQuery if needed
            if isinstance(query, str):
                query = SearchQuery(query=query)
            
            logger.debug(f"🔍 Searching for: {query.query}")
            
            # Perform semantic search
            results = await self.semantic_search.search(query)
            
            logger.debug(f"📊 Found {len(results)} search results")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Search failed: {e}")
            return []
    
    async def get_file_relationships(self, file_path: str) -> RelationshipGraph:
        """
        Get relationship graph for a specific file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Relationship graph for the file
        """
        try:
            # Find the file in our index
            code_file = None
            for file_id, indexed_file in self.indexed_files.items():
                if indexed_file.path == file_path:
                    code_file = indexed_file
                    break
            
            if not code_file:
                logger.warning(f"File not found in index: {file_path}")
                return RelationshipGraph()
            
            # Get relationships for this file
            return await self.relationship_mapper.get_file_relationships(code_file)
            
        except Exception as e:
            logger.error(f"❌ Failed to get relationships for {file_path}: {e}")
            return RelationshipGraph()
    
    async def get_stats(self) -> Dict[str, Any]:
        """
        Get current indexing statistics and system status.
        
        Returns:
            Statistics and status information
        """
        try:
            # Get vector database stats
            vector_stats = await self.vector_client.get_collection_info(self.collection_name)
            
            return {
                'status': 'success',
                'indexing_stats': self.indexing_stats.model_dump(),
                'indexed_files_count': len(self.indexed_files),
                'vector_database': vector_stats,
                'is_indexing': self.is_indexing,
                'workspace_root': str(self.workspace_root)
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get stats: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _process_file_batch(self, files: List[Path], force_reindex: bool):
        """Process a batch of files concurrently."""
        tasks = [self.index_file(file_path, force_reindex) for file_path in files]
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _generate_embeddings(self, chunks: List[CodeChunk]) -> List[str]:
        """Generate embeddings for code chunks."""
        try:
            # Prepare texts for embedding
            texts = [chunk.content for chunk in chunks]
            
            # Generate embeddings in batches
            embeddings = await self.embedding_client.generate_embeddings_batch(texts)
            
            # Store embeddings in vector database
            metadata = [
                {
                    'chunk_id': chunk.chunk_id,
                    'file_id': chunk.file_id,
                    'element_type': chunk.element_type.value,
                    'start_line': chunk.start_line,
                    'end_line': chunk.end_line,
                    'function_name': chunk.function_name,
                    'class_name': chunk.class_name
                }
                for chunk in chunks
            ]
            
            # Upsert to vector database
            embedding_ids = await self.vector_client.upsert_embeddings(
                embeddings=embeddings,
                metadata=metadata,
                collection_name=self.collection_name
            )
            
            # Update chunks with embedding IDs
            for chunk, embedding_id in zip(chunks, embedding_ids):
                chunk.embedding_id = embedding_id
                chunk.embedding_generated = True
            
            return embedding_ids
            
        except Exception as e:
            logger.error(f"❌ Failed to generate embeddings: {e}")
            return []
    
    async def _is_file_up_to_date(self, file_path: Path) -> bool:
        """Check if file is already indexed and up to date."""
        try:
            # Find file in index
            for file_id, code_file in self.indexed_files.items():
                if Path(code_file.absolute_path) == file_path.resolve():
                    # Check if file has been modified since indexing
                    current_mtime = file_path.stat().st_mtime
                    indexed_mtime = code_file.modified_time.timestamp()
                    return current_mtime <= indexed_mtime
            
            return False  # File not in index
            
        except Exception:
            return False  # Assume file needs indexing on error
    
    async def _load_existing_index(self):
        """Load existing index from vector database if available."""
        try:
            # This would load previously indexed files from persistent storage
            # For now, we'll start fresh each time
            logger.debug("📂 Loading existing index (placeholder)")
            pass
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to load existing index: {e}")


# Global instance
code_intelligence_hub = CodeIntelligenceHub()
