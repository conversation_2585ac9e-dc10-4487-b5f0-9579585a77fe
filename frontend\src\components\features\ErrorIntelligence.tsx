import React, { useState, useEffect } from 'react';

interface ErrorPattern {
  id: string;
  pattern: string;
  frequency: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  first_seen: Date;
  last_seen: Date;
  affected_services: string[];
  suggested_fixes: string[];
  ml_confidence: number;
}

interface ErrorTrend {
  timestamp: Date;
  error_count: number;
  error_rate: number;
  resolved_count: number;
  new_errors: number;
}

interface ErrorAnalysis {
  total_errors: number;
  error_rate_change: number;
  most_common_error: string;
  resolution_rate: number;
  avg_resolution_time: number;
  critical_errors: number;
  patterns_detected: number;
  ml_predictions: {
    next_hour_errors: number;
    confidence: number;
    risk_level: 'low' | 'medium' | 'high';
  };
}

interface ErrorEvent {
  id: string;
  timestamp: Date;
  service: string;
  error_type: string;
  message: string;
  stack_trace?: string;
  user_id?: string;
  session_id?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  resolved: boolean;
  resolution_time?: number;
  tags: string[];
}

export const ErrorIntelligence: React.FC = () => {
  const [patterns, setPatterns] = useState<ErrorPattern[]>([]);
  const [trends, setTrends] = useState<ErrorTrend[]>([]);
  const [analysis, setAnalysis] = useState<ErrorAnalysis>({
    total_errors: 0,
    error_rate_change: 0,
    most_common_error: '',
    resolution_rate: 0,
    avg_resolution_time: 0,
    critical_errors: 0,
    patterns_detected: 0,
    ml_predictions: {
      next_hour_errors: 0,
      confidence: 0,
      risk_level: 'low'
    }
  });
  const [recentErrors, setRecentErrors] = useState<ErrorEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPattern, setSelectedPattern] = useState<ErrorPattern | null>(null);
  const [timeRange, setTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h');

  useEffect(() => {
    loadErrorData();
  }, [timeRange]);

  const loadErrorData = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        loadErrorPatterns(),
        loadErrorTrends(),
        loadErrorAnalysis(),
        loadRecentErrors()
      ]);
    } catch (error) {
      console.error('Failed to load error data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadErrorPatterns = async () => {
    try {
      const response = await fetch('/api/monitoring/errors/patterns');
      if (response.ok) {
        const data = await response.json();
        setPatterns(data.patterns || []);
      } else {
        // Mock data
        setPatterns([
          {
            id: '1',
            pattern: 'OpenRouter API Rate Limit Exceeded',
            frequency: 45,
            severity: 'high',
            first_seen: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            last_seen: new Date(Date.now() - 5 * 60 * 1000),
            affected_services: ['DeepNexus API', 'AI Chat'],
            suggested_fixes: [
              'Implement exponential backoff',
              'Add request queuing system',
              'Consider upgrading API plan'
            ],
            ml_confidence: 0.92
          },
          {
            id: '2',
            pattern: 'Context Size Limit Exceeded',
            frequency: 23,
            severity: 'medium',
            first_seen: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
            last_seen: new Date(Date.now() - 15 * 60 * 1000),
            affected_services: ['Context Optimizer'],
            suggested_fixes: [
              'Implement smart context compression',
              'Add context size validation',
              'Use sliding window approach'
            ],
            ml_confidence: 0.87
          },
          {
            id: '3',
            pattern: 'Database Connection Timeout',
            frequency: 12,
            severity: 'critical',
            first_seen: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
            last_seen: new Date(Date.now() - 45 * 60 * 1000),
            affected_services: ['Database', 'DeepNexus API'],
            suggested_fixes: [
              'Increase connection pool size',
              'Add connection retry logic',
              'Monitor database performance'
            ],
            ml_confidence: 0.95
          }
        ]);
      }
    } catch (error) {
      console.error('Failed to load error patterns:', error);
    }
  };

  const loadErrorTrends = async () => {
    try {
      const response = await fetch('/api/monitoring/errors/trends');
      if (response.ok) {
        const data = await response.json();
        setTrends(data.trends || []);
      } else {
        // Mock trend data
        const mockTrends: ErrorTrend[] = [];
        const now = new Date();
        for (let i = 23; i >= 0; i--) {
          mockTrends.push({
            timestamp: new Date(now.getTime() - i * 60 * 60 * 1000),
            error_count: Math.floor(Math.random() * 20) + 5,
            error_rate: Math.random() * 5 + 1,
            resolved_count: Math.floor(Math.random() * 15) + 2,
            new_errors: Math.floor(Math.random() * 8) + 1
          });
        }
        setTrends(mockTrends);
      }
    } catch (error) {
      console.error('Failed to load error trends:', error);
    }
  };

  const loadErrorAnalysis = async () => {
    try {
      const response = await fetch('/api/monitoring/errors/analysis');
      if (response.ok) {
        const data = await response.json();
        setAnalysis(data);
      } else {
        // Mock analysis
        setAnalysis({
          total_errors: 234,
          error_rate_change: -12.5,
          most_common_error: 'OpenRouter API Rate Limit',
          resolution_rate: 87.3,
          avg_resolution_time: 23.5,
          critical_errors: 3,
          patterns_detected: 8,
          ml_predictions: {
            next_hour_errors: 15,
            confidence: 0.89,
            risk_level: 'medium'
          }
        });
      }
    } catch (error) {
      console.error('Failed to load error analysis:', error);
    }
  };

  const loadRecentErrors = async () => {
    try {
      const response = await fetch('/api/monitoring/errors/recent');
      if (response.ok) {
        const data = await response.json();
        setRecentErrors(data.errors || []);
      } else {
        // Mock recent errors
        setRecentErrors([
          {
            id: '1',
            timestamp: new Date(Date.now() - 5 * 60 * 1000),
            service: 'OpenRouter API',
            error_type: 'RateLimitError',
            message: 'Rate limit exceeded: 429 Too Many Requests',
            user_id: 'user_123',
            session_id: 'session_456',
            severity: 'high',
            resolved: false,
            tags: ['api', 'rate-limit', 'external']
          },
          {
            id: '2',
            timestamp: new Date(Date.now() - 15 * 60 * 1000),
            service: 'Context Optimizer',
            error_type: 'ValidationError',
            message: 'Context size exceeds maximum limit of 32k tokens',
            user_id: 'user_789',
            session_id: 'session_012',
            severity: 'medium',
            resolved: true,
            resolution_time: 8.5,
            tags: ['context', 'validation', 'size-limit']
          },
          {
            id: '3',
            timestamp: new Date(Date.now() - 45 * 60 * 1000),
            service: 'Database',
            error_type: 'ConnectionTimeout',
            message: 'Database connection timeout after 30 seconds',
            severity: 'critical',
            resolved: true,
            resolution_time: 12.3,
            tags: ['database', 'timeout', 'connection']
          }
        ]);
      }
    } catch (error) {
      console.error('Failed to load recent errors:', error);
    }
  };

  const runMLAnalysis = async () => {
    try {
      const response = await fetch('/api/monitoring/errors/ml-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ time_range: timeRange })
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('ML Analysis completed:', data);
        await loadErrorData(); // Refresh data
      }
    } catch (error) {
      console.error('Failed to run ML analysis:', error);
    }
  };

  const resolveError = async (errorId: string) => {
    try {
      const response = await fetch(`/api/monitoring/errors/${errorId}/resolve`, {
        method: 'POST'
      });
      
      if (response.ok) {
        setRecentErrors(prev => prev.map(error => 
          error.id === errorId ? { ...error, resolved: true } : error
        ));
      }
    } catch (error) {
      console.error('Failed to resolve error:', error);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-crimson-alert bg-crimson-alert/10 border-crimson-alert';
      case 'high': return 'text-amber-data bg-amber-data/10 border-amber-data';
      case 'medium': return 'text-bloom-highlight bg-bloom-highlight/10 border-bloom-highlight';
      case 'low': return 'text-growth-accent bg-growth-accent/10 border-growth-accent';
      default: return 'text-earth-base bg-mist-gradient border-earth-base/20';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high': return 'text-crimson-alert';
      case 'medium': return 'text-amber-data';
      case 'low': return 'text-growth-accent';
      default: return 'text-earth-base';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-h2 text-forest-primary">🧠 Error Intelligence</h1>
          <p className="text-earth-base/70">
            🔒 <span className="font-medium">Admin Dashboard</span> - ML-powered error analysis and prediction
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-3 py-2 border border-growth-accent/20 rounded-lg bg-mist-gradient focus:outline-none focus:ring-2 focus:ring-growth-accent/50"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
          
          <button
            onClick={runMLAnalysis}
            className="px-4 py-2 border border-growth-accent/20 rounded-lg text-earth-base hover:bg-growth-accent/5 transition-all hover-bloom"
          >
            🤖 Run ML Analysis
          </button>
          
          <button
            onClick={loadErrorData}
            disabled={isLoading}
            className="px-6 py-3 bg-growth-gradient text-crystal-clear rounded-lg font-medium hover-bloom transition-all disabled:opacity-50"
          >
            {isLoading ? '🔄 Loading...' : '🔄 Refresh'}
          </button>
        </div>
      </div>

      {/* Error Analysis Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-8 gap-4">
        {[
          { 
            label: 'Total Errors', 
            value: analysis.total_errors.toLocaleString(), 
            icon: '❌', 
            color: 'text-crimson-alert',
            change: `${analysis.error_rate_change > 0 ? '+' : ''}${analysis.error_rate_change.toFixed(1)}%`
          },
          { 
            label: 'Critical Errors', 
            value: analysis.critical_errors, 
            icon: '🚨', 
            color: 'text-crimson-alert',
            change: 'Active'
          },
          { 
            label: 'Resolution Rate', 
            value: `${analysis.resolution_rate.toFixed(1)}%`, 
            icon: '✅', 
            color: 'text-growth-accent',
            change: '+2.3%'
          },
          { 
            label: 'Avg Resolution', 
            value: `${analysis.avg_resolution_time.toFixed(1)}m`, 
            icon: '⏱️', 
            color: 'text-bloom-highlight',
            change: '-5.2%'
          },
          { 
            label: 'Patterns Found', 
            value: analysis.patterns_detected, 
            icon: '🔍', 
            color: 'text-amber-data',
            change: '+3'
          },
          { 
            label: 'ML Confidence', 
            value: `${(analysis.ml_predictions.confidence * 100).toFixed(1)}%`, 
            icon: '🧠', 
            color: 'text-forest-primary',
            change: 'High'
          },
          { 
            label: 'Predicted Errors', 
            value: analysis.ml_predictions.next_hour_errors, 
            icon: '📈', 
            color: getRiskColor(analysis.ml_predictions.risk_level),
            change: analysis.ml_predictions.risk_level
          },
          { 
            label: 'Most Common', 
            value: analysis.most_common_error.split(' ').slice(0, 2).join(' '), 
            icon: '🎯', 
            color: 'text-golden-success',
            change: '45 times'
          }
        ].map((metric) => (
          <div key={metric.label} className="bg-crystal-clear rounded-lg p-4 border border-growth-accent/20 hover-bloom">
            <div className="flex items-center justify-between mb-2">
              <span className="text-2xl">{metric.icon}</span>
              <span className={`text-xs font-medium ${
                metric.change.includes('%') && metric.change.startsWith('+') ? 'text-growth-accent' :
                metric.change.includes('%') && metric.change.startsWith('-') ? 'text-crimson-alert' :
                'text-earth-base/70'
              }`}>
                {metric.change}
              </span>
            </div>
            <div>
              <p className="text-xs text-earth-base/60 mb-1">{metric.label}</p>
              <p className={`text-lg font-bold ${metric.color}`}>{metric.value}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Error Trends Chart */}
      <div className="bg-crystal-clear rounded-lg p-6 border border-growth-accent/20">
        <h3 className="text-lg font-semibold text-forest-primary mb-4">📈 Error Trends & Predictions</h3>
        <div className="h-64 flex items-end justify-between space-x-1">
          {trends.slice(-24).map((trend, index) => (
            <div key={index} className="flex-1 flex flex-col items-center space-y-1">
              <div className="w-full bg-frost-light rounded-t relative">
                <div
                  className="bg-crimson-alert rounded-t transition-all duration-500"
                  style={{ height: `${Math.min((trend.error_count / 30) * 150, 150)}px` }}
                  title={`Errors: ${trend.error_count}`}
                />
                <div
                  className="bg-growth-accent rounded-t transition-all duration-500 absolute bottom-0 left-0 right-0"
                  style={{ height: `${Math.min((trend.resolved_count / 30) * 150, 150)}px` }}
                  title={`Resolved: ${trend.resolved_count}`}
                />
              </div>
              <span className="text-xs text-earth-base/60 transform -rotate-45">
                {trend.timestamp.getHours()}:00
              </span>
            </div>
          ))}
        </div>
        <div className="flex items-center justify-center space-x-4 mt-4 text-sm">
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-crimson-alert rounded"></div>
            <span className="text-earth-base/70">Errors</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-growth-accent rounded"></div>
            <span className="text-earth-base/70">Resolved</span>
          </div>
        </div>
      </div>

      {/* Error Patterns */}
      <div className="bg-crystal-clear rounded-lg p-6 border border-growth-accent/20">
        <h3 className="text-lg font-semibold text-forest-primary mb-4">🔍 ML-Detected Error Patterns</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {patterns.map((pattern) => (
            <div
              key={pattern.id}
              className={`p-4 rounded-lg border-2 transition-all hover-bloom cursor-pointer ${getSeverityColor(pattern.severity)} ${
                selectedPattern?.id === pattern.id ? 'ring-2 ring-growth-accent' : ''
              }`}
              onClick={() => setSelectedPattern(selectedPattern?.id === pattern.id ? null : pattern)}
            >
              <div className="flex items-start justify-between mb-3">
                <h4 className="font-semibold text-forest-primary">{pattern.pattern}</h4>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 rounded text-xs font-medium capitalize ${getSeverityColor(pattern.severity)}`}>
                    {pattern.severity}
                  </span>
                  <span className="text-xs bg-forest-primary/10 text-forest-primary px-2 py-1 rounded">
                    {(pattern.ml_confidence * 100).toFixed(0)}% ML
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-3 text-sm">
                <div>
                  <div className="text-xs text-earth-base/60">Frequency</div>
                  <div className="font-bold text-crimson-alert">{pattern.frequency} times</div>
                </div>
                <div>
                  <div className="text-xs text-earth-base/60">Services</div>
                  <div className="font-bold text-earth-base">{pattern.affected_services.length}</div>
                </div>
                <div>
                  <div className="text-xs text-earth-base/60">First Seen</div>
                  <div className="font-bold text-earth-base/70">
                    {Math.round((Date.now() - pattern.first_seen.getTime()) / (24 * 60 * 60 * 1000))}d ago
                  </div>
                </div>
                <div>
                  <div className="text-xs text-earth-base/60">Last Seen</div>
                  <div className="font-bold text-earth-base/70">
                    {Math.round((Date.now() - pattern.last_seen.getTime()) / (60 * 1000))}m ago
                  </div>
                </div>
              </div>

              {selectedPattern?.id === pattern.id && (
                <div className="mt-4 pt-4 border-t border-growth-accent/20 space-y-3">
                  <div>
                    <div className="text-sm font-medium text-earth-base mb-2">Affected Services</div>
                    <div className="flex flex-wrap gap-1">
                      {pattern.affected_services.map((service) => (
                        <span key={service} className="text-xs bg-mist-gradient px-2 py-1 rounded">
                          {service}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div>
                    <div className="text-sm font-medium text-earth-base mb-2">🤖 AI Suggested Fixes</div>
                    <div className="space-y-1">
                      {pattern.suggested_fixes.map((fix, index) => (
                        <div key={index} className="text-sm bg-growth-accent/10 text-growth-accent rounded px-3 py-2">
                          {fix}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Recent Errors */}
      <div className="bg-crystal-clear rounded-lg p-6 border border-growth-accent/20">
        <h3 className="text-lg font-semibold text-forest-primary mb-4">⚡ Recent Error Events</h3>
        <div className="space-y-3">
          {recentErrors.map((error) => (
            <div
              key={error.id}
              className={`p-4 rounded-lg border-2 transition-all hover-bloom ${getSeverityColor(error.severity)} ${
                error.resolved ? 'opacity-60' : ''
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="font-semibold text-forest-primary">{error.error_type}</span>
                    <span className="text-sm text-earth-base/70">in {error.service}</span>
                    <span className={`px-2 py-1 rounded text-xs font-medium capitalize ${getSeverityColor(error.severity)}`}>
                      {error.severity}
                    </span>
                  </div>

                  <p className="text-sm text-earth-base/80 mb-2">{error.message}</p>

                  <div className="flex items-center space-x-4 text-xs text-earth-base/60">
                    <span>{error.timestamp.toLocaleString()}</span>
                    {error.user_id && <span>User: {error.user_id}</span>}
                    {error.session_id && <span>Session: {error.session_id.slice(0, 8)}...</span>}
                    {error.resolution_time && (
                      <span className="text-growth-accent">Resolved in {error.resolution_time.toFixed(1)}m</span>
                    )}
                  </div>

                  {error.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {error.tags.map((tag) => (
                        <span key={tag} className="text-xs bg-mist-gradient px-2 py-1 rounded">
                          #{tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  {error.resolved ? (
                    <span className="px-2 py-1 bg-growth-accent/20 text-growth-accent rounded text-xs">
                      ✅ Resolved
                    </span>
                  ) : (
                    <button
                      onClick={() => resolveError(error.id)}
                      className="px-3 py-1 bg-growth-gradient text-crystal-clear rounded text-xs hover-bloom transition-all"
                    >
                      Resolve
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {recentErrors.length === 0 && (
          <div className="text-center py-8">
            <div className="text-4xl mb-2">✅</div>
            <p className="text-earth-base/60">No recent errors</p>
          </div>
        )}
      </div>

      {/* ML Predictions */}
      <div className="bg-crystal-clear rounded-lg p-6 border border-growth-accent/20">
        <h3 className="text-lg font-semibold text-forest-primary mb-4">🔮 ML Predictions & Insights</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-4xl mb-2">🎯</div>
            <h4 className="font-semibold text-forest-primary mb-2">Next Hour Prediction</h4>
            <div className={`text-3xl font-bold mb-1 ${getRiskColor(analysis.ml_predictions.risk_level)}`}>
              {analysis.ml_predictions.next_hour_errors}
            </div>
            <div className="text-sm text-earth-base/70">
              Expected errors ({(analysis.ml_predictions.confidence * 100).toFixed(1)}% confidence)
            </div>
            <div className={`text-sm font-medium mt-2 ${getRiskColor(analysis.ml_predictions.risk_level)}`}>
              Risk Level: {analysis.ml_predictions.risk_level.toUpperCase()}
            </div>
          </div>

          <div className="text-center">
            <div className="text-4xl mb-2">📊</div>
            <h4 className="font-semibold text-forest-primary mb-2">Pattern Analysis</h4>
            <div className="text-3xl font-bold text-amber-data mb-1">
              {patterns.length}
            </div>
            <div className="text-sm text-earth-base/70">
              Active patterns detected
            </div>
            <div className="text-sm text-growth-accent mt-2">
              {patterns.filter(p => p.ml_confidence > 0.8).length} high-confidence patterns
            </div>
          </div>

          <div className="text-center">
            <div className="text-4xl mb-2">🚀</div>
            <h4 className="font-semibold text-forest-primary mb-2">System Health</h4>
            <div className={`text-3xl font-bold mb-1 ${
              analysis.resolution_rate > 90 ? 'text-growth-accent' :
              analysis.resolution_rate > 75 ? 'text-bloom-highlight' :
              'text-crimson-alert'
            }`}>
              {analysis.resolution_rate.toFixed(1)}%
            </div>
            <div className="text-sm text-earth-base/70">
              Resolution rate
            </div>
            <div className="text-sm text-earth-base/60 mt-2">
              Avg resolution: {analysis.avg_resolution_time.toFixed(1)}m
            </div>
          </div>
        </div>
      </div>

      {isLoading && (
        <div className="fixed inset-0 bg-shadow-deep/20 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-crystal-clear rounded-xl p-8 animate-bloom">
            <div className="flex items-center space-x-3">
              <div className="w-4 h-4 bg-growth-accent rounded-full animate-bounce"></div>
              <div className="w-4 h-4 bg-growth-accent rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
              <div className="w-4 h-4 bg-growth-accent rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
              <span className="text-forest-primary font-medium">Analyzing error patterns...</span>
            </div>
          </div>
        )}
      )}
    </div>
  );
};
