/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        'mono': ['JetBrains Mono', 'Monaco', 'Menlo', 'Ubuntu Mono', 'monospace'],
        'sans': ['Inter', 'system-ui', 'sans-serif'],
        'display': ['Poppins', 'system-ui', 'sans-serif'],
      },
      colors: {
        // 🌿 DeepNexus Digital Forest Color Palette
        'forest': {
          'primary': '#2D5016',
        },
        'growth': {
          'accent': '#7CB342',
        },
        'bloom': {
          'highlight': '#FFC107',
        },
        'earth': {
          'base': '#3E2723',
        },
        'mist': {
          'overlay': '#E8F5E8',
        },
        'canopy': {
          'deep': '#1B5E20',
        },
        'meadow': {
          'bright': '#8BC34A',
        },
        'sunset': {
          'warm': '#FF8F00',
        },
        'amber': {
          'data': '#FF6F00',
        },
        'crimson': {
          'alert': '#D32F2F',
        },
        'golden': {
          'success': '#F57F17',
        },
        'frost': {
          'light': '#F1F8E9',
        },
        'shadow': {
          'deep': '#263238',
        },
        'crystal': {
          'clear': '#FFFFFF',
        },
        // Legacy terminal colors for compatibility
        'terminal': {
          'bg': '#1a1a1a',
          'text': '#00ff00',
        }
      },
      animation: {
        'bloom': 'bloom 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55)',
        'breathe': 'breathe 2s cubic-bezier(0.4, 0.0, 0.2, 1) infinite',
        'grow': 'grow 0.5s cubic-bezier(0.4, 0.0, 0.2, 1)',
      },
      keyframes: {
        bloom: {
          '0%': {
            transform: 'scale(0.8) rotate(-5deg)',
            opacity: '0',
          },
          '50%': {
            transform: 'scale(1.05) rotate(2deg)',
            opacity: '0.8',
          },
          '100%': {
            transform: 'scale(1) rotate(0deg)',
            opacity: '1',
          },
        },
        breathe: {
          '0%, 100%': {
            transform: 'scale(1)',
            opacity: '1',
          },
          '50%': {
            transform: 'scale(1.02)',
            opacity: '0.9',
          },
        },
        grow: {
          '0%': {
            transform: 'scaleY(0)',
            'transform-origin': 'bottom',
          },
          '100%': {
            transform: 'scaleY(1)',
            'transform-origin': 'bottom',
          },
        },
      },
      backgroundImage: {
        'forest-gradient': 'linear-gradient(135deg, #2D5016 0%, #1B5E20 100%)',
        'growth-gradient': 'linear-gradient(135deg, #7CB342 0%, #8BC34A 100%)',
        'mist-gradient': 'linear-gradient(135deg, #F1F8E9 0%, #FFFFFF 100%)',
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}
