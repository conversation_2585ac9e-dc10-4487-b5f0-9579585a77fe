"""
Vision Integration Tools for Coding Agent - Phase 8A

This module provides vision analysis capabilities for the main coding agent,
allowing it to request visual analysis from the vision agent when needed.
"""

import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
import asyncio

from pydantic_ai import RunContext
from pydantic import BaseModel

from ..dependencies import CodingDependencies
from ..models import VisionAnalysisResult

logger = logging.getLogger(__name__)


class VisionRequest(BaseModel):
    """Structured request for vision analysis."""
    image_path: str
    analysis_type: str = "general"
    focus_areas: Optional[List[str]] = None
    component_type: Optional[str] = None
    expected_behavior: Optional[str] = None


class VisionComparisonRequest(BaseModel):
    """Structured request for vision comparison."""
    before_image: str
    after_image: str
    comparison_type: str = "changes"


async def request_vision_analysis(
    ctx: RunContext[CodingDependencies],
    image_path: str,
    analysis_type: str = "general",
    focus_areas: Optional[List[str]] = None,
    component_type: Optional[str] = None,
    expected_behavior: Optional[str] = None
) -> Dict[str, Any]:
    """
    Request vision analysis from the vision agent.
    
    This tool allows the coding agent to request visual analysis of screenshots,
    UI components, or other images when working on frontend code or UI validation.
    
    Args:
        ctx: Pydantic AI run context with coding dependencies
        image_path: Path to the image to analyze (relative to workspace)
        analysis_type: Type of analysis (general, accessibility, layout, bugs, component)
        focus_areas: Specific areas to focus on (optional)
        component_type: Type of UI component if analyzing specific component
        expected_behavior: Expected behavior or appearance for component analysis
        
    Returns:
        Dict with vision analysis results
    """
    try:
        # Resolve image path relative to workspace
        workspace_root = ctx.deps.workspace_root
        if not Path(image_path).is_absolute():
            full_image_path = workspace_root / image_path
        else:
            full_image_path = Path(image_path)
        
        # Validate image exists
        if not full_image_path.exists():
            return {
                "status": "error",
                "error": f"Image file not found: {full_image_path}",
                "error_type": "FileNotFoundError"
            }
        
        # Create vision dependencies from coding dependencies
        from ..dependencies import create_vision_dependencies
        vision_deps = await create_vision_dependencies(workspace_root)
        
        # Determine which vision tool to use based on analysis type
        if analysis_type == "component" and component_type:
            # Use component analysis
            task_description = f"""Analyze the {component_type} component in the image at {full_image_path}.
            
Analysis type: {analysis_type}
Component type: {component_type}
Expected behavior: {expected_behavior or 'Not specified'}
Focus areas: {', '.join(focus_areas) if focus_areas else 'None specified'}

Please provide detailed analysis of the component's visual design, usability, and any issues."""
            
        else:
            # Use general screenshot analysis
            task_description = f"""Analyze the screenshot at {full_image_path}.
            
Analysis type: {analysis_type}
Focus areas: {', '.join(focus_areas) if focus_areas else 'None specified'}

Please provide comprehensive analysis including UI/UX quality, accessibility, layout, and any visual issues."""
        
        # Import vision agent here to avoid circular imports
        from ..agents import vision_agent

        # Call vision agent
        logger.info(f"Requesting vision analysis for {full_image_path} (type: {analysis_type})")

        result = await vision_agent.run(
            task_description,
            deps=vision_deps
        )
        
        return {
            "status": "success",
            "analysis_type": analysis_type,
            "image_path": str(full_image_path),
            "component_type": component_type,
            "focus_areas": focus_areas or [],
            "vision_result": result.output if hasattr(result, 'output') else str(result),
            "agent_used": "vision_agent"
        }
        
    except Exception as e:
        logger.error(f"Failed to request vision analysis for {image_path}: {e}")
        return {
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__
        }


async def request_vision_comparison(
    ctx: RunContext[CodingDependencies],
    before_image: str,
    after_image: str,
    comparison_type: str = "changes"
) -> Dict[str, Any]:
    """
    Request comparison of two images from the vision agent.
    
    This tool allows the coding agent to compare before/after screenshots
    to validate that code changes produced the expected visual results.
    
    Args:
        ctx: Pydantic AI run context with coding dependencies
        before_image: Path to the "before" image (relative to workspace)
        after_image: Path to the "after" image (relative to workspace)
        comparison_type: Type of comparison (changes, improvements, regressions)
        
    Returns:
        Dict with comparison results
    """
    try:
        # Resolve image paths relative to workspace
        workspace_root = ctx.deps.workspace_root
        
        if not Path(before_image).is_absolute():
            before_path = workspace_root / before_image
        else:
            before_path = Path(before_image)
            
        if not Path(after_image).is_absolute():
            after_path = workspace_root / after_image
        else:
            after_path = Path(after_image)
        
        # Validate both images exist
        if not before_path.exists():
            return {
                "status": "error",
                "error": f"Before image not found: {before_path}",
                "error_type": "FileNotFoundError"
            }
            
        if not after_path.exists():
            return {
                "status": "error",
                "error": f"After image not found: {after_path}",
                "error_type": "FileNotFoundError"
            }
        
        # Create vision dependencies
        from ..dependencies import create_vision_dependencies
        vision_deps = await create_vision_dependencies(workspace_root)
        
        # Create comparison task
        task_description = f"""Compare these two screenshots to identify {comparison_type}:

Before image: {before_path}
After image: {after_path}
Comparison type: {comparison_type}

Please analyze the differences and provide detailed feedback on what changed, 
whether the changes are improvements or regressions, and any recommendations."""
        
        logger.info(f"Requesting vision comparison: {before_path} vs {after_path} (type: {comparison_type})")

        # Import vision agent here to avoid circular imports
        from ..agents import vision_agent

        # Call vision agent
        result = await vision_agent.run(
            task_description,
            deps=vision_deps
        )
        
        return {
            "status": "success",
            "comparison_type": comparison_type,
            "before_image": str(before_path),
            "after_image": str(after_path),
            "vision_result": result.output if hasattr(result, 'output') else str(result),
            "agent_used": "vision_agent"
        }
        
    except Exception as e:
        logger.error(f"Failed to request vision comparison: {e}")
        return {
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__
        }


async def capture_and_analyze_ui(
    ctx: RunContext[CodingDependencies],
    url: str = "http://localhost:3000",
    analysis_type: str = "general",
    save_screenshot: bool = True,
    screenshot_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    Capture a screenshot of a web application and analyze it.
    
    This tool combines screenshot capture with vision analysis,
    useful for validating UI changes after code modifications.
    
    Args:
        ctx: Pydantic AI run context with coding dependencies
        url: URL to capture (default: localhost:3000)
        analysis_type: Type of analysis to perform
        save_screenshot: Whether to save the screenshot
        screenshot_name: Custom name for the screenshot file
        
    Returns:
        Dict with capture and analysis results
    """
    try:
        # Import screenshot utilities
        from ..image_utils import capture_screenshot
        
        # Generate screenshot filename
        if not screenshot_name:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_name = f"ui_capture_{timestamp}.png"
        
        # Ensure screenshot has .png extension
        if not screenshot_name.endswith('.png'):
            screenshot_name += '.png'
        
        # Create screenshots directory in workspace
        screenshots_dir = ctx.deps.workspace_root / "screenshots"
        screenshots_dir.mkdir(exist_ok=True)
        
        screenshot_path = screenshots_dir / screenshot_name
        
        # Capture screenshot
        logger.info(f"Capturing screenshot of {url}")
        
        capture_result = await capture_screenshot(
            url=url,
            output_path=str(screenshot_path),
            wait_time=2.0  # Wait for page to load
        )
        
        if not capture_result.get("success", False):
            return {
                "status": "error",
                "error": f"Failed to capture screenshot: {capture_result.get('error')}",
                "error_type": "ScreenshotError"
            }
        
        # Now analyze the captured screenshot
        analysis_result = await request_vision_analysis(
            ctx=ctx,
            image_path=str(screenshot_path),
            analysis_type=analysis_type
        )
        
        return {
            "status": "success",
            "url": url,
            "screenshot_path": str(screenshot_path),
            "screenshot_saved": save_screenshot,
            "capture_result": capture_result,
            "analysis_result": analysis_result
        }
        
    except Exception as e:
        logger.error(f"Failed to capture and analyze UI: {e}")
        return {
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__
        }


# Export all vision integration functions
__all__ = [
    'request_vision_analysis',
    'request_vision_comparison', 
    'capture_and_analyze_ui',
    'VisionRequest',
    'VisionComparisonRequest'
]
