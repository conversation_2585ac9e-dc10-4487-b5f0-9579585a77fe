interactions:
- request:
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '169'
      content-type:
      - application/json
      extra-header-key:
      - Extra-Header-Value
      host:
      - api.anthropic.com
    method: POST
    parsed_body:
      max_tokens: 1024
      messages:
      - content:
        - text: hello
          type: text
        role: user
      metadata:
        user_id: '123'
      model: claude-3-5-haiku-latest
      stream: false
    uri: https://api.anthropic.com/v1/messages?beta=true
  response:
    headers:
      connection:
      - keep-alive
      content-length:
      - '398'
      content-type:
      - application/json
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      transfer-encoding:
      - chunked
    parsed_body:
      content:
      - text: Hi there! How are you doing today? Is there anything I can help you with?
        type: text
      id: msg_016xQfqVwNtY1LLF1xHTN5Sk
      model: claude-3-5-haiku-20241022
      role: assistant
      stop_reason: end_turn
      stop_sequence: null
      type: message
      usage:
        cache_creation_input_tokens: 0
        cache_read_input_tokens: 0
        input_tokens: 8
        output_tokens: 21
        service_tier: standard
    status:
      code: 200
      message: OK
version: 1
