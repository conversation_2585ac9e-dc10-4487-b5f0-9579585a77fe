"""
Tools Module for Pydantic AI Migration

This module provides a centralized tool registry that solves circular import
issues by using the constructor pattern instead of decorators.
"""

from .registry import (
    tool_registry,
    get_coding_tools,
    get_vision_tools,
    get_tool_summary
)

# Import tool modules for completeness
from . import file_operations
from . import git_operations
from . import code_analysis
from . import change_tracking
from . import vision_analysis
from . import planning_tools

__all__ = [
    'tool_registry',
    'get_coding_tools',
    'get_vision_tools',
    'get_tool_summary',
    'file_operations',
    'git_operations',
    'code_analysis',
    'change_tracking',
    'vision_analysis',
    'planning_tools'
]
