"""
📊 Enhanced Monitoring API Endpoints

This module provides comprehensive monitoring API endpoints:
- Real-time system health dashboard
- API performance metrics and analytics
- Agent activity monitoring and statistics
- Performance alerts and notifications
- Frontend-ready monitoring data
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field
import logging

from ..monitoring import (
    get_health_dashboard,
    get_api_performance_monitor,
    get_agent_activity_monitor,
    ComponentStatus,
    AlertLevel
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/monitoring", tags=["monitoring"])

# Request/Response Models
class PerformanceMetricsRequest(BaseModel):
    """Request model for performance metrics."""
    endpoint: Optional[str] = Field(None, description="Specific endpoint to get metrics for")
    time_range_hours: int = Field(default=24, description="Time range in hours")


class AgentRegistrationRequest(BaseModel):
    """Request model for agent registration."""
    agent_id: str = Field(..., description="Unique agent identifier")
    agent_type: str = Field(..., description="Agent type/name")


class TaskCompletionRequest(BaseModel):
    """Request model for task completion recording."""
    agent_id: str = Field(..., description="Agent identifier")
    task_duration: float = Field(..., description="Task duration in seconds")
    success: bool = Field(..., description="Whether task was successful")
    tools_used: Optional[List[str]] = Field(None, description="List of tools used")


class ToolUsageRequest(BaseModel):
    """Request model for tool usage recording."""
    tool_name: str = Field(..., description="Tool name")
    execution_time: float = Field(..., description="Execution time in milliseconds")
    success: bool = Field(..., description="Whether tool execution was successful")
    agent_id: Optional[str] = Field(None, description="Agent that used the tool")


# Health Dashboard Endpoints
@router.get("/health")
async def get_system_health():
    """Get comprehensive system health status."""
    try:
        health_dashboard = get_health_dashboard()
        health = await health_dashboard.get_system_health()
        
        return {
            "system_health": health,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get system health: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get system health: {str(e)}")


@router.get("/health/summary")
async def get_health_summary():
    """Get simplified health summary for quick checks."""
    try:
        health_dashboard = get_health_dashboard()
        health = await health_dashboard.get_system_health()
        
        return {
            "overall_status": health.overall_status,
            "health_score": health.health_score,
            "active_alerts": len(health.active_alerts),
            "warnings": len(health.warnings),
            "uptime_hours": health.uptime_seconds / 3600,
            "timestamp": health.last_updated
        }
        
    except Exception as e:
        logger.error(f"Failed to get health summary: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get health summary: {str(e)}")


@router.get("/health/components")
async def get_component_health():
    """Get detailed component health status."""
    try:
        health_dashboard = get_health_dashboard()
        health = await health_dashboard.get_system_health()
        
        return {
            "components": health.components,
            "overall_status": health.overall_status,
            "timestamp": health.last_updated
        }
        
    except Exception as e:
        logger.error(f"Failed to get component health: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get component health: {str(e)}")


# API Performance Endpoints
@router.get("/api/performance")
async def get_api_performance():
    """Get API performance metrics and analytics."""
    try:
        api_monitor = get_api_performance_monitor()
        
        performance_summary = api_monitor.get_performance_summary()
        endpoint_metrics = api_monitor.get_endpoint_metrics()
        active_alerts = api_monitor.get_active_alerts()
        
        return {
            "performance_summary": performance_summary,
            "endpoint_metrics": endpoint_metrics,
            "active_alerts": [alert.model_dump() for alert in active_alerts],
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get API performance: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get API performance: {str(e)}")


@router.get("/api/performance/endpoints")
async def get_endpoint_performance(
    endpoint: Optional[str] = Query(None, description="Filter by specific endpoint")
):
    """Get performance metrics for specific endpoints."""
    try:
        api_monitor = get_api_performance_monitor()
        endpoint_metrics = api_monitor.get_endpoint_metrics(endpoint)
        
        return {
            "endpoint_metrics": endpoint_metrics,
            "filtered_by": endpoint,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get endpoint performance: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get endpoint performance: {str(e)}")


@router.get("/api/performance/alerts")
async def get_performance_alerts():
    """Get active performance alerts."""
    try:
        api_monitor = get_api_performance_monitor()
        active_alerts = api_monitor.get_active_alerts()
        
        return {
            "active_alerts": [alert.model_dump() for alert in active_alerts],
            "alert_count": len(active_alerts),
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get performance alerts: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get performance alerts: {str(e)}")


@router.post("/api/performance/alerts/{alert_id}/resolve")
async def resolve_performance_alert(alert_id: str):
    """Resolve a performance alert."""
    try:
        api_monitor = get_api_performance_monitor()
        success = api_monitor.resolve_alert(alert_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Alert not found")
        
        return {
            "alert_id": alert_id,
            "resolved": True,
            "timestamp": datetime.now()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to resolve alert: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to resolve alert: {str(e)}")


# Agent Activity Endpoints
@router.get("/agents/activity")
async def get_agent_activity():
    """Get comprehensive agent activity metrics."""
    try:
        agent_monitor = get_agent_activity_monitor()
        
        activity_summary = agent_monitor.get_activity_summary()
        agent_metrics = agent_monitor.get_agent_metrics()
        tool_stats = agent_monitor.get_tool_usage_stats()
        
        return {
            "activity_summary": activity_summary,
            "agent_metrics": agent_metrics,
            "tool_usage_stats": tool_stats,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get agent activity: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get agent activity: {str(e)}")


@router.get("/agents/summary")
async def get_agent_summary():
    """Get simplified agent activity summary."""
    try:
        agent_monitor = get_agent_activity_monitor()
        activity_summary = agent_monitor.get_activity_summary()
        
        return {
            "summary": activity_summary,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get agent summary: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get agent summary: {str(e)}")


@router.get("/agents/{agent_id}")
async def get_agent_metrics(agent_id: str):
    """Get metrics for a specific agent."""
    try:
        agent_monitor = get_agent_activity_monitor()
        agent_metrics = agent_monitor.get_agent_metrics(agent_id)
        
        if not agent_metrics:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        return {
            "agent_metrics": agent_metrics,
            "timestamp": datetime.now()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get agent metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get agent metrics: {str(e)}")


@router.post("/agents/register")
async def register_agent(request: AgentRegistrationRequest):
    """Register a new agent for monitoring."""
    try:
        agent_monitor = get_agent_activity_monitor()
        agent_monitor.register_agent(request.agent_id, request.agent_type)
        
        return {
            "agent_id": request.agent_id,
            "agent_type": request.agent_type,
            "registered": True,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to register agent: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to register agent: {str(e)}")


@router.post("/agents/task-completion")
async def record_task_completion(request: TaskCompletionRequest):
    """Record a completed task for an agent."""
    try:
        agent_monitor = get_agent_activity_monitor()
        agent_monitor.record_task_completion(
            request.agent_id,
            request.task_duration,
            request.success,
            request.tools_used
        )
        
        return {
            "recorded": True,
            "agent_id": request.agent_id,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to record task completion: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to record task completion: {str(e)}")


@router.post("/tools/usage")
async def record_tool_usage(request: ToolUsageRequest):
    """Record tool usage statistics."""
    try:
        agent_monitor = get_agent_activity_monitor()
        agent_monitor.record_tool_usage(
            request.tool_name,
            request.execution_time,
            request.success,
            request.agent_id
        )
        
        return {
            "recorded": True,
            "tool_name": request.tool_name,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to record tool usage: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to record tool usage: {str(e)}")


@router.get("/tools/stats")
async def get_tool_usage_stats(
    tool_name: Optional[str] = Query(None, description="Filter by specific tool")
):
    """Get tool usage statistics."""
    try:
        agent_monitor = get_agent_activity_monitor()
        tool_stats = agent_monitor.get_tool_usage_stats(tool_name)
        
        return {
            "tool_usage_stats": tool_stats,
            "filtered_by": tool_name,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get tool usage stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get tool usage stats: {str(e)}")


# System Metrics Endpoints
@router.get("/system/metrics")
async def get_system_metrics():
    """Get detailed system resource metrics."""
    try:
        health_dashboard = get_health_dashboard()
        health = await health_dashboard.get_system_health()
        
        return {
            "system_metrics": health.system_metrics,
            "performance_metrics": health.performance_metrics,
            "activity_metrics": health.activity_metrics,
            "timestamp": health.last_updated
        }
        
    except Exception as e:
        logger.error(f"Failed to get system metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get system metrics: {str(e)}")


@router.get("/system/alerts")
async def get_system_alerts():
    """Get all active system alerts."""
    try:
        health_dashboard = get_health_dashboard()
        health = await health_dashboard.get_system_health()
        
        return {
            "active_alerts": health.active_alerts,
            "warnings": health.warnings,
            "alert_count": len(health.active_alerts),
            "warning_count": len(health.warnings),
            "timestamp": health.last_updated
        }
        
    except Exception as e:
        logger.error(f"Failed to get system alerts: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get system alerts: {str(e)}")


@router.get("/dashboard")
async def get_monitoring_dashboard():
    """Get complete monitoring dashboard data for frontend."""
    try:
        health_dashboard = get_health_dashboard()
        api_monitor = get_api_performance_monitor()
        agent_monitor = get_agent_activity_monitor()
        
        # Get all monitoring data
        system_health = await health_dashboard.get_system_health()
        api_performance = api_monitor.get_performance_summary()
        agent_activity = agent_monitor.get_activity_summary()
        
        return {
            "dashboard": {
                "system_health": system_health,
                "api_performance": api_performance,
                "agent_activity": agent_activity,
                "last_updated": datetime.now()
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get monitoring dashboard: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get monitoring dashboard: {str(e)}")
