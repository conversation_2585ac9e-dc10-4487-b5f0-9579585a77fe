"""
Project Management API Endpoints

Provides REST API endpoints for managing multiple isolated projects.
Each project has its own workspace, embeddings, sessions, and configuration.
"""

import logging
from typing import Optional
from fastapi import APIRouter, HTTPException, Query, Path as PathParam

try:
    import logfire
except ImportError:
    # Mock logfire if not available
    class MockLogfire:
        def span(self, *args, **kwargs):
            from contextlib import nullcontext
            return nullcontext()
    logfire = MockLogfire()

try:
    from backend.app.models.project import (
        ProjectCreateRequest, ProjectResponse, ProjectListResponse, Project
    )
    from backend.app.core.project_manager import project_manager
except ImportError:
    from app.models.project import (
        ProjectCreateRequest, ProjectResponse, ProjectListResponse, Project
    )
    from app.core.project_manager import project_manager

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/projects", tags=["projects"])


@router.post("/create", response_model=ProjectResponse)
async def create_project(request: ProjectCreateRequest):
    """
    Create a new project with isolated workspace and configuration.
    
    This endpoint:
    1. Creates a new project with unique workspace directory
    2. Sets up project-specific Qdrant collection for embeddings
    3. Initializes project database for sessions and plans
    4. Creates basic project structure (src/, docs/, tests/)
    5. Generates project configuration and README
    """
    with logfire.span("create_project", project_slug=request.slug):
        try:
            logger.info(f"🏗️ Creating new project: {request.name} ({request.slug})")
            response = await project_manager.create_project(request)
            
            if response.status == "success":
                logger.info(f"✅ Project created successfully: {request.slug}")
            else:
                logger.error(f"❌ Project creation failed: {response.error}")
            
            return response
            
        except Exception as e:
            logger.error(f"❌ Project creation exception: {e}")
            raise HTTPException(status_code=500, detail=f"Project creation failed: {str(e)}")


@router.get("/list", response_model=ProjectListResponse)
async def list_projects():
    """
    List all projects with their metadata and status.

    Returns:
        List of all projects with statistics
    """
    with logfire.span("list_projects"):
        try:
            logger.info("📋 Listing all projects")
            response = await project_manager.list_projects()

            logger.info(f"📊 Found {response.total} projects ({response.active} active, {response.archived} archived)")
            return response

        except Exception as e:
            logger.error(f"❌ Failed to list projects: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to list projects: {str(e)}")


@router.get("/health")
async def project_system_health():
    """
    Get project management system health status.

    Returns:
        System health information
    """
    with logfire.span("project_system_health"):
        try:
            total_projects = len(project_manager.active_projects)
            current_project = project_manager.get_current_project()
            base_workspace_exists = project_manager.base_workspace.exists()

            return {
                "status": "healthy",
                "total_projects": total_projects,
                "current_project": current_project.slug if current_project else None,
                "base_workspace": str(project_manager.base_workspace),
                "base_workspace_exists": base_workspace_exists,
                "projects_db_path": str(project_manager.projects_db_path),
                "projects_db_exists": project_manager.projects_db_path.exists()
            }

        except Exception as e:
            logger.error(f"❌ Project system health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e)
            }


@router.get("/{project_slug}", response_model=Project)
async def get_project(project_slug: str = PathParam(..., description="Project slug identifier")):
    """
    Get detailed information about a specific project.
    
    Args:
        project_slug: URL-friendly project identifier
        
    Returns:
        Complete project information including metadata
    """
    with logfire.span("get_project", project_slug=project_slug):
        try:
            logger.info(f"🔍 Getting project details: {project_slug}")
            project = await project_manager.get_project(project_slug)
            
            if not project:
                logger.warning(f"❌ Project not found: {project_slug}")
                raise HTTPException(status_code=404, detail=f"Project '{project_slug}' not found")
            
            logger.info(f"✅ Retrieved project: {project.config.name}")
            return project
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Failed to get project {project_slug}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to get project: {str(e)}")


@router.post("/{project_slug}/activate", response_model=ProjectResponse)
async def activate_project(project_slug: str = PathParam(..., description="Project slug identifier")):
    """
    Set a project as the current active project.
    
    This affects:
    - File operations (scoped to project workspace)
    - AI tools (operate within project context)
    - Embeddings (use project-specific collection)
    - Sessions (project-scoped)
    
    Args:
        project_slug: URL-friendly project identifier
        
    Returns:
        Operation result
    """
    with logfire.span("activate_project", project_slug=project_slug):
        try:
            logger.info(f"🎯 Activating project: {project_slug}")
            
            success = await project_manager.set_current_project(project_slug)
            
            if not success:
                logger.warning(f"❌ Project not found for activation: {project_slug}")
                raise HTTPException(status_code=404, detail=f"Project '{project_slug}' not found")
            
            project = await project_manager.get_project(project_slug)
            
            logger.info(f"✅ Project activated: {project.config.name}")
            return ProjectResponse(
                status="success",
                message=f"Project '{project.config.name}' is now active",
                project=project
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Failed to activate project {project_slug}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to activate project: {str(e)}")


@router.get("/current/status")
async def get_current_project_status():
    """
    Get information about the currently active project.
    
    Returns:
        Current project information or null if no project is active
    """
    with logfire.span("get_current_project_status"):
        try:
            current_project = project_manager.get_current_project()
            
            if not current_project:
                return {
                    "status": "no_active_project",
                    "message": "No project is currently active",
                    "project": None
                }
            
            return {
                "status": "active_project",
                "message": f"Current project: {current_project.config.name}",
                "project": current_project
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get current project status: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to get current project status: {str(e)}")


@router.post("/{project_slug}/archive", response_model=ProjectResponse)
async def archive_project(project_slug: str = PathParam(..., description="Project slug identifier")):
    """
    Archive a project (mark as inactive but preserve data).
    
    Args:
        project_slug: URL-friendly project identifier
        
    Returns:
        Operation result
    """
    with logfire.span("archive_project", project_slug=project_slug):
        try:
            logger.info(f"📦 Archiving project: {project_slug}")
            
            project = await project_manager.get_project(project_slug)
            if not project:
                raise HTTPException(status_code=404, detail=f"Project '{project_slug}' not found")
            
            # Update project status
            project.status = "archived"
            await project_manager._save_project(project)
            
            # Remove from active projects if it's the current project
            if project_manager.current_project and project_manager.current_project.project_id == project.project_id:
                project_manager.current_project = None
            
            logger.info(f"✅ Project archived: {project.config.name}")
            return ProjectResponse(
                status="success",
                message=f"Project '{project.config.name}' has been archived",
                project=project
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Failed to archive project {project_slug}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to archive project: {str(e)}")


@router.get("/{project_slug}/workspace/info")
async def get_project_workspace_info(project_slug: str = PathParam(..., description="Project slug identifier")):
    """
    Get workspace information for a specific project.
    
    Args:
        project_slug: URL-friendly project identifier
        
    Returns:
        Workspace path and structure information
    """
    with logfire.span("get_project_workspace_info", project_slug=project_slug):
        try:
            project = await project_manager.get_project(project_slug)
            if not project:
                raise HTTPException(status_code=404, detail=f"Project '{project_slug}' not found")
            
            workspace_path = project.get_workspace_path(str(project_manager.base_workspace))
            config_path = project.get_config_path(str(project_manager.base_workspace))
            embeddings_collection = project.get_embeddings_collection_name()
            sessions_db_path = project.get_sessions_db_path(str(project_manager.base_workspace))
            
            return {
                "project_slug": project_slug,
                "workspace_path": str(workspace_path),
                "config_path": str(config_path),
                "embeddings_collection": embeddings_collection,
                "sessions_db_path": str(sessions_db_path),
                "workspace_exists": workspace_path.exists(),
                "config_exists": config_path.exists(),
                "sessions_db_exists": sessions_db_path.exists()
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Failed to get workspace info for {project_slug}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to get workspace info: {str(e)}")
