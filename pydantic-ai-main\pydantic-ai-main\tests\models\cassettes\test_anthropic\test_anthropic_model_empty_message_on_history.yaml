interactions:
- request:
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '281'
      content-type:
      - application/json
      host:
      - api.anthropic.com
    method: POST
    parsed_body:
      max_tokens: 1024
      messages:
      - content:
        - text: Hello, how can I help you?
          type: text
        role: assistant
      - content:
        - text: I need a potato!
          type: text
        role: user
      model: claude-3-5-sonnet-latest
      stream: false
      system: |+
        You are a helpful assistant.

    uri: https://api.anthropic.com/v1/messages?beta=true
  response:
    headers:
      connection:
      - keep-alive
      content-length:
      - '671'
      content-type:
      - application/json
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      transfer-encoding:
      - chunked
    parsed_body:
      content:
      - text: |-
          I can't physically give you a potato since I'm a computer program. However, I can:

          1. Help you find recipes that use potatoes
          2. Give you tips on how to select, store, or cook potatoes
          3. Share information about different potato varieties
          4. Provide guidance on growing potatoes

          What specifically would you like to know about potatoes?
        type: text
      id: msg_01UjnDmX3B57Drosu49sMteT
      model: claude-3-5-sonnet-20241022
      role: assistant
      stop_reason: end_turn
      stop_sequence: null
      type: message
      usage:
        cache_creation_input_tokens: 0
        cache_read_input_tokens: 0
        input_tokens: 41
        output_tokens: 82
        service_tier: standard
    status:
      code: 200
      message: OK
version: 1
