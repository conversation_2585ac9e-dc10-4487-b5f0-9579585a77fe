interactions:
- request:
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '348'
      content-type:
      - application/json
      host:
      - api.openai.com
    method: POST
    parsed_body:
      input:
      - content: What is the capital of France?
        role: user
      instructions: ''
      model: gpt-4o
      stream: true
      tool_choice: auto
      tools:
      - description: ''
        name: get_capital
        parameters:
          additionalProperties: false
          properties:
            country:
              type: string
          required:
          - country
          type: object
        strict: true
        type: function
    uri: https://api.openai.com/v1/responses
  response:
    body:
      string: |+
        event: response.created
        data: {"type":"response.created","response":{"id":"resp_67e554a155508191900ee113293c4c830794405d35281ae2","object":"response","created_at":1743082657,"status":"in_progress","error":null,"incomplete_details":null,"instructions":"","max_output_tokens":null,"model":"gpt-4o-2024-08-06","output":[],"parallel_tool_calls":true,"previous_response_id":null,"reasoning":{"effort":null,"generate_summary":null},"store":true,"temperature":1.0,"text":{"format":{"type":"text"}},"tool_choice":"auto","tools":[{"type":"function","description":null,"name":"get_capital","parameters":{"additionalProperties":false,"properties":{"country":{"type":"string"}},"required":["country"],"type":"object"},"strict":true}],"top_p":1.0,"truncation":"disabled","usage":null,"user":null,"metadata":{}}}

        event: response.in_progress
        data: {"type":"response.in_progress","response":{"id":"resp_67e554a155508191900ee113293c4c830794405d35281ae2","object":"response","created_at":1743082657,"status":"in_progress","error":null,"incomplete_details":null,"instructions":"","max_output_tokens":null,"model":"gpt-4o-2024-08-06","output":[],"parallel_tool_calls":true,"previous_response_id":null,"reasoning":{"effort":null,"generate_summary":null},"store":true,"temperature":1.0,"text":{"format":{"type":"text"}},"tool_choice":"auto","tools":[{"type":"function","description":null,"name":"get_capital","parameters":{"additionalProperties":false,"properties":{"country":{"type":"string"}},"required":["country"],"type":"object"},"strict":true}],"top_p":1.0,"truncation":"disabled","usage":null,"user":null,"metadata":{}}}

        event: response.output_item.added
        data: {"type":"response.output_item.added","output_index":0,"item":{"type":"function_call","id":"fc_67e554a1de488191af0831d35cbe082e0794405d35281ae2","call_id":"call_kL0PCQV7M2WMoVX8V8OtYSAL","name":"get_capital","arguments":"","status":"in_progress"}}

        event: response.function_call_arguments.delta
        data: {"type":"response.function_call_arguments.delta","item_id":"fc_67e554a1de488191af0831d35cbe082e0794405d35281ae2","output_index":0,"delta":"{\""}

        event: response.function_call_arguments.delta
        data: {"type":"response.function_call_arguments.delta","item_id":"fc_67e554a1de488191af0831d35cbe082e0794405d35281ae2","output_index":0,"delta":"country"}

        event: response.function_call_arguments.delta
        data: {"type":"response.function_call_arguments.delta","item_id":"fc_67e554a1de488191af0831d35cbe082e0794405d35281ae2","output_index":0,"delta":"\":\""}

        event: response.function_call_arguments.delta
        data: {"type":"response.function_call_arguments.delta","item_id":"fc_67e554a1de488191af0831d35cbe082e0794405d35281ae2","output_index":0,"delta":"France"}

        event: response.function_call_arguments.delta
        data: {"type":"response.function_call_arguments.delta","item_id":"fc_67e554a1de488191af0831d35cbe082e0794405d35281ae2","output_index":0,"delta":"\"}"}

        event: response.function_call_arguments.done
        data: {"type":"response.function_call_arguments.done","item_id":"fc_67e554a1de488191af0831d35cbe082e0794405d35281ae2","output_index":0,"arguments":"{\"country\":\"France\"}"}

        event: response.output_item.done
        data: {"type":"response.output_item.done","output_index":0,"item":{"type":"function_call","id":"fc_67e554a1de488191af0831d35cbe082e0794405d35281ae2","call_id":"call_kL0PCQV7M2WMoVX8V8OtYSAL","name":"get_capital","arguments":"{\"country\":\"France\"}","status":"completed"}}

        event: response.completed
        data: {"type":"response.completed","response":{"id":"resp_67e554a155508191900ee113293c4c830794405d35281ae2","object":"response","created_at":1743082657,"status":"completed","error":null,"incomplete_details":null,"instructions":"","max_output_tokens":null,"model":"gpt-4o-2024-08-06","output":[{"type":"function_call","id":"fc_67e554a1de488191af0831d35cbe082e0794405d35281ae2","call_id":"call_kL0PCQV7M2WMoVX8V8OtYSAL","name":"get_capital","arguments":"{\"country\":\"France\"}","status":"completed"}],"parallel_tool_calls":true,"previous_response_id":null,"reasoning":{"effort":null,"generate_summary":null},"store":true,"temperature":1.0,"text":{"format":{"type":"text"}},"tool_choice":"auto","tools":[{"type":"function","description":null,"name":"get_capital","parameters":{"additionalProperties":false,"properties":{"country":{"type":"string"}},"required":["country"],"type":"object"},"strict":true}],"top_p":1.0,"truncation":"disabled","usage":{"input_tokens":255,"input_tokens_details":{"cached_tokens":0},"output_tokens":16,"output_tokens_details":{"reasoning_tokens":0},"total_tokens":271},"user":null,"metadata":{}}}

    headers:
      alt-svc:
      - h3=":443"; ma=86400
      connection:
      - keep-alive
      content-type:
      - text/event-stream; charset=utf-8
      openai-organization:
      - pydantic-28gund
      openai-processing-ms:
      - '100'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      transfer-encoding:
      - chunked
    status:
      code: 200
      message: OK
- request:
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '610'
      content-type:
      - application/json
      cookie:
      - __cf_bm=elE9FknxoKl9hypuWr9U7L0wvq0uECHBbKEn0s4ymsE-1743082657-*******-NtLziVJ.95rmB6AWode1f2xyipNMUbu_BKePPZjYQerhr9gRgMHV4iW.0tAhxdVAlgKvQ1BP.yKoSYj9GVD3phMrStrVxclEQ5scgA37Ie0;
        _cfuvid=jgKaRpCXzc4an8g3tXyrelqdUfEtMZsdBPqFq9LZqR4-1743082657435-*******-604800000
      host:
      - api.openai.com
    method: POST
    parsed_body:
      input:
      - content: What is the capital of France?
        role: user
      - arguments: '{"country":"France"}'
        call_id: fc_67e554a1de488191af0831d35cbe082e0794405d35281ae2
        name: get_capital
        type: function_call
      - call_id: fc_67e554a1de488191af0831d35cbe082e0794405d35281ae2
        output: Paris
        type: function_call_output
      instructions: ''
      model: gpt-4o
      stream: true
      tool_choice: auto
      tools:
      - description: ''
        name: get_capital
        parameters:
          additionalProperties: false
          properties:
            country:
              type: string
          required:
          - country
          type: object
        strict: true
        type: function
    uri: https://api.openai.com/v1/responses
  response:
    body:
      string: |+
        event: response.created
        data: {"type":"response.created","response":{"id":"resp_67e554a21aa88191b65876ac5e5bbe0406c52f0e511c76ed","object":"response","created_at":1743082658,"status":"in_progress","error":null,"incomplete_details":null,"instructions":"","max_output_tokens":null,"model":"gpt-4o-2024-08-06","output":[],"parallel_tool_calls":true,"previous_response_id":null,"reasoning":{"effort":null,"generate_summary":null},"store":true,"temperature":1.0,"text":{"format":{"type":"text"}},"tool_choice":"auto","tools":[{"type":"function","description":null,"name":"get_capital","parameters":{"additionalProperties":false,"properties":{"country":{"type":"string"}},"required":["country"],"type":"object"},"strict":true}],"top_p":1.0,"truncation":"disabled","usage":null,"user":null,"metadata":{}}}

        event: response.in_progress
        data: {"type":"response.in_progress","response":{"id":"resp_67e554a21aa88191b65876ac5e5bbe0406c52f0e511c76ed","object":"response","created_at":1743082658,"status":"in_progress","error":null,"incomplete_details":null,"instructions":"","max_output_tokens":null,"model":"gpt-4o-2024-08-06","output":[],"parallel_tool_calls":true,"previous_response_id":null,"reasoning":{"effort":null,"generate_summary":null},"store":true,"temperature":1.0,"text":{"format":{"type":"text"}},"tool_choice":"auto","tools":[{"type":"function","description":null,"name":"get_capital","parameters":{"additionalProperties":false,"properties":{"country":{"type":"string"}},"required":["country"],"type":"object"},"strict":true}],"top_p":1.0,"truncation":"disabled","usage":null,"user":null,"metadata":{}}}

        event: response.output_item.added
        data: {"type":"response.output_item.added","output_index":0,"item":{"type":"message","id":"msg_67e554a28bec8191b56d3e2331eff88006c52f0e511c76ed","status":"in_progress","role":"assistant","content":[]}}

        event: response.content_part.added
        data: {"type":"response.content_part.added","item_id":"msg_67e554a28bec8191b56d3e2331eff88006c52f0e511c76ed","output_index":0,"content_index":0,"part":{"type":"output_text","text":"","annotations":[]}}

        event: response.output_text.delta
        data: {"type":"response.output_text.delta","item_id":"msg_67e554a28bec8191b56d3e2331eff88006c52f0e511c76ed","output_index":0,"content_index":0,"delta":"The"}

        event: response.output_text.delta
        data: {"type":"response.output_text.delta","item_id":"msg_67e554a28bec8191b56d3e2331eff88006c52f0e511c76ed","output_index":0,"content_index":0,"delta":" capital"}

        event: response.output_text.delta
        data: {"type":"response.output_text.delta","item_id":"msg_67e554a28bec8191b56d3e2331eff88006c52f0e511c76ed","output_index":0,"content_index":0,"delta":" of"}

        event: response.output_text.delta
        data: {"type":"response.output_text.delta","item_id":"msg_67e554a28bec8191b56d3e2331eff88006c52f0e511c76ed","output_index":0,"content_index":0,"delta":" France"}

        event: response.output_text.delta
        data: {"type":"response.output_text.delta","item_id":"msg_67e554a28bec8191b56d3e2331eff88006c52f0e511c76ed","output_index":0,"content_index":0,"delta":" is"}

        event: response.output_text.delta
        data: {"type":"response.output_text.delta","item_id":"msg_67e554a28bec8191b56d3e2331eff88006c52f0e511c76ed","output_index":0,"content_index":0,"delta":" Paris"}

        event: response.output_text.delta
        data: {"type":"response.output_text.delta","item_id":"msg_67e554a28bec8191b56d3e2331eff88006c52f0e511c76ed","output_index":0,"content_index":0,"delta":"."}

        event: response.output_text.done
        data: {"type":"response.output_text.done","item_id":"msg_67e554a28bec8191b56d3e2331eff88006c52f0e511c76ed","output_index":0,"content_index":0,"text":"The capital of France is Paris."}

        event: response.content_part.done
        data: {"type":"response.content_part.done","item_id":"msg_67e554a28bec8191b56d3e2331eff88006c52f0e511c76ed","output_index":0,"content_index":0,"part":{"type":"output_text","text":"The capital of France is Paris.","annotations":[]}}

        event: response.output_item.done
        data: {"type":"response.output_item.done","output_index":0,"item":{"type":"message","id":"msg_67e554a28bec8191b56d3e2331eff88006c52f0e511c76ed","status":"completed","role":"assistant","content":[{"type":"output_text","text":"The capital of France is Paris.","annotations":[]}]}}

        event: response.completed
        data: {"type":"response.completed","response":{"id":"resp_67e554a21aa88191b65876ac5e5bbe0406c52f0e511c76ed","object":"response","created_at":1743082658,"status":"completed","error":null,"incomplete_details":null,"instructions":"","max_output_tokens":null,"model":"gpt-4o-2024-08-06","output":[{"type":"message","id":"msg_67e554a28bec8191b56d3e2331eff88006c52f0e511c76ed","status":"completed","role":"assistant","content":[{"type":"output_text","text":"The capital of France is Paris.","annotations":[]}]}],"parallel_tool_calls":true,"previous_response_id":null,"reasoning":{"effort":null,"generate_summary":null},"store":true,"temperature":1.0,"text":{"format":{"type":"text"}},"tool_choice":"auto","tools":[{"type":"function","description":null,"name":"get_capital","parameters":{"additionalProperties":false,"properties":{"country":{"type":"string"}},"required":["country"],"type":"object"},"strict":true}],"top_p":1.0,"truncation":"disabled","usage":{"input_tokens":278,"input_tokens_details":{"cached_tokens":0},"output_tokens":9,"output_tokens_details":{"reasoning_tokens":0},"total_tokens":287},"user":null,"metadata":{}}}

    headers:
      alt-svc:
      - h3=":443"; ma=86400
      connection:
      - keep-alive
      content-type:
      - text/event-stream; charset=utf-8
      openai-organization:
      - pydantic-28gund
      openai-processing-ms:
      - '122'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      transfer-encoding:
      - chunked
    status:
      code: 200
      message: OK
version: 1
...
