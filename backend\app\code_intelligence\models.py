"""
Pydantic Models for Code Intelligence Hub - Phase 8B

Defines all data structures for code indexing, search, and analysis.
"""

from typing import List, Dict, Optional, Any, Union, Set
from datetime import datetime
from pathlib import Path
from pydantic import BaseModel, Field, validator
from enum import Enum


class CodeLanguage(str, Enum):
    """Supported programming languages."""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    JAVA = "java"
    CPP = "cpp"
    GO = "go"
    RUST = "rust"
    HTML = "html"
    CSS = "css"
    SQL = "sql"
    MARKDOWN = "markdown"
    JSON = "json"
    YAML = "yaml"
    SHELL = "shell"
    UNKNOWN = "unknown"


class CodeElementType(str, Enum):
    """Types of code elements."""
    FILE = "file"
    FUNCTION = "function"
    CLASS = "class"
    METHOD = "method"
    VARIABLE = "variable"
    IMPORT = "import"
    COMMENT = "comment"
    DOCSTRING = "docstring"
    CHUNK = "chunk"


class CodeFile(BaseModel):
    """Represents a code file with metadata."""
    
    file_id: str = Field(..., description="Unique file identifier")
    path: str = Field(..., description="File path relative to workspace")
    absolute_path: str = Field(..., description="Absolute file path")
    language: CodeLanguage = Field(..., description="Programming language")
    size: int = Field(..., description="File size in bytes")
    lines_count: int = Field(..., description="Number of lines")
    modified_time: datetime = Field(..., description="Last modification time")
    created_time: datetime = Field(..., description="Creation time")
    encoding: str = Field(default="utf-8", description="File encoding")
    hash: str = Field(..., description="File content hash for change detection")
    
    # Code analysis results
    functions: List[str] = Field(default_factory=list, description="Function names")
    classes: List[str] = Field(default_factory=list, description="Class names")
    imports: List[str] = Field(default_factory=list, description="Import statements")
    dependencies: List[str] = Field(default_factory=list, description="File dependencies")
    
    # Indexing metadata
    indexed_time: Optional[datetime] = Field(None, description="When file was indexed")
    chunk_count: int = Field(default=0, description="Number of chunks created")
    embedding_count: int = Field(default=0, description="Number of embeddings generated")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            Path: lambda v: str(v)
        }


class CodeChunk(BaseModel):
    """Represents a chunk of code for embedding."""
    
    chunk_id: str = Field(..., description="Unique chunk identifier")
    file_id: str = Field(..., description="Parent file identifier")
    content: str = Field(..., description="Chunk content")
    start_line: int = Field(..., description="Starting line number")
    end_line: int = Field(..., description="Ending line number")
    element_type: CodeElementType = Field(..., description="Type of code element")
    
    # Context information
    function_name: Optional[str] = Field(None, description="Parent function name")
    class_name: Optional[str] = Field(None, description="Parent class name")
    context: str = Field(default="", description="Surrounding context")
    
    # Embedding metadata
    embedding_id: Optional[str] = Field(None, description="Vector database ID")
    embedding_generated: bool = Field(default=False, description="Whether embedding exists")
    
    # Analysis metadata
    complexity_score: Optional[float] = Field(None, description="Code complexity score")
    importance_score: Optional[float] = Field(None, description="Importance score")
    tags: List[str] = Field(default_factory=list, description="Semantic tags")


class CodeFunction(BaseModel):
    """Represents a function or method."""
    
    function_id: str = Field(..., description="Unique function identifier")
    file_id: str = Field(..., description="Parent file identifier")
    name: str = Field(..., description="Function name")
    full_name: str = Field(..., description="Fully qualified name")
    signature: str = Field(..., description="Function signature")
    docstring: Optional[str] = Field(None, description="Function docstring")
    
    # Location information
    start_line: int = Field(..., description="Starting line number")
    end_line: int = Field(..., description="Ending line number")
    
    # Analysis results
    parameters: List[str] = Field(default_factory=list, description="Parameter names")
    return_type: Optional[str] = Field(None, description="Return type")
    calls: List[str] = Field(default_factory=list, description="Functions called")
    called_by: List[str] = Field(default_factory=list, description="Functions that call this")
    
    # Metadata
    is_method: bool = Field(default=False, description="Whether this is a class method")
    class_name: Optional[str] = Field(None, description="Parent class name")
    visibility: str = Field(default="public", description="Function visibility")
    complexity_score: Optional[float] = Field(None, description="Cyclomatic complexity")


class CodeClass(BaseModel):
    """Represents a class definition."""
    
    class_id: str = Field(..., description="Unique class identifier")
    file_id: str = Field(..., description="Parent file identifier")
    name: str = Field(..., description="Class name")
    full_name: str = Field(..., description="Fully qualified name")
    docstring: Optional[str] = Field(None, description="Class docstring")
    
    # Location information
    start_line: int = Field(..., description="Starting line number")
    end_line: int = Field(..., description="Ending line number")
    
    # Analysis results
    methods: List[str] = Field(default_factory=list, description="Method names")
    attributes: List[str] = Field(default_factory=list, description="Attribute names")
    inheritance: List[str] = Field(default_factory=list, description="Parent classes")
    subclasses: List[str] = Field(default_factory=list, description="Child classes")
    
    # Metadata
    is_abstract: bool = Field(default=False, description="Whether class is abstract")
    visibility: str = Field(default="public", description="Class visibility")


class SearchQuery(BaseModel):
    """Represents a search query."""
    
    query: str = Field(..., description="Search query text")
    query_type: str = Field(default="semantic", description="Type of search")
    filters: Dict[str, Any] = Field(default_factory=dict, description="Search filters")
    limit: int = Field(default=10, description="Maximum results")
    threshold: float = Field(default=0.7, description="Similarity threshold")
    
    # Context
    current_file: Optional[str] = Field(None, description="Current file context")
    include_context: bool = Field(default=True, description="Include surrounding context")


class SearchResult(BaseModel):
    """Represents a search result."""
    
    result_id: str = Field(..., description="Result identifier")
    file_path: str = Field(..., description="File path")
    content: str = Field(..., description="Matching content")
    score: float = Field(..., description="Similarity score")
    element_type: CodeElementType = Field(..., description="Type of code element")
    
    # Location information
    start_line: int = Field(..., description="Starting line number")
    end_line: int = Field(..., description="Ending line number")
    
    # Context
    context_before: str = Field(default="", description="Context before match")
    context_after: str = Field(default="", description="Context after match")
    function_name: Optional[str] = Field(None, description="Parent function")
    class_name: Optional[str] = Field(None, description="Parent class")
    
    # Metadata
    language: CodeLanguage = Field(..., description="Programming language")
    tags: List[str] = Field(default_factory=list, description="Semantic tags")


class RelationshipType(str, Enum):
    """Types of code relationships."""
    IMPORTS = "imports"
    CALLS = "calls"
    INHERITS = "inherits"
    IMPLEMENTS = "implements"
    USES = "uses"
    DEFINES = "defines"
    REFERENCES = "references"


class CodeRelationship(BaseModel):
    """Represents a relationship between code elements."""
    
    source_id: str = Field(..., description="Source element ID")
    target_id: str = Field(..., description="Target element ID")
    relationship_type: RelationshipType = Field(..., description="Type of relationship")
    strength: float = Field(default=1.0, description="Relationship strength")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class RelationshipGraph(BaseModel):
    """Represents a graph of code relationships."""
    
    nodes: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="Graph nodes")
    edges: List[CodeRelationship] = Field(default_factory=list, description="Graph edges")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Graph metadata")
    
    def add_node(self, node_id: str, node_data: Dict[str, Any]):
        """Add a node to the graph."""
        self.nodes[node_id] = node_data
    
    def add_edge(self, relationship: CodeRelationship):
        """Add an edge to the graph."""
        self.edges.append(relationship)
    
    def get_dependencies(self, node_id: str) -> List[str]:
        """Get all dependencies of a node."""
        return [edge.target_id for edge in self.edges if edge.source_id == node_id]
    
    def get_dependents(self, node_id: str) -> List[str]:
        """Get all dependents of a node."""
        return [edge.source_id for edge in self.edges if edge.target_id == node_id]


class IndexingStats(BaseModel):
    """Statistics about the indexing process."""
    
    total_files: int = Field(default=0, description="Total files processed")
    indexed_files: int = Field(default=0, description="Successfully indexed files")
    failed_files: int = Field(default=0, description="Failed to index files")
    total_chunks: int = Field(default=0, description="Total chunks created")
    total_embeddings: int = Field(default=0, description="Total embeddings generated")
    
    # Performance metrics
    indexing_time: float = Field(default=0.0, description="Total indexing time")
    average_file_time: float = Field(default=0.0, description="Average time per file")
    
    # Language breakdown
    language_stats: Dict[str, int] = Field(default_factory=dict, description="Files per language")
    
    # Error tracking
    errors: List[str] = Field(default_factory=list, description="Error messages")
    
    def add_file(self, language: CodeLanguage, success: bool, processing_time: float):
        """Add file processing statistics."""
        self.total_files += 1
        if success:
            self.indexed_files += 1
        else:
            self.failed_files += 1
        
        self.indexing_time += processing_time
        self.average_file_time = self.indexing_time / self.total_files
        
        lang_key = language.value
        self.language_stats[lang_key] = self.language_stats.get(lang_key, 0) + 1
