"""
Enhanced Error Recovery System for DeepNexus

This module provides intelligent error handling, retry mechanisms, and recovery strategies
that integrate seamlessly with Pydantic AI agents and the existing resilience system.

Key Features:
- Smart error categorization and routing
- Intelligent retry with exponential backoff
- Auto-recovery for common issues
- User-friendly error messages with actionable suggestions
- Integration with Pydantic AI ModelRetry exceptions
- Circuit breaker patterns for cascading failure prevention
"""

from .retry_manager import EnhancedRetryManager, RetryConfig, RetryResult
from .error_categorizer import ErrorCategorizer, ErrorCategory, ErrorContext
from .recovery_strategies import RecoveryStrategies, RecoveryAction, RecoveryResult
from .user_notifications import UserNotificationManager, NotificationLevel, ErrorNotification
from .integration import ErrorHandlingIntegration

__all__ = [
    # Retry Management
    'EnhancedRetryManager',
    'RetryConfig', 
    'RetryResult',
    
    # Error Categorization
    'ErrorCategorizer',
    'ErrorCategory',
    'ErrorContext',
    
    # Recovery Strategies
    'RecoveryStrategies',
    'RecoveryAction',
    'RecoveryResult',
    
    # User Notifications
    'UserNotificationManager',
    'NotificationLevel',
    'ErrorNotification',
    
    # Integration
    'ErrorHandlingIntegration',
]

# Global instances for easy access (lazy initialization)
_retry_manager = None
_error_categorizer = None
_recovery_strategies = None
_notification_manager = None
_error_integration = None

def get_retry_manager():
    global _retry_manager
    if _retry_manager is None:
        _retry_manager = EnhancedRetryManager()
    return _retry_manager

def get_error_categorizer():
    global _error_categorizer
    if _error_categorizer is None:
        _error_categorizer = ErrorCategorizer()
    return _error_categorizer

def get_recovery_strategies():
    global _recovery_strategies
    if _recovery_strategies is None:
        _recovery_strategies = RecoveryStrategies()
    return _recovery_strategies

def get_notification_manager():
    global _notification_manager
    if _notification_manager is None:
        _notification_manager = UserNotificationManager()
    return _notification_manager

def get_error_integration(enable_ml_intelligence: bool = True):
    global _error_integration
    if _error_integration is None:
        _error_integration = ErrorHandlingIntegration(
            retry_manager=get_retry_manager(),
            error_categorizer=get_error_categorizer(),
            recovery_strategies=get_recovery_strategies(),
            notification_manager=get_notification_manager(),
            enable_ml_intelligence=enable_ml_intelligence
        )
    return _error_integration
