interactions:
- request:
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '169'
      content-type:
      - application/json
      host:
      - generativelanguage.googleapis.com
    method: POST
    parsed_body:
      contents:
      - parts:
        - text: Hello!
        role: user
      generationConfig: {}
      systemInstruction:
        parts:
        - text: You are a chatbot.
        role: user
    uri: https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
  response:
    headers:
      alt-svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      content-length:
      - '644'
      content-type:
      - application/json; charset=UTF-8
      server-timing:
      - gfet4t7; dur=322
      transfer-encoding:
      - chunked
      vary:
      - Origin
      - X-Origin
      - Referer
    parsed_body:
      candidates:
      - avgLogprobs: -0.0009223055941137401
        content:
          parts:
          - text: |
              Hello there! How can I help you today?
          role: model
        finishReason: STOP
      modelVersion: gemini-1.5-flash
      usageMetadata:
        candidatesTokenCount: 11
        candidatesTokensDetails:
        - modality: TEXT
          tokenCount: 11
        promptTokenCount: 7
        promptTokensDetails:
        - modality: TEXT
          tokenCount: 7
        totalTokenCount: 18
    status:
      code: 200
      message: OK
version: 1
