"""
Pydantic Models for Autonomous Continue Mode - Phase 8C

Defines all data structures for autonomous development loops, error handling,
and progress tracking.
"""

from typing import List, Dict, Optional, Any, Union, Set
from datetime import datetime, timedelta
from enum import Enum
from pydantic import BaseModel, Field
from pathlib import Path


class LoopState(str, Enum):
    """States of the autonomous continue loop."""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"
    COMPLETED = "completed"
    STOPPED = "stopped"
    CIRCUIT_BREAKER = "circuit_breaker"


class ErrorSeverity(str, Enum):
    """Severity levels for errors."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ContextPerspective(str, Enum):
    """Different perspectives for context adaptation."""
    ARCHITECTURE = "architecture"
    DEBUGGING = "debugging"
    TESTING = "testing"
    PERFORMANCE = "performance"
    SECURITY = "security"
    USER_EXPERIENCE = "user_experience"


class SafetyLimits(BaseModel):
    """Safety limits for autonomous continue mode."""
    
    max_iterations: int = Field(default=50, description="Maximum number of iterations")
    max_duration: timedelta = Field(default=timedelta(hours=2), description="Maximum duration")
    max_errors: int = Field(default=10, description="Maximum consecutive errors")
    max_retries_per_error: int = Field(default=3, description="Maximum retries per error type")
    circuit_breaker_threshold: int = Field(default=5, description="Circuit breaker error threshold")
    resource_usage_limit: float = Field(default=0.8, description="Resource usage limit (0-1)")
    
    class Config:
        json_encoders = {
            timedelta: lambda v: v.total_seconds()
        }


class ContinueConfig(BaseModel):
    """Configuration for autonomous continue mode."""
    
    session_name: str = Field(..., description="Name of the continue session")
    initial_task: str = Field(..., description="Initial task description")
    workspace_root: str = Field(..., description="Workspace root directory")
    
    # Loop configuration
    safety_limits: SafetyLimits = Field(default_factory=SafetyLimits)
    auto_commit: bool = Field(default=False, description="Auto-commit successful iterations")
    auto_push: bool = Field(default=False, description="Auto-push successful commits")
    
    # Error handling
    enable_error_learning: bool = Field(default=True, description="Enable error pattern learning")
    context_switching: bool = Field(default=True, description="Enable context perspective switching")
    
    # Progress tracking
    progress_reporting_interval: int = Field(default=5, description="Progress report interval (iterations)")
    save_session_state: bool = Field(default=True, description="Save session state to disk")
    
    # Integration
    use_code_intelligence: bool = Field(default=True, description="Use Code Intelligence Hub")
    use_vision_analysis: bool = Field(default=False, description="Use vision analysis for UI tasks")


class ErrorPattern(BaseModel):
    """Represents a detected error pattern."""
    
    pattern_id: str = Field(..., description="Unique pattern identifier")
    error_type: str = Field(..., description="Type of error")
    error_message: str = Field(..., description="Error message pattern")
    severity: ErrorSeverity = Field(..., description="Error severity")
    
    # Pattern analysis
    occurrence_count: int = Field(default=1, description="Number of occurrences")
    first_seen: datetime = Field(default_factory=datetime.now)
    last_seen: datetime = Field(default_factory=datetime.now)
    
    # Context information
    file_patterns: List[str] = Field(default_factory=list, description="File patterns where error occurs")
    code_patterns: List[str] = Field(default_factory=list, description="Code patterns associated with error")
    
    # Resolution information
    successful_resolutions: List[str] = Field(default_factory=list, description="Successful resolution strategies")
    failed_resolutions: List[str] = Field(default_factory=list, description="Failed resolution attempts")
    
    # Metadata
    tags: List[str] = Field(default_factory=list, description="Error tags")
    confidence_score: float = Field(default=1.0, description="Pattern confidence score")


class ProgressReport(BaseModel):
    """Progress report for autonomous continue session."""
    
    session_id: str = Field(..., description="Session identifier")
    iteration: int = Field(..., description="Current iteration number")
    state: LoopState = Field(..., description="Current loop state")
    
    # Timing information
    start_time: datetime = Field(..., description="Session start time")
    current_time: datetime = Field(default_factory=datetime.now)
    elapsed_time: timedelta = Field(..., description="Elapsed time")
    estimated_remaining: Optional[timedelta] = Field(None, description="Estimated remaining time")
    
    # Progress metrics
    tasks_completed: int = Field(default=0, description="Number of tasks completed")
    tasks_failed: int = Field(default=0, description="Number of tasks failed")
    errors_encountered: int = Field(default=0, description="Total errors encountered")
    errors_resolved: int = Field(default=0, description="Errors successfully resolved")
    
    # Current task information
    current_task: str = Field(..., description="Current task description")
    current_perspective: ContextPerspective = Field(..., description="Current context perspective")
    
    # File changes
    files_modified: List[str] = Field(default_factory=list, description="Files modified in this session")
    files_created: List[str] = Field(default_factory=list, description="Files created in this session")
    files_deleted: List[str] = Field(default_factory=list, description="Files deleted in this session")
    
    # Performance metrics
    average_iteration_time: float = Field(default=0.0, description="Average time per iteration")
    success_rate: float = Field(default=0.0, description="Success rate percentage")
    
    # Resource usage
    memory_usage: Optional[float] = Field(None, description="Memory usage percentage")
    cpu_usage: Optional[float] = Field(None, description="CPU usage percentage")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            timedelta: lambda v: v.total_seconds()
        }


class IterationResult(BaseModel):
    """Result of a single iteration."""
    
    iteration_id: str = Field(..., description="Unique iteration identifier")
    iteration_number: int = Field(..., description="Iteration number")
    task_description: str = Field(..., description="Task description")
    
    # Execution results
    success: bool = Field(..., description="Whether iteration was successful")
    output: str = Field(..., description="Iteration output")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    
    # Timing
    start_time: datetime = Field(..., description="Iteration start time")
    end_time: datetime = Field(..., description="Iteration end time")
    duration: timedelta = Field(..., description="Iteration duration")
    
    # Changes made
    files_changed: List[str] = Field(default_factory=list, description="Files changed")
    git_commits: List[str] = Field(default_factory=list, description="Git commits made")
    
    # Context information
    perspective_used: ContextPerspective = Field(..., description="Context perspective used")
    tools_used: List[str] = Field(default_factory=list, description="Tools used in iteration")
    
    # Learning data
    lessons_learned: List[str] = Field(default_factory=list, description="Lessons learned")
    improvement_suggestions: List[str] = Field(default_factory=list, description="Improvement suggestions")


class ContinueSession(BaseModel):
    """Represents an autonomous continue session."""
    
    session_id: str = Field(..., description="Unique session identifier")
    config: ContinueConfig = Field(..., description="Session configuration")
    state: LoopState = Field(default=LoopState.IDLE, description="Current session state")
    
    # Session metadata
    created_time: datetime = Field(default_factory=datetime.now)
    started_time: Optional[datetime] = Field(None, description="When session started")
    completed_time: Optional[datetime] = Field(None, description="When session completed")
    
    # Progress tracking
    current_iteration: int = Field(default=0, description="Current iteration number")
    total_iterations: int = Field(default=0, description="Total iterations completed")
    
    # Results
    iterations: List[IterationResult] = Field(default_factory=list, description="Iteration results")
    error_patterns: List[ErrorPattern] = Field(default_factory=list, description="Detected error patterns")
    
    # Current state
    current_task: str = Field(..., description="Current task description")
    current_perspective: ContextPerspective = Field(default=ContextPerspective.ARCHITECTURE)
    
    # Statistics
    success_count: int = Field(default=0, description="Successful iterations")
    error_count: int = Field(default=0, description="Failed iterations")
    consecutive_errors: int = Field(default=0, description="Consecutive errors")
    
    # Session data
    session_data: Dict[str, Any] = Field(default_factory=dict, description="Session-specific data")
    
    def add_iteration_result(self, result: IterationResult):
        """Add an iteration result to the session."""
        self.iterations.append(result)
        self.total_iterations += 1
        
        if result.success:
            self.success_count += 1
            self.consecutive_errors = 0
        else:
            self.error_count += 1
            self.consecutive_errors += 1
    
    def get_success_rate(self) -> float:
        """Calculate success rate."""
        if self.total_iterations == 0:
            return 0.0
        return (self.success_count / self.total_iterations) * 100
    
    def get_average_iteration_time(self) -> float:
        """Calculate average iteration time in seconds."""
        if not self.iterations:
            return 0.0
        
        total_time = sum(iteration.duration.total_seconds() for iteration in self.iterations)
        return total_time / len(self.iterations)
    
    def should_trigger_circuit_breaker(self, threshold: int = 5) -> bool:
        """Check if circuit breaker should be triggered."""
        return self.consecutive_errors >= threshold
    
    def get_recent_errors(self, count: int = 5) -> List[IterationResult]:
        """Get recent error iterations."""
        error_iterations = [iteration for iteration in self.iterations if not iteration.success]
        return error_iterations[-count:] if error_iterations else []
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            timedelta: lambda v: v.total_seconds()
        }


class CircuitBreakerState(BaseModel):
    """Circuit breaker state for error handling."""
    
    is_open: bool = Field(default=False, description="Whether circuit breaker is open")
    failure_count: int = Field(default=0, description="Current failure count")
    last_failure_time: Optional[datetime] = Field(None, description="Last failure time")
    next_attempt_time: Optional[datetime] = Field(None, description="Next attempt time")
    
    # Configuration
    failure_threshold: int = Field(default=5, description="Failure threshold")
    timeout_duration: timedelta = Field(default=timedelta(minutes=5), description="Timeout duration")
    
    def record_success(self):
        """Record a successful operation."""
        self.failure_count = 0
        self.is_open = False
        self.last_failure_time = None
        self.next_attempt_time = None
    
    def record_failure(self):
        """Record a failed operation."""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.failure_count >= self.failure_threshold:
            self.is_open = True
            self.next_attempt_time = datetime.now() + self.timeout_duration
    
    def can_attempt(self) -> bool:
        """Check if an attempt can be made."""
        if not self.is_open:
            return True
        
        if self.next_attempt_time and datetime.now() >= self.next_attempt_time:
            # Reset to half-open state
            self.is_open = False
            return True
        
        return False
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            timedelta: lambda v: v.total_seconds()
        }
