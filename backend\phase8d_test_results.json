{"timestamp": "2025-06-11T11:41:56.578720", "phase": "8D - AI Planning System", "summary": {"total_tests": 8, "passed_tests": 8, "success_rate": 100.0, "duration": 342.2331464290619, "overall_status": "PASS"}, "detailed_results": {"planning_models": {"status": "success", "task_created": true, "risk_created": true, "resource_created": true, "plan_created": true, "total_hours": 4.0, "ready_tasks_count": 1, "model_validation": "All models created and validated successfully"}, "primary_planning_agent": {"status": "success", "agent_name": "PrimaryPlanner", "model_name": "deepseek/deepseek-r1-0528:free", "plan_created": true, "plan_title": "Test Feature Implementation", "total_tasks": 2, "total_risks": 1, "total_resources": 1, "estimated_hours": 20.0, "planning_duration": 60.053844928741455, "has_objectives": true, "has_success_criteria": true}, "validation_agent": {"status": "success", "agent_name": "ValidationAgent", "model_name": "google/gemini-2.0-flash-exp:free", "validation_created": true, "overall_score": 7.0, "feasibility_score": 7.0, "completeness_score": 6.0, "strengths_count": 2, "improvements_count": 2, "identified_risks_count": 0, "timeline_realistic": true, "validation_duration": 11.72673225402832}, "planning_engine": {"status": "success", "initially_enabled": true, "stats": {"planning_enabled": true, "total_sessions": 0, "total_plans": 0, "total_requests": 0, "total_validations": 0, "average_scores": {"overall": 0.0, "feasibility": 0.0, "completeness": 0.0}, "primary_planner": "PrimaryPlanner", "validation_agent": "ValidationAgent"}, "response_created": true, "plan_validated": true, "confidence_score": 0.725, "completeness_score": 0.6, "feasibility_score": 0.7, "planning_duration": 61.950248, "total_duration": 61.95065665245056, "sessions_count": 1, "recommendations_count": 4, "alternative_approaches_count": 0}, "planning_tools": {"status": "success", "status_check": true, "plan_creation": true, "feature_creation": true, "plans_listing": true, "created_plan_id": "0167e0ce-5694-4953-b3fe-5c2d63077455", "feature_plan_id": "19f4e3b7-fa44-4523-864f-7ebc856c0211", "total_plans": 3, "tools_functional": true}, "api_endpoints": {"status": "success", "health_check": true, "status_check": true, "plan_creation": true, "health_data": {"status": "healthy", "enabled": true, "agents_available": true, "active_sessions": 0}, "status_data": {"status": "operational", "enabled": true, "stats": {"planning_enabled": true, "total_sessions": 0, "total_plans": 0, "total_requests": 0, "total_validations": 0, "average_scores": {"overall": 0.0, "feasibility": 0.0, "completeness": 0.0}, "primary_planner": "PrimaryPlanner", "validation_agent": "ValidationAgent"}, "agents": {"primary_planner": "PrimaryPlanner", "validation_agent": "ValidationAgent"}}, "create_data": {"status": "error", "error": "attempted relative import with no known parent package"}, "api_functional": true}, "tool_registry_integration": {"status": "success", "total_coding_tools": 39, "total_tools": 47, "planning_tools_registered": 7, "expected_planning_tools": 7, "all_planning_tools_registered": true, "registered_planning_tools": ["create_development_plan", "validate_development_plan", "get_planning_status", "toggle_ai_planning", "list_development_plans", "get_plan_details", "create_feature_plan"], "registry_functional": true}, "frontend_integration_readiness": {"status": "success", "toggle_functionality": {"original_state": true, "can_disable": true, "can_enable": true, "toggle_working": true}, "session_management": {"can_list_sessions": true, "sessions_count": 3, "session_tracking_ready": true}, "statistics_available": {"stats_accessible": true, "planning_enabled": true, "total_sessions": 3, "total_plans": 3, "agents_info": {"primary_planner": "PrimaryPlanner", "validation_agent": "ValidationAgent"}}, "api_endpoints_ready": true, "real_time_updates_ready": true, "frontend_ready": true}}}