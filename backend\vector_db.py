# backend/qdrant_client.py
import logging
import uuid
from typing import List, Dict, Any, Optional
from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.exceptions import ResponseHandlingException

try:
    from .config import settings
except ImportError:
    # For direct execution
    from config import settings

logger = logging.getLogger(__name__)


class QdrantVectorClient:
    """
    Qdrant vector database client for storing and searching embeddings.
    Handles collection management and vector operations.
    """
    
    def __init__(self):
        self.url = settings.qdrant_url
        self.embedding_dim = settings.embedding_dim
        self.collection_name = settings.qdrant_collection
        
        # Initialize client
        self.client = QdrantClient(url=self.url)
        
        logger.info(f"Initialized Qdrant client: url={self.url}, collection={self.collection_name}")
    
    async def ensure_collection_exists(self) -> bool:
        """
        Ensure the embeddings collection exists, create if necessary.
        
        Returns:
            True if collection exists or was created successfully
        """
        try:
            # Check if collection exists
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name in collection_names:
                logger.info(f"Collection {self.collection_name} already exists")
                return True
            
            # Create collection
            logger.info(f"Creating collection {self.collection_name} with dimension {self.embedding_dim}")
            
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=models.VectorParams(
                    size=self.embedding_dim,
                    distance=models.Distance.COSINE
                )
            )
            
            logger.info(f"Successfully created collection {self.collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to ensure collection exists: {e}")
            return False
    
    async def upsert_embeddings(
        self,
        embeddings: List[List[float]],
        metadata: List[Dict[str, Any]],
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """
        Upsert embeddings with metadata into the collection.
        
        Args:
            embeddings: List of embedding vectors
            metadata: List of metadata dictionaries for each embedding
            ids: Optional list of IDs (will generate UUIDs if not provided)
            
        Returns:
            List of point IDs that were upserted
            
        Raises:
            Exception: If upsert operation fails
        """
        try:
            # Ensure collection exists
            if not await self.ensure_collection_exists():
                raise Exception("Failed to ensure collection exists")
            
            # Validate inputs
            if len(embeddings) != len(metadata):
                raise ValueError("Number of embeddings must match number of metadata entries")
            
            # Generate IDs if not provided
            if ids is None:
                ids = [str(uuid.uuid4()) for _ in embeddings]
            elif len(ids) != len(embeddings):
                raise ValueError("Number of IDs must match number of embeddings")
            
            # Validate embedding dimensions
            for i, embedding in enumerate(embeddings):
                if len(embedding) != self.embedding_dim:
                    raise ValueError(
                        f"Embedding {i} has dimension {len(embedding)}, "
                        f"expected {self.embedding_dim}"
                    )
            
            # Prepare points
            points = []
            for i, (embedding, meta) in enumerate(zip(embeddings, metadata)):
                point = models.PointStruct(
                    id=ids[i],
                    vector=embedding,
                    payload=meta
                )
                points.append(point)
            
            logger.info(f"Upserting {len(points)} points to collection {self.collection_name}")
            
            # Upsert points
            operation_info = self.client.upsert(
                collection_name=self.collection_name,
                points=points
            )
            
            logger.info(f"Successfully upserted {len(points)} points")
            return ids
            
        except Exception as e:
            logger.error(f"Failed to upsert embeddings: {e}")
            raise Exception(f"Upsert operation failed: {e}")
    
    async def search_similar(
        self,
        query_embedding: List[float],
        top_k: int = 10,
        score_threshold: float = 0.0,
        filter_conditions: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar embeddings in the collection.
        
        Args:
            query_embedding: Query vector to search for
            top_k: Number of top results to return
            score_threshold: Minimum similarity score threshold
            filter_conditions: Optional filter conditions for metadata
            
        Returns:
            List of search results with scores and metadata
        """
        try:
            # Ensure collection exists
            if not await self.ensure_collection_exists():
                logger.warning("Collection does not exist, returning empty results")
                return []
            
            # Validate query embedding dimension
            if len(query_embedding) != self.embedding_dim:
                raise ValueError(
                    f"Query embedding has dimension {len(query_embedding)}, "
                    f"expected {self.embedding_dim}"
                )
            
            logger.debug(f"Searching for {top_k} similar vectors with threshold {score_threshold}")
            
            # Prepare search request
            search_request = models.SearchRequest(
                vector=query_embedding,
                limit=top_k,
                score_threshold=score_threshold,
                with_payload=True,
                with_vector=False  # Don't return vectors to save bandwidth
            )
            
            # Add filter if provided
            if filter_conditions:
                search_request.filter = models.Filter(
                    must=[
                        models.FieldCondition(
                            key=key,
                            match=models.MatchValue(value=value)
                        )
                        for key, value in filter_conditions.items()
                    ]
                )
            
            # Perform search
            search_results = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                limit=top_k,
                score_threshold=score_threshold,
                with_payload=True
            )
            
            # Format results
            results = []
            for result in search_results:
                results.append({
                    "id": result.id,
                    "score": result.score,
                    "metadata": result.payload
                })
            
            logger.info(f"Found {len(results)} similar vectors")
            return results
            
        except Exception as e:
            logger.error(f"Failed to search similar vectors: {e}")
            # Return empty list instead of raising to allow graceful degradation
            return []
    
    def delete_points(self, ids: List[str]) -> bool:
        """
        Delete points from the collection by IDs.
        
        Args:
            ids: List of point IDs to delete
            
        Returns:
            True if deletion was successful
        """
        try:
            if not ids:
                return True
            
            logger.info(f"Deleting {len(ids)} points from collection {self.collection_name}")
            
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=models.PointIdsList(
                    points=ids
                )
            )
            
            logger.info(f"Successfully deleted {len(ids)} points")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete points: {e}")
            return False
    
    def get_collection_info(self) -> Dict[str, Any]:
        """
        Get information about the collection.
        
        Returns:
            Collection information dictionary
        """
        try:
            collection_info = self.client.get_collection(self.collection_name)
            
            return {
                "name": collection_info.config.params.vectors.size,
                "vectors_count": collection_info.vectors_count,
                "indexed_vectors_count": collection_info.indexed_vectors_count,
                "points_count": collection_info.points_count,
                "segments_count": collection_info.segments_count,
                "status": collection_info.status
            }
            
        except Exception as e:
            logger.error(f"Failed to get collection info: {e}")
            return {"error": str(e)}
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Check if Qdrant service is accessible and collection is ready.
        
        Returns:
            Health status dictionary
        """
        try:
            # Test connection by getting collections
            collections = self.client.get_collections()
            
            # Check if our collection exists
            collection_exists = await self.ensure_collection_exists()
            
            # Get collection info if it exists
            collection_info = {}
            if collection_exists:
                collection_info = self.get_collection_info()
            
            return {
                "status": "healthy" if collection_exists else "degraded",
                "url": self.url,
                "collection_name": self.collection_name,
                "collection_exists": collection_exists,
                "collection_info": collection_info,
                "total_collections": len(collections.collections)
            }
            
        except Exception as e:
            logger.error(f"Qdrant health check failed: {e}")
            return {
                "status": "unhealthy",
                "url": self.url,
                "error": str(e)
            }


# Global client instance
qdrant_client = QdrantVectorClient()

# Alias for backward compatibility
VectorDB = QdrantVectorClient
