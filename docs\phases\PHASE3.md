# 🚀 **Phase 3 Implementation Instructions for AI Coder Agent**

## 📋 **Current Status**
Phase 2 in @IMPLEMENTATION_PLAN.md is **COMPLETE** ✅. All external services are integrated:
- ✅ OpenRouter LLM Client (BGE-M3 embeddings, 1024 dimensions)
- ✅ Ollama Embedding Client (BGE-M3 model working perfectly)
- ✅ Qdrant Vector Database Client (healthy, using `code_embeddings_bge_m3` collection)
- ✅ Context Management System (token counting, prompt building)
- ✅ All Docker containers healthy and communicating
- ✅ Full integration workflow tested and working

## 🎯 **Phase 3 Objective**
Implement **File Operations & Git Integration** as defined in `IMPLEMENTATION_PLAN.md`:
- File System Operations (read, write, create, delete)
- Git Integration (status, diff, commit, branch management)
- Code Analysis & Indexing
- File Change Tracking
- Repository Context Management

## 🔧 **Prerequisites Check**
Before starting, verify Phase 2 completion:
```bash
# 1. Ensure all containers are running
docker-compose ps
# Expected: All containers showing as "healthy"

# 2. Run Phase 2 completion test
python test_phase2_complete.py
# Expected: "6/7 tests passed" (Module Imports failure is expected outside Docker)

# 3. Test BGE-M3 embeddings are working
curl -X POST http://localhost:8000/api/test/embeddings
# Expected: 1024-dimensional embeddings with bge-m3 model
```

## 📝 **Phase 3 Implementation Tasks**

### **Task 3.1: File System Operations**

1. **Create `backend/app/agent/file_operations.py`**:
```python
# Required features from IMPLEMENTATION_PLAN.md:
# - read_file() with encoding detection and error handling
# - write_file() with backup creation and atomic writes
# - create_file() with directory creation and conflict resolution
# - delete_file() with safety checks and trash/backup options
# - list_directory() with filtering and recursive options
# - get_file_info() with metadata (size, modified, permissions)
# - watch_file_changes() for real-time monitoring
```

2. **Implementation Requirements**:
   - Use `pathlib.Path` for cross-platform compatibility
   - Implement file encoding detection (chardet library)
   - Add comprehensive error handling for permissions, disk space
   - Create atomic file operations to prevent corruption
   - Support workspace-relative and absolute paths
   - Add file type detection and validation
   - Implement file backup/versioning for safety

3. **Security Considerations**:
   - Path traversal protection (prevent ../../../etc/passwd)
   - File size limits to prevent memory exhaustion
   - Allowed file extensions whitelist
   - Workspace boundary enforcement

### **Task 3.2: Git Integration**

1. **Create `backend/app/agent/git_operations.py`**:
```python
# Required features from IMPLEMENTATION_PLAN.md:
# - get_git_status() with staged/unstaged/untracked files
# - get_git_diff() with file-specific and commit-range diffs
# - git_commit() with message validation and staging
# - git_branch_operations() (create, switch, list, delete)
# - get_commit_history() with filtering and pagination
# - get_file_blame() for line-by-line authorship
# - git_stash_operations() for temporary changes
```

2. **Implementation Requirements**:
   - Use `GitPython` library (add to requirements.txt)
   - Handle non-git repositories gracefully
   - Implement git repository detection and initialization
   - Add branch safety checks (prevent force operations)
   - Support both local and remote operations
   - Implement commit message templates and validation
   - Add merge conflict detection and resolution helpers

3. **Git Safety Features**:
   - Prevent destructive operations without confirmation
   - Backup before major operations (rebase, reset)
   - Branch protection for main/master branches
   - Commit message quality validation

### **Task 3.3: Code Analysis & Indexing**

1. **Create `backend/app/agent/code_analyzer.py`**:
```python
# Required features from IMPLEMENTATION_PLAN.md:
# - parse_code_structure() for functions, classes, imports
# - extract_dependencies() from various file types
# - analyze_code_complexity() with metrics
# - detect_code_patterns() and anti-patterns
# - generate_code_embeddings() for semantic search
# - create_code_index() for fast symbol lookup
```

2. **Implementation Requirements**:
   - Use `tree-sitter` for robust code parsing (multiple languages)
   - Integrate with existing BGE-M3 embedding pipeline
   - Store code embeddings in Qdrant with rich metadata
   - Support Python, JavaScript, TypeScript, Java, C++, Go, Rust
   - Extract docstrings, comments, and inline documentation
   - Implement incremental indexing for performance

3. **Integration with Existing Systems**:
   - Use existing Qdrant client (`code_embeddings_bge_m3` collection)
   - Leverage BGE-M3 embeddings (1024 dimensions)
   - Integrate with file operations for change detection
   - Connect with git operations for diff-based updates

### **Task 3.4: File Change Tracking**

1. **Create `backend/app/agent/change_tracker.py`**:
```python
# Required features from IMPLEMENTATION_PLAN.md:
# - track_file_changes() with filesystem watching
# - detect_code_modifications() with semantic analysis
# - update_embeddings_on_change() for incremental updates
# - maintain_change_history() with timestamps and metadata
# - sync_with_git_changes() for version control integration
```

2. **Implementation Requirements**:
   - Use `watchdog` library for filesystem monitoring
   - Implement debouncing to handle rapid file changes
   - Track both content and metadata changes
   - Maintain change history with rollback capabilities
   - Integrate with git operations for commit-based tracking
   - Support batch processing for large changesets

### **Task 3.5: Repository Context Management**

1. **Create `backend/app/agent/repo_context.py`**:
```python
# Required features from IMPLEMENTATION_PLAN.md:
# - build_repository_context() with project structure analysis
# - extract_project_metadata() (dependencies, configs, docs)
# - generate_codebase_summary() for LLM context
# - maintain_symbol_index() for cross-references
# - track_project_dependencies() and their relationships
```

2. **Implementation Requirements**:
   - Analyze project structure (package.json, requirements.txt, etc.)
   - Extract and parse configuration files
   - Build dependency graphs and import relationships
   - Generate project summaries for LLM context
   - Maintain searchable symbol and reference indexes
   - Support multiple project types and languages

## 🧪 **Testing Strategy**

### **Create Phase 3 Test Suite**

1. **Create `test_phase3_complete.py`**:
```python
#!/usr/bin/env python3
"""
Comprehensive Phase 3 Completion Test for AI Coder Agent
Tests all Phase 3 requirements from IMPLEMENTATION_PLAN.md
"""

def test_file_operations():
    """Test file system operations."""
    # Test file reading with various encodings
    # Test atomic file writing with backup
    # Test directory operations and permissions
    # Test file watching and change detection
    # Test security boundaries and path validation

def test_git_operations():
    """Test git integration functionality."""
    # Test git status and diff operations
    # Test branch creation and switching
    # Test commit operations with validation
    # Test history and blame functionality
    # Test stash operations and conflict detection

def test_code_analysis():
    """Test code analysis and indexing."""
    # Test code structure parsing for multiple languages
    # Test dependency extraction and analysis
    # Test code complexity metrics
    # Test embedding generation and storage
    # Test incremental indexing performance

def test_change_tracking():
    """Test file change tracking system."""
    # Test filesystem monitoring and debouncing
    # Test change history maintenance
    # Test git integration for change tracking
    # Test batch processing of changes
    # Test rollback and recovery operations

def test_repo_context():
    """Test repository context management."""
    # Test project structure analysis
    # Test metadata extraction from configs
    # Test codebase summary generation
    # Test symbol indexing and cross-references
    # Test dependency graph construction

def test_integration():
    """Test integration between all Phase 3 components."""
    # Test full workflow: file change → analysis → indexing → context
    # Test git operations with code analysis
    # Test embedding updates on file changes
    # Test repository-wide operations
```

2. **Add API endpoints for testing**:
```python
# Add to backend/main.py:
@app.post("/api/test/file-ops")
async def test_file_operations_endpoint():
    """Test file operations functionality."""

@app.post("/api/test/git-ops")
async def test_git_operations_endpoint():
    """Test git operations functionality."""

@app.post("/api/test/code-analysis")
async def test_code_analysis_endpoint():
    """Test code analysis and indexing."""

@app.post("/api/test/change-tracking")
async def test_change_tracking_endpoint():
    """Test file change tracking."""

@app.post("/api/test/repo-context")
async def test_repo_context_endpoint():
    """Test repository context management."""
```

## 🔄 **Development Workflow**

### **Step-by-Step Process**

1. **Setup Phase**:
```bash
# Ensure Phase 2 is complete
python test_phase2_complete.py
# Expected: 6/7 tests passed (Module Imports failure is expected)

# Check current directory structure
ls -la backend/app/agent/
# Should show context_manager.py from Phase 2

# Verify BGE-M3 embeddings are working
docker exec ai_coder_backend curl -s -X POST http://localhost:8000/api/test/embeddings
# Expected: 1024-dimensional embeddings with bge-m3 model
```

2. **Dependencies Phase**:
```bash
# Add new dependencies to backend/requirements.txt:
# GitPython>=3.1.40
# tree-sitter>=0.20.4
# tree-sitter-python>=0.20.4
# tree-sitter-javascript>=0.20.3
# watchdog>=3.0.0
# chardet>=5.2.0

# Rebuild backend container with new dependencies
docker-compose build backend
docker-compose up -d backend
```

3. **Implementation Phase**:
```bash
# For each component:
# 1. Create the Python file in backend/app/agent/
# 2. Implement required methods with comprehensive error handling
# 3. Add imports to backend/main.py
# 4. Test individually with API endpoints

# Example for file operations:
# Create backend/app/agent/file_operations.py
# Test: curl -X POST http://localhost:8000/api/test/file-ops
```

4. **Integration Phase**:
```bash
# Test each component integration
curl -X POST http://localhost:8000/api/test/file-ops
curl -X POST http://localhost:8000/api/test/git-ops
curl -X POST http://localhost:8000/api/test/code-analysis
curl -X POST http://localhost:8000/api/test/change-tracking
curl -X POST http://localhost:8000/api/test/repo-context

# Monitor Docker container health
docker ps
# All containers should remain healthy
```

5. **Validation Phase**:
```bash
# Run comprehensive Phase 3 test
python test_phase3_complete.py

# Expected output: "🎉 Phase 3 is COMPLETE! All requirements satisfied."
```

## 📁 **Expected File Structure After Phase 3**

```
backend/
├── app/
│   └── agent/
│       ├── context_manager.py         # EXISTING (Phase 2)
│       ├── file_operations.py         # NEW
│       ├── git_operations.py          # NEW
│       ├── code_analyzer.py           # NEW
│       ├── change_tracker.py          # NEW
│       └── repo_context.py            # NEW
├── llm_client.py                      # EXISTING (Phase 2)
├── ollama_client.py                   # EXISTING (Phase 2)
├── vector_db.py                       # EXISTING (Phase 2)
├── main.py                            # UPDATED with new endpoints
├── config.py                          # UPDATED with new settings
├── requirements.txt                   # UPDATED with new dependencies
└── sessions.py                        # EXISTING

# Root directory:
├── test_phase3_complete.py            # NEW
├── test_phase2_complete.py            # EXISTING
└── IMPLEMENTATION_PLAN.md             # UPDATED with Phase 3 checkmarks
```

## ⚙️ **Configuration Updates**

### **Add to `backend/config.py`**:
```python
# File Operations Settings
max_file_size: int = Field(10_000_000, description="Maximum file size in bytes (10MB)")
allowed_extensions: List[str] = Field([".py", ".js", ".ts", ".java", ".cpp", ".go", ".rs", ".md", ".txt", ".json", ".yaml", ".yml"], description="Allowed file extensions")
workspace_root: str = Field("/workspace", description="Root directory for file operations")
backup_enabled: bool = Field(True, description="Enable file backups before modifications")

# Git Settings
git_safe_mode: bool = Field(True, description="Enable git safety checks")
protected_branches: List[str] = Field(["main", "master", "production"], description="Protected branch names")
max_commit_message_length: int = Field(72, description="Maximum commit message length")

# Code Analysis Settings
supported_languages: List[str] = Field(["python", "javascript", "typescript", "java", "cpp", "go", "rust"], description="Supported programming languages")
max_analysis_file_size: int = Field(1_000_000, description="Maximum file size for code analysis (1MB)")
indexing_batch_size: int = Field(100, description="Batch size for code indexing")

# Change Tracking Settings
watch_debounce_seconds: float = Field(0.5, description="Debounce time for file change events")
change_history_retention_days: int = Field(30, description="Days to retain change history")
```

## 🚨 **Important Implementation Notes**

### **Docker Integration**
1. **File System Access**: All file operations must work within Docker containers
2. **Volume Mounting**: Ensure workspace is properly mounted for file access
3. **Permissions**: Handle Docker user permissions for file operations
4. **Path Resolution**: Use container-relative paths, not host paths

### **Security Considerations**
1. **Path Traversal**: Implement strict path validation to prevent directory traversal attacks
2. **File Size Limits**: Enforce maximum file sizes to prevent memory exhaustion
3. **Extension Filtering**: Only allow safe file extensions for operations
4. **Git Safety**: Prevent destructive git operations without explicit confirmation
5. **Workspace Boundaries**: Ensure all operations stay within designated workspace

### **Performance Optimization**
1. **Incremental Processing**: Update embeddings only for changed files
2. **Batch Operations**: Process multiple files efficiently
3. **Caching**: Cache frequently accessed file metadata and git information
4. **Debouncing**: Prevent excessive processing during rapid file changes
5. **Background Processing**: Use async operations for heavy tasks

### **Error Handling**
1. **Graceful Degradation**: Continue operation even if some components fail
2. **Comprehensive Logging**: Log all operations for debugging and audit
3. **Recovery Mechanisms**: Implement rollback for failed operations
4. **User Feedback**: Provide clear error messages and suggestions
5. **Monitoring**: Track system health and performance metrics

### **Integration with Existing Systems**
1. **BGE-M3 Embeddings**: Use existing 1024-dimensional embedding pipeline
2. **Qdrant Collections**: Leverage `code_embeddings_bge_m3` collection
3. **Context Management**: Integrate with existing token counting and context building
4. **LLM Integration**: Provide rich context for OpenRouter API calls
5. **Backward Compatibility**: Ensure Phase 2 functionality remains intact

## ✅ **Success Criteria**

Phase 3 is complete when:
- [ ] All 5 component classes are implemented and functional
- [ ] File operations work securely within Docker environment
- [ ] Git integration handles all common operations safely
- [ ] Code analysis generates and stores embeddings correctly
- [ ] Change tracking monitors and processes file modifications
- [ ] Repository context provides comprehensive project understanding
- [ ] Integration tests pass with 100% success rate
- [ ] `test_phase3_complete.py` shows all tests passing
- [ ] All API endpoints respond correctly
- [ ] Docker containers remain stable under load
- [ ] No breaking changes to Phase 1 or Phase 2 functionality
- [ ] BGE-M3 embeddings continue working with new code indexing

## 🎯 **Final Validation Commands**

```bash
# 1. Verify all containers are healthy
docker ps
# Expected: All containers showing "healthy" status

# 2. Test Phase 2 functionality still works
python test_phase2_complete.py
# Expected: 6/7 tests passed (Module Imports failure is expected)

# 3. Test new Phase 3 functionality
python test_phase3_complete.py
# Expected: All tests passing

# 4. Test full integration
curl -X POST http://localhost:8000/api/test/file-ops
curl -X POST http://localhost:8000/api/test/git-ops
curl -X POST http://localhost:8000/api/test/code-analysis
# Expected: All endpoints returning success responses

# 5. Final validation
python test_phase3_complete.py && echo "✅ PHASE 3 COMPLETE - READY FOR PHASE 4"
```

## 🔗 **Dependencies to Add**

Update `backend/requirements.txt` with:
```
# Phase 3 Dependencies
GitPython>=3.1.40          # Git operations
tree-sitter>=0.20.4        # Code parsing
tree-sitter-python>=0.20.4 # Python language support
tree-sitter-javascript>=0.20.3  # JavaScript language support
watchdog>=3.0.0            # File system monitoring
chardet>=5.2.0             # Character encoding detection
```

## 🎯 **Key Integration Points**

1. **With Phase 2 BGE-M3**: Use existing embedding pipeline for code analysis
2. **With Qdrant**: Store code embeddings in `code_embeddings_bge_m3` collection
3. **With Context Manager**: Provide file and git context for LLM conversations
4. **With Docker**: Ensure all operations work within containerized environment and the new faster method
5. **With Existing APIs**: Maintain compatibility with current endpoint structure

---

**Next AI: Follow these instructions step-by-step to implement Phase 3. Start with Task 3.1 (File Operations) and work through each component systematically. Pay special attention to Docker integration, security considerations, and maintaining compatibility with the existing BGE-M3 embedding pipeline. Test thoroughly at each step before proceeding.**
