{"version": "1.0", "project": {"name": "AI Coder Agent", "type": "python", "description": "AI-powered coding assistant with advanced code operations"}, "linting": {"python": {"enabled": true, "command": "flake8", "args": ["--max-line-length=88", "--ignore=E203,W503"], "timeout": 60}, "javascript": {"enabled": true, "command": "eslint", "args": ["--ext", ".js,.jsx,.ts,.tsx"], "timeout": 60}}, "testing": {"python": {"enabled": true, "command": "pytest", "args": ["-v", "--tb=short"], "timeout": 180}, "javascript": {"enabled": true, "command": "npm test", "args": ["--", "--watchAll=false"], "timeout": 180}}, "git": {"auto_commit": true, "commit_message_template": "AI Agent: {task_description}", "protected_branches": ["main", "master", "production"]}, "task_runner": {"max_iterations": 3, "timeout_seconds": 300, "auto_fix_enabled": true, "parallel_execution": false}}