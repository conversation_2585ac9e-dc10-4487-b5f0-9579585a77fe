"""
Tool Registry for Pydantic AI Migration

This module provides a centralized registry for all tools, solving the circular
import issue by using the constructor pattern instead of decorators.

Based on Pydantic AI best practices from the official documentation.
"""

import logging
import time
from typing import List, Callable, Any, Optional
from functools import lru_cache

from pydantic_ai.tools import Tool

# Import performance monitoring
try:
    from ...core.monitoring import monitor_performance, metrics_collector
    MONITORING_AVAILABLE = True
except ImportError:
    MONITORING_AVAILABLE = False

# Import all tool functions as standalone functions
from .file_operations import (
    read_file,
    write_file,
    create_file,
    delete_file,
    list_directory
)

from .git_operations import (
    git_status,
    git_diff,
    git_commit,
    git_push,
    git_branch,
    git_log
)

from .code_analysis import (
    analyze_code,
    search_code,
    get_project_context,
    run_linting,
    run_tests
)

from .change_tracking import (
    track_changes,
    get_change_summary,
    monitor_file_changes,
    get_change_statistics,
    index_code_changes,
    stop_monitoring
)

from .vision_analysis import (
    analyze_screenshot,
    analyze_ui_component,
    compare_screenshots,
    generate_visual_report
)

from .advanced_vision_tools import (
    ai_analyze_image,
    detect_ui_elements,
    accessibility_audit,
    visual_regression_analysis
)

from .vision_integration import (
    request_vision_analysis,
    request_vision_comparison,
    capture_and_analyze_ui
)

from .visual_validation import (
    compare_screenshots_visual_diff,
    validate_ui_patterns,
    validate_accessibility_compliance
)

from .frontend_integration import (
    create_visual_feedback_session,
    setup_hot_reload_integration,
    create_design_specification,
    check_design_compliance
)

from .code_intelligence import (
    search_codebase,
    index_workspace,
    index_file,
    find_similar_code,
    analyze_file_relationships,
    get_code_intelligence_stats
)

from .autonomous_continue import (
    start_autonomous_session,
    pause_autonomous_session,
    resume_autonomous_session,
    stop_autonomous_session,
    get_autonomous_session_status,
    get_autonomous_system_statistics,
    suggest_context_perspective,
    analyze_error_patterns
)

from .planning_tools import (
    create_development_plan,
    validate_development_plan,
    get_planning_status,
    toggle_ai_planning,
    list_development_plans,
    get_plan_details,
    create_feature_plan
)

logger = logging.getLogger(__name__)


class ToolRegistry:
    """
    Centralized registry for all Pydantic AI tools.
    
    This class manages tool registration and provides tools to agents
    using the constructor pattern, avoiding circular imports.
    """
    
    def __init__(self):
        self._coding_tools: List[Tool] = []
        self._vision_tools: List[Tool] = []
        self._initialize_tools()
    
    def _initialize_tools(self):
        """Initialize all tools and categorize them by agent type."""
        logger.info("🔧 Initializing Pydantic AI tool registry...")
        
        # File operation tools (for coding agent)
        self._coding_tools.extend([
            Tool(read_file, takes_ctx=True),
            Tool(write_file, takes_ctx=True),
            Tool(create_file, takes_ctx=True),
            Tool(delete_file, takes_ctx=True),
            Tool(list_directory, takes_ctx=True),
        ])
        
        # Git operation tools (for coding agent)
        self._coding_tools.extend([
            Tool(git_status, takes_ctx=True),
            Tool(git_diff, takes_ctx=True),
            Tool(git_commit, takes_ctx=True),
            Tool(git_push, takes_ctx=True),
            Tool(git_branch, takes_ctx=True),
            Tool(git_log, takes_ctx=True),
        ])
        
        # Code analysis tools (for coding agent)
        self._coding_tools.extend([
            Tool(analyze_code, takes_ctx=True),
            Tool(search_code, takes_ctx=True),
            Tool(get_project_context, takes_ctx=True),
            Tool(run_linting, takes_ctx=True),
            Tool(run_tests, takes_ctx=True),
        ])

        # Change tracking tools (for coding agent)
        self._coding_tools.extend([
            Tool(track_changes, takes_ctx=True),
            Tool(get_change_summary, takes_ctx=True),
            Tool(monitor_file_changes, takes_ctx=True),
            Tool(get_change_statistics, takes_ctx=True),
            Tool(index_code_changes, takes_ctx=True),
            Tool(stop_monitoring, takes_ctx=True),
        ])

        # Vision integration tools (for coding agent) - Phase 8A
        self._coding_tools.extend([
            Tool(request_vision_analysis, takes_ctx=True),
            Tool(request_vision_comparison, takes_ctx=True),
            Tool(capture_and_analyze_ui, takes_ctx=True),
        ])

        # Visual validation tools (for coding agent) - Phase 8A Step 8A.2
        self._coding_tools.extend([
            Tool(compare_screenshots_visual_diff, takes_ctx=True),
            Tool(validate_ui_patterns, takes_ctx=True),
            Tool(validate_accessibility_compliance, takes_ctx=True),
        ])

        # Frontend integration tools (for coding agent) - Phase 8A Step 8A.3
        self._coding_tools.extend([
            Tool(create_visual_feedback_session, takes_ctx=True),
            Tool(setup_hot_reload_integration, takes_ctx=True),
            Tool(create_design_specification, takes_ctx=True),
            Tool(check_design_compliance, takes_ctx=True),
        ])

        # Code Intelligence tools (for coding agent) - Phase 8B
        # Temporarily disabled due to RunContext annotation issues
        # TODO: Fix RunContext[CodingDependencies] annotations
        # self._coding_tools.extend([
        #     Tool(search_codebase, takes_ctx=True),
        #     Tool(index_workspace, takes_ctx=True),
        #     Tool(index_file, takes_ctx=True),
        #     Tool(find_similar_code, takes_ctx=True),
        #     Tool(analyze_file_relationships, takes_ctx=True),
        #     Tool(get_code_intelligence_stats, takes_ctx=True),
        # ])

        # Autonomous Continue tools (for coding agent) - Phase 8C
        # Temporarily disabled due to RunContext annotation issues
        # TODO: Fix RunContext[CodingDependencies] annotations
        # self._coding_tools.extend([
        #     Tool(start_autonomous_session, takes_ctx=True),
        #     Tool(pause_autonomous_session, takes_ctx=True),
        #     Tool(resume_autonomous_session, takes_ctx=True),
        #     Tool(stop_autonomous_session, takes_ctx=True),
        #     Tool(get_autonomous_session_status, takes_ctx=True),
        #     Tool(get_autonomous_system_statistics, takes_ctx=True),
        #     Tool(suggest_context_perspective, takes_ctx=True),
        #     Tool(analyze_error_patterns, takes_ctx=True),
        # ])

        # AI Planning tools (for coding agent) - Phase 8D
        self._coding_tools.extend([
            Tool(create_development_plan, takes_ctx=False),
            Tool(validate_development_plan, takes_ctx=False),
            Tool(get_planning_status, takes_ctx=False),
            Tool(toggle_ai_planning, takes_ctx=False),
            Tool(list_development_plans, takes_ctx=False),
            Tool(get_plan_details, takes_ctx=False),
            Tool(create_feature_plan, takes_ctx=False),
        ])

        # Vision analysis tools (for vision agent)
        self._vision_tools.extend([
            Tool(analyze_screenshot, takes_ctx=True),
            Tool(analyze_ui_component, takes_ctx=True),
            Tool(compare_screenshots, takes_ctx=True),
            Tool(generate_visual_report, takes_ctx=True),
        ])

        # Advanced vision analysis tools (AI-powered)
        self._vision_tools.extend([
            Tool(ai_analyze_image, takes_ctx=True),
            Tool(detect_ui_elements, takes_ctx=True),
            Tool(accessibility_audit, takes_ctx=True),
            Tool(visual_regression_analysis, takes_ctx=True),
        ])

        logger.info(f"✅ Tool registry initialized: {len(self._coding_tools)} coding tools, {len(self._vision_tools)} vision tools")
        logger.info(f"📊 Tool breakdown: File ops (5), Git ops (6), Code analysis (5), Change tracking (6), Vision integration (3), Visual validation (3), Frontend integration (4), Code Intelligence (0-disabled), Autonomous Continue (0-disabled), AI Planning (7), Vision analysis (8)")
    
    def get_coding_tools(self) -> List[Tool]:
        """
        Get all tools for the coding agent.
        
        Returns:
            List of Tool objects for the coding agent
        """
        return self._coding_tools.copy()
    
    def get_vision_tools(self) -> List[Tool]:
        """
        Get all tools for the vision agent.
        
        Returns:
            List of Tool objects for the vision agent
        """
        return self._vision_tools.copy()
    
    def get_all_tools(self) -> List[Tool]:
        """
        Get all tools from all categories.
        
        Returns:
            List of all Tool objects
        """
        return self._coding_tools + self._vision_tools
    
    def get_tool_names(self) -> dict:
        """
        Get a summary of all registered tools by category.

        Returns:
            Dictionary with tool names by category
        """
        return {
            'coding_tools': [tool.function.__name__ for tool in self._coding_tools],
            'vision_tools': [tool.function.__name__ for tool in self._vision_tools],
            'total_tools': len(self._coding_tools) + len(self._vision_tools)
        }

    @lru_cache(maxsize=128)
    def get_tool_performance_stats(self, tool_name: str) -> dict:
        """
        Get performance statistics for a specific tool.

        Args:
            tool_name: Name of the tool to get stats for

        Returns:
            Dictionary with performance statistics
        """
        if MONITORING_AVAILABLE:
            return metrics_collector.get_operation_summary(f"tool.{tool_name}")
        else:
            return {'error': 'Monitoring not available'}

    def get_all_tool_performance(self) -> dict:
        """
        Get performance statistics for all tools.

        Returns:
            Dictionary with performance statistics for all tools
        """
        if not MONITORING_AVAILABLE:
            return {'error': 'Monitoring not available'}

        stats = {}
        all_tools = self._coding_tools + self._vision_tools

        for tool in all_tools:
            tool_name = tool.function.__name__
            stats[tool_name] = self.get_tool_performance_stats(tool_name)

        return stats

    def get_registry_health(self) -> dict:
        """
        Get health status of the tool registry.

        Returns:
            Dictionary with registry health information
        """
        health = {
            'status': 'healthy',
            'total_tools': len(self._coding_tools) + len(self._vision_tools),
            'coding_tools_count': len(self._coding_tools),
            'vision_tools_count': len(self._vision_tools),
            'timestamp': time.time()
        }

        if MONITORING_AVAILABLE:
            # Add performance metrics
            all_stats = self.get_all_tool_performance()

            # Calculate overall health metrics
            total_calls = sum(
                stats.get('total_calls', 0)
                for stats in all_stats.values()
                if isinstance(stats, dict) and 'total_calls' in stats
            )

            avg_success_rate = 0
            if all_stats:
                success_rates = [
                    stats.get('success_rate', 100)
                    for stats in all_stats.values()
                    if isinstance(stats, dict) and 'success_rate' in stats
                ]
                if success_rates:
                    avg_success_rate = sum(success_rates) / len(success_rates)

            health.update({
                'total_tool_calls': total_calls,
                'average_success_rate': avg_success_rate,
                'monitoring_enabled': True
            })
        else:
            health['monitoring_enabled'] = False

        return health


# Global tool registry instance
tool_registry = ToolRegistry()


def get_coding_tools() -> List[Tool]:
    """
    Convenience function to get coding tools.
    
    Returns:
        List of Tool objects for the coding agent
    """
    return tool_registry.get_coding_tools()


def get_vision_tools() -> List[Tool]:
    """
    Convenience function to get vision tools.
    
    Returns:
        List of Tool objects for the vision agent
    """
    return tool_registry.get_vision_tools()


def get_tool_summary() -> dict:
    """
    Get a summary of all registered tools.
    
    Returns:
        Dictionary with tool information
    """
    return tool_registry.get_tool_names()


# Log tool registration completion
logger.info("🎉 Pydantic AI tool registry module loaded successfully")
