#!/usr/bin/env python3
"""
Phase 3 Validation Script for AI Coder Agent
Tests all Phase 3 components: File Operations, Git Operations, Code Analysis, Change Tracking, Repository Context

Usage:
    python scripts/validate_phase3.py
"""

import asyncio
import sys
import os
import tempfile
import shutil
from pathlib import Path
import logging
import json
from datetime import datetime

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Phase3Validator:
    """Comprehensive Phase 3 validation suite."""
    
    def __init__(self):
        self.test_results = {}
        self.temp_dir = None
        self.test_repo_path = None
        
    async def setup_test_environment(self):
        """Set up temporary test environment."""
        logger.info("Setting up test environment...")
        
        # Create temporary directory
        self.temp_dir = Path(tempfile.mkdtemp(prefix="phase3_test_"))
        self.test_repo_path = self.temp_dir / "test_repo"
        self.test_repo_path.mkdir()
        
        # Initialize git repo
        import subprocess
        try:
            subprocess.run(["git", "init"], cwd=self.test_repo_path, check=True, capture_output=True)
            subprocess.run(["git", "config", "user.name", "Test User"], cwd=self.test_repo_path, check=True)
            subprocess.run(["git", "config", "user.email", "<EMAIL>"], cwd=self.test_repo_path, check=True)
            
            # Create initial test files
            (self.test_repo_path / "main.py").write_text("""
def hello_world():
    \"\"\"A simple hello world function.\"\"\"
    print("Hello, World!")
    return "Hello, World!"

class Calculator:
    \"\"\"A simple calculator class.\"\"\"
    
    def add(self, a, b):
        return a + b
    
    def multiply(self, a, b):
        return a * b

if __name__ == "__main__":
    hello_world()
    calc = Calculator()
    print(calc.add(2, 3))
""")
            
            (self.test_repo_path / "utils.js").write_text("""
// Utility functions
function formatDate(date) {
    return date.toISOString().split('T')[0];
}

function validateEmail(email) {
    const regex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
    return regex.test(email);
}

module.exports = {
    formatDate,
    validateEmail
};
""")
            
            (self.test_repo_path / "README.md").write_text("""
# Test Repository

This is a test repository for Phase 3 validation.

## Features
- Python code analysis
- JavaScript utilities
- Git operations testing
""")
            
            # Initial commit
            subprocess.run(["git", "add", "."], cwd=self.test_repo_path, check=True)
            subprocess.run(["git", "commit", "-m", "Initial commit"], cwd=self.test_repo_path, check=True)
            
            logger.info(f"Test environment created at: {self.test_repo_path}")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to set up git repo: {e}")
            raise
    
    async def test_file_operations(self):
        """Test file operations module."""
        logger.info("Testing file operations...")
        
        try:
            from app.agent.file_operations import file_operations
            
            test_file = self.test_repo_path / "test_file.txt"
            test_content = "This is a test file for Phase 3 validation."
            
            # Test file creation
            create_result = await file_operations.create_file(str(test_file), test_content)
            assert create_result["status"] == "success", f"File creation failed: {create_result}"
            
            # Test file reading
            read_result = await file_operations.read_file(str(test_file))
            assert read_result["status"] == "success", f"File reading failed: {read_result}"
            assert read_result["content"] == test_content, "File content mismatch"
            
            # Test file info
            info_result = await file_operations.get_file_info(str(test_file))
            assert info_result["status"] == "success", f"File info failed: {info_result}"
            
            # Test directory listing
            list_result = await file_operations.list_directory(str(self.test_repo_path))
            assert list_result["status"] == "success", f"Directory listing failed: {list_result}"
            assert len(list_result["files"]) >= 4, "Expected at least 4 files"
            
            # Test file modification
            new_content = test_content + "\nModified content."
            write_result = await file_operations.write_file(str(test_file), new_content)
            assert write_result["status"] == "success", f"File writing failed: {write_result}"
            
            # Test file deletion
            delete_result = await file_operations.delete_file(str(test_file), create_backup=False)
            assert delete_result["status"] == "success", f"File deletion failed: {delete_result}"
            
            self.test_results["file_operations"] = {
                "status": "success",
                "tests_passed": ["create", "read", "info", "list", "write", "delete"],
                "details": "All file operations working correctly"
            }
            
        except Exception as e:
            logger.error(f"File operations test failed: {e}")
            self.test_results["file_operations"] = {
                "status": "error",
                "error": str(e)
            }
    
    async def test_git_operations(self):
        """Test git operations module."""
        logger.info("Testing git operations...")
        
        try:
            from app.agent.git_operations import git_operations
            
            # Test git status
            status_result = await git_operations.get_git_status(str(self.test_repo_path))
            assert status_result["status"] == "success", f"Git status failed: {status_result}"
            
            # Create a new file for testing
            test_file = self.test_repo_path / "new_feature.py"
            test_file.write_text("# New feature implementation\nprint('New feature')")
            
            # Test git status with changes
            status_result = await git_operations.get_git_status(str(self.test_repo_path))
            assert status_result["status"] == "success", f"Git status with changes failed: {status_result}"
            assert len(status_result["untracked"]) > 0, "Should have untracked files"
            
            # Test git diff
            diff_result = await git_operations.get_git_diff(str(self.test_repo_path))
            assert diff_result["status"] == "success", f"Git diff failed: {diff_result}"
            
            # Test git commit
            commit_result = await git_operations.git_commit(
                str(self.test_repo_path), 
                "Add new feature", 
                add_all=True
            )
            assert commit_result["status"] == "success", f"Git commit failed: {commit_result}"
            
            # Test commit history
            history_result = await git_operations.get_commit_history(str(self.test_repo_path), max_count=5)
            assert history_result["status"] == "success", f"Git history failed: {history_result}"
            assert len(history_result["commits"]) >= 2, "Should have at least 2 commits"
            
            # Test branch operations
            branch_result = await git_operations.git_branch_operations(
                str(self.test_repo_path), "list"
            )
            assert branch_result["status"] == "success", f"Git branch list failed: {branch_result}"
            
            self.test_results["git_operations"] = {
                "status": "success",
                "tests_passed": ["status", "diff", "commit", "history", "branch"],
                "details": "All git operations working correctly"
            }
            
        except Exception as e:
            logger.error(f"Git operations test failed: {e}")
            self.test_results["git_operations"] = {
                "status": "error",
                "error": str(e)
            }
    
    async def test_code_analyzer(self):
        """Test code analyzer module."""
        logger.info("Testing code analyzer...")
        
        try:
            from app.agent.code_analyzer import code_analyzer
            
            python_file = self.test_repo_path / "main.py"
            
            # Test code structure parsing
            parse_result = await code_analyzer.parse_code_structure(str(python_file))
            assert parse_result["status"] == "success", f"Code parsing failed: {parse_result}"
            assert len(parse_result["functions"]) >= 1, "Should find at least 1 function"
            assert len(parse_result["classes"]) >= 1, "Should find at least 1 class"
            
            # Test complexity analysis
            complexity_result = await code_analyzer.analyze_code_complexity(str(python_file))
            assert complexity_result["status"] == "success", f"Complexity analysis failed: {complexity_result}"
            
            # Test code embeddings
            embeddings_result = await code_analyzer.generate_code_embeddings(str(python_file))
            assert embeddings_result["status"] == "success", f"Code embeddings failed: {embeddings_result}"
            assert len(embeddings_result["embeddings"]) > 0, "Should generate embeddings"
            
            # Test code indexing
            index_result = await code_analyzer.index_code_file(str(python_file))
            assert index_result["status"] == "success", f"Code indexing failed: {index_result}"
            
            # Test semantic search
            search_result = await code_analyzer.search_code_semantically("hello world function", limit=5)
            assert search_result["status"] == "success", f"Semantic search failed: {search_result}"
            
            # Test project dependencies
            deps_result = await code_analyzer.analyze_project_dependencies(str(self.test_repo_path))
            assert deps_result["status"] == "success", f"Dependency analysis failed: {deps_result}"
            
            self.test_results["code_analyzer"] = {
                "status": "success",
                "tests_passed": ["parse", "complexity", "embeddings", "index", "search", "dependencies"],
                "details": "All code analysis features working correctly"
            }
            
        except Exception as e:
            logger.error(f"Code analyzer test failed: {e}")
            self.test_results["code_analyzer"] = {
                "status": "error",
                "error": str(e)
            }

    async def test_change_tracker(self):
        """Test change tracker module."""
        logger.info("Testing change tracker...")

        try:
            from app.agent.change_tracker import change_tracker

            # Test starting watch
            watch_result = change_tracker.start_watching(str(self.test_repo_path))
            assert watch_result["status"] == "success", f"Start watching failed: {watch_result}"

            # Test statistics
            stats_result = change_tracker.get_statistics()
            assert stats_result["status"] == "success", f"Get statistics failed: {stats_result}"

            # Test manual scan
            scan_result = await change_tracker.trigger_manual_scan(str(self.test_repo_path))
            assert scan_result["status"] == "success", f"Manual scan failed: {scan_result}"

            # Test recent changes
            changes_result = await change_tracker.get_recent_changes()
            assert changes_result["status"] == "success", f"Get recent changes failed: {changes_result}"

            # Test stopping watch
            stop_result = change_tracker.stop_watching(str(self.test_repo_path))
            assert stop_result["status"] == "success", f"Stop watching failed: {stop_result}"

            self.test_results["change_tracker"] = {
                "status": "success",
                "tests_passed": ["start_watch", "statistics", "manual_scan", "recent_changes", "stop_watch"],
                "details": "All change tracking features working correctly"
            }

        except Exception as e:
            logger.error(f"Change tracker test failed: {e}")
            self.test_results["change_tracker"] = {
                "status": "error",
                "error": str(e)
            }

    async def test_repo_context(self):
        """Test repository context module."""
        logger.info("Testing repository context...")

        try:
            from app.agent.repo_context import repo_context

            # Test project structure analysis
            structure_result = await repo_context.analyze_project_structure(str(self.test_repo_path))
            assert structure_result["status"] == "success", f"Structure analysis failed: {structure_result}"
            assert structure_result["structure"]["total_files"] > 0, "Should find files"
            assert structure_result["structure"]["code_files"] > 0, "Should find code files"

            # Test codebase summary generation
            summary_result = await repo_context.generate_codebase_summary(
                str(self.test_repo_path),
                include_code_samples=True,
                max_files=10
            )
            assert summary_result["status"] == "success", f"Summary generation failed: {summary_result}"
            assert "project_overview" in summary_result["summary"], "Should have project overview"

            # Test relevant files search
            search_result = await repo_context.search_relevant_files(
                "hello world function",
                str(self.test_repo_path),
                limit=5
            )
            assert search_result["status"] == "success", f"Relevant files search failed: {search_result}"

            # Test task context generation
            context_result = await repo_context.get_context_for_task(
                "Add unit tests for the calculator class",
                str(self.test_repo_path)
            )
            assert context_result["status"] == "success", f"Task context failed: {context_result}"
            assert "context" in context_result, "Should have context"

            self.test_results["repo_context"] = {
                "status": "success",
                "tests_passed": ["structure", "summary", "search", "task_context"],
                "details": "All repository context features working correctly"
            }

        except Exception as e:
            logger.error(f"Repository context test failed: {e}")
            self.test_results["repo_context"] = {
                "status": "error",
                "error": str(e)
            }

    async def test_integration(self):
        """Test integration between all Phase 3 components."""
        logger.info("Testing Phase 3 integration...")

        try:
            from app.agent.file_operations import file_operations
            from app.agent.git_operations import git_operations
            from app.agent.code_analyzer import code_analyzer
            from app.agent.repo_context import repo_context

            # Create a new feature file
            feature_file = self.test_repo_path / "advanced_calculator.py"
            feature_content = '''
class AdvancedCalculator:
    """An advanced calculator with more operations."""

    def __init__(self):
        self.history = []

    def power(self, base, exponent):
        """Calculate base raised to the power of exponent."""
        result = base ** exponent
        self.history.append(f"{base}^{exponent} = {result}")
        return result

    def factorial(self, n):
        """Calculate factorial of n."""
        if n < 0:
            raise ValueError("Factorial not defined for negative numbers")
        if n == 0 or n == 1:
            return 1

        result = 1
        for i in range(2, n + 1):
            result *= i

        self.history.append(f"{n}! = {result}")
        return result

    def get_history(self):
        """Get calculation history."""
        return self.history.copy()
'''

            # 1. Create file using file operations
            create_result = await file_operations.create_file(str(feature_file), feature_content)
            assert create_result["status"] == "success", "File creation should succeed"

            # 2. Analyze the new code
            parse_result = await code_analyzer.parse_code_structure(str(feature_file))
            assert parse_result["status"] == "success", "Code parsing should succeed"
            assert len(parse_result["functions"]) >= 3, "Should find at least 3 methods"

            # 3. Index the code for search
            index_result = await code_analyzer.index_code_file(str(feature_file))
            assert index_result["status"] == "success", "Code indexing should succeed"

            # 4. Check git status
            git_status = await git_operations.get_git_status(str(self.test_repo_path))
            assert git_status["status"] == "success", "Git status should succeed"
            assert len(git_status["untracked"]) > 0, "Should have untracked files"

            # 5. Generate updated project context
            context_result = await repo_context.get_context_for_task(
                "Add unit tests for the AdvancedCalculator class",
                str(self.test_repo_path)
            )
            assert context_result["status"] == "success", "Context generation should succeed"

            # 6. Search for the new functionality
            search_result = await code_analyzer.search_code_semantically(
                "factorial calculation", limit=3
            )
            assert search_result["status"] == "success", "Semantic search should succeed"

            # 7. Commit the changes
            commit_result = await git_operations.git_commit(
                str(self.test_repo_path),
                "Add advanced calculator with power and factorial functions",
                add_all=True
            )
            assert commit_result["status"] == "success", "Git commit should succeed"

            self.test_results["integration"] = {
                "status": "success",
                "workflow_steps": [
                    "file_creation", "code_analysis", "code_indexing",
                    "git_status", "context_generation", "semantic_search", "git_commit"
                ],
                "details": "Complete workflow integration successful"
            }

        except Exception as e:
            logger.error(f"Integration test failed: {e}")
            self.test_results["integration"] = {
                "status": "error",
                "error": str(e)
            }

    async def cleanup_test_environment(self):
        """Clean up test environment."""
        logger.info("Cleaning up test environment...")

        try:
            if self.temp_dir and self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                logger.info(f"Cleaned up: {self.temp_dir}")
        except Exception as e:
            logger.error(f"Cleanup failed: {e}")

    def generate_report(self):
        """Generate validation report."""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results.values() if result["status"] == "success")
        failed_tests = total_tests - successful_tests

        report = {
            "phase3_validation_report": {
                "timestamp": datetime.now().isoformat(),
                "summary": {
                    "total_components": total_tests,
                    "successful": successful_tests,
                    "failed": failed_tests,
                    "success_rate": f"{(successful_tests/total_tests)*100:.1f}%" if total_tests > 0 else "0%"
                },
                "component_results": self.test_results,
                "overall_status": "PASS" if failed_tests == 0 else "FAIL"
            }
        }

        return report

    async def run_all_tests(self):
        """Run all Phase 3 validation tests."""
        logger.info("Starting Phase 3 validation...")

        try:
            await self.setup_test_environment()

            # Run all component tests
            await self.test_file_operations()
            await self.test_git_operations()
            await self.test_code_analyzer()
            await self.test_change_tracker()
            await self.test_repo_context()
            await self.test_integration()

        finally:
            await self.cleanup_test_environment()

        # Generate and return report
        return self.generate_report()

async def main():
    """Main validation function."""
    validator = Phase3Validator()

    try:
        report = await validator.run_all_tests()

        # Print report
        print("\n" + "="*80)
        print("PHASE 3 VALIDATION REPORT")
        print("="*80)
        print(json.dumps(report, indent=2))
        print("="*80)

        # Exit with appropriate code
        overall_status = report["phase3_validation_report"]["overall_status"]
        if overall_status == "PASS":
            logger.info("✅ Phase 3 validation PASSED!")
            sys.exit(0)
        else:
            logger.error("❌ Phase 3 validation FAILED!")
            sys.exit(1)

    except Exception as e:
        logger.error(f"Validation failed with exception: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
