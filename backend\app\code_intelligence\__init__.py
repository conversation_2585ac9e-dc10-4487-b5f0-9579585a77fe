"""
Code Intelligence Hub - Phase 8B
The Brain of the AI Development Platform

This module provides universal code indexing, semantic search, and intelligent
code relationship mapping for the DeepNexus AI system.

Features:
- Universal file indexing with metadata extraction
- Semantic search using BGE-M3 embeddings
- Code relationship mapping and dependency analysis
- Real-time indexing with file change detection
- Natural language code queries
- Performance-optimized vector search

Architecture:
- Core: Main Code Intelligence Hub orchestrator
- FileScanner: Universal file indexing system
- CodeAnalyzer: Multi-language code analysis
- SemanticSearch: Natural language search engine
- RelationshipMapper: Code dependency analysis
"""

from .core import CodeIntelligenceHub
from .models import (
    CodeFile,
    CodeChunk,
    CodeFunction,
    CodeClass,
    SearchQuery,
    SearchResult,
    RelationshipGraph
)

# Global instance
code_intelligence_hub = CodeIntelligenceHub()

__all__ = [
    'CodeIntelligenceHub',
    'code_intelligence_hub',
    'CodeFile',
    'CodeChunk',
    'CodeFunction',
    'CodeClass',
    'SearchQuery',
    'SearchResult',
    'RelationshipGraph'
]

__version__ = "8B.1.0"
__author__ = "DeepNexus AI Team"
