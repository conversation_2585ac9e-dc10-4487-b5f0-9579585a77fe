interactions:
- request:
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '206'
      content-type:
      - application/json
      host:
      - api.anthropic.com
    method: POST
    parsed_body:
      max_tokens: 1024
      messages:
      - content:
        - text: What is the capital of France?
          type: text
        role: user
      model: claude-3-opus-latest
      stream: false
      system: |+
        You are a helpful assistant.

    uri: https://api.anthropic.com/v1/messages?beta=true
  response:
    headers:
      connection:
      - keep-alive
      content-length:
      - '354'
      content-type:
      - application/json
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      transfer-encoding:
      - chunked
    parsed_body:
      content:
      - text: The capital of France is Paris.
        type: text
      id: msg_01BznVNBje2zyfpCfNQCD5en
      model: claude-3-opus-20240229
      role: assistant
      stop_reason: end_turn
      stop_sequence: null
      type: message
      usage:
        cache_creation_input_tokens: 0
        cache_read_input_tokens: 0
        input_tokens: 20
        output_tokens: 10
        service_tier: standard
    status:
      code: 200
      message: OK
version: 1
...
