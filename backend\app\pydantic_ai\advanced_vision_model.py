"""
Advanced Vision Model for Pydantic AI

This module implements an advanced vision model that supports:
- Image processing and analysis
- Tool calling with vision capabilities
- Base64 image handling
- Multimodal workflows
"""

import base64
import json
import logging
import re
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, AsyncIterator
from pathlib import Path

from pydantic_ai.models import Model, ModelRequestParameters, check_allow_model_requests
from pydantic_ai.messages import (
    ModelMessage, ModelRequest, ModelResponse, TextPart, ToolCallPart,
    SystemPromptPart, UserPromptPart, ToolReturnPart, RetryPromptPart
)
from pydantic_ai.tools import ToolDefinition
from pydantic_ai.settings import ModelSettings
from pydantic_ai.usage import Usage

from llm_client import openrouter_client

logger = logging.getLogger(__name__)


class AdvancedVisionModel(Model):
    """
    Advanced vision model with full tool calling and image processing support.
    
    This model supports:
    - Image analysis with base64 encoding
    - Tool calling for vision tasks
    - Multimodal content processing
    - JSON-based tool responses
    """
    
    def __init__(self, model_name: str = "google/gemini-2.0-flash-exp:free"):
        self._model_name = model_name
        self._system = "google"
        logger.info(f"🖼️  Initialized AdvancedVisionModel with {model_name}")
    
    @property
    def model_name(self) -> str:
        """Return the model name."""
        return self._model_name
    
    @property
    def system(self) -> str:
        """Return the system name."""
        return self._system
    
    async def request(
        self,
        messages: List[ModelMessage],
        model_settings: Optional[ModelSettings],
        model_request_parameters: ModelRequestParameters,
    ) -> ModelResponse:
        """Make a request with full vision and tool calling support."""
        check_allow_model_requests()
        
        try:
            # Extract tools from parameters
            tools = model_request_parameters.function_tools or []
            
            # Build prompt with image and tool support
            prompt_data = await self._build_multimodal_prompt(messages, tools)
            
            # Make request based on content type
            if prompt_data.get('has_images'):
                # Use vision analysis for image content
                response_text = await self._handle_vision_request(prompt_data)
            else:
                # Use regular chat completion for text-only
                response_text = await self._handle_text_request(prompt_data)
            
            # Parse response for tool calls
            return await self._parse_response(response_text, tools)
            
        except Exception as e:
            logger.error(f"🖼️  Advanced vision model request failed: {e}")
            return ModelResponse(
                parts=[TextPart(content=f"Error: {str(e)}")],
                model_name=self._model_name,
                timestamp=datetime.now(timezone.utc),
                usage=Usage(requests=1)
            )
    
    async def _build_multimodal_prompt(
        self, 
        messages: List[ModelMessage], 
        tools: List[ToolDefinition]
    ) -> Dict[str, Any]:
        """Build a multimodal prompt with image and tool support."""
        prompt_parts = []
        images = []
        has_images = False
        
        # Process messages
        for message in messages:
            if isinstance(message, ModelRequest):
                for part in message.parts:
                    if isinstance(part, SystemPromptPart):
                        prompt_parts.append(f"SYSTEM: {part.content}")
                    elif isinstance(part, UserPromptPart):
                        if isinstance(part.content, str):
                            prompt_parts.append(f"USER: {part.content}")
                        else:
                            # Handle multimodal content
                            text_parts = []
                            for item in part.content:
                                if isinstance(item, str):
                                    text_parts.append(item)
                                elif hasattr(item, 'image_url'):
                                    # Extract image data
                                    image_url = item.image_url
                                    if isinstance(image_url, str) and image_url.startswith('data:image/'):
                                        # Extract base64 data
                                        image_data = image_url.split(',', 1)[1] if ',' in image_url else image_url
                                        images.append(image_data)
                                        has_images = True
                                        text_parts.append("[IMAGE_ATTACHED]")
                                    elif isinstance(image_url, dict):
                                        url = image_url.get('url', '')
                                        if url.startswith('data:image/'):
                                            image_data = url.split(',', 1)[1] if ',' in url else url
                                            images.append(image_data)
                                            has_images = True
                                            text_parts.append("[IMAGE_ATTACHED]")
                                elif hasattr(item, 'content'):
                                    text_parts.append(str(item.content))
                                else:
                                    text_parts.append(str(item))
                            
                            if text_parts:
                                prompt_parts.append(f"USER: {' '.join(text_parts)}")
                    elif isinstance(part, ToolReturnPart):
                        prompt_parts.append(f"TOOL_RESULT[{part.tool_name}]: {part.model_response_str()}")
                    elif isinstance(part, RetryPromptPart):
                        prompt_parts.append(f"RETRY: {part.model_response()}")
            
            elif isinstance(message, ModelResponse):
                for part in message.parts:
                    if isinstance(part, TextPart):
                        prompt_parts.append(f"ASSISTANT: {part.content}")
                    elif isinstance(part, ToolCallPart):
                        args_str = part.args_as_json_str() if part.args else "{}"
                        prompt_parts.append(f"ASSISTANT_TOOL_CALL[{part.tool_name}]: {args_str}")
        
        # Add tool definitions if available
        if tools:
            tools_description = self._format_tools_for_prompt(tools)
            prompt_parts.append(f"\nAVAILABLE_TOOLS:\n{tools_description}")
            
            # Add tool usage instructions
            prompt_parts.append("""
TOOL_USAGE_INSTRUCTIONS:
If you need to use tools, respond with JSON in this exact format:
```json
{
    "tool_calls": [
        {
            "tool": "tool_name",
            "args": {"param1": "value1", "param2": "value2"}
        }
    ]
}
```

If you don't need tools, respond normally with text.
""")
        
        return {
            "prompt": "\n\n".join(prompt_parts),
            "images": images,
            "has_images": has_images
        }
    
    def _format_tools_for_prompt(self, tools: List[ToolDefinition]) -> str:
        """Format tool definitions for inclusion in the prompt."""
        tool_descriptions = []
        
        for tool in tools:
            description = f"- {tool.name}: {tool.description}"
            
            # Add parameter information
            if tool.parameters_json_schema and 'properties' in tool.parameters_json_schema:
                params = []
                properties = tool.parameters_json_schema['properties']
                required = tool.parameters_json_schema.get('required', [])
                
                for param_name, param_info in properties.items():
                    param_desc = param_info.get('description', 'No description')
                    param_type = param_info.get('type', 'unknown')
                    required_marker = " (required)" if param_name in required else " (optional)"
                    params.append(f"    - {param_name} ({param_type}){required_marker}: {param_desc}")
                
                if params:
                    description += "\n" + "\n".join(params)
            
            tool_descriptions.append(description)
        
        return "\n".join(tool_descriptions)
    
    async def _handle_vision_request(self, prompt_data: Dict[str, Any]) -> str:
        """Handle requests with images using vision analysis."""
        prompt = prompt_data["prompt"]
        images = prompt_data["images"]

        if images:
            # Use the first image for vision analysis
            image_base64 = images[0]

            # Enhanced prompt for better vision analysis
            enhanced_prompt = f"""
{prompt}

IMPORTANT: You are analyzing an image. Please provide detailed visual analysis including:
1. Overall layout and design assessment
2. Specific UI elements and their properties
3. Accessibility issues (contrast, size, readability)
4. Usability concerns and recommendations
5. Visual bugs or inconsistencies

If tools are available, use them to provide structured analysis. Otherwise, provide comprehensive text analysis.
"""

            # Make vision analysis request
            response = await openrouter_client.vision_analyze(
                image_base64=image_base64,
                prompt=enhanced_prompt,
                model=self._model_name
            )

            return response
        else:
            # Fallback to text request
            return await self._handle_text_request(prompt_data)
    
    async def _handle_text_request(self, prompt_data: Dict[str, Any]) -> str:
        """Handle text-only requests."""
        prompt = prompt_data["prompt"]
        
        # Make chat completion request
        response = await openrouter_client.chat_completion(
            messages=[{"role": "user", "content": prompt}],
            model=self._model_name
        )
        
        return response
    
    async def _parse_response(
        self,
        content: str,
        tools: List[ToolDefinition]
    ) -> ModelResponse:
        """Parse the model response and convert to ModelResponse."""
        parts = []

        # Try to extract JSON from markdown code blocks first
        json_content = self._extract_json_from_markdown(content)
        if json_content and tools:
            try:
                parsed = json.loads(json_content)
                if isinstance(parsed, dict) and 'tool_calls' in parsed:
                    # Handle tool calls
                    for tool_call in parsed['tool_calls']:
                        if isinstance(tool_call, dict) and 'tool' in tool_call:
                            tool_name = tool_call['tool']
                            tool_args = tool_call.get('args', {})
                            tool_call_id = f"vision_{uuid.uuid4().hex[:8]}"

                            parts.append(ToolCallPart(
                                tool_name=tool_name,
                                args=tool_args,
                                tool_call_id=tool_call_id
                            ))

                    # If we found tool calls, return them
                    if parts:
                        return ModelResponse(
                            parts=parts,
                            model_name=self._model_name,
                            timestamp=datetime.now(timezone.utc),
                            usage=Usage(requests=1)
                        )
            except json.JSONDecodeError:
                pass

        # Regular text response
        parts.append(TextPart(content=content))

        return ModelResponse(
            parts=parts,
            model_name=self._model_name,
            timestamp=datetime.now(timezone.utc),
            usage=Usage(requests=1)
        )

    def _extract_json_from_markdown(self, content: str) -> Optional[str]:
        """Extract JSON content from markdown code blocks."""
        # Look for JSON code blocks
        json_pattern = r'```(?:json)?\s*\n?(.*?)\n?```'
        matches = re.findall(json_pattern, content, re.DOTALL | re.IGNORECASE)

        for match in matches:
            # Clean up the match
            cleaned = match.strip()
            if cleaned.startswith('{') and cleaned.endswith('}'):
                try:
                    # Validate it's actually JSON
                    json.loads(cleaned)
                    return cleaned
                except json.JSONDecodeError:
                    continue

        return None
    
    async def request_stream(
        self,
        messages: List[ModelMessage],
        model_settings: Optional[ModelSettings],
        model_request_parameters: ModelRequestParameters,
    ) -> AsyncIterator[ModelResponse]:
        """Stream response (fallback to regular request for vision)."""
        response = await self.request(messages, model_settings, model_request_parameters)
        yield response


# Export the advanced vision model
__all__ = ['AdvancedVisionModel']
