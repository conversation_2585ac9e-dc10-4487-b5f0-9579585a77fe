# FastAPI and ASGI server
fastapi==0.115.12
uvicorn[standard]==0.32.1
python-multipart==0.0.20

# Socket.IO for real-time communication
python-socketio==5.11.4
python-engineio==4.10.1

# HTTP client for OpenRouter API
httpx>=0.27.0,<0.28.0  # Compatible with ollama requirements
requests==2.32.3

# Retry logic for API calls
tenacity==9.0.0

# Ollama client for embeddings
ollama==0.4.4

# Vector database client
qdrant-client==1.12.1

# Environment and configuration
python-dotenv==1.0.1
pydantic==2.10.4
pydantic-settings==2.7.0

# Token counting and text processing
tiktoken==0.8.0

# Database (sqlite3 is built into Python)

# Git operations
GitPython==3.1.43

# Subprocess and system utilities
psutil==6.1.0

# Logging and monitoring
structlog==24.4.0

# Testing (for development)
pytest==8.3.4
pytest-asyncio==0.24.0
# httpx already specified above for compatibility

# Code quality
flake8==7.1.1
black==24.10.0

# Image processing for screenshots
Pillow==11.0.0

# Async HTTP client for vision API
aiohttp==3.11.10

# File watching and utilities
watchdog==6.0.0

# JSON handling
orjson==3.10.12

# Async utilities
asyncio-mqtt==0.16.2

# Phase 3 Dependencies - Code Analysis and Parsing
tree-sitter>=0.20.4
tree-sitter-python>=0.20.4
tree-sitter-javascript>=0.20.3
chardet>=5.2.0

# Phase 5 Dependencies - Task Runner and Lint/Test Utilities
jsonschema>=4.20.0        # For .aiagent.json validation

# Phase 6 Dependencies - Vision QA and Screenshots
playwright>=1.40.0        # For browser automation and screenshots

# Phase 7 Dependencies - Pydantic AI Migration (COMPLETE)
pydantic-ai>=0.2.15            # Pydantic AI framework
openai>=1.0.0                  # OpenAI client for Pydantic AI models
logfire>=0.54.0                # Monitoring and observability
opentelemetry-api>=1.20.0      # OpenTelemetry for instrumentation
opentelemetry-sdk>=1.20.0      # OpenTelemetry SDK

# CLI Dependencies
click>=8.1.7                   # Professional CLI framework

# Enhanced System Dependencies - Advanced Features
cryptography>=41.0.0           # Security and encryption
PyYAML>=6.0.1                  # YAML configuration support
scikit-learn>=1.3.0            # Machine learning for analytics
numpy>=1.24.0                  # Numerical computing
pandas>=2.0.0                  # Data analysis and manipulation
matplotlib>=3.7.0              # Data visualization
seaborn>=0.12.0                # Statistical data visualization

# Advanced Analytics Dependencies
scipy>=1.11.0                  # Scientific computing
plotly>=5.15.0                 # Interactive visualizations
joblib>=1.3.0                  # Parallel computing utilities

# Enhanced Security Dependencies
bcrypt>=4.0.0                  # Password hashing
PyJWT>=2.8.0                   # JSON Web Tokens
passlib>=1.7.4                 # Password hashing utilities

# Configuration and Validation
cerberus>=1.3.4                # Data validation
marshmallow>=3.20.0            # Object serialization/deserialization

# Advanced Monitoring and Metrics
prometheus-client>=0.17.0      # Prometheus metrics
statsd>=4.0.1                  # StatsD metrics client

# Performance and Optimization
cachetools>=5.3.0              # Caching utilities
redis>=4.6.0                   # Redis client (optional)
python-memcached>=1.59         # Memcached client (optional)
