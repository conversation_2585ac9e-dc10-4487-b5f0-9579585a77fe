# AI Coder Agent - Phase 3: Advanced Code Operations

Phase 3 introduces comprehensive file operations, git integration, advanced code analysis, change tracking, and repository context management to the AI Coder Agent.

## 🚀 New Features

### 1. File Operations (`file_operations.py`)
- **Atomic file operations** with encoding detection and safety checks
- **Directory management** with recursive listing and filtering
- **Backup creation** for safe file modifications
- **Metadata extraction** including file size, permissions, and timestamps
- **Conflict resolution** for file creation and modification

### 2. Git Operations (`git_operations.py`)
- **Complete git workflow** support (status, diff, commit, branch operations)
- **Commit history** analysis with filtering and pagination
- **File blame** information for line-by-line authorship
- **Stash operations** for temporary change management
- **Branch management** (create, switch, list, delete)

### 3. Advanced Code Analysis (`code_analyzer.py`)
- **Multi-language parsing** (Python, JavaScript, TypeScript, Java, C++, Go, Rust)
- **Semantic code embeddings** using BGE-M3 model (1024 dimensions)
- **Code complexity metrics** (cyclomatic complexity, maintainability index)
- **Dependency analysis** and relationship mapping
- **Semantic code search** with vector similarity
- **Automatic code indexing** with structure analysis

### 4. Change Tracking (`change_tracker.py`)
- **Real-time file monitoring** with intelligent debouncing
- **Git integration** for change detection and staging
- **Batch processing** for efficient change handling
- **Change categorization** (created, modified, deleted, renamed)
- **Automatic code indexing** on file changes
- **Statistics and monitoring** for change patterns

### 5. Repository Context Management (`repo_context.py`)
- **Project structure analysis** with file categorization
- **Codebase summaries** optimized for LLM context
- **Task-specific context** generation with relevant file recommendations
- **Dependency mapping** and relationship visualization
- **Code pattern detection** and documentation
- **Context-aware file suggestions**

## 📁 File Structure

```
backend/app/agent/
├── file_operations.py      # File and directory operations
├── git_operations.py       # Git workflow integration
├── code_analyzer.py        # Advanced code analysis and search
├── change_tracker.py       # Real-time change monitoring
├── repo_context.py         # Repository context management
└── context_manager.py      # Phase 2 context management (existing)
```

## 🔧 API Endpoints

### File Operations
- `POST /api/files/read` - Read file with encoding detection
- `POST /api/files/write` - Write file with atomic operations
- `POST /api/files/create` - Create new file with conflict resolution
- `DELETE /api/files/delete` - Delete file with backup options
- `POST /api/files/list` - List directory with filtering
- `POST /api/files/info` - Get file metadata

### Git Operations
- `POST /api/git/status` - Get repository status
- `POST /api/git/diff` - Get file differences
- `POST /api/git/commit` - Commit changes
- `POST /api/git/branch` - Branch operations
- `POST /api/git/history` - Commit history
- `POST /api/git/blame` - File authorship
- `POST /api/git/stash` - Stash operations

### Code Analysis
- `POST /api/code/parse` - Parse code structure
- `POST /api/code/complexity` - Analyze complexity
- `POST /api/code/embeddings` - Generate embeddings
- `POST /api/code/index` - Index code file
- `POST /api/code/search` - Semantic search
- `POST /api/code/dependencies` - Dependency analysis

### Change Tracking
- `POST /api/changes/start-watching` - Start monitoring
- `POST /api/changes/stop-watching` - Stop monitoring
- `GET /api/changes/recent` - Get recent changes
- `POST /api/changes/manual-scan` - Trigger scan
- `GET /api/changes/statistics` - Get statistics

### Repository Context
- `POST /api/repo/analyze-structure` - Analyze project
- `POST /api/repo/generate-summary` - Generate summary
- `POST /api/repo/search-relevant` - Find relevant files
- `POST /api/repo/context-for-task` - Task context

## 🧪 Testing

### Validation Script
Run the comprehensive Phase 3 validation:

```bash
# Inside the backend container
python scripts/validate_phase3.py
```

### Integration Test
Test all Phase 3 components together:

```bash
curl -X POST http://localhost:8000/api/test/phase3-integration
```

## 🔄 Integration with Previous Phases

Phase 3 builds upon and integrates with:

- **Phase 1**: Basic FastAPI setup, database, and session management
- **Phase 2**: LLM integration (OpenRouter), embeddings (Ollama BGE-M3), vector database (Qdrant)

### Key Integration Points:
1. **Code embeddings** use the same BGE-M3 model from Phase 2
2. **Vector search** leverages the existing Qdrant setup
3. **Context management** extends Phase 2's context system
4. **Session management** integrates with Phase 1's database

## 🚀 Getting Started

### 1. Start the Services
```bash
# Start all services including Phase 3 components
docker-compose up -d
```

### 2. Verify Phase 3 Setup
```bash
# Check all services are healthy
curl http://localhost:8000/api/services/health

# Test Phase 3 integration
curl -X POST http://localhost:8000/api/test/phase3-integration
```

### 3. Example Workflow

```python
import httpx

# 1. Analyze project structure
response = await httpx.post("http://localhost:8000/api/repo/analyze-structure", 
                           json={"project_path": "/app/workspace"})

# 2. Start change tracking
response = await httpx.post("http://localhost:8000/api/changes/start-watching",
                           json={"path": "/app/workspace"})

# 3. Create a new file
response = await httpx.post("http://localhost:8000/api/files/create",
                           json={
                               "file_path": "/app/workspace/new_feature.py",
                               "content": "def new_feature():\n    pass"
                           })

# 4. Index the new code
response = await httpx.post("http://localhost:8000/api/code/index",
                           json={"file_path": "/app/workspace/new_feature.py"})

# 5. Search for similar code
response = await httpx.post("http://localhost:8000/api/code/search",
                           json={"query": "new feature implementation"})

# 6. Get context for a task
response = await httpx.post("http://localhost:8000/api/repo/context-for-task",
                           json={
                               "task_description": "Add unit tests for new_feature",
                               "project_path": "/app/workspace"
                           })

# 7. Commit changes
response = await httpx.post("http://localhost:8000/api/git/commit",
                           json={
                               "repo_path": "/app/workspace",
                               "message": "Add new feature implementation",
                               "add_all": True
                           })
```

## 🔧 Configuration

Phase 3 components can be configured via environment variables:

```bash
# File operations
WORKSPACE_ROOT=/app/workspace
MAX_FILE_SIZE=10485760  # 10MB

# Change tracking
CHANGE_DEBOUNCE_SECONDS=1.0
MAX_BATCH_SIZE=50
BATCH_TIMEOUT_SECONDS=5.0
AUTO_INDEX_CODE=true

# Repository context
MAX_CONTEXT_FILES=50
MAX_CONTEXT_SIZE=100000  # 100KB
SUMMARY_CACHE_TTL=3600   # 1 hour

# Code analysis
SUPPORTED_LANGUAGES=python,javascript,typescript,java,cpp,go,rust
COMPLEXITY_THRESHOLD=10
```

## 📊 Monitoring

### Change Tracking Statistics
```bash
curl http://localhost:8000/api/changes/statistics
```

### Code Analysis Metrics
- Files indexed and analyzed
- Embedding generation performance
- Search query performance
- Complexity analysis results

### Git Operation Tracking
- Commit frequency and patterns
- Branch management activity
- File change patterns

## 🔮 Future Enhancements

Phase 3 provides the foundation for:
- **AI-powered code suggestions** based on project context
- **Automated refactoring** recommendations
- **Code quality analysis** and improvement suggestions
- **Intelligent merge conflict resolution**
- **Project documentation generation**
- **Code review automation**

## 🐛 Troubleshooting

### Common Issues

1. **Git operations fail**: Ensure git is configured with user.name and user.email
2. **File operations permission errors**: Check file permissions and ownership
3. **Code analysis fails**: Verify supported file types and syntax
4. **Change tracking not working**: Check file system permissions and watchdog support
5. **Embeddings generation slow**: Monitor Ollama service performance

### Debug Mode
Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
```

### Health Checks
```bash
# Check all Phase 3 components
curl http://localhost:8000/api/test/phase3-integration
```

---

**Phase 3 Status**: ✅ **COMPLETE**

All Phase 3 components are fully implemented, tested, and integrated with the existing AI Coder Agent architecture.
