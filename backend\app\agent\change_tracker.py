#!/usr/bin/env python3
"""
File Change Tracking Module for AI Coder Agent - Phase 3
Provides filesystem monitoring with debouncing and git integration.

Features:
- Real-time file system monitoring with watchdog
- Intelligent debouncing to prevent excessive processing
- Git integration for change detection and staging
- Change categorization (created, modified, deleted, renamed)
- Batch processing for multiple file changes
- Integration with code analysis and indexing
"""

import os
import time
import hashlib
from pathlib import Path
from typing import Dict, List, Optional, Union, Any, Callable, Set
import logging
import asyncio
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, field
from datetime import datetime
import json

from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileSystemEvent

# Import other modules
try:
    from ...config import settings
    from .file_operations import file_operations
    from .git_operations import git_operations
    from .code_analyzer import code_analyzer
except ImportError:
    from config import settings
    from app.agent.file_operations import file_operations
    from app.agent.git_operations import git_operations
    from app.agent.code_analyzer import code_analyzer

logger = logging.getLogger(__name__)

@dataclass
class FileChange:
    """Represents a file change event."""
    file_path: str
    change_type: str  # 'created', 'modified', 'deleted', 'moved'
    timestamp: float
    old_path: Optional[str] = None  # For move events
    file_size: Optional[int] = None
    content_hash: Optional[str] = None
    is_code_file: bool = False
    language: Optional[str] = None
    processed: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)

class ChangeTrackingError(Exception):
    """Custom exception for change tracking errors."""
    pass

class SmartFileSystemEventHandler(FileSystemEventHandler):
    """Enhanced file system event handler with debouncing and filtering."""
    
    def __init__(self, change_tracker):
        super().__init__()
        self.change_tracker = change_tracker
        self.debounce_time = getattr(settings, 'change_debounce_seconds', 1.0)
        self.pending_events = {}  # file_path -> (timer, event_type, timestamp)
        
    def on_created(self, event: FileSystemEvent):
        if not event.is_directory:
            self._debounce_event(event.src_path, 'created', event)
    
    def on_modified(self, event: FileSystemEvent):
        if not event.is_directory:
            self._debounce_event(event.src_path, 'modified', event)
    
    def on_deleted(self, event: FileSystemEvent):
        if not event.is_directory:
            self._debounce_event(event.src_path, 'deleted', event)
    
    def on_moved(self, event: FileSystemEvent):
        if not event.is_directory:
            self._debounce_event(event.dest_path, 'moved', event)
    
    def _debounce_event(self, file_path: str, event_type: str, event: FileSystemEvent):
        """Debounce file events to prevent excessive processing."""
        current_time = time.time()
        
        # Cancel previous event if within debounce time
        if file_path in self.pending_events:
            timer, _, _ = self.pending_events[file_path]
            timer.cancel()
        
        # Schedule new event
        timer = asyncio.get_event_loop().call_later(
            self.debounce_time,
            self._process_event,
            file_path,
            event_type,
            event,
            current_time
        )
        
        self.pending_events[file_path] = (timer, event_type, current_time)
    
    def _process_event(self, file_path: str, event_type: str, event: FileSystemEvent, timestamp: float):
        """Process debounced file event."""
        # Remove from pending events
        if file_path in self.pending_events:
            del self.pending_events[file_path]
        
        try:
            # Create change object
            change = FileChange(
                file_path=file_path,
                change_type=event_type,
                timestamp=timestamp,
                old_path=getattr(event, 'src_path', None) if event_type == 'moved' else None
            )
            
            # Add to change tracker
            asyncio.create_task(self.change_tracker._handle_file_change(change))
            
        except Exception as e:
            logger.error(f"Error processing file event {event_type} for {file_path}: {e}")

class ChangeTracker:
    """File change tracking with git integration and code analysis."""
    
    def __init__(self):
        self.workspace_root = Path(getattr(settings, 'workspace_root', '/app/workspace'))
        self.watch_patterns = getattr(settings, 'watch_patterns', ['*.py', '*.js', '*.ts', '*.java', '*.cpp', '*.go', '*.rs'])
        self.ignore_patterns = getattr(settings, 'ignore_patterns', [
            '*.pyc', '*.pyo', '__pycache__', '.git', 'node_modules', '.vscode', '.idea'
        ])
        self.max_batch_size = getattr(settings, 'max_batch_size', 50)
        self.batch_timeout = getattr(settings, 'batch_timeout_seconds', 5.0)
        self.auto_index_code = getattr(settings, 'auto_index_code', True)
        
        self.observers = {}  # path -> Observer
        self.change_queue = asyncio.Queue()
        self.batch_processor_task = None
        self.change_callbacks = []  # List of callback functions
        self.executor = ThreadPoolExecutor(max_workers=2)
        
        # Statistics
        self.stats = {
            'total_changes': 0,
            'changes_by_type': {'created': 0, 'modified': 0, 'deleted': 0, 'moved': 0},
            'code_files_processed': 0,
            'last_batch_time': None,
            'start_time': time.time()
        }
        
        logger.info(f"ChangeTracker initialized for workspace: {self.workspace_root}")
    
    def _should_track_file(self, file_path: Path) -> bool:
        """Check if file should be tracked based on patterns."""
        file_name = file_path.name
        
        # Check ignore patterns
        for pattern in self.ignore_patterns:
            if file_path.match(pattern) or any(part.startswith('.') for part in file_path.parts):
                return False
        
        # Check watch patterns (if specified)
        if self.watch_patterns:
            return any(file_path.match(pattern) for pattern in self.watch_patterns)
        
        return True
    
    def _detect_file_info(self, file_path: Path) -> Dict[str, Any]:
        """Detect file information including language and content hash."""
        info = {
            'is_code_file': False,
            'language': None,
            'file_size': None,
            'content_hash': None
        }
        
        try:
            if file_path.exists() and file_path.is_file():
                # Get file size
                info['file_size'] = file_path.stat().st_size
                
                # Detect if it's a code file
                language = code_analyzer._detect_language(file_path)
                if language:
                    info['is_code_file'] = True
                    info['language'] = language
                
                # Generate content hash for small files
                if info['file_size'] < 1_000_000:  # 1MB limit
                    with open(file_path, 'rb') as f:
                        content = f.read()
                        info['content_hash'] = hashlib.md5(content).hexdigest()
        
        except Exception as e:
            logger.warning(f"Failed to detect file info for {file_path}: {e}")
        
        return info
    
    async def _handle_file_change(self, change: FileChange):
        """Handle a single file change event."""
        try:
            file_path = Path(change.file_path)
            
            # Check if we should track this file
            if not self._should_track_file(file_path):
                return
            
            # Detect file information
            file_info = self._detect_file_info(file_path)
            change.file_size = file_info['file_size']
            change.content_hash = file_info['content_hash']
            change.is_code_file = file_info['is_code_file']
            change.language = file_info['language']
            
            # Add to queue for batch processing
            await self.change_queue.put(change)
            
            # Update statistics
            self.stats['total_changes'] += 1
            self.stats['changes_by_type'][change.change_type] += 1
            
            logger.debug(f"Queued change: {change.change_type} {change.file_path}")
            
        except Exception as e:
            logger.error(f"Error handling file change: {e}")
    
    async def _batch_processor(self):
        """Process file changes in batches."""
        while True:
            try:
                changes = []
                
                # Collect changes for batch processing
                try:
                    # Wait for first change
                    first_change = await asyncio.wait_for(
                        self.change_queue.get(), 
                        timeout=self.batch_timeout
                    )
                    changes.append(first_change)
                    
                    # Collect additional changes up to batch size or timeout
                    start_time = time.time()
                    while (len(changes) < self.max_batch_size and 
                           time.time() - start_time < self.batch_timeout):
                        try:
                            change = await asyncio.wait_for(
                                self.change_queue.get(), 
                                timeout=0.1
                            )
                            changes.append(change)
                        except asyncio.TimeoutError:
                            break
                
                except asyncio.TimeoutError:
                    # No changes to process
                    continue
                
                if changes:
                    await self._process_change_batch(changes)
                    
            except Exception as e:
                logger.error(f"Error in batch processor: {e}")
                await asyncio.sleep(1)  # Prevent tight loop on errors
    
    async def _process_change_batch(self, changes: List[FileChange]):
        """Process a batch of file changes."""
        try:
            logger.info(f"Processing batch of {len(changes)} changes")
            
            # Group changes by type
            changes_by_type = {
                'created': [],
                'modified': [],
                'deleted': [],
                'moved': []
            }
            
            for change in changes:
                changes_by_type[change.change_type].append(change)
            
            # Process each type
            for change_type, type_changes in changes_by_type.items():
                if type_changes:
                    await self._process_changes_by_type(change_type, type_changes)
            
            # Call registered callbacks
            for callback in self.change_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(changes)
                    else:
                        callback(changes)
                except Exception as e:
                    logger.error(f"Error in change callback: {e}")
            
            # Update statistics
            self.stats['last_batch_time'] = time.time()
            code_changes = [c for c in changes if c.is_code_file]
            self.stats['code_files_processed'] += len(code_changes)
            
            logger.info(f"Completed batch processing: {len(code_changes)} code files")
            
        except Exception as e:
            logger.error(f"Error processing change batch: {e}")
    
    async def _process_changes_by_type(self, change_type: str, changes: List[FileChange]):
        """Process changes of a specific type."""
        try:
            if change_type in ['created', 'modified']:
                # Index new/modified code files
                if self.auto_index_code:
                    code_changes = [c for c in changes if c.is_code_file]
                    for change in code_changes:
                        try:
                            result = await code_analyzer.index_code_file(change.file_path)
                            if result.get('status') == 'success':
                                change.processed = True
                                change.metadata['indexed'] = True
                                change.metadata['chunks_indexed'] = result.get('chunks_indexed', 0)
                            else:
                                logger.warning(f"Failed to index {change.file_path}: {result.get('error')}")
                        except Exception as e:
                            logger.error(f"Error indexing {change.file_path}: {e}")
            
            elif change_type == 'deleted':
                # Handle deleted files (remove from index if needed)
                for change in changes:
                    change.processed = True
                    change.metadata['deleted'] = True
            
            elif change_type == 'moved':
                # Handle moved files (update index if needed)
                for change in changes:
                    if change.is_code_file and self.auto_index_code:
                        try:
                            # Re-index at new location
                            result = await code_analyzer.index_code_file(change.file_path)
                            if result.get('status') == 'success':
                                change.processed = True
                                change.metadata['reindexed'] = True
                        except Exception as e:
                            logger.error(f"Error re-indexing moved file {change.file_path}: {e}")
            
        except Exception as e:
            logger.error(f"Error processing {change_type} changes: {e}")

    def start_watching(self, path: Union[str, Path] = None) -> Dict[str, Any]:
        """
        Start watching a directory for file changes.

        Args:
            path: Directory path to watch (defaults to workspace root)

        Returns:
            Dict with operation result
        """
        try:
            watch_path = Path(path) if path else self.workspace_root
            watch_key = str(watch_path)

            if not watch_path.exists() or not watch_path.is_dir():
                return {
                    'status': 'error',
                    'error': f'Invalid watch path: {watch_path}',
                    'error_type': 'ValidationError'
                }

            # Stop existing watcher if any
            if watch_key in self.observers:
                self.stop_watching(watch_path)

            # Create new observer
            observer = Observer()
            event_handler = SmartFileSystemEventHandler(self)
            observer.schedule(event_handler, str(watch_path), recursive=True)
            observer.start()

            self.observers[watch_key] = observer

            # Start batch processor if not running
            if self.batch_processor_task is None or self.batch_processor_task.done():
                self.batch_processor_task = asyncio.create_task(self._batch_processor())

            logger.info(f"Started watching: {watch_path}")
            return {
                'status': 'success',
                'watching': str(watch_path),
                'watcher_id': watch_key
            }

        except Exception as e:
            logger.error(f"Failed to start watching {path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    def stop_watching(self, path: Union[str, Path] = None) -> Dict[str, Any]:
        """
        Stop watching a directory for file changes.

        Args:
            path: Directory path to stop watching

        Returns:
            Dict with operation result
        """
        try:
            watch_path = Path(path) if path else self.workspace_root
            watch_key = str(watch_path)

            if watch_key in self.observers:
                observer = self.observers[watch_key]
                observer.stop()
                observer.join(timeout=5)
                del self.observers[watch_key]

                logger.info(f"Stopped watching: {watch_key}")
                return {
                    'status': 'success',
                    'stopped_watching': watch_key
                }
            else:
                return {
                    'status': 'error',
                    'error': f'No watcher found for: {path}',
                    'error_type': 'WatcherNotFound'
                }

        except Exception as e:
            logger.error(f"Failed to stop watching {path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    def add_change_callback(self, callback: Callable):
        """Add a callback function to be called when changes are processed."""
        self.change_callbacks.append(callback)

    def remove_change_callback(self, callback: Callable):
        """Remove a callback function."""
        if callback in self.change_callbacks:
            self.change_callbacks.remove(callback)

    async def get_recent_changes(self, limit: int = 100,
                               change_type: Optional[str] = None,
                               since: Optional[float] = None) -> Dict[str, Any]:
        """
        Get recent file changes (this would require storing changes in a database).
        For now, returns current statistics.

        Args:
            limit: Maximum number of changes to return
            change_type: Filter by change type
            since: Unix timestamp to filter changes since

        Returns:
            Dict with recent changes
        """
        try:
            # For now, return statistics since we're not persisting changes
            # In a full implementation, this would query a database

            return {
                'status': 'success',
                'statistics': self.stats,
                'active_watchers': list(self.observers.keys()),
                'queue_size': self.change_queue.qsize(),
                'batch_processor_running': (
                    self.batch_processor_task is not None and
                    not self.batch_processor_task.done()
                )
            }

        except Exception as e:
            logger.error(f"Failed to get recent changes: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def trigger_manual_scan(self, path: Union[str, Path] = None) -> Dict[str, Any]:
        """
        Trigger a manual scan of the directory for changes.

        Args:
            path: Directory path to scan (defaults to workspace root)

        Returns:
            Dict with scan results
        """
        try:
            scan_path = Path(path) if path else self.workspace_root

            if not scan_path.exists() or not scan_path.is_dir():
                return {
                    'status': 'error',
                    'error': f'Invalid scan path: {scan_path}',
                    'error_type': 'ValidationError'
                }

            # Get git status to identify changed files
            git_status = await git_operations.get_git_status(scan_path)

            changes_found = []

            if git_status.get('status') == 'success':
                # Process unstaged changes
                for file_info in git_status.get('unstaged', []):
                    file_path = scan_path / file_info['file']
                    if self._should_track_file(file_path):
                        change = FileChange(
                            file_path=str(file_path),
                            change_type='modified',
                            timestamp=time.time()
                        )
                        await self._handle_file_change(change)
                        changes_found.append(str(file_path))

                # Process untracked files
                for file_name in git_status.get('untracked', []):
                    file_path = scan_path / file_name
                    if self._should_track_file(file_path):
                        change = FileChange(
                            file_path=str(file_path),
                            change_type='created',
                            timestamp=time.time()
                        )
                        await self._handle_file_change(change)
                        changes_found.append(str(file_path))

            return {
                'status': 'success',
                'scan_path': str(scan_path),
                'changes_found': len(changes_found),
                'files': changes_found
            }

        except Exception as e:
            logger.error(f"Failed to trigger manual scan: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    def get_statistics(self) -> Dict[str, Any]:
        """Get change tracking statistics."""
        uptime = time.time() - self.stats['start_time']

        return {
            'status': 'success',
            'statistics': {
                **self.stats,
                'uptime_seconds': uptime,
                'changes_per_minute': self.stats['total_changes'] / (uptime / 60) if uptime > 0 else 0,
                'active_watchers': len(self.observers),
                'queue_size': self.change_queue.qsize(),
                'batch_processor_running': (
                    self.batch_processor_task is not None and
                    not self.batch_processor_task.done()
                )
            }
        }

    def cleanup(self):
        """Clean up resources and stop all watchers."""
        try:
            # Stop all watchers
            for watch_key in list(self.observers.keys()):
                self.stop_watching(watch_key)

            # Cancel batch processor
            if self.batch_processor_task and not self.batch_processor_task.done():
                self.batch_processor_task.cancel()

            # Shutdown executor
            self.executor.shutdown(wait=True)

            logger.info("ChangeTracker cleanup completed")

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Global instance
change_tracker = ChangeTracker()
