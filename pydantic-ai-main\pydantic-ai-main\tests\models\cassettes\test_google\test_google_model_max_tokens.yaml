interactions:
- request:
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '221'
      content-type:
      - application/json
      host:
      - generativelanguage.googleapis.com
    method: POST
    parsed_body:
      contents:
      - parts:
        - text: What is the capital of France?
        role: user
      generationConfig:
        maxOutputTokens: 5
      systemInstruction:
        parts:
        - text: You are a helpful chatbot.
        role: user
    uri: https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
  response:
    headers:
      alt-svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      content-length:
      - '634'
      content-type:
      - application/json; charset=UTF-8
      server-timing:
      - gfet4t7; dur=253
      transfer-encoding:
      - chunked
      vary:
      - Origin
      - X-Origin
      - Referer
    parsed_body:
      candidates:
      - avgLogprobs: -7.873064605519175e-05
        content:
          parts:
          - text: The capital of France is
          role: model
        finishReason: MAX_TOKENS
      modelVersion: gemini-1.5-flash
      usageMetadata:
        candidatesTokenCount: 5
        candidatesTokensDetails:
        - modality: TEXT
          tokenCount: 5
        promptTokenCount: 13
        promptTokensDetails:
        - modality: TEXT
          tokenCount: 13
        totalTokenCount: 18
    status:
      code: 200
      message: OK
version: 1
