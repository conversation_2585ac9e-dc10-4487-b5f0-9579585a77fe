[build-system]
requires = ["hatchling", "uv-dynamic-versioning>=0.7.0"]
build-backend = "hatchling.build"

[tool.hatch.version]
source = "uv-dynamic-versioning"

[tool.uv-dynamic-versioning]
vcs = "git"
style = "pep440"
bump = true

[project]
name = "pydantic-ai"
dynamic = ["version", "dependencies", "optional-dependencies"]
description = "Agent Framework / shim to use Pydantic with LLMs"
authors = [
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
]
license = "MIT"
readme = "README.md"
classifiers = [
    "Development Status :: 4 - Beta",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3 :: Only",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Intended Audience :: Developers",
    "Intended Audience :: Information Technology",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Topic :: Internet",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Framework :: Pydantic",
    "Framework :: Pydantic :: 2",
]
requires-python = ">=3.9"

[tool.hatch.metadata.hooks.uv-dynamic-versioning]
dependencies = [
    "pydantic-ai-slim[openai,vertexai,google,groq,anthropic,mistral,cohere,bedrock,cli,mcp,evals,a2a]=={{ version }}",
]

[tool.hatch.metadata.hooks.uv-dynamic-versioning.optional-dependencies]
examples = ["pydantic-ai-examples=={{ version }}"]
logfire = ["logfire>=3.11.0"]

[project.urls]
Homepage = "https://ai.pydantic.dev"
Source = "https://github.com/pydantic/pydantic-ai"
Documentation = "https://ai.pydantic.dev"
Changelog = "https://github.com/pydantic/pydantic-ai/releases"

[project.scripts]
pai = "pydantic_ai._cli:cli_exit" # TODO remove this when clai has been out for a while

[tool.uv.sources]
pydantic-ai-slim = { workspace = true }
pydantic-evals = { workspace = true }
pydantic-graph = { workspace = true }
pydantic-ai-examples = { workspace = true }
fasta2a = { workspace = true }

[tool.uv.workspace]
members = [
    "pydantic_ai_slim",
    "pydantic_evals",
    "pydantic_graph",
    "mcp-run-python",
    "clai",
    "examples",
    "fasta2a",
]

[dependency-groups]
# dev dependencies are defined in `pydantic-ai-slim/pyproject.toml` to allow for minimal testing
lint = ["mypy>=1.11.2", "pyright>=1.1.390", "ruff>=0.6.9"]
docs = [
    "black>=24.10.0",
    "mkdocs>=1.6.1",
    "mkdocs-glightbox>=0.4.0",
    "mkdocs-llmstxt>=0.2.0",
    "mkdocs-material[imaging]>=9.5.45",
    "mkdocstrings-python>=1.12.2",
]
docs-upload = ["algoliasearch>=4.12.0", "pydantic>=2.10.1"]

[tool.hatch.build.targets.wheel]
only-include = ["/README.md"]

[tool.hatch.build.targets.sdist]
include = ["/README.md", "/Makefile", "/tests"]

[tool.ruff]
line-length = 120
target-version = "py39"
include = [
    "pydantic_ai_slim/**/*.py",
    "pydantic_evals/**/*.py",
    "pydantic_graph/**/*.py",
    "mcp-run-python/**/*.py",
    "fasta2a/**/*.py",
    "examples/**/*.py",
    "clai/**/*.py",
    "tests/**/*.py",
    "docs/**/*.py",
]

[tool.ruff.lint]
extend-select = [
    "Q",
    "RUF100",
    "RUF018", # https://docs.astral.sh/ruff/rules/assignment-in-assert/
    "C90",
    "UP",
    "I",
    "D",
    "TID251",
]
flake8-quotes = { inline-quotes = "single", multiline-quotes = "double" }
isort = { combine-as-imports = true, known-first-party = ["pydantic_ai"] }
mccabe = { max-complexity = 15 }
ignore = [
    "D100", # ignore missing docstring in module
    "D102", # ignore missing docstring in public method
    "D104", # ignore missing docstring in public package
    "D105", # ignore missing docstring in magic methods
    "D107", # ignore missing docstring in __init__ methods
]

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.flake8-tidy-imports.banned-api]
"typing.TypedDict".msg = "Use typing_extensions.TypedDict instead."

[tool.ruff.format]
# don't format python in docstrings, pytest-examples takes care of it
docstring-code-format = false
quote-style = "single"

[tool.ruff.lint.per-file-ignores]
"mcp-run-python/**/*.py" = ["D", "TID251"]
"examples/**/*.py" = ["D101", "D103"]
"tests/**/*.py" = ["D"]
"docs/**/*.py" = ["D"]

[tool.pyright]
pythonVersion = "3.12"
typeCheckingMode = "strict"
reportMissingTypeStubs = false
reportUnnecessaryIsInstance = false
reportUnnecessaryTypeIgnoreComment = true
reportMissingModuleSource = false
include = [
    "pydantic_ai_slim",
    "pydantic_evals",
    "pydantic_graph",
    "mcp-run-python",
    "fasta2a",
    "tests",
    "examples",
    "clai",
]
venvPath = '.'
venv = ".venv"
# see https://github.com/microsoft/pyright/issues/7771 - we don't want to error on decorated functions in tests
# which are not otherwise used
executionEnvironments = [
    { root = "tests", reportUnusedFunction = false, reportPrivateImportUsage = false },
]
exclude = [
    "examples/pydantic_ai_examples/weather_agent_gradio.py",
    "mcp-run-python/node_modules",
]
extraPaths = ["mcp-run-python/stubs"]

[tool.mypy]
files = "tests/typed_agent.py,tests/typed_graph.py"
strict = true

[tool.pytest.ini_options]
testpaths = "tests"
xfail_strict = true
filterwarnings = [
    "error",
    # Issue with python-multipart - we don't want to bump the minimum version of starlette.
    "ignore::PendingDeprecationWarning:starlette",
    # boto3
    "ignore::DeprecationWarning:botocore.*",
    "ignore::RuntimeWarning:pydantic_ai.mcp",
    # uvicorn (mcp server)
    "ignore:websockets.legacy is deprecated.*:DeprecationWarning:websockets.legacy",
    "ignore:websockets.server.WebSocketServerProtocol is deprecated:DeprecationWarning",
    # random resource warnings; I suspect these are coming from vendor SDKs when running examples..
    "ignore:unclosed <socket:ResourceWarning",
    "ignore:unclosed event loop:ResourceWarning",
]

# https://coverage.readthedocs.io/en/latest/config.html#run
[tool.coverage.run]
# required to avoid warnings about files created by create_module fixture
include = [
    "pydantic_ai_slim/**/*.py",
    "pydantic_evals/**/*.py",
    "pydantic_graph/**/*.py",
    # TODO(Marcelo): Add 100% coverage for A2A.
    # "fasta2a/**/*.py",
    "tests/**/*.py",
]
omit = ["tests/test_live.py", "tests/example_modules/*.py"]
branch = true

# https://coverage.readthedocs.io/en/latest/config.html#report
[tool.coverage.report]
skip_covered = true
show_missing = true
ignore_errors = true
precision = 2
exclude_lines = [
    # `# pragma: no cover` is standard marker for code that's not covered, this will error if code is covered
    'pragma: no cover',
    # use `# pragma: lax no cover` if you want to ignore cases where (some of) the code is covered
    'pragma: lax no cover',
    'raise NotImplementedError',
    'if TYPE_CHECKING:',
    'if typing.TYPE_CHECKING:',
    '@overload',
    '@deprecated',
    '@typing.overload',
    '@abstractmethod',
    '\(Protocol\):$',
    'typing.assert_never',
    '$\s*assert_never\(',
    'if __name__ == .__main__.:',
    'except ImportError as _import_error:',
    '$\s*pass$',
]

[tool.logfire]
ignore_no_config = true

[tool.inline-snapshot]
format-command = "ruff format --stdin-filename {filename}"

[tool.inline-snapshot.shortcuts]
snap-fix = ["create", "fix"]
snap = ["create"]

[tool.codespell]
# Ref: https://github.com/codespell-project/codespell#using-a-config-file
skip = '.git*,*.svg,*.lock,*.css,*.yaml'
check-hidden = true
# Ignore "formatting" like **L**anguage
ignore-regex = '\*\*[A-Z]\*\*[a-z]+\b'
ignore-words-list = 'asend'
