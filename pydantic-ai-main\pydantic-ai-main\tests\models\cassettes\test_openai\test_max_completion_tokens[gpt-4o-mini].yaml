interactions:
- request:
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '119'
      content-type:
      - application/json
      host:
      - api.openai.com
    method: POST
    parsed_body:
      max_completion_tokens: 100
      messages:
      - content: hello
        role: user
      model: gpt-4o-mini
      n: 1
      stream: false
    uri: https://api.openai.com/v1/chat/completions
  response:
    headers:
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      connection:
      - keep-alive
      content-length:
      - '840'
      content-type:
      - application/json
      openai-organization:
      - pydantic-28gund
      openai-processing-ms:
      - '278'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      transfer-encoding:
      - chunked
    parsed_body:
      choices:
      - finish_reason: stop
        index: 0
        logprobs: null
        message:
          annotations: []
          content: Hello! How can I assist you today?
          refusal: null
          role: assistant
      created: 1742636224
      id: chatcmpl-BDp<PERSON>oxw4i90ZesN8iyrwLmGWRJ5lz
      model: gpt-4o-mini-2024-07-18
      object: chat.completion
      service_tier: default
      system_fingerprint: fp_b8bc95a0ac
      usage:
        completion_tokens: 10
        completion_tokens_details:
          accepted_prediction_tokens: 0
          audio_tokens: 0
          reasoning_tokens: 0
          rejected_prediction_tokens: 0
        prompt_tokens: 8
        prompt_tokens_details:
          audio_tokens: 0
          cached_tokens: 0
        total_tokens: 18
    status:
      code: 200
      message: OK
version: 1
