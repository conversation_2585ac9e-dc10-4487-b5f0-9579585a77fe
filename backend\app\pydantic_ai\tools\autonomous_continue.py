"""
Autonomous Continue Tools for Pydantic AI - Phase 8C

Tools that provide the coding agent with autonomous development loop capabilities
including session management, progress monitoring, and intelligent error handling.
"""

import logging
from typing import List, Dict, Optional, Any, Union, TYPE_CHECKING
from pydantic import BaseModel, Field
import logfire

if TYPE_CHECKING:
    from pydantic_ai import RunContext
    from ..dependencies import CodingDependencies

# Import Autonomous Continue components
try:
    from ...autonomous_continue import (
        AutonomousContinueEngine, ContinueConfig, SafetyLimits,
        ContextPerspective, autonomous_continue_engine
    )
except ImportError:
    # Fallback for testing
    AutonomousContinueEngine = None
    ContinueConfig = None
    SafetyLimits = None
    ContextPerspective = None
    autonomous_continue_engine = None

logger = logging.getLogger(__name__)


class ContinueSessionRequest(BaseModel):
    """Request model for starting a continue session."""
    session_name: str = Field(..., description="Name for the continue session")
    initial_task: str = Field(..., description="Initial task to work on")
    max_iterations: int = Field(default=50, description="Maximum number of iterations")
    max_duration_hours: float = Field(default=2.0, description="Maximum duration in hours")
    auto_commit: bool = Field(default=False, description="Auto-commit successful iterations")
    enable_error_learning: bool = Field(default=True, description="Enable error pattern learning")
    use_code_intelligence: bool = Field(default=True, description="Use Code Intelligence Hub")


async def start_autonomous_session(
    ctx: "RunContext[CodingDependencies]",
    session_name: str,
    initial_task: str,
    max_iterations: int = 50,
    max_duration_hours: float = 2.0,
    auto_commit: bool = False,
    enable_error_learning: bool = True,
    use_code_intelligence: bool = True
) -> Dict[str, Any]:
    """
    Start an autonomous continue session for progressive development.
    
    This tool starts an autonomous development loop that will continuously
    work on the specified task, learning from errors and adapting its approach
    for better results over multiple iterations.
    
    Args:
        ctx: Run context with dependencies
        session_name: Name for the continue session
        initial_task: Initial task description to work on
        max_iterations: Maximum number of iterations (default: 50)
        max_duration_hours: Maximum duration in hours (default: 2.0)
        auto_commit: Whether to auto-commit successful iterations
        enable_error_learning: Enable intelligent error pattern learning
        use_code_intelligence: Use Code Intelligence Hub for context
        
    Returns:
        Dictionary with session start result and session ID
    """
    with logfire.span("start_autonomous_session", session_name=session_name):
        try:
            if not autonomous_continue_engine:
                return {
                    'status': 'error',
                    'error': 'Autonomous Continue Engine not available'
                }
            
            logger.info(f"🚀 Starting autonomous session: {session_name}")
            
            # Create configuration
            from datetime import timedelta
            
            safety_limits = SafetyLimits(
                max_iterations=max_iterations,
                max_duration=timedelta(hours=max_duration_hours)
            )
            
            config = ContinueConfig(
                session_name=session_name,
                initial_task=initial_task,
                workspace_root=str(ctx.deps.workspace_root) if hasattr(ctx, 'deps') else "/workspace",
                safety_limits=safety_limits,
                auto_commit=auto_commit,
                enable_error_learning=enable_error_learning,
                use_code_intelligence=use_code_intelligence
            )
            
            # Initialize engine if needed
            init_result = await autonomous_continue_engine.initialize()
            if init_result['status'] != 'success':
                return init_result
            
            # Start session
            result = await autonomous_continue_engine.start_autonomous_session(config)
            
            logfire.info("Autonomous session started", 
                        session_name=session_name,
                        session_id=result.get('session_id'),
                        status=result['status'])
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to start autonomous session: {e}")
            logfire.error("Autonomous session start failed", error=str(e))
            return {
                'status': 'error',
                'error': str(e)
            }


async def pause_autonomous_session(
    ctx: "RunContext[CodingDependencies]",
    session_id: str
) -> Dict[str, Any]:
    """
    Pause an active autonomous continue session.
    
    This tool pauses an autonomous development session, allowing you to
    temporarily stop the loop while preserving the session state.
    
    Args:
        ctx: Run context with dependencies
        session_id: ID of the session to pause
        
    Returns:
        Dictionary with pause result
    """
    with logfire.span("pause_autonomous_session", session_id=session_id):
        try:
            if not autonomous_continue_engine:
                return {
                    'status': 'error',
                    'error': 'Autonomous Continue Engine not available'
                }
            
            logger.info(f"⏸️ Pausing autonomous session: {session_id}")
            
            result = await autonomous_continue_engine.pause_session(session_id)
            
            logfire.info("Autonomous session paused", 
                        session_id=session_id,
                        status=result['status'])
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to pause session: {e}")
            logfire.error("Session pause failed", error=str(e), session_id=session_id)
            return {
                'status': 'error',
                'error': str(e)
            }


async def resume_autonomous_session(
    ctx: "RunContext[CodingDependencies]",
    session_id: str
) -> Dict[str, Any]:
    """
    Resume a paused autonomous continue session.
    
    This tool resumes a previously paused autonomous development session,
    continuing the loop from where it left off.
    
    Args:
        ctx: Run context with dependencies
        session_id: ID of the session to resume
        
    Returns:
        Dictionary with resume result
    """
    with logfire.span("resume_autonomous_session", session_id=session_id):
        try:
            if not autonomous_continue_engine:
                return {
                    'status': 'error',
                    'error': 'Autonomous Continue Engine not available'
                }
            
            logger.info(f"▶️ Resuming autonomous session: {session_id}")
            
            result = await autonomous_continue_engine.resume_session(session_id)
            
            logfire.info("Autonomous session resumed", 
                        session_id=session_id,
                        status=result['status'])
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to resume session: {e}")
            logfire.error("Session resume failed", error=str(e), session_id=session_id)
            return {
                'status': 'error',
                'error': str(e)
            }


async def stop_autonomous_session(
    ctx: "RunContext[CodingDependencies]",
    session_id: str
) -> Dict[str, Any]:
    """
    Stop an autonomous continue session.
    
    This tool stops an autonomous development session, ending the loop
    and finalizing the session results.
    
    Args:
        ctx: Run context with dependencies
        session_id: ID of the session to stop
        
    Returns:
        Dictionary with stop result
    """
    with logfire.span("stop_autonomous_session", session_id=session_id):
        try:
            if not autonomous_continue_engine:
                return {
                    'status': 'error',
                    'error': 'Autonomous Continue Engine not available'
                }
            
            logger.info(f"⏹️ Stopping autonomous session: {session_id}")
            
            result = await autonomous_continue_engine.stop_session(session_id)
            
            logfire.info("Autonomous session stopped", 
                        session_id=session_id,
                        status=result['status'])
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to stop session: {e}")
            logfire.error("Session stop failed", error=str(e), session_id=session_id)
            return {
                'status': 'error',
                'error': str(e)
            }


async def get_autonomous_session_status(
    ctx: "RunContext[CodingDependencies]",
    session_id: str
) -> Dict[str, Any]:
    """
    Get the status and progress of an autonomous continue session.
    
    This tool provides detailed information about a running or completed
    autonomous development session including progress, statistics, and
    current state.
    
    Args:
        ctx: Run context with dependencies
        session_id: ID of the session to check
        
    Returns:
        Dictionary with session status and progress information
    """
    with logfire.span("get_autonomous_session_status", session_id=session_id):
        try:
            if not autonomous_continue_engine:
                return {
                    'status': 'error',
                    'error': 'Autonomous Continue Engine not available'
                }
            
            logger.debug(f"📊 Getting status for session: {session_id}")
            
            result = await autonomous_continue_engine.get_session_status(session_id)
            
            logfire.info("Session status retrieved", 
                        session_id=session_id,
                        status=result['status'])
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to get session status: {e}")
            logfire.error("Session status failed", error=str(e), session_id=session_id)
            return {
                'status': 'error',
                'error': str(e)
            }


async def get_autonomous_system_statistics(
    ctx: "RunContext[CodingDependencies]"
) -> Dict[str, Any]:
    """
    Get comprehensive statistics about the autonomous continue system.
    
    This tool provides insights into the autonomous development system
    including error patterns, context adaptation effectiveness, and
    overall system performance.
    
    Args:
        ctx: Run context with dependencies
        
    Returns:
        Dictionary with comprehensive system statistics
    """
    with logfire.span("get_autonomous_system_statistics"):
        try:
            if not autonomous_continue_engine:
                return {
                    'status': 'error',
                    'error': 'Autonomous Continue Engine not available'
                }
            
            logger.info("📊 Getting autonomous system statistics")
            
            result = await autonomous_continue_engine.get_system_statistics()
            
            logfire.info("System statistics retrieved", 
                        status=result['status'])
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to get system statistics: {e}")
            logfire.error("System statistics failed", error=str(e))
            return {
                'status': 'error',
                'error': str(e)
            }


async def suggest_context_perspective(
    ctx: "RunContext[CodingDependencies]",
    task_description: str,
    current_perspective: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get context perspective recommendations for a task.
    
    This tool analyzes a task description and suggests the most effective
    context perspective for approaching the work, based on learned patterns
    and task characteristics.
    
    Args:
        ctx: Run context with dependencies
        task_description: Description of the task to analyze
        current_perspective: Current perspective being used (optional)
        
    Returns:
        Dictionary with perspective recommendations and reasoning
    """
    with logfire.span("suggest_context_perspective", task=task_description):
        try:
            if not autonomous_continue_engine:
                return {
                    'status': 'error',
                    'error': 'Autonomous Continue Engine not available'
                }
            
            logger.debug(f"🎯 Getting perspective suggestions for: {task_description}")
            
            # Get recommendations from context adapter
            recommendations = autonomous_continue_engine.context_adapter.get_perspective_recommendations(
                task_description
            )
            
            # Format recommendations
            formatted_recommendations = []
            for perspective, confidence in recommendations[:3]:  # Top 3
                formatted_recommendations.append({
                    'perspective': perspective.value,
                    'confidence': confidence,
                    'description': autonomous_continue_engine.context_adapter.perspective_characteristics[perspective]['focus'],
                    'strengths': autonomous_continue_engine.context_adapter.perspective_characteristics[perspective]['strengths']
                })
            
            result = {
                'status': 'success',
                'task_description': task_description,
                'current_perspective': current_perspective,
                'recommendations': formatted_recommendations,
                'top_recommendation': formatted_recommendations[0] if formatted_recommendations else None
            }
            
            logfire.info("Context perspective suggestions generated", 
                        task=task_description,
                        top_perspective=result.get('top_recommendation', {}).get('perspective'))
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to suggest context perspective: {e}")
            logfire.error("Context perspective suggestion failed", error=str(e))
            return {
                'status': 'error',
                'error': str(e)
            }


async def analyze_error_patterns(
    ctx: "RunContext[CodingDependencies]",
    session_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Analyze error patterns from autonomous sessions.
    
    This tool provides insights into error patterns detected during
    autonomous development, including resolution strategies and
    learning outcomes.
    
    Args:
        ctx: Run context with dependencies
        session_id: Specific session to analyze (optional, analyzes all if not provided)
        
    Returns:
        Dictionary with error pattern analysis and insights
    """
    with logfire.span("analyze_error_patterns", session_id=session_id):
        try:
            if not autonomous_continue_engine:
                return {
                    'status': 'error',
                    'error': 'Autonomous Continue Engine not available'
                }
            
            logger.info(f"🔍 Analyzing error patterns for session: {session_id or 'all'}")
            
            # Get error statistics
            error_stats = autonomous_continue_engine.error_intelligence.get_error_statistics()
            
            # Get specific session errors if requested
            session_errors = None
            if session_id and session_id in autonomous_continue_engine.active_sessions:
                session = autonomous_continue_engine.active_sessions[session_id]
                session_errors = [
                    {
                        'pattern_id': pattern.pattern_id,
                        'error_type': pattern.error_type,
                        'severity': pattern.severity.value,
                        'occurrence_count': pattern.occurrence_count,
                        'successful_resolutions': pattern.successful_resolutions,
                        'failed_resolutions': pattern.failed_resolutions
                    }
                    for pattern in session.error_patterns
                ]
            
            result = {
                'status': 'success',
                'session_id': session_id,
                'global_error_statistics': error_stats,
                'session_specific_errors': session_errors,
                'insights': {
                    'most_common_error_type': max(error_stats.get('type_distribution', {}).items(), 
                                                 key=lambda x: x[1], default=('none', 0))[0],
                    'resolution_success_rate': len([p for p in autonomous_continue_engine.error_intelligence.error_patterns.values() 
                                                   if p.successful_resolutions]) / max(len(autonomous_continue_engine.error_intelligence.error_patterns), 1) * 100
                }
            }
            
            logfire.info("Error pattern analysis completed", 
                        session_id=session_id,
                        total_patterns=error_stats.get('total_patterns', 0))
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to analyze error patterns: {e}")
            logfire.error("Error pattern analysis failed", error=str(e))
            return {
                'status': 'error',
                'error': str(e)
            }
