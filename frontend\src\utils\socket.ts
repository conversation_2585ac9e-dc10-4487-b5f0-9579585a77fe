// frontend/src/utils/socket.ts
import { io, Socket } from 'socket.io-client';

// Get backend URL from environment or default to localhost
const BACKEND_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:8000';

// Create socket instance
export const socket: Socket = io(BACKEND_URL, {
  autoConnect: true,
  reconnection: true,
  reconnectionDelay: 1000,
  reconnectionAttempts: 5,
  timeout: 20000,
});

// Socket event types
export interface LogEvent {
  session_id: string;
  message: string;
  level: 'info' | 'warning' | 'error';
  timestamp: number;
}

export interface TerminalOutputEvent {
  session_id: string;
  output: string;
  timestamp: number;
}

export interface ConnectedEvent {
  message: string;
}

export interface JoinedSessionEvent {
  session_id: string;
}

// Socket event handlers
export const socketEvents = {
  // Connection events
  onConnect: (callback: () => void) => {
    socket.on('connect', callback);
  },
  
  onDisconnect: (callback: () => void) => {
    socket.on('disconnect', callback);
  },
  
  onConnected: (callback: (data: ConnectedEvent) => void) => {
    socket.on('connected', callback);
  },
  
  // Session events
  onJoinedSession: (callback: (data: JoinedSessionEvent) => void) => {
    socket.on('joined_session', callback);
  },
  
  // Log events
  onLog: (callback: (data: LogEvent) => void) => {
    socket.on('log', callback);
  },
  
  // Terminal events
  onTerminalOutput: (callback: (data: TerminalOutputEvent) => void) => {
    socket.on('terminal_output', callback);
  },
  
  // Cleanup
  off: (event: string, callback?: (...args: any[]) => void) => {
    socket.off(event, callback);
  },
};

// Socket actions
export const socketActions = {
  // Join a session to receive session-specific events
  joinSession: (sessionId: string) => {
    socket.emit('join_session', { session_id: sessionId });
  },
  
  // Leave a session
  leaveSession: (sessionId: string) => {
    socket.emit('leave_session', { session_id: sessionId });
  },
};

// Connection status
export const getConnectionStatus = () => socket.connected;

// Export socket instance as default
export default socket;
