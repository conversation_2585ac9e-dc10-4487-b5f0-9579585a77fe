repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
      - id: no-commit-to-branch # prevent direct commits to the `main` branch
      - id: check-yaml
        args: ["--unsafe"]
      - id: check-toml
      - id: end-of-file-fixer
      - id: trailing-whitespace

  - repo: https://github.com/sirosen/texthooks
    rev: 0.6.8
    hooks:
      - id: fix-smartquotes
      - id: fix-spaces
      - id: fix-ligatures

  - repo: local
    hooks:
      - id: format
        name: Format
        entry: make
        args: [format]
        language: system
        types: [python]
        pass_filenames: false
      - id: lint
        name: Lint
        entry: make
        args: [lint]
        types: [python]
        language: system
        pass_filenames: false
      - id: lint-js
        name: Lint JS
        entry: make
        args: [lint-js]
        language: system
        types_or: [javascript, ts, json]
        files: '^mcp-run-python/'
        pass_filenames: false
      - id: clai-help
        name: clai help output
        entry: uv
        args: [run, pytest, 'clai/update_readme.py']
        language: system
        types_or: [python, markdown]
        pass_filenames: false
      - id: typecheck
        name: Typecheck
        entry: make
        args: [typecheck]
        language: system
        types: [python]
        pass_filenames: false

  - repo: https://github.com/codespell-project/codespell
    # Configuration for codespell is in pyproject.toml
    rev: v2.3.0
    hooks:
    - id: codespell
      args: ['--skip', 'tests/models/cassettes/*,docs/a2a/fasta2a.md']
      additional_dependencies:
      - tomli
