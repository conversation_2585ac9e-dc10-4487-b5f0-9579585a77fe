# Development-specific requirements (lighter than full Docker requirements)
# Core FastAPI and dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP and async
httpx==0.25.2
aiofiles==23.2.1
requests==2.31.0

# Vector database and embeddings
qdrant-client==1.7.0

# Code analysis and parsing
tree-sitter==0.20.4
tree-sitter-python==0.20.4
tree-sitter-javascript==0.20.2
tree-sitter-typescript==0.20.2
tree-sitter-java==0.20.2
tree-sitter-cpp==0.20.0
tree-sitter-go==0.20.0
tree-sitter-rust==0.20.4

# File operations and monitoring
watchdog==3.0.0
chardet==5.2.0

# Git operations
GitPython==3.1.40

# Development tools
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0

# Optional: For local Ollama client (if not using Docker)
# ollama==0.1.7
