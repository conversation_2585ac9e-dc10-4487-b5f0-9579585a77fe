# 🎂🚀 DeepNexus Development Requirements (Fast Setup)
# Core FastAPI and dependencies
fastapi==0.115.12
uvicorn[standard]==0.32.1
pydantic==2.10.4
pydantic-settings==2.7.0
python-multipart==0.0.20

# HTTP and async
httpx>=0.27.0,<0.28.0
aiofiles==23.2.1
requests==2.32.3

# Vector database and embeddings
qdrant-client==1.12.1

# AI Integration (simplified)
openai>=1.75.0
python-dotenv==1.0.1
tiktoken==0.8.0
tenacity==9.0.0

# System monitoring
psutil==6.1.0
structlog==24.4.0

# Code analysis and parsing
tree-sitter==0.20.4
tree-sitter-python==0.20.4
tree-sitter-javascript==0.20.2
tree-sitter-typescript==0.20.2
tree-sitter-java==0.20.2
tree-sitter-cpp==0.20.0
tree-sitter-go==0.20.0
tree-sitter-rust==0.20.4

# File operations and monitoring
watchdog==3.0.0
chardet==5.2.0

# Git operations
GitPython==3.1.40

# Development tools
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0

# Optional: For local Ollama client (if not using Docker)
# ollama==0.1.7
