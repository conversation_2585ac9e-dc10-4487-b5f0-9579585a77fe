"""
Loop Manager for Autonomous Continue Mode - Phase 8C

Manages the autonomous development loop with safety mechanisms,
progress tracking, and intelligent iteration control.
"""

import asyncio
import logging
import time
import psutil
from typing import Optional, Dict, Any, Callable, Awaitable
from datetime import datetime, timedelta
from pathlib import Path

from .models import (
    ContinueSession, LoopState, IterationResult, ProgressReport,
    ContinueConfig, SafetyLimits, CircuitBreakerState, ContextPerspective
)

logger = logging.getLogger(__name__)


class LoopManager:
    """
    Manages autonomous development loops with safety mechanisms.
    
    Provides configurable loop execution with:
    - Cycle counting and limits
    - Progress tracking and reporting
    - Pause/resume functionality
    - Safety mechanisms and circuit breakers
    - Resource monitoring
    """
    
    def __init__(self, config: ContinueConfig):
        """Initialize the loop manager."""
        self.config = config
        self.session: Optional[ContinueSession] = None
        self.circuit_breaker = CircuitBreakerState(
            failure_threshold=config.safety_limits.circuit_breaker_threshold
        )
        
        # State management
        self.is_running = False
        self.is_paused = False
        self.should_stop = False
        
        # Progress tracking
        self.start_time: Optional[datetime] = None
        self.last_progress_report: Optional[datetime] = None
        
        # Resource monitoring
        self.process = psutil.Process()
        
        # Callbacks
        self.progress_callback: Optional[Callable[[ProgressReport], Awaitable[None]]] = None
        self.iteration_callback: Optional[Callable[[IterationResult], Awaitable[None]]] = None
        self.error_callback: Optional[Callable[[Exception, IterationResult], Awaitable[None]]] = None
        
        logger.info(f"🔄 Loop manager initialized for session: {config.session_name}")
    
    def set_progress_callback(self, callback: Callable[[ProgressReport], Awaitable[None]]):
        """Set callback for progress updates."""
        self.progress_callback = callback
    
    def set_iteration_callback(self, callback: Callable[[IterationResult], Awaitable[None]]):
        """Set callback for iteration completion."""
        self.iteration_callback = callback
    
    def set_error_callback(self, callback: Callable[[Exception, IterationResult], Awaitable[None]]):
        """Set callback for error handling."""
        self.error_callback = callback
    
    async def start_session(self, initial_task: str) -> ContinueSession:
        """Start a new autonomous continue session."""
        try:
            # Create new session
            session_id = f"session_{int(time.time())}"
            self.session = ContinueSession(
                session_id=session_id,
                config=self.config,
                current_task=initial_task,
                state=LoopState.IDLE
            )
            
            # Initialize session state
            self.start_time = datetime.now()
            self.session.started_time = self.start_time
            self.session.state = LoopState.RUNNING
            self.is_running = True
            self.should_stop = False
            
            logger.info(f"🚀 Started autonomous continue session: {session_id}")
            
            # Save session state if configured
            if self.config.save_session_state:
                await self._save_session_state()
            
            return self.session
            
        except Exception as e:
            logger.error(f"❌ Failed to start session: {e}")
            raise
    
    async def run_loop(self, 
                      iteration_func: Callable[[str, ContextPerspective], Awaitable[IterationResult]]) -> ContinueSession:
        """
        Run the autonomous continue loop.
        
        Args:
            iteration_func: Function to execute for each iteration
            
        Returns:
            Completed session with results
        """
        if not self.session:
            raise ValueError("No active session. Call start_session() first.")
        
        try:
            logger.info(f"🔄 Starting autonomous loop for session: {self.session.session_id}")
            
            while self._should_continue():
                # Check for pause
                if self.is_paused:
                    await asyncio.sleep(1)
                    continue
                
                # Check circuit breaker
                if not self.circuit_breaker.can_attempt():
                    logger.warning("⚡ Circuit breaker is open, waiting...")
                    self.session.state = LoopState.CIRCUIT_BREAKER
                    await asyncio.sleep(30)  # Wait before retry
                    continue
                
                # Execute iteration
                await self._execute_iteration(iteration_func)
                
                # Generate progress report
                if self._should_generate_progress_report():
                    await self._generate_progress_report()
                
                # Check safety limits
                if self._check_safety_limits():
                    logger.warning("⚠️ Safety limits reached, stopping loop")
                    break
                
                # Brief pause between iterations
                await asyncio.sleep(1)
            
            # Complete session
            await self._complete_session()
            
            logger.info(f"✅ Autonomous loop completed for session: {self.session.session_id}")
            
            return self.session
            
        except Exception as e:
            logger.error(f"❌ Loop execution failed: {e}")
            if self.session:
                self.session.state = LoopState.ERROR
            raise
        finally:
            self.is_running = False
    
    async def pause_session(self):
        """Pause the current session."""
        if self.session and self.is_running:
            self.is_paused = True
            self.session.state = LoopState.PAUSED
            logger.info(f"⏸️ Session paused: {self.session.session_id}")
    
    async def resume_session(self):
        """Resume the paused session."""
        if self.session and self.is_paused:
            self.is_paused = False
            self.session.state = LoopState.RUNNING
            logger.info(f"▶️ Session resumed: {self.session.session_id}")
    
    async def stop_session(self):
        """Stop the current session."""
        if self.session:
            self.should_stop = True
            self.session.state = LoopState.STOPPED
            logger.info(f"⏹️ Session stopped: {self.session.session_id}")
    
    async def emergency_stop(self):
        """Emergency stop with immediate termination."""
        if self.session:
            self.should_stop = True
            self.is_running = False
            self.session.state = LoopState.STOPPED
            logger.warning(f"🚨 Emergency stop activated for session: {self.session.session_id}")
    
    def get_current_progress(self) -> Optional[ProgressReport]:
        """Get current progress report."""
        if not self.session or not self.start_time:
            return None
        
        return self._create_progress_report()
    
    async def _execute_iteration(self, iteration_func: Callable[[str, ContextPerspective], Awaitable[IterationResult]]):
        """Execute a single iteration."""
        iteration_start = datetime.now()
        iteration_id = f"{self.session.session_id}_iter_{self.session.current_iteration + 1}"
        
        try:
            logger.debug(f"🔄 Executing iteration {self.session.current_iteration + 1}")
            
            # Execute the iteration
            result = await iteration_func(self.session.current_task, self.session.current_perspective)
            result.iteration_id = iteration_id
            result.iteration_number = self.session.current_iteration + 1
            result.start_time = iteration_start
            result.end_time = datetime.now()
            result.duration = result.end_time - result.start_time
            
            # Update session
            self.session.add_iteration_result(result)
            self.session.current_iteration += 1
            
            # Handle result
            if result.success:
                self.circuit_breaker.record_success()
                logger.info(f"✅ Iteration {result.iteration_number} completed successfully")
            else:
                self.circuit_breaker.record_failure()
                logger.warning(f"❌ Iteration {result.iteration_number} failed: {result.error_message}")
            
            # Call iteration callback
            if self.iteration_callback:
                await self.iteration_callback(result)
            
        except Exception as e:
            # Create error result
            error_result = IterationResult(
                iteration_id=iteration_id,
                iteration_number=self.session.current_iteration + 1,
                task_description=self.session.current_task,
                success=False,
                output="",
                error_message=str(e),
                start_time=iteration_start,
                end_time=datetime.now(),
                duration=datetime.now() - iteration_start,
                perspective_used=self.session.current_perspective
            )
            
            self.session.add_iteration_result(error_result)
            self.session.current_iteration += 1
            self.circuit_breaker.record_failure()
            
            logger.error(f"❌ Iteration {error_result.iteration_number} failed with exception: {e}")
            
            # Call error callback
            if self.error_callback:
                await self.error_callback(e, error_result)
    
    def _should_continue(self) -> bool:
        """Check if the loop should continue."""
        if not self.session or self.should_stop:
            return False
        
        # Check iteration limit
        if self.session.current_iteration >= self.config.safety_limits.max_iterations:
            logger.info(f"📊 Reached maximum iterations: {self.config.safety_limits.max_iterations}")
            return False
        
        # Check time limit
        if self.start_time:
            elapsed = datetime.now() - self.start_time
            if elapsed >= self.config.safety_limits.max_duration:
                logger.info(f"⏰ Reached maximum duration: {self.config.safety_limits.max_duration}")
                return False
        
        # Check consecutive errors
        if self.session.consecutive_errors >= self.config.safety_limits.max_errors:
            logger.warning(f"🚨 Too many consecutive errors: {self.session.consecutive_errors}")
            return False
        
        return True
    
    def _check_safety_limits(self) -> bool:
        """Check if safety limits have been reached."""
        try:
            # Check resource usage
            memory_percent = self.process.memory_percent()
            cpu_percent = self.process.cpu_percent()
            
            if memory_percent > self.config.safety_limits.resource_usage_limit * 100:
                logger.warning(f"⚠️ High memory usage: {memory_percent:.1f}%")
                return True
            
            if cpu_percent > self.config.safety_limits.resource_usage_limit * 100:
                logger.warning(f"⚠️ High CPU usage: {cpu_percent:.1f}%")
                return True
            
            return False
            
        except Exception as e:
            logger.debug(f"Resource monitoring failed: {e}")
            return False
    
    def _should_generate_progress_report(self) -> bool:
        """Check if a progress report should be generated."""
        if not self.last_progress_report:
            return True
        
        iterations_since_report = self.session.current_iteration % self.config.progress_reporting_interval
        return iterations_since_report == 0
    
    async def _generate_progress_report(self):
        """Generate and send progress report."""
        try:
            report = self._create_progress_report()
            self.last_progress_report = datetime.now()
            
            logger.info(f"📊 Progress: {report.iteration} iterations, {report.success_rate:.1f}% success rate")
            
            # Call progress callback
            if self.progress_callback:
                await self.progress_callback(report)
                
        except Exception as e:
            logger.error(f"Failed to generate progress report: {e}")
    
    def _create_progress_report(self) -> ProgressReport:
        """Create a progress report."""
        if not self.session or not self.start_time:
            raise ValueError("No active session")
        
        current_time = datetime.now()
        elapsed_time = current_time - self.start_time
        
        # Calculate metrics
        success_rate = self.session.get_success_rate()
        avg_iteration_time = self.session.get_average_iteration_time()
        
        # Estimate remaining time
        estimated_remaining = None
        if avg_iteration_time > 0 and self.session.current_iteration > 0:
            remaining_iterations = self.config.safety_limits.max_iterations - self.session.current_iteration
            estimated_remaining = timedelta(seconds=remaining_iterations * avg_iteration_time)
        
        # Get resource usage
        memory_usage = None
        cpu_usage = None
        try:
            memory_usage = self.process.memory_percent()
            cpu_usage = self.process.cpu_percent()
        except:
            pass
        
        # Get file changes
        files_modified = []
        files_created = []
        files_deleted = []
        
        for iteration in self.session.iterations:
            files_modified.extend(iteration.files_changed)
        
        return ProgressReport(
            session_id=self.session.session_id,
            iteration=self.session.current_iteration,
            state=self.session.state,
            start_time=self.start_time,
            current_time=current_time,
            elapsed_time=elapsed_time,
            estimated_remaining=estimated_remaining,
            tasks_completed=self.session.success_count,
            tasks_failed=self.session.error_count,
            errors_encountered=self.session.error_count,
            errors_resolved=self.session.success_count,  # Simplified
            current_task=self.session.current_task,
            current_perspective=self.session.current_perspective,
            files_modified=list(set(files_modified)),
            files_created=files_created,
            files_deleted=files_deleted,
            average_iteration_time=avg_iteration_time,
            success_rate=success_rate,
            memory_usage=memory_usage,
            cpu_usage=cpu_usage
        )
    
    async def _complete_session(self):
        """Complete the current session."""
        if self.session:
            self.session.state = LoopState.COMPLETED
            self.session.completed_time = datetime.now()
            
            # Save final session state
            if self.config.save_session_state:
                await self._save_session_state()
            
            logger.info(f"🎯 Session completed: {self.session.session_id}")
    
    async def _save_session_state(self):
        """Save session state to disk."""
        try:
            if not self.session:
                return
            
            # Create sessions directory
            sessions_dir = Path(self.config.workspace_root) / ".deepnexus" / "sessions"
            sessions_dir.mkdir(parents=True, exist_ok=True)
            
            # Save session data
            session_file = sessions_dir / f"{self.session.session_id}.json"
            with open(session_file, 'w') as f:
                f.write(self.session.model_dump_json(indent=2))
            
            logger.debug(f"💾 Session state saved: {session_file}")
            
        except Exception as e:
            logger.error(f"Failed to save session state: {e}")
    
    async def load_session_state(self, session_id: str) -> Optional[ContinueSession]:
        """Load session state from disk."""
        try:
            sessions_dir = Path(self.config.workspace_root) / ".deepnexus" / "sessions"
            session_file = sessions_dir / f"{session_id}.json"
            
            if session_file.exists():
                with open(session_file, 'r') as f:
                    session_data = f.read()
                
                self.session = ContinueSession.model_validate_json(session_data)
                logger.info(f"📂 Session state loaded: {session_id}")
                return self.session
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to load session state: {e}")
            return None
