"""
Image Processing Utilities for Vision Analysis

This module provides utilities for handling images in the vision analysis system,
including base64 encoding, image validation, and format conversion.
"""

import base64
import logging
from pathlib import Path
from typing import Optional, Tuple, Dict, Any
from io import BytesIO
import asyncio

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    logging.warning("PIL not available - some image processing features will be limited")

try:
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    logging.warning("Playwright not available - screenshot capture will be limited")

logger = logging.getLogger(__name__)


def encode_image_to_base64(image_path: str) -> Optional[str]:
    """
    Encode an image file to base64 string.
    
    Args:
        image_path: Path to the image file
        
    Returns:
        Base64 encoded string or None if failed
    """
    try:
        image_file = Path(image_path)
        if not image_file.exists():
            logger.error(f"Image file not found: {image_path}")
            return None
        
        with open(image_file, "rb") as f:
            image_data = f.read()
        
        return base64.b64encode(image_data).decode('utf-8')
        
    except Exception as e:
        logger.error(f"Failed to encode image {image_path}: {e}")
        return None


def create_data_url(image_path: str, mime_type: Optional[str] = None) -> Optional[str]:
    """
    Create a data URL from an image file.
    
    Args:
        image_path: Path to the image file
        mime_type: MIME type (auto-detected if None)
        
    Returns:
        Data URL string or None if failed
    """
    try:
        image_file = Path(image_path)
        if not image_file.exists():
            logger.error(f"Image file not found: {image_path}")
            return None
        
        # Auto-detect MIME type if not provided
        if mime_type is None:
            extension = image_file.suffix.lower()
            mime_types = {
                '.jpg': 'image/jpeg',
                '.jpeg': 'image/jpeg',
                '.png': 'image/png',
                '.gif': 'image/gif',
                '.bmp': 'image/bmp',
                '.webp': 'image/webp'
            }
            mime_type = mime_types.get(extension, 'image/jpeg')
        
        # Encode image
        base64_data = encode_image_to_base64(image_path)
        if base64_data is None:
            return None
        
        return f"data:{mime_type};base64,{base64_data}"
        
    except Exception as e:
        logger.error(f"Failed to create data URL for {image_path}: {e}")
        return None


def get_image_info(image_path: str) -> Dict[str, Any]:
    """
    Get information about an image file.
    
    Args:
        image_path: Path to the image file
        
    Returns:
        Dictionary with image information
    """
    try:
        image_file = Path(image_path)
        if not image_file.exists():
            return {
                "status": "error",
                "error": f"Image file not found: {image_path}",
                "exists": False
            }
        
        # Basic file info
        file_size = image_file.stat().st_size
        file_extension = image_file.suffix.lower()
        
        info = {
            "status": "success",
            "path": str(image_file),
            "exists": True,
            "size_bytes": file_size,
            "size_mb": round(file_size / (1024 * 1024), 2),
            "extension": file_extension
        }
        
        # Try to get image dimensions if PIL is available
        if PIL_AVAILABLE:
            try:
                with Image.open(image_file) as img:
                    info.update({
                        "width": img.width,
                        "height": img.height,
                        "format": img.format,
                        "mode": img.mode,
                        "has_transparency": img.mode in ('RGBA', 'LA') or 'transparency' in img.info
                    })
            except Exception as e:
                info["pil_error"] = str(e)
        else:
            info["pil_available"] = False
        
        return info
        
    except Exception as e:
        logger.error(f"Failed to get image info for {image_path}: {e}")
        return {
            "status": "error",
            "error": str(e),
            "exists": False
        }


def validate_image_file(image_path: str) -> Tuple[bool, str]:
    """
    Validate if a file is a valid image.
    
    Args:
        image_path: Path to the image file
        
    Returns:
        Tuple of (is_valid, message)
    """
    try:
        image_file = Path(image_path)
        
        # Check if file exists
        if not image_file.exists():
            return False, f"File not found: {image_path}"
        
        # Check file extension
        valid_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
        if image_file.suffix.lower() not in valid_extensions:
            return False, f"Unsupported file extension: {image_file.suffix}"
        
        # Check file size (max 10MB)
        file_size = image_file.stat().st_size
        max_size = 10 * 1024 * 1024  # 10MB
        if file_size > max_size:
            return False, f"File too large: {file_size / (1024*1024):.1f}MB (max 10MB)"
        
        # Try to open with PIL if available
        if PIL_AVAILABLE:
            try:
                with Image.open(image_file) as img:
                    # Verify it's a valid image
                    img.verify()
                return True, "Valid image file"
            except Exception as e:
                return False, f"Invalid image file: {e}"
        else:
            # Basic validation without PIL
            return True, "File appears to be an image (PIL not available for full validation)"
        
    except Exception as e:
        logger.error(f"Failed to validate image {image_path}: {e}")
        return False, f"Validation error: {e}"


def resize_image_if_needed(image_path: str, max_width: int = 1024, max_height: int = 1024) -> Optional[str]:
    """
    Resize an image if it's too large, save to temporary file.
    
    Args:
        image_path: Path to the original image
        max_width: Maximum width in pixels
        max_height: Maximum height in pixels
        
    Returns:
        Path to resized image or original path if no resize needed
    """
    if not PIL_AVAILABLE:
        logger.warning("PIL not available - cannot resize images")
        return image_path
    
    try:
        image_file = Path(image_path)
        if not image_file.exists():
            return None
        
        with Image.open(image_file) as img:
            # Check if resize is needed
            if img.width <= max_width and img.height <= max_height:
                return image_path
            
            # Calculate new size maintaining aspect ratio
            ratio = min(max_width / img.width, max_height / img.height)
            new_width = int(img.width * ratio)
            new_height = int(img.height * ratio)
            
            # Resize image
            resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Save to temporary file
            temp_path = image_file.parent / f"{image_file.stem}_resized{image_file.suffix}"
            resized_img.save(temp_path, format=img.format)
            
            logger.info(f"Resized image from {img.width}x{img.height} to {new_width}x{new_height}")
            return str(temp_path)
        
    except Exception as e:
        logger.error(f"Failed to resize image {image_path}: {e}")
        return image_path


def create_thumbnail(image_path: str, size: Tuple[int, int] = (200, 200)) -> Optional[str]:
    """
    Create a thumbnail of an image.
    
    Args:
        image_path: Path to the original image
        size: Thumbnail size as (width, height)
        
    Returns:
        Path to thumbnail or None if failed
    """
    if not PIL_AVAILABLE:
        logger.warning("PIL not available - cannot create thumbnails")
        return None
    
    try:
        image_file = Path(image_path)
        if not image_file.exists():
            return None
        
        with Image.open(image_file) as img:
            # Create thumbnail
            img.thumbnail(size, Image.Resampling.LANCZOS)
            
            # Save thumbnail
            thumb_path = image_file.parent / f"{image_file.stem}_thumb{image_file.suffix}"
            img.save(thumb_path, format=img.format)
            
            logger.info(f"Created thumbnail: {thumb_path}")
            return str(thumb_path)
        
    except Exception as e:
        logger.error(f"Failed to create thumbnail for {image_path}: {e}")
        return None


def extract_image_from_data_url(data_url: str) -> Optional[bytes]:
    """
    Extract image data from a data URL.

    Args:
        data_url: Data URL string

    Returns:
        Image data as bytes or None if failed
    """
    try:
        if not data_url.startswith('data:image/'):
            return None

        # Extract base64 data
        if ',' in data_url:
            base64_data = data_url.split(',', 1)[1]
        else:
            base64_data = data_url

        # Decode base64
        image_data = base64.b64decode(base64_data)
        return image_data

    except Exception as e:
        logger.error(f"Failed to extract image from data URL: {e}")
        return None


async def capture_screenshot(
    url: str,
    output_path: str,
    wait_time: float = 2.0,
    viewport_width: int = 1280,
    viewport_height: int = 720,
    full_page: bool = False
) -> Dict[str, Any]:
    """
    Capture a screenshot of a web page using Playwright.

    Args:
        url: URL to capture
        output_path: Path to save the screenshot
        wait_time: Time to wait for page load (seconds)
        viewport_width: Browser viewport width
        viewport_height: Browser viewport height
        full_page: Whether to capture the full page or just viewport

    Returns:
        Dict with capture results
    """
    if not PLAYWRIGHT_AVAILABLE:
        return {
            "success": False,
            "error": "Playwright not available - cannot capture screenshots",
            "error_type": "DependencyError"
        }

    try:
        async with async_playwright() as p:
            # Launch browser
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context(
                viewport={'width': viewport_width, 'height': viewport_height}
            )
            page = await context.new_page()

            # Navigate to URL
            logger.info(f"Navigating to {url}")
            await page.goto(url, wait_until='networkidle', timeout=30000)

            # Wait for additional loading
            if wait_time > 0:
                await asyncio.sleep(wait_time)

            # Ensure output directory exists
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            # Capture screenshot
            logger.info(f"Capturing screenshot to {output_path}")
            await page.screenshot(
                path=output_path,
                full_page=full_page,
                type='png'
            )

            # Close browser
            await browser.close()

            # Verify screenshot was created
            if output_file.exists():
                file_size = output_file.stat().st_size
                return {
                    "success": True,
                    "output_path": output_path,
                    "file_size": file_size,
                    "url": url,
                    "viewport": f"{viewport_width}x{viewport_height}",
                    "full_page": full_page
                }
            else:
                return {
                    "success": False,
                    "error": "Screenshot file was not created",
                    "error_type": "FileError"
                }

    except Exception as e:
        logger.error(f"Failed to capture screenshot of {url}: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__
        }


# Export utility functions
__all__ = [
    'encode_image_to_base64',
    'create_data_url',
    'get_image_info',
    'validate_image_file',
    'resize_image_if_needed',
    'create_thumbnail',
    'extract_image_from_data_url',
    'capture_screenshot'
]
