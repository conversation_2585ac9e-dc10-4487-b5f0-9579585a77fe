name: Manual Docs Deploy

on:
  workflow_dispatch:

jobs:
  build-docs-manual:
    # Note: this should match the `docs` job in ci.yml
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true

      - run: uv sync --group docs

      # always build docs to check it works without insiders packages
      - run: make docs

      - run: make docs-insiders
        env:
          PPPR_TOKEN: ${{ secrets.PPPR_TOKEN }}

      - run: tree -sh site
      - uses: actions/setup-node@v4
      - run: npm install
        working-directory: docs-site
      - run: npm run typecheck
        working-directory: docs-site

      - name: Store docs
        uses: actions/upload-artifact@v4
        with:
          name: site
          path: site

  deploy-docs-manual:
    # Note: this should match the `deploy-docs` job in ci.yml
    needs: [build-docs-manual]
    runs-on: ubuntu-latest
    environment:
      name: deploy-docs
      url: https://ai.pydantic.dev

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
      - run: npm install
        working-directory: docs-site

      - uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true

      - uses: actions/download-artifact@v4
        with:
          name: site
          path: site

      - uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          workingDirectory: docs-site
          command: >
            deploy
            --var GIT_COMMIT_SHA:${{ github.sha }}
            --var GIT_BRANCH:main

      - run: uv sync --group docs-upload
      - run: uv run python docs/.hooks/algolia.py upload
        env:
          ALGOLIA_WRITE_API_KEY: ${{ secrets.ALGOLIA_WRITE_API_KEY }}
