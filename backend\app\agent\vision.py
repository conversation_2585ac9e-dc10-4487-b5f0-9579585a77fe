#!/usr/bin/env python3
"""
Vision Analysis Module for AI Coder Agent - Phase 6
Provides AI-powered visual analysis of web applications and UI components.

Features:
- Screenshot capture integration
- Vision model analysis using OpenRouter
- UI issue detection and reporting
- Visual feedback loop integration
- Accessibility analysis
- Layout and design assessment
"""

import asyncio
import base64
import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from concurrent.futures import ThreadPoolExecutor
import io
from PIL import Image

# Import Phase 2 LLM client
try:
    from ...llm_client import openrouter_client
except ImportError:
    from llm_client import openrouter_client

# Import screenshot utilities
from utils.screenshot_utils import screenshot_utils

logger = logging.getLogger(__name__)

class VisionAnalyzer:
    """AI-powered vision analysis for web applications."""
    
    def __init__(self, workspace_root: Union[str, Path] = "/app/workspace"):
        self.workspace_root = Path(workspace_root)
        self.executor = ThreadPoolExecutor(max_workers=2)
        
        # Analysis templates
        self.analysis_prompts = {
            'ui_issues': """
Analyze this screenshot of a web application and identify any UI/UX issues:

1. **Layout Issues**: Overlapping elements, misaligned components, spacing problems
2. **Accessibility Issues**: Poor contrast, missing alt text indicators, small click targets
3. **Responsive Design**: Elements that appear cut off or poorly sized
4. **Visual Hierarchy**: Unclear information hierarchy, poor typography
5. **User Experience**: Confusing navigation, unclear call-to-actions
6. **Performance Indicators**: Loading states, error messages, empty states

Provide specific, actionable feedback in JSON format:
{
    "issues": [
        {
            "type": "layout|accessibility|responsive|hierarchy|ux|performance",
            "severity": "low|medium|high|critical",
            "description": "Clear description of the issue",
            "location": "Where the issue is located",
            "suggestion": "How to fix the issue"
        }
    ],
    "overall_score": "1-10 rating",
    "summary": "Brief overall assessment"
}
""",
            
            'design_review': """
Review this web application screenshot for design quality and best practices:

1. **Visual Design**: Color scheme, typography, spacing, visual consistency
2. **Brand Alignment**: Professional appearance, brand consistency
3. **Modern Standards**: Current design trends, modern UI patterns
4. **Information Architecture**: Content organization, navigation clarity
5. **Interactive Elements**: Button design, form elements, hover states
6. **Mobile Considerations**: Touch-friendly design, responsive elements

Provide design feedback in JSON format:
{
    "design_score": "1-10 rating",
    "strengths": ["List of design strengths"],
    "improvements": [
        {
            "area": "Design area needing improvement",
            "current_state": "What you observe",
            "recommendation": "Specific improvement suggestion",
            "priority": "low|medium|high"
        }
    ],
    "overall_impression": "Summary of design quality"
}
""",
            
            'accessibility_audit': """
Perform an accessibility audit of this web application screenshot:

1. **Color Contrast**: Text readability, sufficient contrast ratios
2. **Text Size**: Readable font sizes, scalability
3. **Interactive Elements**: Button sizes, click target areas
4. **Visual Indicators**: Focus states, error indicators, status messages
5. **Content Structure**: Heading hierarchy, content organization
6. **Navigation**: Clear navigation patterns, breadcrumbs

Provide accessibility assessment in JSON format:
{
    "accessibility_score": "1-10 rating",
    "wcag_compliance": "estimated compliance level",
    "issues": [
        {
            "guideline": "WCAG guideline reference",
            "issue": "Accessibility issue description",
            "impact": "low|medium|high",
            "solution": "How to fix the issue"
        }
    ],
    "recommendations": ["General accessibility improvements"]
}
"""
        }
    
    def _validate_path(self, path: Union[str, Path]) -> Path:
        """Validate and resolve path within workspace."""
        path = Path(path).resolve()
        
        # Ensure path is within workspace
        try:
            path.relative_to(self.workspace_root.resolve())
        except ValueError:
            raise ValueError(f"Path {path} is outside workspace {self.workspace_root}")
        
        return path
    
    async def _encode_image_to_base64(self, image_path: Path) -> str:
        """Encode image to base64 for vision API."""
        try:
            def _encode():
                with open(image_path, 'rb') as image_file:
                    return base64.b64encode(image_file.read()).decode('utf-8')
            
            return await asyncio.get_event_loop().run_in_executor(self.executor, _encode)
            
        except Exception as e:
            logger.error(f"Failed to encode image {image_path}: {e}")
            raise
    
    async def analyze_screenshot(self, image_path: Union[str, Path],
                               analysis_type: str = 'ui_issues',
                               custom_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        Analyze a screenshot using AI vision models.
        
        Args:
            image_path: Path to screenshot image
            analysis_type: Type of analysis ('ui_issues', 'design_review', 'accessibility_audit')
            custom_prompt: Custom analysis prompt
            
        Returns:
            Dict with analysis results
        """
        try:
            image_path = self._validate_path(image_path)
            
            if not image_path.exists():
                return {
                    'status': 'error',
                    'error': f'Image file not found: {image_path}',
                    'error_type': 'FileNotFoundError'
                }
            
            # Encode image to base64
            image_base64 = await self._encode_image_to_base64(image_path)
            
            # Get analysis prompt
            if custom_prompt:
                prompt = custom_prompt
            elif analysis_type in self.analysis_prompts:
                prompt = self.analysis_prompts[analysis_type]
            else:
                return {
                    'status': 'error',
                    'error': f'Unknown analysis type: {analysis_type}',
                    'available_types': list(self.analysis_prompts.keys())
                }
            
            # Perform vision analysis
            start_time = time.time()
            
            vision_result = await openrouter_client.vision_analyze(
                image_base64=image_base64,
                prompt=prompt,
                model="google/gemini-2.0-flash-exp:free"
            )
            
            analysis_time = time.time() - start_time
            
            if vision_result['status'] != 'success':
                return {
                    'status': 'error',
                    'error': f"Vision analysis failed: {vision_result.get('error')}",
                    'vision_result': vision_result
                }
            
            # Try to parse JSON response
            analysis_content = vision_result.get('content', '')
            try:
                # Extract JSON from response if it's wrapped in text
                if '```json' in analysis_content:
                    json_start = analysis_content.find('```json') + 7
                    json_end = analysis_content.find('```', json_start)
                    json_content = analysis_content[json_start:json_end].strip()
                elif '{' in analysis_content and '}' in analysis_content:
                    json_start = analysis_content.find('{')
                    json_end = analysis_content.rfind('}') + 1
                    json_content = analysis_content[json_start:json_end]
                else:
                    json_content = analysis_content
                
                parsed_analysis = json.loads(json_content)
                
            except json.JSONDecodeError:
                # If JSON parsing fails, return raw content
                parsed_analysis = {
                    'raw_analysis': analysis_content,
                    'parsing_error': 'Failed to parse JSON response'
                }
            
            return {
                'status': 'success',
                'image_path': str(image_path),
                'analysis_type': analysis_type,
                'analysis': parsed_analysis,
                'raw_response': analysis_content,
                'analysis_time': analysis_time,
                'model_used': vision_result.get('model', 'unknown')
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze screenshot {image_path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__,
                'image_path': str(image_path) if image_path else None
            }
    
    async def capture_and_analyze(self, url: str,
                                project_path: Union[str, Path],
                                analysis_type: str = 'ui_issues',
                                screenshot_settings: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Capture screenshot and analyze it in one operation.
        
        Args:
            url: URL to capture and analyze
            project_path: Project directory path
            analysis_type: Type of analysis to perform
            screenshot_settings: Screenshot capture settings
            
        Returns:
            Dict with capture and analysis results
        """
        try:
            project_path = self._validate_path(project_path)
            
            # Create screenshots directory
            screenshots_dir = project_path / "screenshots"
            screenshots_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate screenshot filename
            timestamp = int(time.time())
            screenshot_filename = f"analysis_{analysis_type}_{timestamp}.png"
            screenshot_path = screenshots_dir / screenshot_filename
            
            # Capture screenshot
            capture_result = await screenshot_utils.capture_screenshot(
                url, screenshot_path, screenshot_settings
            )
            
            if capture_result['status'] != 'success':
                return {
                    'status': 'error',
                    'error': f"Screenshot capture failed: {capture_result.get('error')}",
                    'capture_result': capture_result
                }
            
            # Analyze screenshot
            analysis_result = await self.analyze_screenshot(
                screenshot_path, analysis_type
            )
            
            # Combine results
            return {
                'status': 'success',
                'url': url,
                'project_path': str(project_path),
                'screenshot': capture_result,
                'analysis': analysis_result,
                'combined_success': (capture_result['status'] == 'success' and 
                                   analysis_result['status'] == 'success')
            }
            
        except Exception as e:
            logger.error(f"Failed to capture and analyze {url}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__,
                'url': url
            }
    
    async def analyze_application_ui(self, project_path: Union[str, Path],
                                   analysis_types: List[str] = None) -> Dict[str, Any]:
        """
        Comprehensive UI analysis of a local web application.
        
        Args:
            project_path: Path to project directory
            analysis_types: List of analysis types to perform
            
        Returns:
            Dict with comprehensive analysis results
        """
        try:
            project_path = self._validate_path(project_path)
            
            if analysis_types is None:
                analysis_types = ['ui_issues', 'design_review', 'accessibility_audit']
            
            # Detect local server
            port = await screenshot_utils.detect_local_server_port(project_path)
            if not port:
                return {
                    'status': 'error',
                    'error': 'No local development server detected',
                    'project_path': str(project_path),
                    'suggestion': 'Start your development server first'
                }
            
            url = f"http://localhost:{port}"
            
            # Perform all analyses
            analysis_results = {}
            overall_success = True
            
            for analysis_type in analysis_types:
                result = await self.capture_and_analyze(
                    url, project_path, analysis_type
                )
                analysis_results[analysis_type] = result
                
                if result['status'] != 'success':
                    overall_success = False
            
            # Generate summary
            summary = self._generate_analysis_summary(analysis_results)
            
            return {
                'status': 'success' if overall_success else 'partial',
                'project_path': str(project_path),
                'url': url,
                'port': port,
                'analyses': analysis_results,
                'summary': summary,
                'total_analyses': len(analysis_types),
                'successful_analyses': sum(1 for r in analysis_results.values() 
                                         if r.get('status') == 'success')
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze application UI: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__,
                'project_path': str(project_path)
            }
    
    def _generate_analysis_summary(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary from multiple analysis results."""
        try:
            summary = {
                'overall_health': 'good',
                'critical_issues': 0,
                'total_issues': 0,
                'recommendations': [],
                'scores': {}
            }
            
            for analysis_type, result in analysis_results.items():
                if result.get('status') == 'success':
                    analysis_data = result.get('analysis', {}).get('analysis', {})
                    
                    # Extract scores
                    if 'overall_score' in analysis_data:
                        summary['scores'][analysis_type] = analysis_data['overall_score']
                    elif 'design_score' in analysis_data:
                        summary['scores'][analysis_type] = analysis_data['design_score']
                    elif 'accessibility_score' in analysis_data:
                        summary['scores'][analysis_type] = analysis_data['accessibility_score']
                    
                    # Count issues
                    if 'issues' in analysis_data:
                        issues = analysis_data['issues']
                        summary['total_issues'] += len(issues)
                        
                        for issue in issues:
                            if issue.get('severity') == 'critical' or issue.get('impact') == 'high':
                                summary['critical_issues'] += 1
                    
                    # Collect recommendations
                    if 'recommendations' in analysis_data:
                        summary['recommendations'].extend(analysis_data['recommendations'])
            
            # Determine overall health
            if summary['critical_issues'] > 0:
                summary['overall_health'] = 'poor'
            elif summary['total_issues'] > 5:
                summary['overall_health'] = 'fair'
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to generate analysis summary: {e}")
            return {'error': str(e)}
    
    def cleanup(self):
        """Clean up resources."""
        try:
            self.executor.shutdown(wait=False)
        except Exception as e:
            logger.error(f"Vision analyzer cleanup failed: {e}")


# Global instance
vision_analyzer = VisionAnalyzer()
