#!/usr/bin/env python3
"""
Phase 8A Vision Integration Test Suite

Tests the new vision-accessible coding agent functionality where the main
coding agent can call vision analysis tools for UI validation.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
if str(backend_dir) not in sys.path:
    sys.path.insert(0, str(backend_dir))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Test imports
try:
    from app.pydantic_ai.dependencies import create_dependencies
    from app.pydantic_ai.tools.vision_integration import (
        request_vision_analysis,
        request_vision_comparison,
        capture_and_analyze_ui
    )
    from app.pydantic_ai.image_utils import capture_screenshot
    print("✅ All Phase 8A imports successful!")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


async def test_vision_tool_registration():
    """Test that vision integration tools are properly registered with coding agent."""
    print("\n🔧 Testing Vision Tool Registration...")

    try:
        # Import tool registry to check registered tools
        from app.pydantic_ai.tools.registry import tool_registry, get_tool_summary

        # Get tool summary from registry
        tool_summary = get_tool_summary()
        coding_tool_names = tool_summary['coding_tools']
        vision_tool_names = tool_summary['vision_tools']
        total_tools = tool_summary['total_tools']

        print(f"📊 Found {len(coding_tool_names)} coding tools, {len(vision_tool_names)} vision tools")
        print(f"🔍 Total tools registered: {total_tools}")

        # Check for vision integration tools in coding tools
        expected_vision_integration_tools = [
            'request_vision_analysis',
            'request_vision_comparison',
            'capture_and_analyze_ui',
            'compare_screenshots_visual_diff',
            'validate_ui_patterns',
            'validate_accessibility_compliance',
            'create_visual_feedback_session',
            'setup_hot_reload_integration',
            'create_design_specification',
            'check_design_compliance'
        ]

        # Check if all expected vision integration tools are in coding tools
        missing_tools = [tool for tool in expected_vision_integration_tools if tool not in coding_tool_names]
        found_tools = [tool for tool in expected_vision_integration_tools if tool in coding_tool_names]

        print(f"✅ Vision integration tools found: {len(found_tools)}/{len(expected_vision_integration_tools)}")

        if missing_tools:
            print(f"❌ Missing vision integration tools in coding agent: {missing_tools}")
            return False

        print(f"✅ All {len(expected_vision_integration_tools)} vision integration tools found in coding agent!")

        # Check expected tool counts
        expected_coding_tools = 32  # 22 original + 10 new vision integration tools
        expected_vision_tools = 8   # Vision analysis tools

        if len(coding_tool_names) >= expected_coding_tools:
            print(f"✅ Coding tools count verification passed: {len(coding_tool_names)} >= {expected_coding_tools}")
        else:
            print(f"⚠️ Expected at least {expected_coding_tools} coding tools, found {len(coding_tool_names)}")

        if len(vision_tool_names) >= expected_vision_tools:
            print(f"✅ Vision tools count verification passed: {len(vision_tool_names)} >= {expected_vision_tools}")
        else:
            print(f"⚠️ Expected at least {expected_vision_tools} vision tools, found {len(vision_tool_names)}")

        return True

    except Exception as e:
        print(f"❌ Tool registration test failed: {e}")
        return False


async def test_dependencies_creation():
    """Test that dependencies can be created for vision integration."""
    print("\n🔧 Testing Dependencies Creation...")
    
    try:
        # Create dependencies
        deps = await create_dependencies(workspace_root=".")
        
        # Check required dependencies for vision integration
        required_attrs = [
            'file_operations',
            'openrouter_client',
            'workspace_root'
        ]
        
        missing_attrs = []
        for attr in required_attrs:
            if not hasattr(deps, attr):
                missing_attrs.append(attr)
        
        if missing_attrs:
            print(f"❌ Missing dependencies: {missing_attrs}")
            return False
        
        print("✅ All required dependencies available!")
        return True
        
    except Exception as e:
        print(f"❌ Dependencies creation test failed: {e}")
        return False


async def test_screenshot_capture():
    """Test screenshot capture functionality."""
    print("\n📸 Testing Screenshot Capture...")
    
    try:
        # Create test screenshots directory
        screenshots_dir = Path("screenshots")
        screenshots_dir.mkdir(exist_ok=True)
        
        # Test capturing a simple webpage (using a reliable test URL)
        test_url = "https://httpbin.org/html"  # Simple HTML page for testing
        output_path = screenshots_dir / "test_capture.png"
        
        print(f"Capturing screenshot of {test_url}...")
        
        result = await capture_screenshot(
            url=test_url,
            output_path=str(output_path),
            wait_time=1.0,
            viewport_width=1024,
            viewport_height=768
        )
        
        if result.get("success"):
            print(f"✅ Screenshot captured successfully: {result['output_path']}")
            print(f"📊 File size: {result['file_size']} bytes")
            
            # Verify file exists
            if Path(result['output_path']).exists():
                print("✅ Screenshot file verified!")
                return True
            else:
                print("❌ Screenshot file not found!")
                return False
        else:
            print(f"❌ Screenshot capture failed: {result.get('error')}")
            return False
        
    except Exception as e:
        print(f"❌ Screenshot capture test failed: {e}")
        return False


async def test_vision_integration_tools():
    """Test vision integration tools directly."""
    print("\n🔍 Testing Vision Integration Tools...")
    
    try:
        # Create dependencies
        deps = await create_dependencies(workspace_root=".")
        
        # Create a mock context for testing
        class MockContext:
            def __init__(self, deps):
                self.deps = deps
        
        ctx = MockContext(deps)
        
        # Test 1: Test with a non-existent image (should handle gracefully)
        print("Testing with non-existent image...")
        result = await request_vision_analysis(
            ctx=ctx,
            image_path="non_existent_image.png",
            analysis_type="general"
        )
        
        if result.get("status") == "error" and "not found" in result.get("error", "").lower():
            print("✅ Properly handled non-existent image!")
        else:
            print(f"❌ Unexpected result for non-existent image: {result}")
            return False
        
        # Test 2: Test capture and analyze (if we have a working screenshot)
        screenshots_dir = Path("screenshots")
        test_screenshot = screenshots_dir / "test_capture.png"
        
        if test_screenshot.exists():
            print(f"Testing analysis of existing screenshot: {test_screenshot}")
            result = await request_vision_analysis(
                ctx=ctx,
                image_path=str(test_screenshot),
                analysis_type="general",
                focus_areas=["layout", "design"]
            )
            
            print(f"📊 Analysis result status: {result.get('status')}")
            if result.get("status") == "success":
                print("✅ Vision analysis tool working!")
            else:
                print(f"⚠️ Vision analysis had issues: {result.get('error')}")
        else:
            print("⚠️ No test screenshot available for analysis test")
        
        return True
        
    except Exception as e:
        print(f"❌ Vision integration tools test failed: {e}")
        return False


async def test_coding_agent_with_vision():
    """Test the coding agent using vision integration tools."""
    print("\n🤖 Testing Coding Agent with Vision Integration...")

    try:
        # Import coding agent here to avoid circular imports
        from app.pydantic_ai.agents import coding_agent

        # Create dependencies
        deps = await create_dependencies(workspace_root=".")

        # Test task that should trigger vision analysis
        task = """I need to analyze a UI component. Please help me understand how to use the vision analysis tools.

Can you tell me what vision analysis capabilities are available to you?"""

        print("Asking coding agent about vision capabilities...")

        result = await coding_agent.run(task, deps=deps)

        print(f"📊 Agent response type: {type(result)}")

        if hasattr(result, 'output'):
            response = result.output
            print(f"🤖 Agent response: {str(response)[:200]}...")

            # Check if the response mentions vision tools
            response_str = str(response).lower()
            vision_keywords = ['vision', 'screenshot', 'analyze', 'image', 'ui']

            found_keywords = [kw for kw in vision_keywords if kw in response_str]

            if found_keywords:
                print(f"✅ Agent mentioned vision-related keywords: {found_keywords}")
                return True
            else:
                print("⚠️ Agent response didn't mention vision capabilities")
                return True  # Still a success, just different response
        else:
            print(f"⚠️ Unexpected result format: {result}")
            return True

    except Exception as e:
        print(f"❌ Coding agent with vision test failed: {e}")
        return False


async def main():
    """Run all Phase 8A tests."""
    print("🚀 Phase 8A Vision Integration Test Suite")
    print("=" * 60)
    
    tests = [
        ("Vision Tool Registration", test_vision_tool_registration),
        ("Dependencies Creation", test_dependencies_creation),
        ("Screenshot Capture", test_screenshot_capture),
        ("Vision Integration Tools", test_vision_integration_tools),
        ("Coding Agent with Vision", test_coding_agent_with_vision),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            print(f"💥 {test_name}: CRASHED - {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 PHASE 8A TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:.<40} {status}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL PHASE 8A TESTS PASSED!")
        print("\n🚀 Phase 8A Vision Integration is ready for production!")
    else:
        print(f"⚠️ {total - passed} tests failed. Please review the issues above.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())
