# backend/llm_client.py
import asyncio
import json
import logging
import time
from typing import Optional, Dict, Any, List
import httpx
from tenacity import retry, stop_after_attempt, wait_exponential
import random

try:
    from .config import settings
except ImportError:
    # For direct execution
    from config import settings

logger = logging.getLogger(__name__)


class OpenRouterError(Exception):
    """Base exception for OpenRouter API errors."""
    pass


class RateLimitError(OpenRouterError):
    """Exception for rate limiting errors."""
    pass


class ServerError(OpenRouterError):
    """Exception for server errors (5xx)."""
    pass


class ProviderError(OpenRouterError):
    """Exception for underlying provider errors."""
    pass


def is_retryable_error(exception):
    """Check if an exception should trigger a retry."""
    if isinstance(exception, (httpx.HTTPStatusError, httpx.RequestError, RateLimitError, ServerError, ProviderError)):
        return True
    return False


def add_jitter(wait_time: float) -> float:
    """Add jitter to wait time to prevent thundering herd."""
    jitter = random.uniform(0.1, 0.5)  # 10-50% jitter
    return wait_time + jitter


class CircuitBreaker:
    """Simple circuit breaker to prevent cascading failures."""

    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN

    def is_open(self) -> bool:
        """Check if circuit breaker is open (blocking requests)."""
        if self.state == "OPEN":
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = "HALF_OPEN"
                logger.info("Circuit breaker moving to HALF_OPEN state")
                return False
            return True
        return False

    def record_success(self):
        """Record a successful request."""
        self.failure_count = 0
        if self.state == "HALF_OPEN":
            self.state = "CLOSED"
            logger.info("Circuit breaker CLOSED - service recovered")

    def record_failure(self):
        """Record a failed request."""
        self.failure_count += 1
        self.last_failure_time = time.time()

        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"
            logger.warning(f"Circuit breaker OPEN - too many failures ({self.failure_count})")


class OpenRouterClient:
    """
    OpenRouter LLM client with retry logic and error handling.
    Supports chat completion, vision analysis, and text summarization.
    """
    
    def __init__(self):
        self.api_key = settings.openrouter_api_key
        self.base_url = "https://openrouter.ai/api/v1"
        self.timeout = 120.0

        # Model configurations from settings
        self.code_model = settings.code_model
        self.vision_model = settings.vision_model
        self.summarization_model = settings.summarization_model

        # Fallback models for when primary models fail
        self.fallback_code_model = getattr(settings, 'fallback_code_model', 'deepseek/deepseek-chat-v3-0324:free')
        self.fallback_vision_model = getattr(settings, 'fallback_vision_model', 'google/gemini-2.0-flash-exp:free')

        # Circuit breaker for each model
        self.circuit_breakers = {
            self.code_model: CircuitBreaker(),
            self.vision_model: CircuitBreaker(),
            self.summarization_model: CircuitBreaker()
        }

        logger.info(f"Initialized OpenRouter client with models: code={self.code_model}, vision={self.vision_model}")
        logger.info(f"Fallback models: code={self.fallback_code_model}, vision={self.fallback_vision_model}")
    
    def _get_headers(self) -> Dict[str, str]:
        """Get headers for OpenRouter API requests."""
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://ai-coder-agent.local",  # Required by OpenRouter
            "X-Title": "AI Coder Agent"  # Optional but recommended
        }
    
    @retry(
        stop=stop_after_attempt(5),  # Increased from 3 to 5 attempts
        wait=wait_exponential(multiplier=1, min=1, max=30),  # Longer max wait for server issues
        retry=is_retryable_error,  # Use custom retry logic
        reraise=True  # Reraise the last exception after all retries fail
    )
    async def _make_request(self, endpoint: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make an HTTP request to OpenRouter API with retry logic.
        
        Args:
            endpoint: API endpoint (e.g., "chat/completions")
            payload: Request payload
            
        Returns:
            Response JSON data
            
        Raises:
            httpx.HTTPStatusError: For HTTP errors
            httpx.RequestError: For network errors
            ValueError: For invalid responses
        """
        url = f"{self.base_url}/{endpoint}"
        model = payload.get("model", "unknown")

        # Check circuit breaker
        circuit_breaker = self.circuit_breakers.get(model)
        if circuit_breaker and circuit_breaker.is_open():
            logger.warning(f"Circuit breaker OPEN for model {model} - request blocked")
            raise ProviderError(f"Circuit breaker open for model {model}")

        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                response = await client.post(
                    url,
                    headers=self._get_headers(),
                    data=json.dumps(payload)
                )
                
                # Handle rate limiting
                if response.status_code == 429:
                    logger.warning("Rate limited by OpenRouter (429). Retrying...")
                    raise RateLimitError(f"Rate limited (HTTP 429)")

                # Handle server errors
                if response.status_code >= 500:
                    logger.warning(f"Server error {response.status_code} from OpenRouter. Retrying...")
                    raise ServerError(f"Server error {response.status_code}")

                # Handle client errors (4xx except 429)
                if 400 <= response.status_code < 500:
                    logger.error(f"Client error {response.status_code} from OpenRouter")
                    raise OpenRouterError(f"Client error {response.status_code}: {response.text}")

                
                response.raise_for_status()
                data = response.json()

                # Check for OpenRouter wrapped errors
                if "error" in data:
                    error_info = data["error"]
                    error_code = error_info.get("code", 0)
                    error_message = error_info.get("message", "Unknown error")

                    # Handle different error types
                    if error_code == 429 or "rate limit" in error_message.lower():
                        logger.warning(f"Rate limit error: {error_message}")
                        raise RateLimitError(f"Rate limited: {error_message}")
                    elif error_code >= 500 or "server error" in error_message.lower() or "provider returned error" in error_message.lower():
                        logger.warning(f"Server/Provider error {error_code}: {error_message}")
                        raise ProviderError(f"Provider error {error_code}: {error_message}")
                    else:
                        logger.error(f"API error {error_code}: {error_message}")
                        raise OpenRouterError(f"API error {error_code}: {error_message}")

                # Validate response structure
                if "choices" not in data or not data["choices"]:
                    logger.error(f"Invalid response structure: {data}")
                    raise ValueError(f"Invalid response structure: {data}")

                # Record success in circuit breaker
                if circuit_breaker:
                    circuit_breaker.record_success()

                # Add jitter to successful requests to prevent rate limiting
                await asyncio.sleep(add_jitter(0.1))

                return data
                
            except httpx.TimeoutException as e:
                if circuit_breaker:
                    circuit_breaker.record_failure()
                logger.error(f"Request timeout for {endpoint}: {e}")
                raise httpx.RequestError(f"Request timeout: {e}")
            except (RateLimitError, ServerError, ProviderError) as e:
                if circuit_breaker:
                    circuit_breaker.record_failure()
                logger.warning(f"Retryable error for {endpoint}: {e}")
                raise
            except Exception as e:
                if circuit_breaker:
                    circuit_breaker.record_failure()
                logger.error(f"Request failed for {endpoint}: {e}")
                raise
    
    async def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: float = 0.7
    ) -> str:
        """
        Generate a chat completion using OpenRouter.
        
        Args:
            messages: List of message dicts with 'role' and 'content'
            model: Model to use (defaults to code_model)
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature (0.0 to 1.0)
            
        Returns:
            Generated text response
            
        Raises:
            Exception: If the request fails after retries
        """
        if not model:
            model = self.code_model
            
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature
        }
        
        if max_tokens:
            payload["max_tokens"] = max_tokens
        
        try:
            logger.info(f"Making chat completion request with model: {model}")
            data = await self._make_request("chat/completions", payload)

            response_text = data["choices"][0]["message"]["content"]
            logger.info(f"Chat completion successful, response length: {len(response_text)}")

            return response_text

        except (ProviderError, ServerError) as e:
            # Try fallback model for provider/server errors
            if model == self.code_model and hasattr(self, 'fallback_code_model'):
                logger.warning(f"Primary model {model} failed, trying fallback {self.fallback_code_model}")
                try:
                    fallback_payload = payload.copy()
                    fallback_payload["model"] = self.fallback_code_model
                    data = await self._make_request("chat/completions", fallback_payload)
                    response_text = data["choices"][0]["message"]["content"]
                    logger.info(f"Fallback model successful, response length: {len(response_text)}")
                    return response_text
                except Exception as fallback_error:
                    logger.error(f"Fallback model also failed: {fallback_error}")

            logger.error(f"Chat completion failed: {e}")
            raise Exception(f"Failed to get chat completion: {e}")
        except Exception as e:
            logger.error(f"Chat completion failed: {e}")
            raise Exception(f"Failed to get chat completion: {e}")

    async def vision_analyze(
        self,
        image_base64: str,
        prompt: str,
        model: Optional[str] = None
    ) -> str:
        """
        Analyze an image using OpenRouter's vision models.

        Args:
            image_base64: Base64 encoded image data
            prompt: Text prompt for analysis
            model: Vision model to use (defaults to vision_model)

        Returns:
            Analysis result text

        Raises:
            Exception: If the request fails after retries
        """
        if not model:
            model = self.vision_model

        # Construct message with image
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{image_base64}"
                        }
                    }
                ]
            }
        ]

        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": 1000
        }

        try:
            logger.info(f"Making vision analysis request with model: {model}")
            data = await self._make_request("chat/completions", payload)

            response_text = data["choices"][0]["message"]["content"]
            logger.info(f"Vision analysis successful, response length: {len(response_text)}")

            return response_text

        except Exception as e:
            logger.error(f"Vision analysis failed: {e}")
            raise Exception(f"Failed to analyze image: {e}")

    async def summarize_text(
        self,
        text: str,
        max_length: int = 500,
        model: Optional[str] = None
    ) -> str:
        """
        Summarize text using OpenRouter.

        Args:
            text: Text to summarize
            max_length: Maximum length of summary in tokens
            model: Model to use (defaults to summarization_model)

        Returns:
            Summarized text

        Raises:
            Exception: If the request fails after retries
        """
        if not model:
            model = self.summarization_model

        messages = [
            {
                "role": "system",
                "content": f"You are a helpful assistant that summarizes text concisely. Keep summaries under {max_length} tokens while preserving key information."
            },
            {
                "role": "user",
                "content": f"Please summarize the following text:\n\n{text}"
            }
        ]

        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_length,
            "temperature": 0.3  # Lower temperature for more consistent summaries
        }

        try:
            logger.info(f"Making text summarization request with model: {model}")
            data = await self._make_request("chat/completions", payload)

            response_text = data["choices"][0]["message"]["content"]
            logger.info(f"Text summarization successful, response length: {len(response_text)}")

            return response_text

        except Exception as e:
            logger.error(f"Text summarization failed: {e}")
            raise Exception(f"Failed to summarize text: {e}")

    async def health_check(self) -> Dict[str, Any]:
        """
        Check if OpenRouter API is accessible and API key is valid.

        Returns:
            Health status dictionary
        """
        try:
            # Simple test request
            messages = [{"role": "user", "content": "Hello"}]
            await self.chat_completion(messages, max_tokens=10)

            return {
                "status": "healthy",
                "api_key_valid": True,
                "base_url": self.base_url,
                "models": {
                    "code": self.code_model,
                    "vision": self.vision_model,
                    "summarization": self.summarization_model
                }
            }

        except Exception as e:
            logger.error(f"OpenRouter health check failed: {e}")
            return {
                "status": "unhealthy",
                "api_key_valid": False,
                "error": str(e),
                "base_url": self.base_url
            }


# Global client instance
openrouter_client = OpenRouterClient()

# Alias for backward compatibility
LLMClient = OpenRouterClient
