interactions:
- request:
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '169'
      content-type:
      - application/json
      host:
      - generativelanguage.googleapis.com
    method: POST
    parsed_body:
      contents:
      - parts:
        - text: What is the capital of France?
        role: user
      system_instruction:
        parts:
        - text: You are a helpful assistant.
        role: user
    uri: https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
  response:
    headers:
      alt-svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      content-length:
      - '637'
      content-type:
      - application/json; charset=UTF-8
      server-timing:
      - gfet4t7; dur=415
      transfer-encoding:
      - chunked
      vary:
      - Origin
      - X-Origin
      - Referer
    parsed_body:
      candidates:
      - avgLogprobs: -0.0008098740363493562
        content:
          parts:
          - text: |
              The capital of France is Paris.
          role: model
        finishReason: STOP
      modelVersion: gemini-1.5-flash
      usageMetadata:
        candidatesTokenCount: 8
        candidatesTokensDetails:
        - modality: TEXT
          tokenCount: 8
        promptTokenCount: 13
        promptTokensDetails:
        - modality: TEXT
          tokenCount: 13
        totalTokenCount: 21
    status:
      code: 200
      message: OK
version: 1
