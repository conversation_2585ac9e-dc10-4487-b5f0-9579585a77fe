interactions:
- request:
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '264'
      content-type:
      - application/json
      host:
      - generativelanguage.googleapis.com
    method: POST
    parsed_body:
      contents:
      - parts:
        - text: What is the temperature in Tokyo?
        role: user
      tools:
        function_declarations:
        - description: ''
          name: get_temperature
          parameters:
            properties:
              location:
                type: object
            required:
            - location
            type: object
    uri: https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
  response:
    headers:
      alt-svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      content-length:
      - '741'
      content-type:
      - application/json; charset=UTF-8
      server-timing:
      - gfet4t7; dur=534
      transfer-encoding:
      - chunked
      vary:
      - Origin
      - X-Origin
      - Referer
    parsed_body:
      candidates:
      - avgLogprobs: -0.15060695580073766
        content:
          parts:
          - text: |
              I need a location dictionary to use the `get_temperature` function.  I cannot provide the temperature in Tokyo without more information.
          role: model
        finishReason: STOP
      modelVersion: gemini-1.5-flash
      usageMetadata:
        candidatesTokenCount: 28
        candidatesTokensDetails:
        - modality: TEXT
          tokenCount: 28
        promptTokenCount: 12
        promptTokensDetails:
        - modality: TEXT
          tokenCount: 12
        totalTokenCount: 40
    status:
      code: 200
      message: OK
version: 1
