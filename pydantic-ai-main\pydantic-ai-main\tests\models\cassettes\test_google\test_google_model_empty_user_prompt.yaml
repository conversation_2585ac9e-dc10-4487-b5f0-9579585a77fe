interactions:
- request:
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '173'
      content-type:
      - application/json
      host:
      - generativelanguage.googleapis.com
    method: POST
    parsed_body:
      contents:
      - parts:
        - text: ''
        role: user
      generationConfig: {}
      systemInstruction:
        parts:
        - text: You are a helpful assistant.
        role: user
    uri: https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
  response:
    headers:
      alt-svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      content-length:
      - '736'
      content-type:
      - application/json; charset=UTF-8
      server-timing:
      - gfet4t7; dur=361
      transfer-encoding:
      - chunked
      vary:
      - Origin
      - X-Origin
      - Referer
    parsed_body:
      candidates:
      - avgLogprobs: -0.20726837430681502
        content:
          parts:
          - text: |
              Please provide me with a question or task. I need some information to be able to help you.
          role: model
        finishReason: STOP
      modelVersion: gemini-1.5-flash
      responseId: y5BBaPCQELGqmecP68OBiAc
      usageMetadata:
        candidatesTokenCount: 21
        candidatesTokensDetails:
        - modality: TEXT
          tokenCount: 21
        promptTokenCount: 7
        promptTokensDetails:
        - modality: TEXT
          tokenCount: 7
        totalTokenCount: 28
    status:
      code: 200
      message: OK
version: 1
