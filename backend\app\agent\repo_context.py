#!/usr/bin/env python3
"""
Repository Context Management Module for AI Coder Agent - Phase 3
Provides project structure analysis and codebase summaries for LLM context.

Features:
- Project structure analysis and visualization
- Codebase summary generation for LLM context
- Dependency mapping and relationship analysis
- Code pattern detection and documentation
- Context-aware file recommendations
- Integration with all Phase 3 components
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Union, Any, Tuple
import logging
import asyncio
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, field
from datetime import datetime
import re

# Import other modules
try:
    from ...config import settings
    from .file_operations import file_operations
    from .git_operations import git_operations
    from .code_analyzer import code_analyzer
    from .change_tracker import change_tracker
except ImportError:
    from config import settings
    from app.agent.file_operations import file_operations
    from app.agent.git_operations import git_operations
    from app.agent.code_analyzer import code_analyzer
    from app.agent.change_tracker import change_tracker

logger = logging.getLogger(__name__)

@dataclass
class ProjectStructure:
    """Represents the structure of a project."""
    root_path: str
    total_files: int = 0
    code_files: int = 0
    languages: Dict[str, int] = field(default_factory=dict)
    directories: List[str] = field(default_factory=list)
    main_files: List[str] = field(default_factory=list)
    config_files: List[str] = field(default_factory=list)
    documentation: List[str] = field(default_factory=list)
    tests: List[str] = field(default_factory=list)
    dependencies: Dict[str, Any] = field(default_factory=dict)
    git_info: Dict[str, Any] = field(default_factory=dict)
    complexity_metrics: Dict[str, Any] = field(default_factory=dict)

class RepoContextError(Exception):
    """Custom exception for repository context errors."""
    pass

class RepositoryContext:
    """Repository context management and analysis."""
    
    def __init__(self):
        self.workspace_root = Path(getattr(settings, 'workspace_root', '/app/workspace'))
        self.max_context_files = getattr(settings, 'max_context_files', 50)
        self.max_context_size = getattr(settings, 'max_context_size', 100_000)  # 100KB
        self.summary_cache_ttl = getattr(settings, 'summary_cache_ttl', 3600)  # 1 hour
        self.executor = ThreadPoolExecutor(max_workers=2)
        
        # File type patterns
        self.file_patterns = {
            'main': ['main.py', 'index.js', 'app.py', 'server.py', 'main.go', 'main.rs'],
            'config': ['*.json', '*.yaml', '*.yml', '*.toml', '*.ini', '*.cfg', 'Dockerfile', 'docker-compose.yml'],
            'documentation': ['README*', '*.md', '*.rst', '*.txt', 'docs/*'],
            'tests': ['test_*.py', '*_test.py', '*.test.js', 'tests/*', 'test/*', '__tests__/*'],
            'build': ['Makefile', 'CMakeLists.txt', 'build.gradle', 'pom.xml', 'package.json', 'Cargo.toml']
        }
        
        # Cache for summaries
        self.summary_cache = {}
        
        logger.info(f"RepositoryContext initialized for workspace: {self.workspace_root}")
    
    def _categorize_file(self, file_path: Path) -> str:
        """Categorize a file based on its name and path."""
        file_name = file_path.name
        relative_path = str(file_path.relative_to(self.workspace_root))
        
        # Check each category
        for category, patterns in self.file_patterns.items():
            for pattern in patterns:
                if file_path.match(pattern) or Path(relative_path).match(pattern):
                    return category
        
        # Check if it's a code file
        language = code_analyzer._detect_language(file_path)
        if language:
            return 'code'
        
        return 'other'
    
    async def analyze_project_structure(self, project_path: Union[str, Path] = None) -> Dict[str, Any]:
        """
        Analyze the complete project structure.
        
        Args:
            project_path: Path to project (defaults to workspace root)
            
        Returns:
            Dict with project structure analysis
        """
        try:
            project_path = Path(project_path) if project_path else self.workspace_root
            
            if not project_path.exists() or not project_path.is_dir():
                return {
                    'status': 'error',
                    'error': f'Invalid project path: {project_path}',
                    'error_type': 'ValidationError'
                }
            
            def _analyze():
                structure = ProjectStructure(root_path=str(project_path))
                
                # Categorized files
                categorized_files = {
                    'main': [],
                    'config': [],
                    'documentation': [],
                    'tests': [],
                    'code': [],
                    'other': []
                }
                
                # Walk through all files
                for file_path in project_path.rglob('*'):
                    if file_path.is_file():
                        structure.total_files += 1
                        
                        # Skip hidden files and common ignore patterns
                        if any(part.startswith('.') for part in file_path.parts):
                            continue
                        
                        if any(ignore in str(file_path) for ignore in ['__pycache__', 'node_modules', '.git']):
                            continue
                        
                        # Categorize file
                        category = self._categorize_file(file_path)
                        relative_path = str(file_path.relative_to(project_path))
                        categorized_files[category].append(relative_path)
                        
                        # Count languages for code files
                        if category == 'code':
                            structure.code_files += 1
                            language = code_analyzer._detect_language(file_path)
                            if language:
                                structure.languages[language] = structure.languages.get(language, 0) + 1
                
                # Set categorized files
                structure.main_files = categorized_files['main']
                structure.config_files = categorized_files['config']
                structure.documentation = categorized_files['documentation']
                structure.tests = categorized_files['tests']
                
                # Get directories
                structure.directories = [
                    str(d.relative_to(project_path)) 
                    for d in project_path.rglob('*') 
                    if d.is_dir() and not any(part.startswith('.') for part in d.parts)
                ]
                
                return structure
            
            structure = await asyncio.get_event_loop().run_in_executor(self.executor, _analyze)
            
            # Get git information
            git_info = await git_operations.get_git_status(project_path)
            if git_info.get('status') == 'success':
                structure.git_info = {
                    'current_branch': git_info.get('current_branch'),
                    'is_dirty': git_info.get('is_dirty', False),
                    'ahead': git_info.get('ahead', 0),
                    'behind': git_info.get('behind', 0),
                    'staged_files': len(git_info.get('staged', [])),
                    'unstaged_files': len(git_info.get('unstaged', [])),
                    'untracked_files': len(git_info.get('untracked', []))
                }
            
            # Get dependency analysis
            deps_result = await code_analyzer.analyze_project_dependencies(project_path)
            if deps_result.get('status') == 'success':
                structure.dependencies = deps_result.get('summary', {})
            
            return {
                'status': 'success',
                'project_path': str(project_path),
                'structure': structure.__dict__,
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze project structure: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }
    
    async def generate_codebase_summary(self, project_path: Union[str, Path] = None,
                                      include_code_samples: bool = True,
                                      max_files: Optional[int] = None) -> Dict[str, Any]:
        """
        Generate a comprehensive codebase summary for LLM context.
        
        Args:
            project_path: Path to project (defaults to workspace root)
            include_code_samples: Whether to include code samples
            max_files: Maximum number of files to include
            
        Returns:
            Dict with codebase summary
        """
        try:
            project_path = Path(project_path) if project_path else self.workspace_root
            max_files = max_files or self.max_context_files
            
            # Check cache
            cache_key = f"{project_path}_{include_code_samples}_{max_files}"
            if cache_key in self.summary_cache:
                cached_summary, timestamp = self.summary_cache[cache_key]
                if datetime.now().timestamp() - timestamp < self.summary_cache_ttl:
                    return cached_summary
            
            # Get project structure
            structure_result = await self.analyze_project_structure(project_path)
            if structure_result['status'] != 'success':
                return structure_result
            
            structure = structure_result['structure']
            
            # Build summary
            summary = {
                'project_overview': {
                    'root_path': str(project_path),
                    'total_files': structure['total_files'],
                    'code_files': structure['code_files'],
                    'languages': structure['languages'],
                    'main_language': max(structure['languages'].items(), key=lambda x: x[1])[0] if structure['languages'] else None
                },
                'project_structure': {
                    'main_files': structure['main_files'][:5],  # Top 5
                    'key_directories': structure['directories'][:10],  # Top 10
                    'config_files': structure['config_files'][:5],
                    'documentation': structure['documentation'][:5],
                    'test_files': structure['tests'][:5]
                },
                'git_status': structure.get('git_info', {}),
                'dependencies': structure.get('dependencies', {}),
                'code_samples': {},
                'recommendations': []
            }
            
            # Add code samples if requested
            if include_code_samples:
                code_samples = await self._get_representative_code_samples(project_path, max_files)
                summary['code_samples'] = code_samples
            
            # Generate recommendations
            recommendations = self._generate_recommendations(structure)
            summary['recommendations'] = recommendations
            
            # Cache the result
            result = {
                'status': 'success',
                'summary': summary,
                'generated_at': datetime.now().isoformat()
            }
            
            self.summary_cache[cache_key] = (result, datetime.now().timestamp())
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to generate codebase summary: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def _get_representative_code_samples(self, project_path: Path, max_files: int) -> Dict[str, Any]:
        """Get representative code samples from the project."""
        try:
            samples = {}
            current_size = 0
            files_included = 0

            # Prioritize main files and important code files
            priority_files = []

            # Add main files first
            for main_file in ['main.py', 'index.js', 'app.py', 'server.py']:
                main_path = project_path / main_file
                if main_path.exists():
                    priority_files.append(main_path)

            # Add other code files
            for file_path in project_path.rglob('*'):
                if (file_path.is_file() and
                    files_included < max_files and
                    current_size < self.max_context_size):

                    language = code_analyzer._detect_language(file_path)
                    if language and file_path not in priority_files:
                        priority_files.append(file_path)

            # Process files
            for file_path in priority_files[:max_files]:
                if current_size >= self.max_context_size:
                    break

                try:
                    file_result = await file_operations.read_file(file_path)
                    if file_result.get('status') == 'success':
                        content = file_result['content']

                        # Truncate if too long
                        if len(content) > 2000:  # 2KB per file max
                            content = content[:2000] + '\n... (truncated)'

                        relative_path = str(file_path.relative_to(project_path))
                        samples[relative_path] = {
                            'language': code_analyzer._detect_language(file_path),
                            'size': len(content),
                            'content': content
                        }

                        current_size += len(content)
                        files_included += 1

                except Exception as e:
                    logger.warning(f"Failed to read file {file_path}: {e}")

            return {
                'total_files': files_included,
                'total_size': current_size,
                'files': samples
            }

        except Exception as e:
            logger.error(f"Failed to get code samples: {e}")
            return {}

    def _generate_recommendations(self, structure: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on project structure."""
        recommendations = []

        try:
            # Check for missing documentation
            if not structure.get('documentation'):
                recommendations.append("Consider adding a README.md file to document your project")

            # Check for missing tests
            if not structure.get('tests') and structure.get('code_files', 0) > 5:
                recommendations.append("Consider adding unit tests to improve code reliability")

            # Check for configuration management
            if not structure.get('config_files'):
                recommendations.append("Consider adding configuration files for better project management")

            # Check git status
            git_info = structure.get('git_info', {})
            if git_info.get('is_dirty'):
                recommendations.append("You have uncommitted changes - consider committing your work")

            if git_info.get('untracked_files', 0) > 0:
                recommendations.append(f"You have {git_info['untracked_files']} untracked files")

            # Language-specific recommendations
            languages = structure.get('languages', {})
            if 'python' in languages:
                if not any('requirements.txt' in f for f in structure.get('config_files', [])):
                    recommendations.append("Consider adding requirements.txt for Python dependencies")

            if 'javascript' in languages:
                if not any('package.json' in f for f in structure.get('config_files', [])):
                    recommendations.append("Consider adding package.json for JavaScript dependencies")

            # Code organization recommendations
            if structure.get('code_files', 0) > 20 and len(structure.get('directories', [])) < 3:
                recommendations.append("Consider organizing code into subdirectories for better structure")

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")

        return recommendations

    async def search_relevant_files(self, query: str,
                                  project_path: Union[str, Path] = None,
                                  limit: int = 10) -> Dict[str, Any]:
        """
        Search for files relevant to a query using semantic search.

        Args:
            query: Search query
            project_path: Path to project (defaults to workspace root)
            limit: Maximum number of results

        Returns:
            Dict with relevant files
        """
        try:
            project_path = Path(project_path) if project_path else self.workspace_root

            # Use semantic search from code analyzer
            search_result = await code_analyzer.search_code_semantically(query, limit)

            if search_result.get('status') != 'success':
                return search_result

            # Filter results to project path
            relevant_files = []
            for result in search_result.get('results', []):
                file_path = Path(result['file_path'])
                try:
                    # Check if file is within project
                    file_path.relative_to(project_path)
                    relevant_files.append(result)
                except ValueError:
                    # File is outside project, skip
                    continue

            return {
                'status': 'success',
                'query': query,
                'project_path': str(project_path),
                'total_results': len(relevant_files),
                'files': relevant_files
            }

        except Exception as e:
            logger.error(f"Failed to search relevant files: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def get_context_for_task(self, task_description: str,
                                 project_path: Union[str, Path] = None,
                                 include_git_status: bool = True) -> Dict[str, Any]:
        """
        Get comprehensive context for a specific task.

        Args:
            task_description: Description of the task
            project_path: Path to project (defaults to workspace root)
            include_git_status: Whether to include git status

        Returns:
            Dict with task-specific context
        """
        try:
            project_path = Path(project_path) if project_path else self.workspace_root

            # Get project summary
            summary_result = await self.generate_codebase_summary(
                project_path,
                include_code_samples=True,
                max_files=20
            )

            if summary_result['status'] != 'success':
                return summary_result

            # Search for relevant files
            relevant_files_result = await self.search_relevant_files(
                task_description,
                project_path,
                limit=15
            )

            # Get recent changes if change tracker is available
            recent_changes = {}
            try:
                # Import here to avoid circular imports
                from .change_tracker import change_tracker
                recent_changes = await change_tracker.get_recent_changes(limit=20)
            except Exception as e:
                logger.warning(f"Failed to get recent changes: {e}")

            # Build comprehensive context
            context = {
                'task_description': task_description,
                'project_summary': summary_result['summary'],
                'relevant_files': relevant_files_result.get('files', []),
                'recent_changes': recent_changes.get('statistics', {}),
                'recommendations': [],
                'context_metadata': {
                    'generated_at': datetime.now().isoformat(),
                    'project_path': str(project_path),
                    'total_relevant_files': len(relevant_files_result.get('files', [])),
                    'context_size_estimate': self._estimate_context_size(summary_result, relevant_files_result)
                }
            }

            # Add task-specific recommendations
            task_recommendations = self._generate_task_recommendations(
                task_description,
                summary_result['summary']
            )
            context['recommendations'] = task_recommendations

            return {
                'status': 'success',
                'context': context
            }

        except Exception as e:
            logger.error(f"Failed to get context for task: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    def _estimate_context_size(self, summary_result: Dict, relevant_files_result: Dict) -> int:
        """Estimate the total context size in characters."""
        try:
            size = 0

            # Summary size
            import json
            summary_str = json.dumps(summary_result.get('summary', {}))
            size += len(summary_str)

            # Relevant files size
            for file_info in relevant_files_result.get('files', []):
                content = file_info.get('content', '')
                size += len(content)

            return size

        except Exception:
            return 0

    def _generate_task_recommendations(self, task_description: str,
                                     project_summary: Dict[str, Any]) -> List[str]:
        """Generate task-specific recommendations."""
        recommendations = []

        try:
            task_lower = task_description.lower()

            # Testing-related tasks
            if any(word in task_lower for word in ['test', 'testing', 'unit test', 'integration']):
                if not project_summary.get('project_structure', {}).get('test_files'):
                    recommendations.append("No existing test files found - you may need to set up a testing framework")
                else:
                    recommendations.append("Consider following existing test patterns in the project")

            # Documentation tasks
            if any(word in task_lower for word in ['document', 'readme', 'docs', 'documentation']):
                docs = project_summary.get('project_structure', {}).get('documentation', [])
                if docs:
                    recommendations.append(f"Existing documentation found: {', '.join(docs[:3])}")
                else:
                    recommendations.append("No existing documentation structure found - you may need to create one")

            # API/web development tasks
            if any(word in task_lower for word in ['api', 'endpoint', 'route', 'web', 'server']):
                main_files = project_summary.get('project_structure', {}).get('main_files', [])
                if main_files:
                    recommendations.append(f"Main application files: {', '.join(main_files)}")

            # Database tasks
            if any(word in task_lower for word in ['database', 'db', 'sql', 'migration']):
                config_files = project_summary.get('project_structure', {}).get('config_files', [])
                db_configs = [f for f in config_files if any(db in f.lower() for db in ['db', 'database', 'sql'])]
                if db_configs:
                    recommendations.append(f"Database configuration files found: {', '.join(db_configs)}")

            # Git-related tasks
            git_status = project_summary.get('git_status', {})
            if git_status.get('is_dirty'):
                recommendations.append("Working directory has uncommitted changes")

        except Exception as e:
            logger.error(f"Error generating task recommendations: {e}")

        return recommendations

    def cleanup(self):
        """Clean up resources."""
        try:
            self.executor.shutdown(wait=True)
            self.summary_cache.clear()
            logger.info("RepositoryContext cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

    async def generate_project_summary(self, project_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Generate a comprehensive project summary.

        Args:
            project_path: Path to project directory

        Returns:
            Dict with project summary
        """
        try:
            # Use existing codebase summary method
            return await self.generate_codebase_summary(project_path)

        except Exception as e:
            logger.error(f"Failed to generate project summary: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def generate_task_context(self, project_path: Union[str, Path],
                                  task_description: str) -> Dict[str, Any]:
        """
        Generate task-specific context for AI operations.

        Args:
            project_path: Path to project directory
            task_description: Description of the task

        Returns:
            Dict with task context
        """
        try:
            # Use existing get_context_for_task method
            return await self.get_context_for_task(task_description, project_path)

        except Exception as e:
            logger.error(f"Failed to generate task context: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

# Global instance
repo_context = RepositoryContext()
