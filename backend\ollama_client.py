# backend/ollama_client.py
import asyncio
import logging
from typing import List, Dict, Any, Optional
import ollama
from ollama import AsyncClient

try:
    from .config import settings
except ImportError:
    # For direct execution
    from config import settings

logger = logging.getLogger(__name__)


class OllamaEmbeddingClient:
    """
    Ollama client for generating embeddings using local models.
    Handles model management and batch processing.
    """
    
    def __init__(self):
        self.host = settings.ollama_host
        self.embedding_model = settings.embedding_model
        self.embedding_dim = settings.embedding_dim
        
        # Initialize async client
        self.client = AsyncClient(host=self.host)
        
        logger.info(f"Initialized Ollama client: host={self.host}, model={self.embedding_model}")
    
    async def _ensure_model_available(self, model_name: str) -> bool:
        """
        Ensure the specified model is available, pull if necessary.
        
        Args:
            model_name: Name of the model to check/pull
            
        Returns:
            True if model is available, False otherwise
        """
        try:
            # Check if model is already available
            models = await self.client.list()
            available_models = []

            # Handle different response formats
            if 'models' in models:
                for model in models['models']:
                    if isinstance(model, dict):
                        # Try different possible keys for model name
                        name = model.get('name') or model.get('model') or model.get('id')
                        if name:
                            available_models.append(name)
                    elif isinstance(model, str):
                        available_models.append(model)

            logger.debug(f"Available models: {available_models}")

            # Check for exact match or with :latest suffix
            if (model_name in available_models or
                f"{model_name}:latest" in available_models or
                model_name.replace(":latest", "") in available_models):
                logger.info(f"Model {model_name} is already available")
                return True
            
            # Try to pull the model
            logger.info(f"Pulling model {model_name}...")
            await self.client.pull(model_name)
            logger.info(f"Successfully pulled model {model_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to ensure model {model_name} is available: {e}")
            return False
    
    async def embed_text(self, text: str, model: Optional[str] = None) -> List[float]:
        """
        Generate embedding for a single text.
        
        Args:
            text: Text to embed
            model: Model to use (defaults to embedding_model)
            
        Returns:
            List of embedding values
            
        Raises:
            Exception: If embedding generation fails
        """
        if not model:
            model = self.embedding_model
        
        try:
            # Ensure model is available
            if not await self._ensure_model_available(model):
                raise Exception(f"Model {model} is not available")
            
            logger.debug(f"Generating embedding for text (length: {len(text)})")
            
            # Generate embedding
            response = await self.client.embeddings(
                model=model,
                prompt=text
            )
            
            embedding = response['embedding']
            
            # Validate embedding dimensions
            if len(embedding) != self.embedding_dim:
                logger.warning(
                    f"Embedding dimension mismatch: expected {self.embedding_dim}, "
                    f"got {len(embedding)} for model {model}"
                )
            
            logger.debug(f"Generated embedding with {len(embedding)} dimensions")
            return embedding
            
        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            raise Exception(f"Embedding generation failed: {e}")
    
    async def embed_batch(
        self, 
        texts: List[str], 
        model: Optional[str] = None,
        batch_size: int = 10
    ) -> List[List[float]]:
        """
        Generate embeddings for multiple texts in batches.
        
        Args:
            texts: List of texts to embed
            model: Model to use (defaults to embedding_model)
            batch_size: Number of texts to process in each batch
            
        Returns:
            List of embedding lists
            
        Raises:
            Exception: If batch embedding generation fails
        """
        if not model:
            model = self.embedding_model
        
        if not texts:
            return []
        
        try:
            # Ensure model is available
            if not await self._ensure_model_available(model):
                raise Exception(f"Model {model} is not available")
            
            logger.info(f"Generating embeddings for {len(texts)} texts in batches of {batch_size}")
            
            embeddings = []
            
            # Process in batches
            for i in range(0, len(texts), batch_size):
                batch = texts[i:i + batch_size]
                logger.debug(f"Processing batch {i//batch_size + 1}/{(len(texts) + batch_size - 1)//batch_size}")
                
                # Generate embeddings for this batch
                batch_embeddings = []
                for text in batch:
                    embedding = await self.embed_text(text, model)
                    batch_embeddings.append(embedding)
                
                embeddings.extend(batch_embeddings)
                
                # Small delay between batches to avoid overwhelming the service
                if i + batch_size < len(texts):
                    await asyncio.sleep(0.1)
            
            logger.info(f"Successfully generated {len(embeddings)} embeddings")
            return embeddings
            
        except Exception as e:
            logger.error(f"Failed to generate batch embeddings: {e}")
            raise Exception(f"Batch embedding generation failed: {e}")
    
    async def list_models(self) -> List[Dict[str, Any]]:
        """
        List available models in Ollama.
        
        Returns:
            List of model information dictionaries
        """
        try:
            response = await self.client.list()
            models = response.get('models', [])
            
            logger.info(f"Found {len(models)} available models")
            return models
            
        except Exception as e:
            logger.error(f"Failed to list models: {e}")
            return []
    
    async def check_model_availability(self, model_name: str) -> bool:
        """
        Check if a specific model is available.

        Args:
            model_name: Name of the model to check

        Returns:
            True if model is available, False otherwise
        """
        try:
            models = await self.list_models()
            available_models = []

            for model in models:
                if isinstance(model, dict):
                    # Try different possible keys for model name
                    name = model.get('name') or model.get('model') or model.get('id')
                    if name:
                        available_models.append(name)
                elif isinstance(model, str):
                    available_models.append(model)

            # Check for exact match or with :latest suffix
            return (model_name in available_models or
                    f"{model_name}:latest" in available_models or
                    model_name.replace(":latest", "") in available_models)

        except Exception as e:
            logger.error(f"Failed to check model availability: {e}")
            return False
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Check if Ollama service is accessible and embedding model is available.
        
        Returns:
            Health status dictionary
        """
        try:
            # Test connection by listing models
            models = await self.list_models()
            
            # Check if embedding model is available
            model_available = await self.check_model_availability(self.embedding_model)
            
            # Test embedding generation (more reliable than model listing)
            embedding_test_success = False
            try:
                test_embedding = await self.embed_text("test", self.embedding_model)
                embedding_test_success = len(test_embedding) == self.embedding_dim
            except Exception as e:
                logger.debug(f"Embedding test failed: {e}")

            # If embeddings work, consider the service healthy regardless of model listing
            service_healthy = embedding_test_success

            return {
                "status": "healthy" if service_healthy else "degraded",
                "host": self.host,
                "embedding_model": self.embedding_model,
                "model_available": model_available,
                "embedding_test_success": embedding_test_success,
                "service_healthy": service_healthy,
                "expected_dimensions": self.embedding_dim,
                "available_models": len(models)
            }
            
        except Exception as e:
            logger.error(f"Ollama health check failed: {e}")
            return {
                "status": "unhealthy",
                "host": self.host,
                "error": str(e)
            }


# Global client instance
ollama_client = OllamaEmbeddingClient()

# Alias for backward compatibility
OllamaClient = OllamaEmbeddingClient
