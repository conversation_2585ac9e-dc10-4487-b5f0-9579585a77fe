"""
📝 Context Summarizer - Summarize long contexts efficiently

This module provides intelligent context summarization with:
- Multi-level summarization strategies
- Semantic preservation techniques
- Quality-aware compression
- Template-based summarization
- Performance optimization
"""

import re
import time
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)


class SummarizationStrategy(str, Enum):
    """Summarization strategies."""
    EXTRACTIVE = "extractive"        # Extract key sentences
    ABSTRACTIVE = "abstractive"      # Generate new summary
    HYBRID = "hybrid"                # Combine both approaches
    TEMPLATE = "template"            # Template-based summarization


class SummaryQuality(str, Enum):
    """Summary quality levels."""
    HIGH = "high"                    # Maximum quality, minimal compression
    MEDIUM = "medium"                # Balanced quality and compression
    LOW = "low"                      # Maximum compression, acceptable quality


class SummarizationConfig(BaseModel):
    """Summarization configuration."""
    
    strategy: SummarizationStrategy = Field(default=SummarizationStrategy.HYBRID, description="Summarization strategy")
    quality: SummaryQuality = Field(default=SummaryQuality.MEDIUM, description="Target quality level")
    max_length: int = Field(default=500, description="Maximum summary length in words")
    preserve_code: bool = Field(default=True, description="Preserve code blocks")
    preserve_decisions: bool = Field(default=True, description="Preserve decision points")
    preserve_errors: bool = Field(default=True, description="Preserve error information")
    include_statistics: bool = Field(default=False, description="Include summary statistics")
    
    # Quality thresholds
    min_compression_ratio: float = Field(default=0.3, description="Minimum compression ratio")
    max_compression_ratio: float = Field(default=0.8, description="Maximum compression ratio")
    
    # Template settings
    template_sections: List[str] = Field(
        default_factory=lambda: ["key_points", "decisions", "files", "issues"],
        description="Template sections to include"
    )


class SummaryResult(BaseModel):
    """Summarization result with metrics."""
    
    summary_id: str = Field(..., description="Unique summary identifier")
    strategy: SummarizationStrategy = Field(..., description="Strategy used")
    quality: SummaryQuality = Field(..., description="Quality level achieved")
    
    # Content
    original_content: str = Field(..., description="Original content")
    summary_content: str = Field(..., description="Generated summary")
    
    # Metrics
    original_length: int = Field(..., description="Original content length in words")
    summary_length: int = Field(..., description="Summary length in words")
    compression_ratio: float = Field(..., description="Compression ratio (0-1)")
    
    # Quality metrics
    key_points_preserved: int = Field(..., description="Number of key points preserved")
    code_blocks_preserved: int = Field(..., description="Number of code blocks preserved")
    decisions_preserved: int = Field(..., description="Number of decisions preserved")
    semantic_score: float = Field(..., description="Semantic preservation score (0-1)")
    
    # Processing metrics
    processing_time: float = Field(..., description="Processing time in seconds")
    techniques_used: List[str] = Field(default_factory=list, description="Summarization techniques used")
    
    # Metadata
    timestamp: datetime = Field(default_factory=datetime.now, description="Summary timestamp")
    success: bool = Field(default=True, description="Whether summarization was successful")
    warnings: List[str] = Field(default_factory=list, description="Summarization warnings")


class ContextSummarizer:
    """
    📝 Advanced Context Summarizer
    
    Efficiently summarizes long contexts while preserving semantic meaning
    and critical information using multiple summarization strategies.
    """
    
    def __init__(self):
        # Quality thresholds for different levels
        self.quality_thresholds = {
            SummaryQuality.HIGH: {
                "min_semantic_score": 0.9,
                "max_compression": 0.5,
                "preserve_detail": True
            },
            SummaryQuality.MEDIUM: {
                "min_semantic_score": 0.8,
                "max_compression": 0.7,
                "preserve_detail": False
            },
            SummaryQuality.LOW: {
                "min_semantic_score": 0.7,
                "max_compression": 0.8,
                "preserve_detail": False
            }
        }
        
        # Summarization templates
        self.summary_templates = {
            "conversation": {
                "sections": ["key_points", "decisions", "files", "issues", "outcomes"],
                "format": "structured"
            },
            "code_review": {
                "sections": ["changes", "issues", "suggestions", "decisions"],
                "format": "technical"
            },
            "debugging": {
                "sections": ["problem", "investigation", "solution", "verification"],
                "format": "problem_solving"
            },
            "planning": {
                "sections": ["objectives", "approach", "decisions", "next_steps"],
                "format": "action_oriented"
            }
        }
        
        # Summarization statistics
        self.summarization_stats = {
            "total_summaries": 0,
            "total_compression_ratio": 0.0,
            "average_semantic_score": 0.0,
            "strategy_usage": {strategy: 0 for strategy in SummarizationStrategy},
            "quality_distribution": {quality: 0 for quality in SummaryQuality}
        }
    
    def summarize_content(
        self,
        content: str,
        config: Optional[SummarizationConfig] = None,
        context_type: str = "conversation"
    ) -> SummaryResult:
        """Summarize content using specified configuration."""
        start_time = time.time()
        summary_id = f"summary_{int(time.time() * 1000)}"
        
        # Use default config if not provided
        if config is None:
            config = SummarizationConfig()
        
        try:
            # Analyze content
            content_analysis = self._analyze_content(content)
            
            # Select optimal strategy if not specified
            if config.strategy == SummarizationStrategy.HYBRID:
                optimal_strategy = self._select_optimal_strategy(content_analysis, config)
            else:
                optimal_strategy = config.strategy
            
            # Generate summary
            summary_content, techniques_used = self._generate_summary(
                content, optimal_strategy, config, context_type, content_analysis
            )
            
            # Calculate metrics
            original_length = len(content.split())
            summary_length = len(summary_content.split())
            compression_ratio = 1 - (summary_length / original_length) if original_length > 0 else 0
            
            # Evaluate quality
            quality_metrics = self._evaluate_summary_quality(content, summary_content, content_analysis)
            
            # Create result
            result = SummaryResult(
                summary_id=summary_id,
                strategy=optimal_strategy,
                quality=config.quality,
                original_content=content,
                summary_content=summary_content,
                original_length=original_length,
                summary_length=summary_length,
                compression_ratio=compression_ratio,
                key_points_preserved=quality_metrics["key_points"],
                code_blocks_preserved=quality_metrics["code_blocks"],
                decisions_preserved=quality_metrics["decisions"],
                semantic_score=quality_metrics["semantic_score"],
                processing_time=time.time() - start_time,
                techniques_used=techniques_used
            )
            
            # Validate quality
            quality_threshold = self.quality_thresholds[config.quality]
            if result.semantic_score < quality_threshold["min_semantic_score"]:
                result.warnings.append(f"Semantic score {result.semantic_score:.2f} below threshold")
            
            if result.compression_ratio > quality_threshold["max_compression"]:
                result.warnings.append(f"Compression ratio {result.compression_ratio:.2f} exceeds maximum")
            
            # Update statistics
            self._update_statistics(result)
            
            logger.info(f"Content summarized: {compression_ratio:.1f} compression, {result.semantic_score:.2f} quality")
            return result
            
        except Exception as e:
            logger.error(f"Summarization failed: {e}")
            return SummaryResult(
                summary_id=summary_id,
                strategy=config.strategy,
                quality=config.quality,
                original_content=content,
                summary_content=content,  # Return original on failure
                original_length=len(content.split()),
                summary_length=len(content.split()),
                compression_ratio=0.0,
                key_points_preserved=0,
                code_blocks_preserved=0,
                decisions_preserved=0,
                semantic_score=0.0,
                processing_time=time.time() - start_time,
                warnings=[f"Summarization failed: {str(e)}"],
                success=False
            )
    
    def _analyze_content(self, content: str) -> Dict[str, Any]:
        """Analyze content to determine optimal summarization approach."""
        analysis = {
            "total_words": len(content.split()),
            "total_sentences": len(re.split(r'[.!?]+', content)),
            "code_blocks": len(re.findall(r'```[\s\S]*?```', content)),
            "inline_code": len(re.findall(r'`[^`]+`', content)),
            "file_references": len(re.findall(r'/[^\s]+\.[a-zA-Z]+', content)),
            "decisions": len(re.findall(r'\b(decided|chosen|implemented|approach)\b', content, re.IGNORECASE)),
            "errors": len(re.findall(r'\b(error|exception|failed|issue)\b', content, re.IGNORECASE)),
            "questions": len(re.findall(r'\?', content)),
            "key_indicators": []
        }
        
        # Identify content characteristics
        if analysis["code_blocks"] > 0 or analysis["inline_code"] > 5:
            analysis["key_indicators"].append("code_heavy")
        
        if analysis["decisions"] > 2:
            analysis["key_indicators"].append("decision_heavy")
        
        if analysis["errors"] > 1:
            analysis["key_indicators"].append("error_heavy")
        
        if analysis["questions"] > 3:
            analysis["key_indicators"].append("interactive")
        
        if analysis["total_words"] > 1000:
            analysis["key_indicators"].append("long_content")
        
        return analysis
    
    def _select_optimal_strategy(
        self,
        content_analysis: Dict[str, Any],
        config: SummarizationConfig
    ) -> SummarizationStrategy:
        """Select optimal summarization strategy based on content analysis."""
        indicators = content_analysis["key_indicators"]
        
        # Code-heavy content - use template-based
        if "code_heavy" in indicators:
            return SummarizationStrategy.TEMPLATE
        
        # Decision-heavy content - use extractive
        if "decision_heavy" in indicators:
            return SummarizationStrategy.EXTRACTIVE
        
        # Interactive content - use hybrid
        if "interactive" in indicators:
            return SummarizationStrategy.HYBRID
        
        # Long content - use abstractive
        if "long_content" in indicators and config.quality != SummaryQuality.HIGH:
            return SummarizationStrategy.ABSTRACTIVE
        
        # Default to extractive for safety
        return SummarizationStrategy.EXTRACTIVE
    
    def _generate_summary(
        self,
        content: str,
        strategy: SummarizationStrategy,
        config: SummarizationConfig,
        context_type: str,
        content_analysis: Dict[str, Any]
    ) -> Tuple[str, List[str]]:
        """Generate summary using specified strategy."""
        techniques_used = []
        
        if strategy == SummarizationStrategy.EXTRACTIVE:
            summary = self._extractive_summarization(content, config, content_analysis)
            techniques_used.append("extractive_sentences")
        
        elif strategy == SummarizationStrategy.ABSTRACTIVE:
            summary = self._abstractive_summarization(content, config, content_analysis)
            techniques_used.append("abstractive_generation")
        
        elif strategy == SummarizationStrategy.TEMPLATE:
            summary = self._template_summarization(content, config, context_type, content_analysis)
            techniques_used.append("template_based")
        
        elif strategy == SummarizationStrategy.HYBRID:
            summary = self._hybrid_summarization(content, config, content_analysis)
            techniques_used.extend(["extractive_sentences", "abstractive_generation"])
        
        else:
            # Fallback to extractive
            summary = self._extractive_summarization(content, config, content_analysis)
            techniques_used.append("extractive_fallback")
        
        return summary, techniques_used
    
    def _extractive_summarization(
        self,
        content: str,
        config: SummarizationConfig,
        content_analysis: Dict[str, Any]
    ) -> str:
        """Extract key sentences to create summary."""
        sentences = re.split(r'[.!?]+', content)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        if not sentences:
            return content
        
        # Score sentences by importance
        sentence_scores = []
        for i, sentence in enumerate(sentences):
            score = self._score_sentence_importance(sentence, content_analysis)
            sentence_scores.append((i, sentence, score))
        
        # Sort by score and select top sentences
        sentence_scores.sort(key=lambda x: x[2], reverse=True)
        
        # Calculate how many sentences to keep
        target_length = min(config.max_length, len(content.split()) // 2)
        selected_sentences = []
        current_length = 0
        
        for _, sentence, score in sentence_scores:
            sentence_length = len(sentence.split())
            if current_length + sentence_length <= target_length:
                selected_sentences.append((_, sentence))
                current_length += sentence_length
            
            if current_length >= target_length:
                break
        
        # Sort selected sentences by original order
        selected_sentences.sort(key=lambda x: x[0])
        
        # Join sentences
        summary = '. '.join([sentence for _, sentence in selected_sentences])
        
        # Add preserved elements
        if config.preserve_code:
            code_blocks = re.findall(r'```[\s\S]*?```', content)
            if code_blocks:
                summary += "\n\n" + "\n\n".join(code_blocks[:2])  # Limit to 2 blocks
        
        return summary
    
    def _abstractive_summarization(
        self,
        content: str,
        config: SummarizationConfig,
        content_analysis: Dict[str, Any]
    ) -> str:
        """Generate abstractive summary (simplified implementation)."""
        # Extract key information
        key_points = self._extract_key_points(content)
        decisions = self._extract_decisions(content)
        files = self._extract_file_references(content)
        issues = self._extract_issues(content)
        
        # Generate summary sections
        summary_parts = []
        
        if key_points:
            summary_parts.append(f"Key points: {'; '.join(key_points[:3])}")
        
        if decisions:
            summary_parts.append(f"Decisions made: {'; '.join(decisions[:2])}")
        
        if files:
            summary_parts.append(f"Files involved: {', '.join(files[:3])}")
        
        if issues:
            summary_parts.append(f"Issues addressed: {'; '.join(issues[:2])}")
        
        # Create coherent summary
        if summary_parts:
            summary = "Summary: " + " | ".join(summary_parts)
        else:
            # Fallback to first few sentences
            sentences = re.split(r'[.!?]+', content)[:3]
            summary = '. '.join([s.strip() for s in sentences if s.strip()])
        
        return summary
    
    def _template_summarization(
        self,
        content: str,
        config: SummarizationConfig,
        context_type: str,
        content_analysis: Dict[str, Any]
    ) -> str:
        """Generate template-based summary."""
        template = self.summary_templates.get(context_type, self.summary_templates["conversation"])
        
        summary_sections = []
        
        for section in template["sections"]:
            if section == "key_points":
                points = self._extract_key_points(content)
                if points:
                    summary_sections.append(f"**Key Points:** {'; '.join(points[:3])}")
            
            elif section == "decisions":
                decisions = self._extract_decisions(content)
                if decisions:
                    summary_sections.append(f"**Decisions:** {'; '.join(decisions[:2])}")
            
            elif section == "files":
                files = self._extract_file_references(content)
                if files:
                    summary_sections.append(f"**Files:** {', '.join(files[:3])}")
            
            elif section == "issues":
                issues = self._extract_issues(content)
                if issues:
                    summary_sections.append(f"**Issues:** {'; '.join(issues[:2])}")
            
            elif section == "outcomes":
                outcomes = self._extract_outcomes(content)
                if outcomes:
                    summary_sections.append(f"**Outcomes:** {'; '.join(outcomes[:2])}")
        
        # Add code blocks if configured
        if config.preserve_code:
            code_blocks = re.findall(r'```[\s\S]*?```', content)
            if code_blocks:
                summary_sections.append(f"**Code:** {len(code_blocks)} code block(s) preserved")
        
        return "\n\n".join(summary_sections) if summary_sections else content[:500]
    
    def _hybrid_summarization(
        self,
        content: str,
        config: SummarizationConfig,
        content_analysis: Dict[str, Any]
    ) -> str:
        """Combine extractive and abstractive approaches."""
        # Start with extractive summary
        extractive_summary = self._extractive_summarization(content, config, content_analysis)
        
        # Add abstractive elements
        key_insights = self._extract_key_insights(content)
        
        if key_insights:
            hybrid_summary = extractive_summary + "\n\nKey insights: " + "; ".join(key_insights[:2])
        else:
            hybrid_summary = extractive_summary
        
        return hybrid_summary
    
    # Helper methods for content extraction
    def _score_sentence_importance(self, sentence: str, content_analysis: Dict[str, Any]) -> float:
        """Score sentence importance for extractive summarization."""
        score = 0.0
        sentence_lower = sentence.lower()
        
        # Length bonus (not too short, not too long)
        word_count = len(sentence.split())
        if 5 <= word_count <= 25:
            score += 0.2
        
        # Key indicator bonus
        if any(indicator in sentence_lower for indicator in ['decided', 'implemented', 'created', 'fixed']):
            score += 0.3
        
        # Code reference bonus
        if re.search(r'`[^`]+`|/[^\s]+\.[a-zA-Z]+', sentence):
            score += 0.2
        
        # Question bonus
        if '?' in sentence:
            score += 0.1
        
        # Error/issue bonus
        if any(word in sentence_lower for word in ['error', 'issue', 'problem', 'bug']):
            score += 0.2
        
        return score
    
    def _extract_key_points(self, content: str) -> List[str]:
        """Extract key points from content."""
        # Look for sentences with key indicators
        sentences = re.split(r'[.!?]+', content)
        key_points = []
        
        for sentence in sentences:
            if any(indicator in sentence.lower() for indicator in 
                  ['important', 'key', 'main', 'primary', 'essential', 'critical']):
                key_points.append(sentence.strip()[:100])
        
        return key_points[:5]
    
    def _extract_decisions(self, content: str) -> List[str]:
        """Extract decisions from content."""
        decision_patterns = [
            r'decided to [^.!?]*',
            r'will use [^.!?]*',
            r'implemented [^.!?]*',
            r'chosen [^.!?]*'
        ]
        
        decisions = []
        for pattern in decision_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            decisions.extend([match[:80] for match in matches])
        
        return decisions[:3]
    
    def _extract_file_references(self, content: str) -> List[str]:
        """Extract file references from content."""
        file_patterns = [
            r'/[^\s]+\.[a-zA-Z]+',
            r'[a-zA-Z0-9_]+\.[a-zA-Z]+'
        ]
        
        files = []
        for pattern in file_patterns:
            matches = re.findall(pattern, content)
            files.extend(matches)
        
        return list(set(files))[:5]
    
    def _extract_issues(self, content: str) -> List[str]:
        """Extract issues from content."""
        issue_patterns = [
            r'error[^.!?]*',
            r'issue[^.!?]*',
            r'problem[^.!?]*',
            r'bug[^.!?]*'
        ]
        
        issues = []
        for pattern in issue_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            issues.extend([match[:60] for match in matches])
        
        return issues[:3]
    
    def _extract_outcomes(self, content: str) -> List[str]:
        """Extract outcomes from content."""
        outcome_patterns = [
            r'result[^.!?]*',
            r'outcome[^.!?]*',
            r'completed[^.!?]*',
            r'finished[^.!?]*'
        ]
        
        outcomes = []
        for pattern in outcome_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            outcomes.extend([match[:60] for match in matches])
        
        return outcomes[:2]
    
    def _extract_key_insights(self, content: str) -> List[str]:
        """Extract key insights for hybrid summarization."""
        insight_patterns = [
            r'learned that [^.!?]*',
            r'discovered [^.!?]*',
            r'found that [^.!?]*',
            r'realized [^.!?]*'
        ]
        
        insights = []
        for pattern in insight_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            insights.extend([match[:80] for match in matches])
        
        return insights[:2]
    
    def _evaluate_summary_quality(
        self,
        original: str,
        summary: str,
        content_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Evaluate summary quality metrics."""
        # Count preserved elements
        original_key_points = len(self._extract_key_points(original))
        summary_key_points = len(self._extract_key_points(summary))
        
        original_code_blocks = len(re.findall(r'```[\s\S]*?```', original))
        summary_code_blocks = len(re.findall(r'```[\s\S]*?```', summary))
        
        original_decisions = len(self._extract_decisions(original))
        summary_decisions = len(self._extract_decisions(summary))
        
        # Calculate semantic similarity (simplified)
        original_words = set(original.lower().split())
        summary_words = set(summary.lower().split())
        
        if original_words:
            intersection = original_words.intersection(summary_words)
            semantic_score = len(intersection) / len(original_words)
        else:
            semantic_score = 1.0
        
        return {
            "key_points": summary_key_points,
            "code_blocks": summary_code_blocks,
            "decisions": summary_decisions,
            "semantic_score": min(semantic_score, 1.0)
        }
    
    def _update_statistics(self, result: SummaryResult) -> None:
        """Update summarization statistics."""
        if result.success:
            self.summarization_stats["total_summaries"] += 1
            self.summarization_stats["total_compression_ratio"] += result.compression_ratio
            
            # Update averages
            total_summaries = self.summarization_stats["total_summaries"]
            current_avg_semantic = self.summarization_stats["average_semantic_score"]
            
            self.summarization_stats["average_semantic_score"] = (
                (current_avg_semantic * (total_summaries - 1) + result.semantic_score) / total_summaries
            )
            
            # Update strategy and quality usage
            self.summarization_stats["strategy_usage"][result.strategy] += 1
            self.summarization_stats["quality_distribution"][result.quality] += 1
    
    def get_summarization_stats(self) -> Dict[str, Any]:
        """Get summarization statistics."""
        stats = self.summarization_stats.copy()
        
        if stats["total_summaries"] > 0:
            stats["average_compression_ratio"] = stats["total_compression_ratio"] / stats["total_summaries"]
        else:
            stats["average_compression_ratio"] = 0.0
        
        return stats


# Global context summarizer instance
_context_summarizer: Optional[ContextSummarizer] = None


def get_context_summarizer() -> ContextSummarizer:
    """Get the global context summarizer instance."""
    global _context_summarizer
    if _context_summarizer is None:
        _context_summarizer = ContextSummarizer()
    return _context_summarizer
