"""
Autonomous Continue Engine Core - Phase 8C
Main orchestrator for autonomous development loops.

This module coordinates all autonomous continue components to provide
intelligent, self-improving development loops with error handling,
context adaptation, and progress monitoring.
"""

import asyncio
import logging
import time
from typing import Optional, Dict, Any, Callable, Awaitable, List
from datetime import datetime, timedelta
from pathlib import Path

from .models import (
    ContinueSession, ContinueConfig, LoopState, IterationResult,
    ProgressReport, ContextPerspective, SafetyLimits
)
from .loop_manager import LoopManager
from .error_intelligence import ErrorIntelligenceSystem
from .context_adapter import ContextAdaptationEngine

# Import existing infrastructure
try:
    from ..pydantic_ai.agents import get_coding_agent
    from ..code_intelligence import code_intelligence_hub
except ImportError:
    get_coding_agent = None
    code_intelligence_hub = None

logger = logging.getLogger(__name__)


class AutonomousContinueEngine:
    """
    Main Autonomous Continue Engine orchestrator.
    
    Coordinates autonomous development loops with:
    - Intelligent error handling and learning
    - Context adaptation based on progress and errors
    - Integration with Code Intelligence Hub
    - Safety mechanisms and monitoring
    - Progress tracking and reporting
    """
    
    def __init__(self, workspace_root: str):
        """Initialize the Autonomous Continue Engine."""
        self.workspace_root = Path(workspace_root)
        
        # Core components
        self.error_intelligence = ErrorIntelligenceSystem()
        self.context_adapter = ContextAdaptationEngine()
        self.loop_manager: Optional[LoopManager] = None
        
        # State management
        self.active_sessions: Dict[str, ContinueSession] = {}
        self.current_session: Optional[ContinueSession] = None
        
        # Integration components
        self.coding_agent = None
        self.code_intelligence = code_intelligence_hub
        
        # Callbacks
        self.progress_callbacks: List[Callable[[ProgressReport], Awaitable[None]]] = []
        self.completion_callbacks: List[Callable[[ContinueSession], Awaitable[None]]] = []
        
        logger.info(f"🤖 Autonomous Continue Engine initialized for workspace: {workspace_root}")
    
    async def initialize(self) -> Dict[str, Any]:
        """
        Initialize the Autonomous Continue Engine.
        
        Returns:
            Initialization status and configuration
        """
        try:
            logger.info("🚀 Initializing Autonomous Continue Engine...")
            
            # Initialize coding agent
            if get_coding_agent:
                self.coding_agent = get_coding_agent()
                logger.info("✅ Coding agent initialized")
            else:
                logger.warning("⚠️ Coding agent not available")
            
            # Initialize Code Intelligence Hub if available
            if self.code_intelligence:
                await self.code_intelligence.initialize()
                logger.info("✅ Code Intelligence Hub connected")
            else:
                logger.warning("⚠️ Code Intelligence Hub not available")
            
            logger.info("✅ Autonomous Continue Engine initialized successfully")
            
            return {
                'status': 'success',
                'workspace_root': str(self.workspace_root),
                'coding_agent_available': self.coding_agent is not None,
                'code_intelligence_available': self.code_intelligence is not None,
                'components_initialized': True
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Autonomous Continue Engine: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def start_autonomous_session(self, config: ContinueConfig) -> Dict[str, Any]:
        """
        Start a new autonomous continue session.
        
        Args:
            config: Configuration for the continue session
            
        Returns:
            Session start result
        """
        try:
            logger.info(f"🚀 Starting autonomous session: {config.session_name}")
            
            # Validate configuration
            if not self.coding_agent:
                return {
                    'status': 'error',
                    'error': 'Coding agent not available'
                }
            
            # Create loop manager
            self.loop_manager = LoopManager(config)
            
            # Set up callbacks
            self.loop_manager.set_progress_callback(self._handle_progress_update)
            self.loop_manager.set_iteration_callback(self._handle_iteration_completion)
            self.loop_manager.set_error_callback(self._handle_iteration_error)
            
            # Start session
            session = await self.loop_manager.start_session(config.initial_task)
            self.current_session = session
            self.active_sessions[session.session_id] = session
            
            # Start the autonomous loop
            asyncio.create_task(self._run_autonomous_loop())
            
            logger.info(f"✅ Autonomous session started: {session.session_id}")
            
            return {
                'status': 'success',
                'session_id': session.session_id,
                'config': config.model_dump(),
                'started_at': session.started_time.isoformat() if session.started_time else None
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to start autonomous session: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def pause_session(self, session_id: str) -> Dict[str, Any]:
        """Pause an autonomous session."""
        try:
            if session_id in self.active_sessions and self.loop_manager:
                await self.loop_manager.pause_session()
                logger.info(f"⏸️ Session paused: {session_id}")
                return {'status': 'success', 'message': 'Session paused'}
            else:
                return {'status': 'error', 'error': 'Session not found or not active'}
                
        except Exception as e:
            logger.error(f"❌ Failed to pause session: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def resume_session(self, session_id: str) -> Dict[str, Any]:
        """Resume a paused session."""
        try:
            if session_id in self.active_sessions and self.loop_manager:
                await self.loop_manager.resume_session()
                logger.info(f"▶️ Session resumed: {session_id}")
                return {'status': 'success', 'message': 'Session resumed'}
            else:
                return {'status': 'error', 'error': 'Session not found or not active'}
                
        except Exception as e:
            logger.error(f"❌ Failed to resume session: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def stop_session(self, session_id: str) -> Dict[str, Any]:
        """Stop an autonomous session."""
        try:
            if session_id in self.active_sessions and self.loop_manager:
                await self.loop_manager.stop_session()
                logger.info(f"⏹️ Session stopped: {session_id}")
                return {'status': 'success', 'message': 'Session stopped'}
            else:
                return {'status': 'error', 'error': 'Session not found or not active'}
                
        except Exception as e:
            logger.error(f"❌ Failed to stop session: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """Get status of a session."""
        try:
            if session_id in self.active_sessions:
                session = self.active_sessions[session_id]
                progress = self.loop_manager.get_current_progress() if self.loop_manager else None
                
                return {
                    'status': 'success',
                    'session': {
                        'session_id': session.session_id,
                        'state': session.state.value,
                        'current_iteration': session.current_iteration,
                        'success_rate': session.get_success_rate(),
                        'current_task': session.current_task,
                        'current_perspective': session.current_perspective.value
                    },
                    'progress': progress.model_dump() if progress else None
                }
            else:
                return {'status': 'error', 'error': 'Session not found'}
                
        except Exception as e:
            logger.error(f"❌ Failed to get session status: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def get_system_statistics(self) -> Dict[str, Any]:
        """Get system statistics and insights."""
        try:
            # Get error intelligence statistics
            error_stats = self.error_intelligence.get_error_statistics()
            
            # Get context adaptation statistics
            context_stats = self.context_adapter.get_adaptation_statistics()
            
            # Get session statistics
            session_stats = {
                'total_sessions': len(self.active_sessions),
                'active_sessions': len([s for s in self.active_sessions.values() if s.state == LoopState.RUNNING]),
                'completed_sessions': len([s for s in self.active_sessions.values() if s.state == LoopState.COMPLETED])
            }
            
            return {
                'status': 'success',
                'error_intelligence': error_stats,
                'context_adaptation': context_stats,
                'sessions': session_stats,
                'system_health': {
                    'coding_agent_available': self.coding_agent is not None,
                    'code_intelligence_available': self.code_intelligence is not None,
                    'active_loop_manager': self.loop_manager is not None
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get system statistics: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def add_progress_callback(self, callback: Callable[[ProgressReport], Awaitable[None]]):
        """Add a progress update callback."""
        self.progress_callbacks.append(callback)
    
    def add_completion_callback(self, callback: Callable[[ContinueSession], Awaitable[None]]):
        """Add a session completion callback."""
        self.completion_callbacks.append(callback)
    
    async def _run_autonomous_loop(self):
        """Run the main autonomous loop."""
        try:
            if not self.loop_manager or not self.current_session:
                return
            
            # Run the loop with our iteration function
            completed_session = await self.loop_manager.run_loop(self._execute_autonomous_iteration)
            
            # Notify completion callbacks
            for callback in self.completion_callbacks:
                try:
                    await callback(completed_session)
                except Exception as e:
                    logger.error(f"Completion callback failed: {e}")
            
            logger.info(f"🎯 Autonomous loop completed: {completed_session.session_id}")
            
        except Exception as e:
            logger.error(f"❌ Autonomous loop failed: {e}")
    
    async def _execute_autonomous_iteration(self, task: str, perspective: ContextPerspective) -> IterationResult:
        """
        Execute a single autonomous iteration.
        
        Args:
            task: Current task description
            perspective: Current context perspective
            
        Returns:
            Iteration result
        """
        iteration_start = datetime.now()
        
        try:
            logger.debug(f"🔄 Executing autonomous iteration: {task} (perspective: {perspective.value})")
            
            # Prepare context for the coding agent
            context = await self._prepare_iteration_context(task, perspective)
            
            # Execute task with coding agent
            if self.coding_agent:
                # Use the coding agent to execute the task
                result = await self._execute_with_coding_agent(task, context, perspective)
            else:
                # Fallback simulation
                result = IterationResult(
                    iteration_id="",
                    iteration_number=0,
                    task_description=task,
                    success=False,
                    output="Coding agent not available",
                    error_message="No coding agent available for execution",
                    start_time=iteration_start,
                    end_time=datetime.now(),
                    duration=datetime.now() - iteration_start,
                    perspective_used=perspective
                )
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Autonomous iteration failed: {e}")
            
            return IterationResult(
                iteration_id="",
                iteration_number=0,
                task_description=task,
                success=False,
                output="",
                error_message=str(e),
                start_time=iteration_start,
                end_time=datetime.now(),
                duration=datetime.now() - iteration_start,
                perspective_used=perspective
            )
    
    async def _prepare_iteration_context(self, task: str, perspective: ContextPerspective) -> Dict[str, Any]:
        """Prepare context for iteration execution."""
        context = {
            'task': task,
            'perspective': perspective.value,
            'workspace_root': str(self.workspace_root)
        }
        
        # Add Code Intelligence context if available
        if self.code_intelligence and self.current_session:
            try:
                # Search for relevant code context
                search_results = await self.code_intelligence.search(task)
                context['relevant_code'] = [
                    {
                        'file_path': result.file_path,
                        'content': result.content[:500],  # Limit content
                        'score': result.score
                    }
                    for result in search_results[:3]  # Top 3 results
                ]
            except Exception as e:
                logger.debug(f"Code Intelligence context failed: {e}")
        
        # Add error context
        if self.current_session:
            recent_errors = self.current_session.get_recent_errors(3)
            context['recent_errors'] = [
                {
                    'error_message': error.error_message,
                    'files_changed': error.files_changed
                }
                for error in recent_errors
            ]
        
        return context
    
    async def _execute_with_coding_agent(self, task: str, context: Dict[str, Any], perspective: ContextPerspective) -> IterationResult:
        """Execute task with the coding agent."""
        iteration_start = datetime.now()
        
        try:
            # Prepare prompt with context and perspective
            prompt = self._create_contextual_prompt(task, context, perspective)
            
            # Execute with coding agent (this would be the actual implementation)
            # For now, we'll simulate the execution
            success = True  # Simulate success
            output = f"Executed task: {task} with perspective: {perspective.value}"
            files_changed = []  # Would be populated by actual execution
            
            return IterationResult(
                iteration_id="",
                iteration_number=0,
                task_description=task,
                success=success,
                output=output,
                start_time=iteration_start,
                end_time=datetime.now(),
                duration=datetime.now() - iteration_start,
                files_changed=files_changed,
                perspective_used=perspective,
                tools_used=['coding_agent']
            )
            
        except Exception as e:
            return IterationResult(
                iteration_id="",
                iteration_number=0,
                task_description=task,
                success=False,
                output="",
                error_message=str(e),
                start_time=iteration_start,
                end_time=datetime.now(),
                duration=datetime.now() - iteration_start,
                perspective_used=perspective
            )
    
    def _create_contextual_prompt(self, task: str, context: Dict[str, Any], perspective: ContextPerspective) -> str:
        """Create a contextual prompt for the coding agent."""
        prompt_parts = [
            f"Task: {task}",
            f"Context Perspective: {perspective.value}",
            f"Workspace: {context['workspace_root']}"
        ]
        
        # Add relevant code context
        if 'relevant_code' in context and context['relevant_code']:
            prompt_parts.append("Relevant Code Context:")
            for code_item in context['relevant_code']:
                prompt_parts.append(f"- {code_item['file_path']}: {code_item['content'][:200]}...")
        
        # Add error context
        if 'recent_errors' in context and context['recent_errors']:
            prompt_parts.append("Recent Errors to Consider:")
            for error in context['recent_errors']:
                prompt_parts.append(f"- {error['error_message']}")
        
        return "\n\n".join(prompt_parts)
    
    async def _handle_progress_update(self, progress: ProgressReport):
        """Handle progress updates from loop manager."""
        try:
            # Notify all progress callbacks
            for callback in self.progress_callbacks:
                try:
                    await callback(progress)
                except Exception as e:
                    logger.error(f"Progress callback failed: {e}")
            
            logger.debug(f"📊 Progress update: Iteration {progress.iteration}, Success rate: {progress.success_rate:.1f}%")
            
        except Exception as e:
            logger.error(f"❌ Progress update handling failed: {e}")
    
    async def _handle_iteration_completion(self, iteration_result: IterationResult):
        """Handle iteration completion."""
        try:
            if not self.current_session:
                return
            
            # Analyze error if iteration failed
            if not iteration_result.success:
                error_pattern = await self.error_intelligence.analyze_error(iteration_result)
                if error_pattern:
                    self.current_session.error_patterns.append(error_pattern)
            
            # Learn from perspective effectiveness
            await self.context_adapter.learn_from_perspective_effectiveness(
                self.current_session, iteration_result.perspective_used, iteration_result
            )
            
            # Check if perspective switch is needed
            recent_errors = self.current_session.get_recent_errors(3)
            error_patterns = [
                pattern for pattern in self.current_session.error_patterns
                if pattern.pattern_id in [self.error_intelligence._generate_pattern_id(
                    self.error_intelligence._classify_error_type(error.error_message),
                    error.error_message
                ) for error in recent_errors]
            ]
            
            suggested_perspective = await self.context_adapter.suggest_perspective_switch(
                self.current_session, error_patterns
            )
            
            if suggested_perspective:
                await self.context_adapter.adapt_context(self.current_session, suggested_perspective)
            
            logger.debug(f"🔄 Iteration completed: {iteration_result.success}")
            
        except Exception as e:
            logger.error(f"❌ Iteration completion handling failed: {e}")
    
    async def _handle_iteration_error(self, exception: Exception, iteration_result: IterationResult):
        """Handle iteration errors."""
        try:
            logger.warning(f"⚠️ Iteration error: {exception}")
            
            # This could trigger additional error handling logic
            # such as emergency stops, alternative strategies, etc.
            
        except Exception as e:
            logger.error(f"❌ Error handling failed: {e}")


# Global instance
autonomous_continue_engine = AutonomousContinueEngine("/workspace")
