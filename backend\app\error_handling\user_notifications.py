"""
User Notification Manager for Clear, Actionable Error Messages

Provides user-friendly error messages with suggested actions and recovery guidance.
"""

import logging
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel

from .error_categorizer import E<PERSON>r<PERSON><PERSON><PERSON><PERSON>, <PERSON>rror<PERSON>ontext, ErrorSeverity
from .recovery_strategies import RecoveryAction, RecoveryResult, RecoveryStatus

logger = logging.getLogger(__name__)


class NotificationLevel(Enum):
    """Notification severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"
    SUCCESS = "success"


class NotificationType(Enum):
    """Types of notifications."""
    ERROR_OCCURRED = "error_occurred"
    RECOVERY_STARTED = "recovery_started"
    RECOVERY_SUCCESS = "recovery_success"
    RECOVERY_FAILED = "recovery_failed"
    RETRY_ATTEMPT = "retry_attempt"
    USER_ACTION_REQUIRED = "user_action_required"
    SYSTEM_STATUS = "system_status"


@dataclass
class ErrorNotification:
    """User-friendly error notification."""
    
    # Basic information
    title: str
    message: str
    level: NotificationLevel
    notification_type: NotificationType
    
    # Timing
    timestamp: datetime = field(default_factory=datetime.now)
    
    # Context
    error_category: Optional[ErrorCategory] = None
    error_severity: Optional[ErrorSeverity] = None
    operation: str = ""
    
    # User guidance
    suggested_actions: List[str] = field(default_factory=list)
    user_actions_required: List[str] = field(default_factory=list)
    help_links: List[str] = field(default_factory=list)
    
    # Technical details (for advanced users)
    technical_details: Dict[str, Any] = field(default_factory=dict)
    show_technical_details: bool = False
    
    # Recovery information
    recovery_in_progress: bool = False
    recovery_action: Optional[RecoveryAction] = None
    estimated_recovery_time: Optional[int] = None  # seconds
    
    # Metadata
    notification_id: str = ""
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert notification to dictionary for API responses."""
        return {
            "id": self.notification_id,
            "title": self.title,
            "message": self.message,
            "level": self.level.value,
            "type": self.notification_type.value,
            "timestamp": self.timestamp.isoformat(),
            "operation": self.operation,
            "suggested_actions": self.suggested_actions,
            "user_actions_required": self.user_actions_required,
            "help_links": self.help_links,
            "recovery_in_progress": self.recovery_in_progress,
            "recovery_action": self.recovery_action.value if self.recovery_action else None,
            "estimated_recovery_time": self.estimated_recovery_time,
            "technical_details": self.technical_details if self.show_technical_details else {},
            "session_id": self.session_id,
        }


class UserNotificationManager:
    """Manager for creating and handling user notifications."""
    
    def __init__(self):
        self.notification_history: List[ErrorNotification] = []
        self.active_notifications: Dict[str, ErrorNotification] = {}
        self.notification_counter = 0
        
        # User-friendly message templates
        self.message_templates = self._initialize_message_templates()
    
    def _initialize_message_templates(self) -> Dict[str, Dict[str, str]]:
        """Initialize user-friendly message templates."""
        return {
            # API and Network Errors
            ErrorCategory.API_RATE_LIMIT.value: {
                "title": "Rate Limit Reached",
                "message": "We're making requests too quickly. Taking a short break to avoid overwhelming the service.",
                "user_message": "The AI service is temporarily busy. We'll automatically retry in a moment.",
                "actions": [
                    "Wait for automatic retry",
                    "Try again in a few minutes if the issue persists"
                ]
            },
            
            ErrorCategory.API_UNAVAILABLE.value: {
                "title": "Service Temporarily Unavailable",
                "message": "The AI service is currently unavailable. We're trying alternative approaches.",
                "user_message": "Having trouble connecting to the AI service. Trying backup options...",
                "actions": [
                    "Wait for automatic recovery",
                    "Check your internet connection",
                    "Try again in a few minutes"
                ]
            },
            
            ErrorCategory.NETWORK_CONNECTION.value: {
                "title": "Connection Issue",
                "message": "Having trouble connecting to the service. Retrying with improved connection handling.",
                "user_message": "Network connection issue detected. Attempting to reconnect...",
                "actions": [
                    "Check your internet connection",
                    "Wait for automatic retry",
                    "Try refreshing the page"
                ]
            },
            
            ErrorCategory.NETWORK_TIMEOUT.value: {
                "title": "Request Timeout",
                "message": "The request is taking longer than expected. We're optimizing and trying again.",
                "user_message": "This is taking longer than usual. Optimizing the request and retrying...",
                "actions": [
                    "Wait for the optimized retry",
                    "Try breaking your request into smaller parts",
                    "Check if your request is very complex"
                ]
            },
            
            # Model and AI Errors
            ErrorCategory.MODEL_ERROR.value: {
                "title": "AI Processing Issue",
                "message": "The AI encountered an issue processing your request. Trying alternative approaches.",
                "user_message": "The AI is having trouble with this request. Trying a different approach...",
                "actions": [
                    "Wait for automatic retry with alternative approach",
                    "Try rephrasing your request",
                    "Break complex requests into simpler parts"
                ]
            },
            
            ErrorCategory.MODEL_RETRY.value: {
                "title": "AI Refining Response",
                "message": "The AI is refining its approach based on the specific requirements.",
                "user_message": "The AI is adjusting its approach to better handle your request...",
                "actions": [
                    "Wait for the AI to complete its refinement",
                    "No action needed - this is automatic"
                ]
            },
            
            ErrorCategory.MODEL_CONTEXT.value: {
                "title": "Request Too Complex",
                "message": "Your request contains more information than can be processed at once. We're optimizing it.",
                "user_message": "This request is quite complex. Breaking it down for better processing...",
                "actions": [
                    "Wait for automatic optimization",
                    "Consider breaking your request into smaller parts",
                    "Try focusing on the most important aspects first"
                ]
            },
            
            # User Input Errors
            ErrorCategory.USER_INPUT.value: {
                "title": "Input Validation Issue",
                "message": "There's an issue with the provided input that needs to be corrected.",
                "user_message": "Please check your input and try again.",
                "actions": [
                    "Review the input format requirements",
                    "Check for any missing required information",
                    "Ensure all data is in the correct format"
                ]
            },
            
            # System Errors
            ErrorCategory.SYSTEM_INTERNAL.value: {
                "title": "System Issue",
                "message": "An internal system issue occurred. Our recovery systems are working on it.",
                "user_message": "We encountered a system issue. Our automated recovery is in progress...",
                "actions": [
                    "Wait for automatic recovery",
                    "Try again in a few minutes",
                    "Contact support if the issue persists"
                ]
            },
            
            # Default/Unknown
            ErrorCategory.UNKNOWN.value: {
                "title": "Unexpected Issue",
                "message": "An unexpected issue occurred. We're analyzing and attempting recovery.",
                "user_message": "Something unexpected happened. We're working on resolving it...",
                "actions": [
                    "Wait for automatic recovery attempt",
                    "Try again in a moment",
                    "Contact support if the issue continues"
                ]
            }
        }
    
    def create_error_notification(
        self, 
        error_context: ErrorContext,
        operation: str = "",
        show_technical_details: bool = False
    ) -> ErrorNotification:
        """Create a user-friendly error notification."""
        
        self.notification_counter += 1
        notification_id = f"error_{self.notification_counter}_{int(datetime.now().timestamp())}"
        
        # Get template for this error category
        template = self.message_templates.get(
            error_context.category.value,
            self.message_templates[ErrorCategory.UNKNOWN.value]
        )
        
        # Determine notification level based on error severity
        level_mapping = {
            ErrorSeverity.LOW: NotificationLevel.INFO,
            ErrorSeverity.MEDIUM: NotificationLevel.WARNING,
            ErrorSeverity.HIGH: NotificationLevel.ERROR,
            ErrorSeverity.CRITICAL: NotificationLevel.CRITICAL
        }
        
        notification_level = level_mapping.get(error_context.severity, NotificationLevel.WARNING)
        
        # Create notification
        notification = ErrorNotification(
            notification_id=notification_id,
            title=template["title"],
            message=template.get("user_message", template["message"]),
            level=notification_level,
            notification_type=NotificationType.ERROR_OCCURRED,
            error_category=error_context.category,
            error_severity=error_context.severity,
            operation=operation,
            suggested_actions=template.get("actions", []),
            technical_details={
                "error_type": type(error_context.error).__name__,
                "error_message": str(error_context.error),
                "category": error_context.category.value,
                "severity": error_context.severity.value,
                "confidence": error_context.confidence,
                "timestamp": error_context.timestamp.isoformat()
            },
            show_technical_details=show_technical_details,
            session_id=error_context.session_id,
            user_id=error_context.user_id
        )
        
        # Add specific guidance based on error context
        if error_context.suggested_actions:
            notification.suggested_actions.extend(error_context.suggested_actions)
        
        # Store notification
        self.notification_history.append(notification)
        self.active_notifications[notification_id] = notification
        
        logger.info(f"Created error notification: {notification.title}")
        
        return notification
    
    def create_recovery_notification(
        self,
        recovery_result: RecoveryResult,
        operation: str = "",
        session_id: Optional[str] = None
    ) -> ErrorNotification:
        """Create a notification for recovery attempts."""
        
        self.notification_counter += 1
        notification_id = f"recovery_{self.notification_counter}_{int(datetime.now().timestamp())}"
        
        # Determine notification type and level based on recovery status
        if recovery_result.status == RecoveryStatus.IN_PROGRESS:
            notification_type = NotificationType.RECOVERY_STARTED
            level = NotificationLevel.INFO
            title = "Recovery in Progress"
            message = f"Attempting to recover using {recovery_result.action.value.replace('_', ' ')}..."
        
        elif recovery_result.status == RecoveryStatus.SUCCESS:
            notification_type = NotificationType.RECOVERY_SUCCESS
            level = NotificationLevel.SUCCESS
            title = "Recovery Successful"
            message = f"Successfully recovered from the issue. {recovery_result.message}"
        
        elif recovery_result.status == RecoveryStatus.PARTIAL_SUCCESS:
            notification_type = NotificationType.RECOVERY_SUCCESS
            level = NotificationLevel.WARNING
            title = "Partial Recovery"
            message = f"Partially recovered from the issue. {recovery_result.message}"
        
        elif recovery_result.status == RecoveryStatus.FAILED:
            notification_type = NotificationType.RECOVERY_FAILED
            level = NotificationLevel.ERROR
            title = "Recovery Failed"
            message = f"Unable to automatically recover. {recovery_result.message}"
        
        else:  # SKIPPED
            notification_type = NotificationType.SYSTEM_STATUS
            level = NotificationLevel.INFO
            title = "Recovery Skipped"
            message = f"Recovery was skipped. {recovery_result.message}"
        
        # Create notification
        notification = ErrorNotification(
            notification_id=notification_id,
            title=title,
            message=message,
            level=level,
            notification_type=notification_type,
            operation=operation,
            recovery_in_progress=(recovery_result.status == RecoveryStatus.IN_PROGRESS),
            recovery_action=recovery_result.action,
            technical_details={
                "recovery_action": recovery_result.action.value,
                "recovery_status": recovery_result.status.value,
                "duration": recovery_result.duration.total_seconds() if recovery_result.duration else None,
                "original_error": str(recovery_result.original_error) if recovery_result.original_error else None
            },
            session_id=session_id
        )
        
        # Add appropriate actions based on recovery status
        if recovery_result.status == RecoveryStatus.SUCCESS:
            notification.suggested_actions = ["Continue with your task"]
        elif recovery_result.status == RecoveryStatus.FAILED:
            notification.suggested_actions = [
                "Try again in a few minutes",
                "Contact support if the issue persists",
                "Check system status page"
            ]
            notification.user_actions_required = ["Manual intervention may be required"]
        
        # Store notification
        self.notification_history.append(notification)
        self.active_notifications[notification_id] = notification
        
        logger.info(f"Created recovery notification: {notification.title}")
        
        return notification
    
    def create_retry_notification(
        self,
        attempt_number: int,
        max_attempts: int,
        delay: float,
        reason: str,
        operation: str = "",
        session_id: Optional[str] = None
    ) -> ErrorNotification:
        """Create a notification for retry attempts."""
        
        self.notification_counter += 1
        notification_id = f"retry_{self.notification_counter}_{int(datetime.now().timestamp())}"
        
        notification = ErrorNotification(
            notification_id=notification_id,
            title=f"Retry Attempt {attempt_number}/{max_attempts}",
            message=f"Retrying in {delay:.1f} seconds due to {reason}...",
            level=NotificationLevel.INFO,
            notification_type=NotificationType.RETRY_ATTEMPT,
            operation=operation,
            suggested_actions=["Please wait for the retry to complete"],
            estimated_recovery_time=int(delay),
            technical_details={
                "attempt_number": attempt_number,
                "max_attempts": max_attempts,
                "delay": delay,
                "reason": reason
            },
            session_id=session_id
        )
        
        # Store notification
        self.notification_history.append(notification)
        self.active_notifications[notification_id] = notification
        
        return notification
    
    def dismiss_notification(self, notification_id: str) -> bool:
        """Dismiss an active notification."""
        if notification_id in self.active_notifications:
            del self.active_notifications[notification_id]
            logger.info(f"Dismissed notification: {notification_id}")
            return True
        return False
    
    def get_active_notifications(
        self, 
        session_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> List[ErrorNotification]:
        """Get active notifications, optionally filtered by session or user."""
        notifications = list(self.active_notifications.values())
        
        if session_id:
            notifications = [n for n in notifications if n.session_id == session_id]
        
        if user_id:
            notifications = [n for n in notifications if n.user_id == user_id]
        
        return sorted(notifications, key=lambda n: n.timestamp, reverse=True)
    
    def get_notification_history(
        self,
        limit: int = 50,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> List[ErrorNotification]:
        """Get notification history."""
        notifications = self.notification_history
        
        if session_id:
            notifications = [n for n in notifications if n.session_id == session_id]
        
        if user_id:
            notifications = [n for n in notifications if n.user_id == user_id]
        
        return sorted(notifications, key=lambda n: n.timestamp, reverse=True)[:limit]
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get notification statistics."""
        if not self.notification_history:
            return {"total_notifications": 0}
        
        # Count by type
        type_counts = {}
        level_counts = {}
        
        for notification in self.notification_history:
            type_counts[notification.notification_type.value] = type_counts.get(notification.notification_type.value, 0) + 1
            level_counts[notification.level.value] = level_counts.get(notification.level.value, 0) + 1
        
        return {
            "total_notifications": len(self.notification_history),
            "active_notifications": len(self.active_notifications),
            "type_distribution": type_counts,
            "level_distribution": level_counts,
            "recent_notifications": [
                {
                    "title": n.title,
                    "level": n.level.value,
                    "type": n.notification_type.value,
                    "timestamp": n.timestamp.isoformat()
                }
                for n in self.notification_history[-10:]
            ]
        }
