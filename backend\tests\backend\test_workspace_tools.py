#!/usr/bin/env python3
"""
Test Tools in Proper Workspace Environment

This script creates a proper test workspace and tests all tools
within the workspace constraints to verify they work correctly.
"""

import asyncio
import logging
import sys
import os
import tempfile
import shutil
from pathlib import Path

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.pydantic_ai.agents import coding_agent
from app.pydantic_ai.dependencies import create_dependencies

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def setup_test_workspace():
    """Create a proper test workspace with sample files."""
    print("🏗️  Setting up test workspace...")
    
    # Create temporary directory for testing
    test_workspace = Path(tempfile.mkdtemp(prefix="deepnexus_test_"))
    print(f"📁 Test workspace: {test_workspace}")
    
    # Create sample files and directories
    (test_workspace / "src").mkdir()
    (test_workspace / "tests").mkdir()
    (test_workspace / "docs").mkdir()
    
    # Create sample Python file
    sample_py = test_workspace / "src" / "main.py"
    sample_py.write_text("""#!/usr/bin/env python3
\"\"\"
Sample Python application for testing.
\"\"\"

def hello_world():
    \"\"\"Print hello world message.\"\"\"
    print("Hello, World!")

def add_numbers(a: int, b: int) -> int:
    \"\"\"Add two numbers together.\"\"\"
    return a + b

if __name__ == "__main__":
    hello_world()
    result = add_numbers(5, 3)
    print(f"5 + 3 = {result}")
""")
    
    # Create sample test file
    test_py = test_workspace / "tests" / "test_main.py"
    test_py.write_text("""#!/usr/bin/env python3
\"\"\"
Tests for main.py
\"\"\"

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from main import add_numbers

def test_add_numbers():
    \"\"\"Test the add_numbers function.\"\"\"
    assert add_numbers(2, 3) == 5
    assert add_numbers(0, 0) == 0
    assert add_numbers(-1, 1) == 0

if __name__ == "__main__":
    test_add_numbers()
    print("All tests passed!")
""")
    
    # Create README file
    readme = test_workspace / "README.md"
    readme.write_text("""# Test Project

This is a test project for verifying DeepNexus AI Coder Agent functionality.

## Structure

- `src/` - Source code
- `tests/` - Test files
- `docs/` - Documentation

## Features

- Simple Python application
- Unit tests
- Documentation
""")
    
    # Create requirements file
    requirements = test_workspace / "requirements.txt"
    requirements.write_text("""# Test project requirements
pytest>=7.0.0
black>=22.0.0
flake8>=4.0.0
""")
    
    # Initialize git repository
    try:
        import subprocess
        subprocess.run(["git", "init"], cwd=test_workspace, check=True, capture_output=True)
        subprocess.run(["git", "config", "user.name", "Test User"], cwd=test_workspace, check=True)
        subprocess.run(["git", "config", "user.email", "<EMAIL>"], cwd=test_workspace, check=True)
        subprocess.run(["git", "add", "."], cwd=test_workspace, check=True)
        subprocess.run(["git", "commit", "-m", "Initial commit"], cwd=test_workspace, check=True)
        print("✅ Git repository initialized")
    except Exception as e:
        print(f"⚠️  Git initialization failed: {e}")
    
    return test_workspace


async def test_file_operations(workspace_path: Path):
    """Test file operation tools in the workspace."""
    print("\n🧪 Testing File Operations in Workspace...")
    
    try:
        # Create dependencies with the test workspace
        deps = await create_dependencies(workspace_root=str(workspace_path))
        
        # Test 1: List directory contents
        result = await coding_agent.run(
            "Please list the contents of the current directory and describe what you see.",
            deps=deps
        )
        print(f"✅ Directory listing: {result.output[:200]}...")
        
        # Test 2: Read a file
        result = await coding_agent.run(
            "Please read the README.md file and tell me what this project is about.",
            deps=deps
        )
        print(f"✅ File reading: {result.output[:200]}...")
        
        # Test 3: Read source code
        result = await coding_agent.run(
            "Please read the src/main.py file and analyze the code structure.",
            deps=deps
        )
        print(f"✅ Code reading: {result.output[:200]}...")
        
        # Test 4: Create a new file
        result = await coding_agent.run(
            "Please create a new file called 'config.py' in the src directory with some basic configuration settings.",
            deps=deps
        )
        print(f"✅ File creation: {result.output[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ File operations error: {e}")
        return False


async def test_git_operations(workspace_path: Path):
    """Test git operation tools in the workspace."""
    print("\n🧪 Testing Git Operations in Workspace...")
    
    try:
        # Create dependencies with the test workspace
        deps = await create_dependencies(workspace_root=str(workspace_path))
        
        # Test 1: Check git status
        result = await coding_agent.run(
            "Please check the git status of this repository.",
            deps=deps
        )
        print(f"✅ Git status: {result.output[:200]}...")
        
        # Test 2: Show git log
        result = await coding_agent.run(
            "Please show the git log to see the commit history.",
            deps=deps
        )
        print(f"✅ Git log: {result.output[:200]}...")
        
        # Test 3: Check for any changes
        result = await coding_agent.run(
            "Please check if there are any uncommitted changes and show the git diff if any.",
            deps=deps
        )
        print(f"✅ Git diff: {result.output[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Git operations error: {e}")
        return False


async def test_code_analysis(workspace_path: Path):
    """Test code analysis tools in the workspace."""
    print("\n🧪 Testing Code Analysis in Workspace...")
    
    try:
        # Create dependencies with the test workspace
        deps = await create_dependencies(workspace_root=str(workspace_path))
        
        # Test 1: Analyze code structure
        result = await coding_agent.run(
            "Please analyze the code structure and complexity of the src/main.py file.",
            deps=deps
        )
        print(f"✅ Code analysis: {result.output[:200]}...")
        
        # Test 2: Get project context
        result = await coding_agent.run(
            "Please analyze the overall project structure and provide a summary of this codebase.",
            deps=deps
        )
        print(f"✅ Project context: {result.output[:200]}...")
        
        # Test 3: Search for code patterns
        result = await coding_agent.run(
            "Please search for any functions in the codebase and describe what they do.",
            deps=deps
        )
        print(f"✅ Code search: {result.output[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Code analysis error: {e}")
        return False


async def test_linting_and_testing(workspace_path: Path):
    """Test linting and testing tools in the workspace."""
    print("\n🧪 Testing Linting and Testing in Workspace...")
    
    try:
        # Create dependencies with the test workspace
        deps = await create_dependencies(workspace_root=str(workspace_path))
        
        # Test 1: Run linting
        result = await coding_agent.run(
            "Please run linting on the Python files to check for code quality issues.",
            deps=deps
        )
        print(f"✅ Linting: {result.output[:200]}...")
        
        # Test 2: Run tests
        result = await coding_agent.run(
            "Please run the tests in the tests directory to verify the code works correctly.",
            deps=deps
        )
        print(f"✅ Testing: {result.output[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Linting/testing error: {e}")
        return False


async def test_complex_workflow(workspace_path: Path):
    """Test a complex multi-tool workflow in the workspace."""
    print("\n🧪 Testing Complex Multi-Tool Workflow in Workspace...")
    
    try:
        # Create dependencies with the test workspace
        deps = await create_dependencies(workspace_root=str(workspace_path))
        
        # Complex workflow test
        result = await coding_agent.run(
            """Please perform a comprehensive analysis of this project:
            
            1. List all files in the project
            2. Read and analyze the main source code
            3. Check the git status and history
            4. Run code quality checks
            5. Provide recommendations for improvements
            
            Use multiple tools as needed to complete this comprehensive analysis.""",
            deps=deps
        )
        print(f"✅ Complex workflow: {result.output[:300]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Complex workflow error: {e}")
        return False


async def test_change_tracking(workspace_path: Path):
    """Test change tracking tools in the workspace."""
    print("\n🧪 Testing Change Tracking in Workspace...")
    
    try:
        # Create dependencies with the test workspace
        deps = await create_dependencies(workspace_root=str(workspace_path))
        
        # Test 1: Start monitoring changes
        result = await coding_agent.run(
            "Please start monitoring file changes in this project.",
            deps=deps
        )
        print(f"✅ Change monitoring: {result.output[:200]}...")
        
        # Test 2: Make a change and track it
        result = await coding_agent.run(
            "Please modify the src/main.py file to add a new function, then track the changes made.",
            deps=deps
        )
        print(f"✅ Change tracking: {result.output[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Change tracking error: {e}")
        return False


async def cleanup_test_workspace(workspace_path: Path):
    """Clean up the test workspace."""
    try:
        shutil.rmtree(workspace_path)
        print(f"🧹 Cleaned up test workspace: {workspace_path}")
    except Exception as e:
        print(f"⚠️  Failed to clean up workspace: {e}")


async def main():
    """Run all workspace tool tests."""
    print("🚀 Testing Tools in Proper Workspace Environment")
    print("=" * 70)
    
    workspace_path = None
    
    try:
        # Set up test workspace
        workspace_path = await setup_test_workspace()
        
        # Run all tests
        tests = [
            ("File Operations", test_file_operations),
            ("Git Operations", test_git_operations),
            ("Code Analysis", test_code_analysis),
            ("Linting and Testing", test_linting_and_testing),
            ("Change Tracking", test_change_tracking),
            ("Complex Workflow", test_complex_workflow),
        ]
        
        results = []
        
        for test_name, test_func in tests:
            print(f"\n🧪 Running {test_name} test...")
            try:
                success = await test_func(workspace_path)
                results.append((test_name, success))
                status = "✅ PASSED" if success else "❌ FAILED"
                print(f"{status}: {test_name}")
            except Exception as e:
                print(f"❌ FAILED: {test_name} - {e}")
                results.append((test_name, False))
        
        # Summary
        print("\n" + "=" * 70)
        print("📊 Workspace Tool Test Results:")
        print("=" * 70)
        
        passed = sum(1 for _, success in results if success)
        total = len(results)
        
        for test_name, success in results:
            status = "✅ PASSED" if success else "❌ FAILED"
            print(f"{status}: {test_name}")
        
        print(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All workspace tool tests passed! Tools are working perfectly!")
            return 0
        else:
            print("⚠️  Some tests failed. Check the output above for details.")
            return 1
            
    finally:
        # Clean up
        if workspace_path:
            await cleanup_test_workspace(workspace_path)


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
