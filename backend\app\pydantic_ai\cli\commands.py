"""
CLI Commands for Pydantic AI

This module provides command-line interface commands for interacting with
the Pydantic AI system including agent execution, testing, and evaluation.
"""

import asyncio
import json
import logging
import time
from pathlib import Path
from typing import Any, Dict, List, Optional

import click

from ..agents import coding_agent, vision_agent, simple_vision_agent
from ..dependencies import create_dependencies, create_vision_dependencies
from ..testing import (
    create_test_dataset, run_performance_tests, generate_performance_report,
    create_coding_evaluators, create_vision_evaluators, ComprehensiveEvaluator
)
from .utils import format_output, parse_agent_args

logger = logging.getLogger(__name__)


@click.command()
@click.argument('prompt', type=str)
@click.option('--agent', '-a', type=click.Choice(['coding', 'vision', 'simple-vision']), 
              default='coding', help='Agent type to use')
@click.option('--workspace', '-w', type=click.Path(exists=True), default='.', 
              help='Workspace directory')
@click.option('--output', '-o', type=click.Path(), help='Output file for results')
@click.option('--format', '-f', type=click.Choice(['text', 'json', 'markdown']), 
              default='text', help='Output format')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
def run_agent_command(prompt: str, agent: str, workspace: str, output: Optional[str], 
                     format: str, verbose: bool):
    """Run an AI agent with the given prompt."""
    
    async def _run_agent():
        try:
            # Setup logging
            if verbose:
                logging.basicConfig(level=logging.INFO)
            
            # Select agent
            if agent == 'coding':
                selected_agent = coding_agent
                deps = await create_dependencies(workspace_root=workspace)
            elif agent == 'vision':
                selected_agent = vision_agent
                deps = await create_vision_dependencies(workspace_root=workspace)
            elif agent == 'simple-vision':
                selected_agent = simple_vision_agent
                deps = None  # Simple vision agent doesn't need deps
            else:
                raise ValueError(f"Unknown agent type: {agent}")
            
            # Run agent
            start_time = time.time()
            
            if deps:
                result = await selected_agent.run(prompt, deps=deps)
            else:
                result = await selected_agent.run(prompt)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # Format output
            output_data = {
                "prompt": prompt,
                "agent": agent,
                "result": result.output,
                "execution_time": execution_time,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            formatted_output = format_output(output_data, format)
            
            # Save or print output
            if output:
                with open(output, 'w') as f:
                    f.write(formatted_output)
                click.echo(f"✅ Results saved to {output}")
            else:
                click.echo(formatted_output)
            
            if verbose:
                click.echo(f"⏱️  Execution time: {execution_time:.2f}s")
            
        except Exception as e:
            click.echo(f"❌ Error: {e}", err=True)
            return 1
        
        return 0
    
    return asyncio.run(_run_agent())


@click.command()
@click.option('--dataset', '-d', type=click.Choice(['coding', 'vision', 'comprehensive']), 
              default='coding', help='Test dataset to use')
@click.option('--workspace', '-w', type=click.Path(exists=True), default='.', 
              help='Workspace directory')
@click.option('--runs', '-r', type=int, default=5, help='Number of test runs')
@click.option('--output', '-o', type=click.Path(), help='Output file for results')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
def test_command(dataset: str, workspace: str, runs: int, output: Optional[str], verbose: bool):
    """Run tests on the AI agents."""
    
    async def _run_tests():
        try:
            # Setup logging
            if verbose:
                logging.basicConfig(level=logging.INFO)
            
            # Create test dataset
            test_dataset = create_test_dataset(dataset)
            click.echo(f"🧪 Running {len(test_dataset)} tests with {runs} runs each...")
            
            # Select agent and dependencies based on dataset
            if dataset == 'vision':
                agent = vision_agent
                deps = await create_vision_dependencies(workspace_root=workspace)
            else:
                agent = coding_agent
                deps = await create_dependencies(workspace_root=workspace)
            
            # Run tests
            start_time = time.time()
            
            results = await run_performance_tests(
                agent=agent,
                test_dataset=test_dataset,
                deps=deps,
                num_runs=runs,
                concurrency=1
            )
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Generate report
            report = generate_performance_report(results)
            
            # Add summary info
            report['test_summary'] = {
                "dataset": dataset,
                "total_tests": len(test_dataset),
                "runs_per_test": runs,
                "total_execution_time": total_time
            }
            
            # Format and output results
            if output:
                with open(output, 'w') as f:
                    json.dump(report, f, indent=2)
                click.echo(f"✅ Test results saved to {output}")
            else:
                # Print summary
                summary = report['summary']
                click.echo(f"\n📊 Test Results Summary:")
                click.echo(f"   Total tests: {summary['total_tests']}")
                click.echo(f"   Success rate: {summary['overall_success_rate']:.2%}")
                click.echo(f"   Avg response time: {summary['overall_avg_response_time']:.2f}s")
                click.echo(f"   Total execution time: {total_time:.2f}s")
                
                # Print recommendations
                click.echo(f"\n💡 Recommendations:")
                for rec in report['recommendations']:
                    click.echo(f"   • {rec}")
            
            if verbose:
                click.echo(f"\n📈 Detailed Results:")
                for result in results:
                    click.echo(f"   {result.test_name}: {result.success_rate:.2%} success, "
                             f"{result.avg_response_time:.2f}s avg")
            
        except Exception as e:
            click.echo(f"❌ Error: {e}", err=True)
            return 1
        
        return 0
    
    return asyncio.run(_run_tests())


@click.command()
@click.option('--agent', '-a', type=click.Choice(['coding', 'vision']), 
              default='coding', help='Agent type to benchmark')
@click.option('--workspace', '-w', type=click.Path(exists=True), default='.', 
              help='Workspace directory')
@click.option('--runs', '-r', type=int, default=10, help='Number of benchmark runs')
@click.option('--concurrency', '-c', type=int, default=1, help='Concurrent requests')
@click.option('--output', '-o', type=click.Path(), help='Output file for results')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
def benchmark_command(agent: str, workspace: str, runs: int, concurrency: int, 
                     output: Optional[str], verbose: bool):
    """Run performance benchmarks on AI agents."""
    
    async def _run_benchmark():
        try:
            # Setup logging
            if verbose:
                logging.basicConfig(level=logging.INFO)
            
            # Create test dataset
            dataset_type = 'vision' if agent == 'vision' else 'coding'
            test_dataset = create_test_dataset(dataset_type)
            
            # Select agent and dependencies
            if agent == 'vision':
                selected_agent = vision_agent
                deps = await create_vision_dependencies(workspace_root=workspace)
            else:
                selected_agent = coding_agent
                deps = await create_dependencies(workspace_root=workspace)
            
            click.echo(f"🏃 Running benchmark: {agent} agent, {runs} runs, concurrency {concurrency}")
            
            # Run benchmark
            start_time = time.time()
            
            results = await run_performance_tests(
                agent=selected_agent,
                test_dataset=test_dataset,
                deps=deps,
                num_runs=runs,
                concurrency=concurrency,
                warmup_runs=2
            )
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Generate detailed report
            report = generate_performance_report(results)
            
            # Add benchmark metadata
            report['benchmark_config'] = {
                "agent": agent,
                "runs": runs,
                "concurrency": concurrency,
                "total_execution_time": total_time,
                "tests_count": len(test_dataset)
            }
            
            # Output results
            if output:
                with open(output, 'w') as f:
                    json.dump(report, f, indent=2)
                click.echo(f"✅ Benchmark results saved to {output}")
            else:
                # Print benchmark summary
                summary = report['summary']
                click.echo(f"\n🏆 Benchmark Results:")
                click.echo(f"   Agent: {agent}")
                click.echo(f"   Total tests: {summary['total_tests']}")
                click.echo(f"   Success rate: {summary['overall_success_rate']:.2%}")
                click.echo(f"   Avg response time: {summary['overall_avg_response_time']:.2f}s")
                click.echo(f"   Max throughput: {summary['overall_max_throughput']:.2f} req/s")
                click.echo(f"   Total time: {total_time:.2f}s")
            
        except Exception as e:
            click.echo(f"❌ Error: {e}", err=True)
            return 1
        
        return 0
    
    return asyncio.run(_run_benchmark())


@click.command()
@click.argument('prompt', type=str)
@click.option('--agent', '-a', type=click.Choice(['coding', 'vision']), 
              default='coding', help='Agent type to evaluate')
@click.option('--workspace', '-w', type=click.Path(exists=True), default='.', 
              help='Workspace directory')
@click.option('--evaluators', '-e', multiple=True, 
              type=click.Choice(['code-quality', 'tool-accuracy', 'response-time']),
              default=['code-quality', 'response-time'], help='Evaluators to use')
@click.option('--output', '-o', type=click.Path(), help='Output file for results')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
def evaluate_command(prompt: str, agent: str, workspace: str, evaluators: List[str], 
                    output: Optional[str], verbose: bool):
    """Evaluate an AI agent response with custom evaluators."""
    
    async def _run_evaluation():
        try:
            # Setup logging
            if verbose:
                logging.basicConfig(level=logging.INFO)
            
            # Select agent and dependencies
            if agent == 'vision':
                selected_agent = vision_agent
                deps = await create_vision_dependencies(workspace_root=workspace)
                base_evaluators = create_vision_evaluators()
            else:
                selected_agent = coding_agent
                deps = await create_dependencies(workspace_root=workspace)
                base_evaluators = create_coding_evaluators()
            
            # Filter evaluators based on selection
            selected_evaluators = []
            for evaluator in base_evaluators:
                if 'code-quality' in evaluators and 'CodeQuality' in evaluator.name:
                    selected_evaluators.append(evaluator)
                elif 'tool-accuracy' in evaluators and 'ToolCallAccuracy' in evaluator.name:
                    selected_evaluators.append(evaluator)
                elif 'response-time' in evaluators and 'ResponseTime' in evaluator.name:
                    selected_evaluators.append(evaluator)
            
            if not selected_evaluators:
                selected_evaluators = base_evaluators  # Use all if none selected
            
            # Create comprehensive evaluator
            comprehensive_evaluator = ComprehensiveEvaluator(selected_evaluators)
            
            click.echo(f"🔍 Evaluating prompt with {len(selected_evaluators)} evaluators...")
            
            # Run agent
            start_time = time.time()
            result = await selected_agent.run(prompt, deps=deps)
            end_time = time.time()
            execution_time = end_time - start_time
            
            # Run evaluation
            evaluation = await comprehensive_evaluator.evaluate(
                input_data=prompt,
                output_data=result.output,
                messages=getattr(result, 'messages', []),
                metadata={"response_time": execution_time}
            )
            
            # Format results
            evaluation_data = {
                "prompt": prompt,
                "agent": agent,
                "response": result.output,
                "execution_time": execution_time,
                "evaluation": {
                    "score": evaluation.score,
                    "passed": evaluation.passed,
                    "feedback": evaluation.feedback,
                    "details": evaluation.details
                },
                "evaluators_used": [e.name for e in selected_evaluators],
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # Output results
            if output:
                with open(output, 'w') as f:
                    json.dump(evaluation_data, f, indent=2)
                click.echo(f"✅ Evaluation results saved to {output}")
            else:
                # Print evaluation summary
                click.echo(f"\n📊 Evaluation Results:")
                click.echo(f"   Overall Score: {evaluation.score:.2f}")
                click.echo(f"   Passed: {'✅ Yes' if evaluation.passed else '❌ No'}")
                click.echo(f"   Execution Time: {execution_time:.2f}s")
                click.echo(f"\n💬 Feedback:")
                click.echo(f"   {evaluation.feedback}")
                
                if verbose:
                    click.echo(f"\n📝 Response Preview:")
                    click.echo(f"   {result.output[:200]}...")
            
        except Exception as e:
            click.echo(f"❌ Error: {e}", err=True)
            return 1
        
        return 0
    
    return asyncio.run(_run_evaluation())


# Export all commands
__all__ = [
    'run_agent_command',
    'test_command',
    'benchmark_command',
    'evaluate_command'
]
