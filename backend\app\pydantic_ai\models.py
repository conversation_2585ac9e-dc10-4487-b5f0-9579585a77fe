"""
Structured Output Models for Pydantic AI Migration

This module defines Pydantic models for structured outputs from AI agents,
replacing manual JSON parsing with type-safe, validated data structures.
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union, Literal
from enum import Enum
from datetime import datetime


class TaskStatus(str, Enum):
    """Task execution status."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"


class ChangeType(str, Enum):
    """Type of code change."""
    CREATE = "create"
    MODIFY = "modify"
    DELETE = "delete"
    RENAME = "rename"


class FileOperationResult(BaseModel):
    """Result of file operation."""
    
    operation: str = Field(..., description="Type of file operation performed")
    file_path: str = Field(..., description="Path to the file")
    success: bool = Field(..., description="Whether operation was successful")
    content: Optional[str] = Field(None, description="File content if applicable")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class GitOperationResult(BaseModel):
    """Result of git operation."""
    
    operation: str = Field(..., description="Type of git operation performed")
    success: bool = Field(..., description="Whether operation was successful")
    output: str = Field(..., description="Command output or result")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    files_changed: List[str] = Field(default_factory=list, description="List of changed files")
    commit_hash: Optional[str] = Field(None, description="Commit hash if applicable")


class CodeChange(BaseModel):
    """Individual code change."""
    
    file_path: str = Field(..., description="Path to the changed file")
    change_type: ChangeType = Field(..., description="Type of change")
    line_number: Optional[int] = Field(None, description="Line number of change")
    old_content: Optional[str] = Field(None, description="Original content")
    new_content: Optional[str] = Field(None, description="New content")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence in change")
    reasoning: str = Field(..., description="Reasoning for the change")


class CodeAnalysisResult(BaseModel):
    """Result of code analysis."""
    
    file_path: str = Field(..., description="Path to analyzed file")
    language: str = Field(..., description="Programming language")
    complexity_score: float = Field(..., ge=0.0, description="Cyclomatic complexity score")
    maintainability_index: float = Field(..., ge=0.0, le=100.0, description="Maintainability index")
    lines_of_code: int = Field(..., ge=0, description="Total lines of code")
    issues: List[str] = Field(default_factory=list, description="Identified issues")
    suggestions: List[str] = Field(default_factory=list, description="Improvement suggestions")
    dependencies: List[str] = Field(default_factory=list, description="Code dependencies")
    functions: List[Dict[str, Any]] = Field(default_factory=list, description="Function definitions")
    classes: List[Dict[str, Any]] = Field(default_factory=list, description="Class definitions")


class ChangeTrackingResult(BaseModel):
    """Result of change tracking operation."""
    
    changes_detected: int = Field(..., ge=0, description="Number of changes detected")
    files_modified: List[str] = Field(default_factory=list, description="List of modified files")
    files_created: List[str] = Field(default_factory=list, description="List of created files")
    files_deleted: List[str] = Field(default_factory=list, description="List of deleted files")
    timestamp: datetime = Field(default_factory=datetime.now, description="Timestamp of tracking")
    summary: str = Field(..., description="Summary of changes")


class LintTestResult(BaseModel):
    """Result of linting and testing."""
    
    lint_passed: bool = Field(..., description="Whether linting passed")
    tests_passed: bool = Field(..., description="Whether tests passed")
    lint_errors: List[str] = Field(default_factory=list, description="Linting errors")
    test_errors: List[str] = Field(default_factory=list, description="Test errors")
    coverage: Optional[float] = Field(None, ge=0.0, le=100.0, description="Test coverage percentage")
    execution_time: float = Field(..., ge=0.0, description="Total execution time in seconds")


class TaskResult(BaseModel):
    """Complete task execution result."""
    
    task_id: str = Field(..., description="Unique task identifier")
    status: TaskStatus = Field(..., description="Task execution status")
    description: str = Field(..., description="Task description")
    changes: List[CodeChange] = Field(default_factory=list, description="Code changes made")
    analysis_results: List[CodeAnalysisResult] = Field(default_factory=list, description="Analysis results")
    lint_test_results: Optional[LintTestResult] = Field(None, description="Lint and test results")
    git_operations: List[GitOperationResult] = Field(default_factory=list, description="Git operations performed")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    execution_time: float = Field(..., ge=0.0, description="Total execution time in seconds")
    iterations: int = Field(..., ge=0, description="Number of iterations performed")
    success: bool = Field(..., description="Whether task completed successfully")
    
    class Config:
        json_encoders = {
            TaskStatus: lambda v: v.value,
            ChangeType: lambda v: v.value,
            datetime: lambda v: v.isoformat()
        }


class VisionAnalysisResult(BaseModel):
    """Result of vision analysis."""
    
    image_path: str = Field(..., description="Path to analyzed image")
    analysis: str = Field(..., description="Vision analysis result")
    ui_issues: List[str] = Field(default_factory=list, description="Identified UI issues")
    suggestions: List[str] = Field(default_factory=list, description="UI improvement suggestions")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Analysis confidence")
    processing_time: float = Field(..., ge=0.0, description="Processing time in seconds")


class AgentResponse(BaseModel):
    """Generic agent response wrapper."""
    
    agent_type: Literal["coding", "vision"] = Field(..., description="Type of agent")
    response: str = Field(..., description="Agent response text")
    structured_output: Optional[Union[TaskResult, VisionAnalysisResult]] = Field(
        None, description="Structured output if applicable"
    )
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")
