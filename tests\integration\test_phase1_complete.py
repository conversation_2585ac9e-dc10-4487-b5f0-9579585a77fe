#!/usr/bin/env python3
"""
Comprehensive Phase 1 Completion Test for AI Coder Agent
Tests all Phase 1 requirements from IMPLEMENTATION_PLAN.md
"""

import requests
import time
import json
import subprocess
import sys
from pathlib import Path

def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_status(test_name, passed, details=""):
    """Print test status with color coding."""
    status = "✅ PASS" if passed else "❌ FAIL"
    print(f"{status} {test_name}")
    if details:
        print(f"    {details}")

def test_docker_containers():
    """Test that all Docker containers are running."""
    print_header("Testing Docker Container Status")
    
    try:
        result = subprocess.run(
            ["docker-compose", "ps", "--format", "json"], 
            capture_output=True, 
            text=True,
            timeout=10
        )
        
        if result.returncode != 0:
            print_status("Docker Compose Status", False, f"Error: {result.stderr}")
            return False
        
        containers = []
        for line in result.stdout.strip().split('\n'):
            if line.strip():
                try:
                    container = json.loads(line)
                    containers.append(container)
                except json.JSONDecodeError:
                    continue
        
        required_services = ["frontend", "backend", "ollama", "qdrant"]
        running_services = []
        
        for container in containers:
            service = container.get("Service", "")
            state = container.get("State", "")
            health = container.get("Health", "")
            
            if service in required_services:
                running_services.append(service)
                is_healthy = state == "running" and (health in ["healthy", ""] or "healthy" in health)
                print_status(f"Service: {service}", is_healthy, 
                           f"State: {state}, Health: {health}")
        
        all_running = all(service in running_services for service in required_services)
        print_status("All required services running", all_running)
        
        return all_running
        
    except Exception as e:
        print_status("Docker container check", False, f"Error: {e}")
        return False

def test_backend_endpoints():
    """Test all backend API endpoints."""
    print_header("Testing Backend API Endpoints")
    
    base_url = "http://localhost:8000"
    endpoints_to_test = [
        ("/health", "Basic health check"),
        ("/api/health", "API health check"),
        ("/api/config/validate", "Configuration validation"),
        ("/api/database/health", "Database health check"),
        ("/api/services/health", "External services health"),
        ("/api/sessions", "Session listing"),
    ]
    
    all_passed = True
    
    for endpoint, description in endpoints_to_test:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            passed = response.status_code == 200
            print_status(f"{description} ({endpoint})", passed, 
                        f"Status: {response.status_code}")
            if not passed:
                all_passed = False
        except Exception as e:
            print_status(f"{description} ({endpoint})", False, f"Error: {e}")
            all_passed = False
    
    return all_passed

def test_session_management():
    """Test session CRUD operations."""
    print_header("Testing Session Management")
    
    base_url = "http://localhost:8000/api"
    
    try:
        # Test session creation
        create_response = requests.post(f"{base_url}/sessions", 
                                      json={"repo_url": "https://github.com/test/repo.git", "branch": "main"},
                                      timeout=10)
        
        if create_response.status_code != 200:
            print_status("Session creation", False, f"Status: {create_response.status_code}")
            return False
        
        session_data = create_response.json()
        session_id = session_data.get("session_id")
        
        if not session_id:
            print_status("Session creation", False, "No session_id returned")
            return False
        
        print_status("Session creation", True, f"Created session: {session_id[:8]}...")
        
        # Test session retrieval
        get_response = requests.get(f"{base_url}/sessions/{session_id}", timeout=10)
        get_passed = get_response.status_code == 200
        print_status("Session retrieval", get_passed, f"Status: {get_response.status_code}")
        
        # Test session listing
        list_response = requests.get(f"{base_url}/sessions", timeout=10)
        list_passed = list_response.status_code == 200
        print_status("Session listing", list_passed, f"Status: {list_response.status_code}")
        
        # Test session deletion
        delete_response = requests.delete(f"{base_url}/sessions/{session_id}", timeout=10)
        delete_passed = delete_response.status_code == 200
        print_status("Session deletion", delete_passed, f"Status: {delete_response.status_code}")
        
        return get_passed and list_passed and delete_passed
        
    except Exception as e:
        print_status("Session management", False, f"Error: {e}")
        return False

def test_service_communication():
    """Test communication between services."""
    print_header("Testing Service Communication")
    
    try:
        # Test backend → services communication
        response = requests.get("http://localhost:8000/api/services/health", timeout=15)
        
        if response.status_code != 200:
            print_status("Service communication test", False, f"Status: {response.status_code}")
            return False
        
        data = response.json()
        services = data.get("services", [])
        
        all_healthy = True
        for service in services:
            name = service.get("name")
            status = service.get("status")
            is_healthy = status == "healthy"
            print_status(f"Backend → {name}", is_healthy, 
                        f"Status: {status}, URL: {service.get('url')}")
            if not is_healthy:
                all_healthy = False
        
        return all_healthy
        
    except Exception as e:
        print_status("Service communication", False, f"Error: {e}")
        return False

def test_frontend_accessibility():
    """Test that frontend is accessible."""
    print_header("Testing Frontend Accessibility")
    
    try:
        response = requests.get("http://localhost:3000", timeout=10)
        passed = response.status_code == 200 and "html" in response.text.lower()
        print_status("Frontend accessibility", passed, 
                    f"Status: {response.status_code}, Content-Type: {response.headers.get('content-type', 'unknown')}")
        return passed
        
    except Exception as e:
        print_status("Frontend accessibility", False, f"Error: {e}")
        return False

def main():
    """Run all Phase 1 completion tests."""
    print_header("AI Coder Agent - Phase 1 Completion Test")
    print("Testing all Phase 1 requirements from IMPLEMENTATION_PLAN.md...")
    
    tests = [
        ("Docker Containers", test_docker_containers),
        ("Backend Endpoints", test_backend_endpoints),
        ("Session Management", test_session_management),
        ("Service Communication", test_service_communication),
        ("Frontend Accessibility", test_frontend_accessibility),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print_status(f"{test_name} (EXCEPTION)", False, f"Unexpected error: {e}")
            results.append((test_name, False))
    
    # Summary
    print_header("Phase 1 Completion Test Results")
    passed_count = sum(1 for _, passed in results if passed)
    total_count = len(results)
    
    for test_name, passed in results:
        print_status(test_name, passed)
    
    print(f"\nOverall: {passed_count}/{total_count} tests passed")
    
    if passed_count == total_count:
        print("\n🎉 Phase 1 is COMPLETE! All requirements satisfied.")
        print("✅ Ready to proceed to Phase 2: External Service Integration")
        return True
    else:
        print(f"\n❌ Phase 1 is INCOMPLETE. {total_count - passed_count} issues need to be resolved.")
        print("🔧 Please fix the failing tests before proceeding to Phase 2.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
