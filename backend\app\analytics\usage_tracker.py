"""
📊 Usage Tracker - Revolutionary Real-Time Analytics

This module provides comprehensive usage tracking with:
- Real-time event tracking and aggregation
- Multi-dimensional metrics collection
- User journey mapping and analysis
- Performance impact measurement
- Predictive usage patterns
- Advanced filtering and segmentation
"""

import asyncio
import time
from collections import defaultdict, deque
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)


class EventType(str, Enum):
    """Types of trackable events."""
    # User Actions
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    SESSION_START = "session_start"
    SESSION_END = "session_end"
    
    # AI Interactions
    AI_REQUEST = "ai_request"
    AI_RESPONSE = "ai_response"
    CONTEXT_OPTIMIZATION = "context_optimization"
    AUTO_COMPACTION = "auto_compaction"
    
    # Project Operations
    PROJECT_CREATE = "project_create"
    PROJECT_OPEN = "project_open"
    PROJECT_CLOSE = "project_close"
    FILE_OPERATION = "file_operation"
    
    # Performance Events
    PERFORMANCE_METRIC = "performance_metric"
    ERROR_OCCURRED = "error_occurred"
    SYSTEM_ALERT = "system_alert"
    
    # Feature Usage
    FEATURE_USED = "feature_used"
    TOOL_INVOKED = "tool_invoked"
    API_CALL = "api_call"


class UsageEvent(BaseModel):
    """Individual usage event with comprehensive metadata."""
    
    event_id: str = Field(..., description="Unique event identifier")
    event_type: EventType = Field(..., description="Type of event")
    timestamp: datetime = Field(default_factory=datetime.now, description="Event timestamp")
    
    # User context
    user_id: Optional[str] = Field(None, description="User identifier")
    session_id: Optional[str] = Field(None, description="Session identifier")
    project_id: Optional[str] = Field(None, description="Project identifier")
    
    # Event data
    event_data: Dict[str, Any] = Field(default_factory=dict, description="Event-specific data")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    # Performance metrics
    duration_ms: Optional[float] = Field(None, description="Event duration in milliseconds")
    cpu_usage: Optional[float] = Field(None, description="CPU usage during event")
    memory_usage: Optional[float] = Field(None, description="Memory usage during event")
    
    # Context information
    user_agent: Optional[str] = Field(None, description="User agent string")
    ip_address: Optional[str] = Field(None, description="Client IP address")
    feature_flags: List[str] = Field(default_factory=list, description="Active feature flags")
    
    # Quality metrics
    success: bool = Field(default=True, description="Whether event was successful")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    quality_score: Optional[float] = Field(None, description="Quality score (0-1)")


class UsageMetrics(BaseModel):
    """Aggregated usage metrics."""
    
    # Time period
    start_time: datetime = Field(..., description="Metrics start time")
    end_time: datetime = Field(..., description="Metrics end time")
    duration_hours: float = Field(..., description="Time period in hours")
    
    # User metrics
    total_users: int = Field(default=0, description="Total unique users")
    active_users: int = Field(default=0, description="Active users in period")
    new_users: int = Field(default=0, description="New users in period")
    returning_users: int = Field(default=0, description="Returning users")
    
    # Session metrics
    total_sessions: int = Field(default=0, description="Total sessions")
    average_session_duration: float = Field(default=0.0, description="Average session duration (minutes)")
    bounce_rate: float = Field(default=0.0, description="Bounce rate percentage")
    
    # AI interaction metrics
    total_ai_requests: int = Field(default=0, description="Total AI requests")
    successful_ai_requests: int = Field(default=0, description="Successful AI requests")
    ai_success_rate: float = Field(default=0.0, description="AI success rate percentage")
    average_response_time: float = Field(default=0.0, description="Average AI response time (ms)")
    
    # Feature usage
    feature_usage: Dict[str, int] = Field(default_factory=dict, description="Feature usage counts")
    most_used_features: List[Tuple[str, int]] = Field(default_factory=list, description="Top features by usage")
    
    # Performance metrics
    average_cpu_usage: float = Field(default=0.0, description="Average CPU usage")
    average_memory_usage: float = Field(default=0.0, description="Average memory usage")
    error_rate: float = Field(default=0.0, description="Error rate percentage")
    
    # Context management metrics
    context_optimizations: int = Field(default=0, description="Context optimizations performed")
    auto_compactions: int = Field(default=0, description="Auto-compactions performed")
    average_token_reduction: float = Field(default=0.0, description="Average token reduction percentage")


class UsageTracker:
    """
    📊 Revolutionary Usage Tracker
    
    Provides comprehensive real-time usage tracking with advanced analytics,
    user behavior analysis, and performance monitoring capabilities.
    """
    
    def __init__(self, max_events: int = 100000, retention_days: int = 30):
        self.max_events = max_events
        self.retention_days = retention_days
        
        # Event storage
        self.events: deque = deque(maxlen=max_events)
        self.events_by_type: Dict[EventType, List[UsageEvent]] = defaultdict(list)
        self.events_by_user: Dict[str, List[UsageEvent]] = defaultdict(list)
        self.events_by_session: Dict[str, List[UsageEvent]] = defaultdict(list)
        
        # Real-time metrics
        self.active_sessions: Set[str] = set()
        self.active_users: Set[str] = set()
        self.current_metrics = UsageMetrics(
            start_time=datetime.now(),
            end_time=datetime.now(),
            duration_hours=0.0
        )
        
        # Performance tracking
        self.performance_samples: deque = deque(maxlen=1000)
        self.error_counts: Dict[str, int] = defaultdict(int)
        
        # User journey tracking
        self.user_journeys: Dict[str, List[UsageEvent]] = defaultdict(list)
        self.session_starts: Dict[str, datetime] = {}
        
        # Feature usage tracking
        self.feature_usage_counts: Dict[str, int] = defaultdict(int)
        self.feature_last_used: Dict[str, datetime] = {}
        
        # Background cleanup task
        self._cleanup_task: Optional[asyncio.Task] = None
        self._start_cleanup_task()
    
    async def track_event(
        self,
        event_type: EventType,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        project_id: Optional[str] = None,
        event_data: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        duration_ms: Optional[float] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ) -> str:
        """Track a usage event with comprehensive metadata."""
        event_id = f"evt_{int(time.time() * 1000)}_{len(self.events)}"
        
        # Create event
        event = UsageEvent(
            event_id=event_id,
            event_type=event_type,
            user_id=user_id,
            session_id=session_id,
            project_id=project_id,
            event_data=event_data or {},
            metadata=metadata or {},
            duration_ms=duration_ms,
            success=success,
            error_message=error_message
        )
        
        # Add performance metrics if available
        await self._add_performance_metrics(event)
        
        # Store event
        self.events.append(event)
        self.events_by_type[event_type].append(event)
        
        if user_id:
            self.events_by_user[user_id].append(event)
            self.active_users.add(user_id)
            self.user_journeys[user_id].append(event)
        
        if session_id:
            self.events_by_session[session_id].append(event)
            self.active_sessions.add(session_id)
            
            # Track session start
            if event_type == EventType.SESSION_START:
                self.session_starts[session_id] = event.timestamp
        
        # Update feature usage
        if event_type == EventType.FEATURE_USED:
            feature_name = event.event_data.get("feature_name", "unknown")
            self.feature_usage_counts[feature_name] += 1
            self.feature_last_used[feature_name] = event.timestamp
        
        # Update error tracking
        if not success and error_message:
            self.error_counts[error_message] += 1
        
        # Update real-time metrics
        await self._update_real_time_metrics(event)
        
        logger.debug(f"Tracked event: {event_type.value} for user {user_id}")
        return event_id
    
    async def get_usage_metrics(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        user_id: Optional[str] = None,
        project_id: Optional[str] = None
    ) -> UsageMetrics:
        """Get comprehensive usage metrics for specified period."""
        # Set default time range
        if end_time is None:
            end_time = datetime.now()
        if start_time is None:
            start_time = end_time - timedelta(hours=24)
        
        # Filter events
        filtered_events = self._filter_events(start_time, end_time, user_id, project_id)
        
        # Calculate metrics
        metrics = await self._calculate_metrics(filtered_events, start_time, end_time)
        
        return metrics
    
    async def get_user_journey(self, user_id: str, limit: int = 100) -> List[UsageEvent]:
        """Get user journey events."""
        user_events = self.user_journeys.get(user_id, [])
        return user_events[-limit:] if user_events else []
    
    async def get_feature_usage_stats(self) -> Dict[str, Any]:
        """Get feature usage statistics."""
        total_usage = sum(self.feature_usage_counts.values())
        
        # Sort features by usage
        sorted_features = sorted(
            self.feature_usage_counts.items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        return {
            "total_feature_uses": total_usage,
            "unique_features_used": len(self.feature_usage_counts),
            "top_features": sorted_features[:10],
            "feature_usage_distribution": dict(sorted_features),
            "recently_used_features": self._get_recently_used_features()
        }
    
    async def get_performance_insights(self) -> Dict[str, Any]:
        """Get performance insights and recommendations."""
        if not self.performance_samples:
            return {"message": "No performance data available"}
        
        # Calculate performance statistics
        cpu_values = [sample["cpu_usage"] for sample in self.performance_samples if sample.get("cpu_usage")]
        memory_values = [sample["memory_usage"] for sample in self.performance_samples if sample.get("memory_usage")]
        response_times = [sample["response_time"] for sample in self.performance_samples if sample.get("response_time")]
        
        insights = {
            "performance_summary": {
                "avg_cpu_usage": sum(cpu_values) / len(cpu_values) if cpu_values else 0,
                "max_cpu_usage": max(cpu_values) if cpu_values else 0,
                "avg_memory_usage": sum(memory_values) / len(memory_values) if memory_values else 0,
                "max_memory_usage": max(memory_values) if memory_values else 0,
                "avg_response_time": sum(response_times) / len(response_times) if response_times else 0,
                "max_response_time": max(response_times) if response_times else 0
            },
            "recommendations": await self._generate_performance_recommendations(),
            "alerts": await self._check_performance_alerts()
        }
        
        return insights
    
    async def get_real_time_stats(self) -> Dict[str, Any]:
        """Get real-time system statistics."""
        current_time = datetime.now()
        
        # Active sessions in last hour
        recent_sessions = [
            session_id for session_id, start_time in self.session_starts.items()
            if current_time - start_time < timedelta(hours=1)
        ]
        
        # Recent events (last 5 minutes)
        recent_events = [
            event for event in self.events
            if current_time - event.timestamp < timedelta(minutes=5)
        ]
        
        return {
            "timestamp": current_time,
            "active_users": len(self.active_users),
            "active_sessions": len(recent_sessions),
            "recent_events": len(recent_events),
            "events_per_minute": len(recent_events) / 5,
            "total_events_tracked": len(self.events),
            "system_health": await self._calculate_system_health(),
            "top_activities": await self._get_top_recent_activities()
        }
    
    def _filter_events(
        self,
        start_time: datetime,
        end_time: datetime,
        user_id: Optional[str] = None,
        project_id: Optional[str] = None
    ) -> List[UsageEvent]:
        """Filter events by criteria."""
        filtered = []
        
        for event in self.events:
            # Time filter
            if not (start_time <= event.timestamp <= end_time):
                continue
            
            # User filter
            if user_id and event.user_id != user_id:
                continue
            
            # Project filter
            if project_id and event.project_id != project_id:
                continue
            
            filtered.append(event)
        
        return filtered
    
    async def _calculate_metrics(
        self,
        events: List[UsageEvent],
        start_time: datetime,
        end_time: datetime
    ) -> UsageMetrics:
        """Calculate comprehensive metrics from events."""
        duration_hours = (end_time - start_time).total_seconds() / 3600
        
        # User metrics
        unique_users = set(event.user_id for event in events if event.user_id)
        unique_sessions = set(event.session_id for event in events if event.session_id)
        
        # AI interaction metrics
        ai_requests = [e for e in events if e.event_type == EventType.AI_REQUEST]
        successful_ai = [e for e in ai_requests if e.success]
        
        # Performance metrics
        response_times = [e.duration_ms for e in events if e.duration_ms is not None]
        cpu_usage = [e.cpu_usage for e in events if e.cpu_usage is not None]
        memory_usage = [e.memory_usage for e in events if e.memory_usage is not None]
        
        # Feature usage
        feature_events = [e for e in events if e.event_type == EventType.FEATURE_USED]
        feature_counts = defaultdict(int)
        for event in feature_events:
            feature_name = event.event_data.get("feature_name", "unknown")
            feature_counts[feature_name] += 1
        
        # Context management metrics
        context_opts = len([e for e in events if e.event_type == EventType.CONTEXT_OPTIMIZATION])
        auto_compacts = len([e for e in events if e.event_type == EventType.AUTO_COMPACTION])
        
        # Calculate token reduction
        token_reductions = []
        for event in events:
            if event.event_type in [EventType.CONTEXT_OPTIMIZATION, EventType.AUTO_COMPACTION]:
                reduction = event.event_data.get("token_reduction", 0)
                if reduction > 0:
                    token_reductions.append(reduction)
        
        return UsageMetrics(
            start_time=start_time,
            end_time=end_time,
            duration_hours=duration_hours,
            total_users=len(unique_users),
            active_users=len(unique_users),
            total_sessions=len(unique_sessions),
            total_ai_requests=len(ai_requests),
            successful_ai_requests=len(successful_ai),
            ai_success_rate=(len(successful_ai) / len(ai_requests) * 100) if ai_requests else 0,
            average_response_time=sum(response_times) / len(response_times) if response_times else 0,
            feature_usage=dict(feature_counts),
            most_used_features=sorted(feature_counts.items(), key=lambda x: x[1], reverse=True)[:10],
            average_cpu_usage=sum(cpu_usage) / len(cpu_usage) if cpu_usage else 0,
            average_memory_usage=sum(memory_usage) / len(memory_usage) if memory_usage else 0,
            error_rate=(len([e for e in events if not e.success]) / len(events) * 100) if events else 0,
            context_optimizations=context_opts,
            auto_compactions=auto_compacts,
            average_token_reduction=sum(token_reductions) / len(token_reductions) if token_reductions else 0
        )
    
    async def _add_performance_metrics(self, event: UsageEvent) -> None:
        """Add performance metrics to event."""
        try:
            import psutil
            
            # Get current system metrics
            cpu_percent = psutil.cpu_percent(interval=None)
            memory = psutil.virtual_memory()
            
            event.cpu_usage = cpu_percent
            event.memory_usage = memory.percent
            
            # Store performance sample
            self.performance_samples.append({
                "timestamp": event.timestamp,
                "cpu_usage": cpu_percent,
                "memory_usage": memory.percent,
                "response_time": event.duration_ms
            })
            
        except ImportError:
            # psutil not available, skip performance metrics
            pass
        except Exception as e:
            logger.warning(f"Failed to collect performance metrics: {e}")
    
    async def _update_real_time_metrics(self, event: UsageEvent) -> None:
        """Update real-time metrics with new event."""
        # Update current metrics timestamp
        self.current_metrics.end_time = event.timestamp
        
        # Update counters based on event type
        if event.event_type == EventType.AI_REQUEST:
            self.current_metrics.total_ai_requests += 1
            if event.success:
                self.current_metrics.successful_ai_requests += 1
        
        elif event.event_type == EventType.CONTEXT_OPTIMIZATION:
            self.current_metrics.context_optimizations += 1
        
        elif event.event_type == EventType.AUTO_COMPACTION:
            self.current_metrics.auto_compactions += 1
    
    def _get_recently_used_features(self, hours: int = 24) -> List[Tuple[str, datetime]]:
        """Get recently used features."""
        cutoff = datetime.now() - timedelta(hours=hours)
        recent = [
            (feature, last_used) for feature, last_used in self.feature_last_used.items()
            if last_used > cutoff
        ]
        return sorted(recent, key=lambda x: x[1], reverse=True)
    
    async def _generate_performance_recommendations(self) -> List[str]:
        """Generate performance optimization recommendations."""
        recommendations = []
        
        if not self.performance_samples:
            return recommendations
        
        # Analyze CPU usage
        cpu_values = [s["cpu_usage"] for s in self.performance_samples if s.get("cpu_usage")]
        if cpu_values:
            avg_cpu = sum(cpu_values) / len(cpu_values)
            if avg_cpu > 80:
                recommendations.append("🔥 High CPU usage detected. Consider optimizing AI request processing.")
            elif avg_cpu > 60:
                recommendations.append("⚠️ Moderate CPU usage. Monitor for performance bottlenecks.")
        
        # Analyze memory usage
        memory_values = [s["memory_usage"] for s in self.performance_samples if s.get("memory_usage")]
        if memory_values:
            avg_memory = sum(memory_values) / len(memory_values)
            if avg_memory > 85:
                recommendations.append("🔥 High memory usage. Enable aggressive context compaction.")
            elif avg_memory > 70:
                recommendations.append("📊 Consider enabling auto-compaction for better memory efficiency.")
        
        # Analyze response times
        response_times = [s["response_time"] for s in self.performance_samples if s.get("response_time")]
        if response_times:
            avg_response = sum(response_times) / len(response_times)
            if avg_response > 5000:  # 5 seconds
                recommendations.append("🚀 Slow response times detected. Enable context caching.")
            elif avg_response > 2000:  # 2 seconds
                recommendations.append("⚡ Consider optimizing context management for faster responses.")
        
        return recommendations
    
    async def _check_performance_alerts(self) -> List[Dict[str, Any]]:
        """Check for performance alerts."""
        alerts = []
        
        # Check error rate
        total_events = len(self.events)
        if total_events > 0:
            error_events = len([e for e in self.events if not e.success])
            error_rate = (error_events / total_events) * 100
            
            if error_rate > 10:
                alerts.append({
                    "severity": "high",
                    "message": f"High error rate: {error_rate:.1f}%",
                    "recommendation": "Investigate recent errors and implement fixes"
                })
            elif error_rate > 5:
                alerts.append({
                    "severity": "medium",
                    "message": f"Elevated error rate: {error_rate:.1f}%",
                    "recommendation": "Monitor error patterns and consider preventive measures"
                })
        
        return alerts
    
    async def _calculate_system_health(self) -> str:
        """Calculate overall system health."""
        if not self.events:
            return "unknown"
        
        recent_events = [
            e for e in self.events
            if datetime.now() - e.timestamp < timedelta(minutes=10)
        ]
        
        if not recent_events:
            return "idle"
        
        error_rate = len([e for e in recent_events if not e.success]) / len(recent_events)
        
        if error_rate > 0.2:
            return "critical"
        elif error_rate > 0.1:
            return "warning"
        elif len(recent_events) > 50:
            return "busy"
        else:
            return "healthy"
    
    async def _get_top_recent_activities(self) -> List[Dict[str, Any]]:
        """Get top recent activities."""
        recent_events = [
            e for e in self.events
            if datetime.now() - e.timestamp < timedelta(minutes=30)
        ]
        
        activity_counts = defaultdict(int)
        for event in recent_events:
            activity_counts[event.event_type.value] += 1
        
        return [
            {"activity": activity, "count": count}
            for activity, count in sorted(activity_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        ]
    
    def _start_cleanup_task(self) -> None:
        """Start background cleanup task."""
        async def cleanup_old_data():
            while True:
                try:
                    cutoff = datetime.now() - timedelta(days=self.retention_days)
                    
                    # Clean old events
                    self.events = deque(
                        [e for e in self.events if e.timestamp > cutoff],
                        maxlen=self.max_events
                    )
                    
                    # Clean old user journeys
                    for user_id in list(self.user_journeys.keys()):
                        self.user_journeys[user_id] = [
                            e for e in self.user_journeys[user_id] if e.timestamp > cutoff
                        ]
                        if not self.user_journeys[user_id]:
                            del self.user_journeys[user_id]
                    
                    # Clean old session data
                    for session_id in list(self.session_starts.keys()):
                        if self.session_starts[session_id] < cutoff:
                            del self.session_starts[session_id]
                    
                    logger.debug("Completed usage data cleanup")
                    
                except Exception as e:
                    logger.error(f"Error during usage data cleanup: {e}")
                
                # Sleep for 1 hour
                await asyncio.sleep(3600)
        
        self._cleanup_task = asyncio.create_task(cleanup_old_data())


# Global usage tracker instance
_usage_tracker: Optional[UsageTracker] = None


def get_usage_tracker() -> UsageTracker:
    """Get the global usage tracker instance."""
    global _usage_tracker
    if _usage_tracker is None:
        _usage_tracker = UsageTracker()
    return _usage_tracker
