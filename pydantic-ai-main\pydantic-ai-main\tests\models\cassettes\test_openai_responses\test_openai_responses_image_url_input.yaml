interactions:
- request:
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '253'
      content-type:
      - application/json
      host:
      - api.openai.com
    method: POST
    parsed_body:
      input:
      - content:
        - text: hello
          type: input_text
        - detail: auto
          image_url: https://t3.ftcdn.net/jpg/00/85/79/92/360_F_85799278_0BBGV9OAdQDTLnKwAPBCcg1J7QtiieJY.jpg
          type: input_image
        role: user
      instructions: ''
      model: gpt-4o
    uri: https://api.openai.com/v1/responses
  response:
    headers:
      alt-svc:
      - h3=":443"; ma=86400
      connection:
      - keep-alive
      content-length:
      - '1231'
      content-type:
      - application/json
      openai-organization:
      - pydantic-28gund
      openai-processing-ms:
      - '2022'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      transfer-encoding:
      - chunked
    parsed_body:
      created_at: 1743078348
      error: null
      id: resp_67e543cc186c8191a80e0735f8c14c8800ad571c503f65a0
      incomplete_details: null
      instructions: ''
      max_output_tokens: null
      metadata: {}
      model: gpt-4o-2024-08-06
      object: response
      output:
      - content:
        - annotations: []
          text: Hello! I see you've shared an image of a potato. How can I assist you today?
          type: output_text
        id: msg_67e543cd87748191ab90607256280a3b00ad571c503f65a0
        role: assistant
        status: completed
        type: message
      parallel_tool_calls: true
      previous_response_id: null
      reasoning:
        effort: null
        generate_summary: null
      status: completed
      store: true
      temperature: 1.0
      text:
        format:
          type: text
      tool_choice: auto
      tools: []
      top_p: 1.0
      truncation: disabled
      usage:
        input_tokens: 663
        input_tokens_details:
          cached_tokens: 0
        output_tokens: 20
        output_tokens_details:
          reasoning_tokens: 0
        total_tokens: 683
      user: null
    status:
      code: 200
      message: OK
version: 1
