interactions:
  - request:
      body: grant_type=%5B%27refresh_token%27%5D&client_id=%5B%27************-6qr4p6gpi6hn506pt8ejuq83di341hur.apps.googleusercontent.com%27%5D&client_secret=%5B%27scrubbed%27%5D&refresh_token=%5B%27scrubbed%27%5D
      headers:
        accept:
          - "*/*"
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "268"
        content-type:
          - application/x-www-form-urlencoded
      method: POST
      uri: https://oauth2.googleapis.com/token
    response:
      headers:
        alt-svc:
          - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
        cache-control:
          - no-cache, no-store, max-age=0, must-revalidate
        content-length:
          - "1420"
        content-type:
          - application/json; charset=utf-8
        expires:
          - Mon, 01 Jan 1990 00:00:00 GMT
        pragma:
          - no-cache
        transfer-encoding:
          - chunked
        vary:
          - Origin
          - X-Origin
          - Referer
      parsed_body:
        access_token: scrubbed
        expires_in: 3599
        id_token: eyJhbGciOiJSUzI1NiIsImtpZCI6IjY2MGVmM2I5Nzg0YmRmNTZlYmU4NTlmNTc3ZjdmYjJlOGMxY2VmZmIiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dHg3qRlYoQ8WyIml7-kGqsuefvkl5deuZ0yTQM-RvKuuqtF_t6p8TrWbndEuSbZpRn9JhVPnsoEAYVPexbGy-pon4gu1aHH0dJNq3ghhdim7qp5JWpegLaZqvNvELvEHjj2VNLWXQ70-5wEaI_HCtAWTjlROAHQxvoWHJAdeH0Yf9zoljEBQvx3VLDLEpdCcMd-UGNCBucpQlFHcCJs5Qq8yj8R8f27BCEmRo7z9K3Axuedj_wcJ_tWV1x1tWxojUloJaKsIfztFOPFxzOdNPOlTHXsE47d4v43v87a8LhdDGloD72xN_kLapfIqyTIwRTj4cQvQp5H0u7As49fvMA
        scope: https://www.googleapis.com/auth/userinfo.email openid https://www.googleapis.com/auth/sqlservice.login https://www.googleapis.com/auth/cloud-platform
        token_type: Bearer
      status:
        code: 200
        message: OK
  - request:
      headers:
        accept:
          - "*/*"
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "257"
        content-type:
          - application/json
        host:
          - aiplatform.googleapis.com
      method: POST
      parsed_body:
        contents:
          - parts:
              - text: What is the capital of France?
            role: user
        generationConfig: {}
        labels:
          environment: test
          team: analytics
        systemInstruction:
          parts:
            - text: You are a helpful chatbot.
          role: user
      uri: https://aiplatform.googleapis.com/v1beta1/projects/pydantic-ai/locations/global/publishers/google/models/gemini-2.0-flash:generateContent
    response:
      headers:
        alt-svc:
          - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
        content-length:
          - "759"
        content-type:
          - application/json; charset=UTF-8
        transfer-encoding:
          - chunked
        vary:
          - Origin
          - X-Origin
          - Referer
      parsed_body:
        candidates:
          - avgLogprobs: -0.0005532301729544997
            content:
              parts:
                - text: |
                    The capital of France is Paris.
              role: model
            finishReason: STOP
        createTime: "2025-05-23T07:09:59.524624Z"
        modelVersion: gemini-2.0-flash
        responseId: sN0paKOZFtmtyOgPqMyL6AE
        usageMetadata:
          candidatesTokenCount: 8
          candidatesTokensDetails:
            - modality: TEXT
              tokenCount: 8
          promptTokenCount: 13
          promptTokensDetails:
            - modality: TEXT
              tokenCount: 13
          totalTokenCount: 21
          trafficType: ON_DEMAND
      status:
        code: 200
        message: OK
version: 1
