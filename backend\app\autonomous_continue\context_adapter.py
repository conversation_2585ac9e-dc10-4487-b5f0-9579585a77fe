"""
Context Adaptation Engine for Autonomous Continue Mode - Phase 8C

Provides intelligent context switching and adaptation based on error patterns,
progress, and task requirements for enhanced autonomous development.
"""

import logging
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict

from .models import (
    ContextPerspective, <PERSON>rror<PERSON>attern, IterationR<PERSON>ult, ContinueSession,
    ErrorSeverity
)

logger = logging.getLogger(__name__)


class ContextAdaptationEngine:
    """
    Intelligent context adaptation system for autonomous development.
    
    Provides:
    - Context perspective switching based on error patterns
    - Adaptive strategy selection
    - Learning from context effectiveness
    - Progress-based context optimization
    """
    
    def __init__(self):
        """Initialize the context adaptation engine."""
        self.perspective_effectiveness: Dict[ContextPerspective, Dict[str, float]] = defaultdict(lambda: defaultdict(float))
        self.perspective_usage_history: List[Dict] = []
        self.current_perspective = ContextPerspective.ARCHITECTURE
        
        # Context switching rules
        self.error_to_perspective_map = {
            'syntax_error': ContextPerspective.DEBUGGING,
            'import_error': ContextPerspective.ARCHITECTURE,
            'type_error': ContextPerspective.DEBUGGING,
            'file_error': ContextPerspective.ARCHITECTURE,
            'git_error': ContextPerspective.ARCHITECTURE,
            'network_error': ContextPerspective.DEBUGGING,
            'performance_issue': ContextPerspective.PERFORMANCE,
            'security_issue': ContextPerspective.SECURITY,
            'ui_issue': ContextPerspective.USER_EXPERIENCE,
            'test_failure': ContextPerspective.TESTING
        }
        
        # Perspective characteristics
        self.perspective_characteristics = {
            ContextPerspective.ARCHITECTURE: {
                'focus': 'System design and structure',
                'strengths': ['File organization', 'Module dependencies', 'System design'],
                'approach': 'Top-down analysis and systematic organization'
            },
            ContextPerspective.DEBUGGING: {
                'focus': 'Error identification and resolution',
                'strengths': ['Error analysis', 'Step-by-step debugging', 'Root cause analysis'],
                'approach': 'Detailed investigation and systematic elimination'
            },
            ContextPerspective.TESTING: {
                'focus': 'Quality assurance and validation',
                'strengths': ['Test coverage', 'Edge case identification', 'Validation'],
                'approach': 'Comprehensive testing and verification'
            },
            ContextPerspective.PERFORMANCE: {
                'focus': 'Optimization and efficiency',
                'strengths': ['Performance analysis', 'Resource optimization', 'Scalability'],
                'approach': 'Metrics-driven optimization and profiling'
            },
            ContextPerspective.SECURITY: {
                'focus': 'Security and vulnerability assessment',
                'strengths': ['Security analysis', 'Vulnerability detection', 'Best practices'],
                'approach': 'Security-first design and threat modeling'
            },
            ContextPerspective.USER_EXPERIENCE: {
                'focus': 'User interface and experience',
                'strengths': ['UI design', 'User interaction', 'Accessibility'],
                'approach': 'User-centered design and usability testing'
            }
        }
        
        logger.info("🎯 Context Adaptation Engine initialized")
    
    async def suggest_perspective_switch(self, 
                                       session: ContinueSession,
                                       recent_errors: List[ErrorPattern]) -> Optional[ContextPerspective]:
        """
        Suggest a perspective switch based on current context and errors.
        
        Args:
            session: Current continue session
            recent_errors: Recent error patterns
            
        Returns:
            Suggested perspective or None if no switch recommended
        """
        try:
            current_perspective = session.current_perspective
            
            # Analyze recent error patterns
            if recent_errors:
                suggested_perspective = self._analyze_error_patterns(recent_errors)
                if suggested_perspective and suggested_perspective != current_perspective:
                    logger.info(f"🔄 Suggesting perspective switch: {current_perspective.value} → {suggested_perspective.value}")
                    return suggested_perspective
            
            # Check if current perspective is ineffective
            if self._is_perspective_ineffective(session, current_perspective):
                alternative = self._suggest_alternative_perspective(session, current_perspective)
                if alternative:
                    logger.info(f"🔄 Current perspective ineffective, suggesting: {alternative.value}")
                    return alternative
            
            # Check for progress stagnation
            if self._is_progress_stagnating(session):
                fresh_perspective = self._suggest_fresh_perspective(session)
                if fresh_perspective and fresh_perspective != current_perspective:
                    logger.info(f"🔄 Progress stagnating, suggesting fresh perspective: {fresh_perspective.value}")
                    return fresh_perspective
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Perspective suggestion failed: {e}")
            return None
    
    async def adapt_context(self, 
                          session: ContinueSession,
                          new_perspective: ContextPerspective) -> Dict[str, str]:
        """
        Adapt context for the new perspective.
        
        Args:
            session: Current continue session
            new_perspective: New perspective to switch to
            
        Returns:
            Context adaptation instructions
        """
        try:
            old_perspective = session.current_perspective
            
            # Record perspective switch
            self.perspective_usage_history.append({
                'timestamp': datetime.now(),
                'session_id': session.session_id,
                'from_perspective': old_perspective.value,
                'to_perspective': new_perspective.value,
                'iteration': session.current_iteration,
                'reason': 'adaptive_switch'
            })
            
            # Update session perspective
            session.current_perspective = new_perspective
            
            # Generate context adaptation instructions
            adaptation_instructions = self._generate_adaptation_instructions(
                old_perspective, new_perspective, session
            )
            
            logger.info(f"🎯 Context adapted: {old_perspective.value} → {new_perspective.value}")
            
            return adaptation_instructions
            
        except Exception as e:
            logger.error(f"❌ Context adaptation failed: {e}")
            return {}
    
    async def learn_from_perspective_effectiveness(self,
                                                 session: ContinueSession,
                                                 perspective: ContextPerspective,
                                                 iteration_result: IterationResult):
        """
        Learn from the effectiveness of a perspective.
        
        Args:
            session: Continue session
            perspective: Perspective used
            iteration_result: Result of the iteration
        """
        try:
            # Calculate effectiveness score
            effectiveness_score = self._calculate_effectiveness_score(iteration_result)
            
            # Update perspective effectiveness
            task_type = self._classify_task_type(session.current_task)
            self.perspective_effectiveness[perspective][task_type] += effectiveness_score
            
            # Normalize scores to prevent unbounded growth
            self._normalize_effectiveness_scores(perspective)
            
            logger.debug(f"📊 Updated effectiveness for {perspective.value}: {effectiveness_score:.2f}")
            
        except Exception as e:
            logger.error(f"❌ Learning from perspective effectiveness failed: {e}")
    
    def get_perspective_recommendations(self, task_description: str) -> List[Tuple[ContextPerspective, float]]:
        """
        Get perspective recommendations for a task.
        
        Args:
            task_description: Description of the task
            
        Returns:
            List of (perspective, confidence) tuples
        """
        try:
            task_type = self._classify_task_type(task_description)
            recommendations = []
            
            for perspective in ContextPerspective:
                # Get effectiveness score for this perspective and task type
                effectiveness = self.perspective_effectiveness[perspective][task_type]
                
                # Add base score based on perspective characteristics
                base_score = self._get_base_perspective_score(perspective, task_description)
                
                # Combine scores
                total_score = (effectiveness * 0.7) + (base_score * 0.3)
                recommendations.append((perspective, total_score))
            
            # Sort by score
            recommendations.sort(key=lambda x: x[1], reverse=True)
            
            return recommendations
            
        except Exception as e:
            logger.error(f"❌ Perspective recommendations failed: {e}")
            return []
    
    def get_adaptation_statistics(self) -> Dict[str, any]:
        """Get context adaptation statistics."""
        try:
            total_switches = len(self.perspective_usage_history)
            
            # Count switches by perspective
            perspective_usage = defaultdict(int)
            perspective_switches = defaultdict(int)
            
            for entry in self.perspective_usage_history:
                perspective_usage[entry['to_perspective']] += 1
                switch_key = f"{entry['from_perspective']} → {entry['to_perspective']}"
                perspective_switches[switch_key] += 1
            
            # Calculate effectiveness averages
            effectiveness_summary = {}
            for perspective, task_scores in self.perspective_effectiveness.items():
                if task_scores:
                    avg_effectiveness = sum(task_scores.values()) / len(task_scores)
                    effectiveness_summary[perspective.value] = avg_effectiveness
            
            # Recent activity
            recent_switches = [
                entry for entry in self.perspective_usage_history
                if entry['timestamp'] > datetime.now() - timedelta(hours=1)
            ]
            
            return {
                'total_perspective_switches': total_switches,
                'perspective_usage': dict(perspective_usage),
                'common_switches': dict(perspective_switches),
                'effectiveness_summary': effectiveness_summary,
                'recent_switches_count': len(recent_switches),
                'current_perspective': self.current_perspective.value
            }
            
        except Exception as e:
            logger.error(f"❌ Adaptation statistics failed: {e}")
            return {}
    
    def _analyze_error_patterns(self, error_patterns: List[ErrorPattern]) -> Optional[ContextPerspective]:
        """Analyze error patterns to suggest perspective."""
        if not error_patterns:
            return None
        
        # Count error types
        error_type_counts = defaultdict(int)
        for pattern in error_patterns:
            error_type_counts[pattern.error_type] += pattern.occurrence_count
        
        # Find most common error type
        most_common_error = max(error_type_counts.items(), key=lambda x: x[1])[0]
        
        # Map to perspective
        return self.error_to_perspective_map.get(most_common_error)
    
    def _is_perspective_ineffective(self, session: ContinueSession, perspective: ContextPerspective) -> bool:
        """Check if current perspective is ineffective."""
        if session.current_iteration < 5:  # Need some iterations to judge
            return False
        
        # Check recent success rate with this perspective
        recent_iterations = session.iterations[-5:]  # Last 5 iterations
        perspective_iterations = [
            iter_result for iter_result in recent_iterations
            if iter_result.perspective_used == perspective
        ]
        
        if len(perspective_iterations) >= 3:
            success_rate = sum(1 for iter_result in perspective_iterations if iter_result.success) / len(perspective_iterations)
            return success_rate < 0.3  # Less than 30% success rate
        
        return False
    
    def _suggest_alternative_perspective(self, session: ContinueSession, current: ContextPerspective) -> Optional[ContextPerspective]:
        """Suggest an alternative perspective."""
        task_type = self._classify_task_type(session.current_task)
        
        # Get effectiveness scores for all perspectives except current
        alternatives = []
        for perspective in ContextPerspective:
            if perspective != current:
                effectiveness = self.perspective_effectiveness[perspective][task_type]
                alternatives.append((perspective, effectiveness))
        
        # Sort by effectiveness
        alternatives.sort(key=lambda x: x[1], reverse=True)
        
        return alternatives[0][0] if alternatives else None
    
    def _is_progress_stagnating(self, session: ContinueSession) -> bool:
        """Check if progress is stagnating."""
        if session.current_iteration < 10:
            return False
        
        # Check if no successful iterations in last 5 attempts
        recent_iterations = session.iterations[-5:]
        successful_recent = sum(1 for iter_result in recent_iterations if iter_result.success)
        
        return successful_recent == 0
    
    def _suggest_fresh_perspective(self, session: ContinueSession) -> Optional[ContextPerspective]:
        """Suggest a fresh perspective for stagnating progress."""
        # Find least recently used perspective
        perspective_usage = defaultdict(int)
        
        for iteration in session.iterations[-10:]:  # Last 10 iterations
            perspective_usage[iteration.perspective_used] += 1
        
        # Find perspective with least usage
        all_perspectives = list(ContextPerspective)
        least_used = min(all_perspectives, key=lambda p: perspective_usage[p])
        
        return least_used
    
    def _generate_adaptation_instructions(self,
                                        old_perspective: ContextPerspective,
                                        new_perspective: ContextPerspective,
                                        session: ContinueSession) -> Dict[str, str]:
        """Generate context adaptation instructions."""
        new_characteristics = self.perspective_characteristics[new_perspective]
        
        return {
            'perspective_change': f"Switching from {old_perspective.value} to {new_perspective.value}",
            'new_focus': new_characteristics['focus'],
            'approach': new_characteristics['approach'],
            'strengths': ', '.join(new_characteristics['strengths']),
            'task_context': session.current_task,
            'adaptation_reason': f"Adapting context to improve effectiveness for current challenges"
        }
    
    def _calculate_effectiveness_score(self, iteration_result: IterationResult) -> float:
        """Calculate effectiveness score for an iteration."""
        if iteration_result.success:
            base_score = 1.0
            
            # Bonus for quick completion
            if iteration_result.duration.total_seconds() < 30:
                base_score += 0.2
            
            # Bonus for file changes (productive work)
            if iteration_result.files_changed:
                base_score += 0.1
            
            return min(base_score, 2.0)
        else:
            # Partial credit for learning
            return 0.1
    
    def _classify_task_type(self, task_description: str) -> str:
        """Classify task type from description."""
        task_lower = task_description.lower()
        
        if any(word in task_lower for word in ['test', 'testing', 'unittest']):
            return 'testing'
        elif any(word in task_lower for word in ['debug', 'error', 'fix', 'bug']):
            return 'debugging'
        elif any(word in task_lower for word in ['ui', 'interface', 'frontend', 'design']):
            return 'ui_development'
        elif any(word in task_lower for word in ['performance', 'optimize', 'speed']):
            return 'performance'
        elif any(word in task_lower for word in ['security', 'auth', 'permission']):
            return 'security'
        elif any(word in task_lower for word in ['architecture', 'structure', 'design']):
            return 'architecture'
        else:
            return 'general_development'
    
    def _get_base_perspective_score(self, perspective: ContextPerspective, task_description: str) -> float:
        """Get base score for perspective based on task description."""
        task_lower = task_description.lower()
        
        # Perspective-specific keywords
        perspective_keywords = {
            ContextPerspective.DEBUGGING: ['debug', 'error', 'fix', 'bug', 'issue'],
            ContextPerspective.TESTING: ['test', 'testing', 'unittest', 'verify'],
            ContextPerspective.PERFORMANCE: ['performance', 'optimize', 'speed', 'memory'],
            ContextPerspective.SECURITY: ['security', 'auth', 'permission', 'secure'],
            ContextPerspective.USER_EXPERIENCE: ['ui', 'interface', 'user', 'design'],
            ContextPerspective.ARCHITECTURE: ['architecture', 'structure', 'organize', 'design']
        }
        
        keywords = perspective_keywords.get(perspective, [])
        matches = sum(1 for keyword in keywords if keyword in task_lower)
        
        return min(matches * 0.3, 1.0)
    
    def _normalize_effectiveness_scores(self, perspective: ContextPerspective):
        """Normalize effectiveness scores to prevent unbounded growth."""
        task_scores = self.perspective_effectiveness[perspective]
        
        if len(task_scores) > 10:  # Only normalize if we have enough data
            max_score = max(task_scores.values())
            if max_score > 10:  # Normalize if scores get too high
                for task_type in task_scores:
                    task_scores[task_type] = task_scores[task_type] / max_score * 5
