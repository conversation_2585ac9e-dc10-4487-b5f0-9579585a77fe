#!/usr/bin/env python3
"""
Tool Registration Test Script

This script validates that the tool registration system is working correctly
after fixing the circular import issues.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def print_status(test_name: str, success: bool, details: str = ""):
    """Print test status with color coding."""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} - {test_name}")
    if details:
        print(f"    {details}")


async def test_tool_registration():
    """Test the tool registration system."""
    print("🔧 Testing Tool Registration System")
    print("=" * 60)
    
    all_tests_passed = True
    
    try:
        # Test 1: Import tool registry
        print("\n📦 Testing tool registry import...")
        try:
            from app.pydantic_ai.tools import tool_registry, get_coding_tools, get_vision_tools, get_tool_summary
            print_status("Tool registry import", True, "Tool registry imported successfully")
        except ImportError as e:
            print_status("Tool registry import", False, f"Import error: {e}")
            all_tests_passed = False
            return all_tests_passed
        
        # Test 2: Check tool registry initialization
        print("\n🔧 Testing tool registry initialization...")
        try:
            tool_summary = get_tool_summary()
            coding_tools_count = len(tool_summary['coding_tools'])
            vision_tools_count = len(tool_summary['vision_tools'])
            total_tools = tool_summary['total_tools']
            
            print_status("Tool registry initialization", True, 
                        f"Found {coding_tools_count} coding tools, {vision_tools_count} vision tools, {total_tools} total")
            
            # Verify we have the expected tools
            expected_tools = [
                'read_file', 'write_file', 'create_file', 'delete_file', 'list_directory',
                'git_status', 'git_diff', 'git_commit', 'git_push', 'git_branch', 'git_log',
                'analyze_code', 'search_code', 'get_project_context', 'run_linting', 'run_tests',
                'track_changes', 'get_change_summary', 'monitor_file_changes', 
                'get_change_statistics', 'index_code_changes', 'stop_monitoring'
            ]
            
            missing_tools = [tool for tool in expected_tools if tool not in tool_summary['coding_tools']]
            if missing_tools:
                print_status("Expected tools check", False, f"Missing tools: {missing_tools}")
                all_tests_passed = False
            else:
                print_status("Expected tools check", True, f"All {len(expected_tools)} expected tools found")
                
        except Exception as e:
            print_status("Tool registry initialization", False, f"Error: {e}")
            all_tests_passed = False
        
        # Test 3: Test agent import with tools
        print("\n🤖 Testing agent import with tools...")
        try:
            from app.pydantic_ai.agents import coding_agent, vision_agent
            
            # Check if agents have tools
            coding_tools = coding_agent.tools if hasattr(coding_agent, 'tools') else []
            vision_tools = vision_agent.tools if hasattr(vision_agent, 'tools') else []
            
            print_status("Agent import with tools", True, 
                        f"Coding agent: {len(coding_tools)} tools, Vision agent: {len(vision_tools)} tools")
            
        except Exception as e:
            print_status("Agent import with tools", False, f"Error: {e}")
            all_tests_passed = False
        
        # Test 4: Test individual tool functions
        print("\n🛠️ Testing individual tool functions...")
        try:
            from app.pydantic_ai.tools.file_operations import read_file
            from app.pydantic_ai.tools.git_operations import git_status
            from app.pydantic_ai.tools.code_analysis import analyze_code
            
            # Check that functions are callable (not decorated)
            import inspect
            
            # These should be regular async functions, not decorated
            if inspect.iscoroutinefunction(read_file):
                print_status("Tool function types", True, "Tool functions are proper async functions")
            else:
                print_status("Tool function types", False, "Tool functions are not async functions")
                all_tests_passed = False
                
        except Exception as e:
            print_status("Individual tool functions", False, f"Error: {e}")
            all_tests_passed = False
        
        # Test 5: Test tool creation from registry
        print("\n🏗️ Testing tool creation from registry...")
        try:
            coding_tools = get_coding_tools()
            
            if coding_tools and len(coding_tools) > 0:
                # Check first tool
                first_tool = coding_tools[0]
                tool_name = first_tool.function.__name__ if hasattr(first_tool, 'function') else 'unknown'
                print_status("Tool creation", True, f"Successfully created {len(coding_tools)} tools, first: {tool_name}")
            else:
                print_status("Tool creation", False, "No tools created from registry")
                all_tests_passed = False
                
        except Exception as e:
            print_status("Tool creation", False, f"Error: {e}")
            all_tests_passed = False
        
        # Test 6: Test no circular imports
        print("\n🔄 Testing for circular imports...")
        try:
            # Try importing everything in different orders
            import importlib
            
            # Clear any cached imports
            modules_to_clear = [
                'app.pydantic_ai.agents',
                'app.pydantic_ai.tools',
                'app.pydantic_ai.tools.registry',
                'app.pydantic_ai.tools.file_operations',
                'app.pydantic_ai.tools.git_operations',
                'app.pydantic_ai.tools.code_analysis',
                'app.pydantic_ai.tools.change_tracking'
            ]
            
            for module_name in modules_to_clear:
                if module_name in sys.modules:
                    del sys.modules[module_name]
            
            # Try importing in different orders
            from app.pydantic_ai.tools import tool_registry
            from app.pydantic_ai.agents import coding_agent
            
            print_status("Circular import test", True, "No circular imports detected")
            
        except Exception as e:
            print_status("Circular import test", False, f"Circular import error: {e}")
            all_tests_passed = False
        
    except Exception as e:
        logger.error(f"Unexpected error during tool registration testing: {e}")
        all_tests_passed = False
    
    # Summary
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 Tool Registration: ALL TESTS PASSED")
        print("✅ Tool registration system is working correctly")
        print("✅ No circular import issues")
        print("✅ All tools properly registered with agents")
        print("✅ Ready for Phase 7A completion")
    else:
        print("❌ Tool Registration: SOME TESTS FAILED")
        print("🔧 Please check the errors above and fix the issues")
    
    return all_tests_passed


async def main():
    """Main test function."""
    try:
        success = await test_tool_registration()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
