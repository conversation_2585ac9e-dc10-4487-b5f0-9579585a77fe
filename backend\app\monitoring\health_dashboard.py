"""
🏥 Unified Health Dashboard - Comprehensive system health monitoring

This module provides a unified health dashboard with:
- Real-time system health status
- Component health monitoring
- Performance metrics aggregation
- Health alerts and notifications
- Frontend-ready health data
"""

import asyncio
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field
import logging

from ..core.monitoring import get_system_monitor
from .api_performance import get_api_performance_monitor
from .agent_activity import get_agent_activity_monitor
from ..sessions import get_session_manager, get_session_recovery
from ..error_handling import get_error_integration

logger = logging.getLogger(__name__)


class ComponentStatus(str, Enum):
    """Component health status."""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    OFFLINE = "offline"
    UNKNOWN = "unknown"


class SystemHealth(BaseModel):
    """Overall system health model."""
    
    overall_status: ComponentStatus = Field(..., description="Overall system health status")
    health_score: float = Field(..., description="Health score (0-100)")
    
    # Component health
    components: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="Component health details")
    
    # System metrics
    system_metrics: Dict[str, Any] = Field(default_factory=dict, description="System resource metrics")
    
    # Performance metrics
    performance_metrics: Dict[str, Any] = Field(default_factory=dict, description="Performance metrics")
    
    # Activity metrics
    activity_metrics: Dict[str, Any] = Field(default_factory=dict, description="Activity metrics")
    
    # Alerts and warnings
    active_alerts: List[Dict[str, Any]] = Field(default_factory=list, description="Active system alerts")
    warnings: List[str] = Field(default_factory=list, description="System warnings")
    
    # Timestamps
    last_updated: datetime = Field(default_factory=datetime.now, description="Last health check timestamp")
    uptime_seconds: float = Field(default=0.0, description="System uptime in seconds")


class HealthDashboard:
    """
    🏥 Unified Health Dashboard
    
    Provides comprehensive system health monitoring with real-time
    status updates, performance metrics, and alert management.
    """
    
    def __init__(self):
        self.system_start_time = datetime.now()
        
        # Component monitors
        self.system_monitor = get_system_monitor()
        self.api_monitor = get_api_performance_monitor()
        self.agent_monitor = get_agent_activity_monitor()
        
        # Health thresholds
        self.health_thresholds = {
            "cpu_warning": 80.0,
            "cpu_critical": 95.0,
            "memory_warning": 80.0,
            "memory_critical": 95.0,
            "disk_warning": 85.0,
            "disk_critical": 95.0,
            "response_time_warning": 1000.0,
            "response_time_critical": 5000.0,
            "error_rate_warning": 5.0,
            "error_rate_critical": 10.0
        }
        
        # Start background health monitoring
        self._start_health_monitoring()
    
    async def get_system_health(self) -> SystemHealth:
        """Get comprehensive system health status."""
        try:
            # Get component health
            components = await self._check_all_components()
            
            # Calculate overall health score
            health_score = self._calculate_health_score(components)
            overall_status = self._determine_overall_status(health_score, components)
            
            # Get system metrics
            system_metrics = await self._get_system_metrics()
            
            # Get performance metrics
            performance_metrics = await self._get_performance_metrics()
            
            # Get activity metrics
            activity_metrics = await self._get_activity_metrics()
            
            # Get active alerts
            active_alerts = await self._get_active_alerts()
            
            # Get warnings
            warnings = self._get_system_warnings(components, system_metrics)
            
            # Calculate uptime
            uptime_seconds = (datetime.now() - self.system_start_time).total_seconds()
            
            return SystemHealth(
                overall_status=overall_status,
                health_score=health_score,
                components=components,
                system_metrics=system_metrics,
                performance_metrics=performance_metrics,
                activity_metrics=activity_metrics,
                active_alerts=active_alerts,
                warnings=warnings,
                uptime_seconds=uptime_seconds
            )
            
        except Exception as e:
            logger.error(f"Health dashboard error: {e}")
            return SystemHealth(
                overall_status=ComponentStatus.UNKNOWN,
                health_score=0.0,
                warnings=[f"Health check failed: {str(e)}"]
            )
    
    async def _check_all_components(self) -> Dict[str, Dict[str, Any]]:
        """Check health of all system components."""
        components = {}
        
        # System resources
        try:
            system_stats = self.system_monitor.get_system_stats()
            components["system_resources"] = {
                "status": self._get_resource_status(system_stats),
                "cpu_usage": system_stats.get("cpu_usage", 0),
                "memory_usage": system_stats.get("memory_usage", 0),
                "disk_usage": system_stats.get("disk_usage", 0),
                "last_check": datetime.now().isoformat()
            }
        except Exception as e:
            components["system_resources"] = {
                "status": ComponentStatus.UNKNOWN,
                "error": str(e)
            }
        
        # API Performance
        try:
            api_summary = self.api_monitor.get_performance_summary()
            components["api_performance"] = {
                "status": self._get_api_status(api_summary),
                "avg_response_time": api_summary.get("avg_response_time", 0),
                "error_rate": api_summary.get("overall_error_rate", 0),
                "total_requests": api_summary.get("total_requests", 0),
                "active_alerts": api_summary.get("active_alerts", 0)
            }
        except Exception as e:
            components["api_performance"] = {
                "status": ComponentStatus.UNKNOWN,
                "error": str(e)
            }
        
        # Agent Activity
        try:
            activity_summary = self.agent_monitor.get_activity_summary()
            components["agent_activity"] = {
                "status": self._get_agent_status(activity_summary),
                "total_agents": activity_summary.get("total_agents", 0),
                "active_agents": activity_summary.get("active_agents", 0),
                "success_rate": activity_summary.get("overall_success_rate", 0),
                "tasks_completed": activity_summary.get("total_tasks_completed", 0)
            }
        except Exception as e:
            components["agent_activity"] = {
                "status": ComponentStatus.UNKNOWN,
                "error": str(e)
            }
        
        # Session Management
        try:
            session_manager = get_session_manager()
            session_stats = session_manager.get_session_stats()
            components["session_management"] = {
                "status": ComponentStatus.HEALTHY,
                "active_sessions": session_stats.get("total_active_sessions", 0),
                "total_tokens": session_stats.get("total_tokens_used", 0),
                "api_calls": session_stats.get("total_api_calls", 0)
            }
        except Exception as e:
            components["session_management"] = {
                "status": ComponentStatus.UNKNOWN,
                "error": str(e)
            }
        
        # Error Handling System
        try:
            error_integration = get_error_integration()
            error_health = error_integration.get_system_health()
            components["error_handling"] = {
                "status": ComponentStatus.HEALTHY if error_health.get("overall_health") == "healthy" else ComponentStatus.WARNING,
                "ml_intelligence": error_health.get("component_health", {}).get("ml_intelligence", {}),
                "retry_manager": error_health.get("component_health", {}).get("retry_manager", {}),
                "recovery_strategies": error_health.get("component_health", {}).get("recovery_strategies", {})
            }
        except Exception as e:
            components["error_handling"] = {
                "status": ComponentStatus.UNKNOWN,
                "error": str(e)
            }
        
        # Session Recovery
        try:
            session_recovery = get_session_recovery()
            recovery_stats = session_recovery.get_recovery_stats()
            components["session_recovery"] = {
                "status": ComponentStatus.HEALTHY,
                "total_recoveries": recovery_stats.get("total_recoveries", 0),
                "successful_recoveries": recovery_stats.get("successful_recoveries", 0),
                "total_snapshots": recovery_stats.get("total_snapshots", 0)
            }
        except Exception as e:
            components["session_recovery"] = {
                "status": ComponentStatus.UNKNOWN,
                "error": str(e)
            }
        
        return components
    
    def _get_resource_status(self, system_stats: Dict[str, Any]) -> ComponentStatus:
        """Determine system resource status."""
        cpu = system_stats.get("cpu_usage", 0)
        memory = system_stats.get("memory_usage", 0)
        disk = system_stats.get("disk_usage", 0)
        
        if (cpu >= self.health_thresholds["cpu_critical"] or 
            memory >= self.health_thresholds["memory_critical"] or 
            disk >= self.health_thresholds["disk_critical"]):
            return ComponentStatus.CRITICAL
        
        if (cpu >= self.health_thresholds["cpu_warning"] or 
            memory >= self.health_thresholds["memory_warning"] or 
            disk >= self.health_thresholds["disk_warning"]):
            return ComponentStatus.WARNING
        
        return ComponentStatus.HEALTHY
    
    def _get_api_status(self, api_summary: Dict[str, Any]) -> ComponentStatus:
        """Determine API performance status."""
        response_time = api_summary.get("avg_response_time", 0)
        error_rate = api_summary.get("overall_error_rate", 0)
        
        if (response_time >= self.health_thresholds["response_time_critical"] or 
            error_rate >= self.health_thresholds["error_rate_critical"]):
            return ComponentStatus.CRITICAL
        
        if (response_time >= self.health_thresholds["response_time_warning"] or 
            error_rate >= self.health_thresholds["error_rate_warning"]):
            return ComponentStatus.WARNING
        
        return ComponentStatus.HEALTHY
    
    def _get_agent_status(self, activity_summary: Dict[str, Any]) -> ComponentStatus:
        """Determine agent activity status."""
        error_agents = activity_summary.get("error_agents", 0)
        total_agents = activity_summary.get("total_agents", 1)
        success_rate = activity_summary.get("overall_success_rate", 100)
        
        error_rate = (error_agents / total_agents * 100) if total_agents > 0 else 0
        
        if error_rate >= 50 or success_rate < 50:
            return ComponentStatus.CRITICAL
        
        if error_rate >= 20 or success_rate < 80:
            return ComponentStatus.WARNING
        
        return ComponentStatus.HEALTHY
    
    def _calculate_health_score(self, components: Dict[str, Dict[str, Any]]) -> float:
        """Calculate overall health score (0-100)."""
        if not components:
            return 0.0
        
        status_scores = {
            ComponentStatus.HEALTHY: 100,
            ComponentStatus.WARNING: 70,
            ComponentStatus.CRITICAL: 30,
            ComponentStatus.OFFLINE: 0,
            ComponentStatus.UNKNOWN: 50
        }
        
        total_score = 0
        component_count = 0
        
        for component_data in components.values():
            status = component_data.get("status", ComponentStatus.UNKNOWN)
            if isinstance(status, str):
                status = ComponentStatus(status)
            
            total_score += status_scores.get(status, 50)
            component_count += 1
        
        return total_score / component_count if component_count > 0 else 0.0
    
    def _determine_overall_status(
        self, 
        health_score: float, 
        components: Dict[str, Dict[str, Any]]
    ) -> ComponentStatus:
        """Determine overall system status."""
        # Check for any critical components
        for component_data in components.values():
            status = component_data.get("status", ComponentStatus.UNKNOWN)
            if isinstance(status, str):
                status = ComponentStatus(status)
            
            if status == ComponentStatus.CRITICAL:
                return ComponentStatus.CRITICAL
        
        # Check health score
        if health_score >= 90:
            return ComponentStatus.HEALTHY
        elif health_score >= 70:
            return ComponentStatus.WARNING
        else:
            return ComponentStatus.CRITICAL
    
    async def _get_system_metrics(self) -> Dict[str, Any]:
        """Get system resource metrics."""
        try:
            return self.system_monitor.get_system_stats()
        except Exception as e:
            logger.error(f"Failed to get system metrics: {e}")
            return {}
    
    async def _get_performance_metrics(self) -> Dict[str, Any]:
        """Get API performance metrics."""
        try:
            return self.api_monitor.get_performance_summary()
        except Exception as e:
            logger.error(f"Failed to get performance metrics: {e}")
            return {}
    
    async def _get_activity_metrics(self) -> Dict[str, Any]:
        """Get agent activity metrics."""
        try:
            return self.agent_monitor.get_activity_summary()
        except Exception as e:
            logger.error(f"Failed to get activity metrics: {e}")
            return {}
    
    async def _get_active_alerts(self) -> List[Dict[str, Any]]:
        """Get all active system alerts."""
        alerts = []
        
        try:
            # Get API performance alerts
            api_alerts = self.api_monitor.get_active_alerts()
            for alert in api_alerts:
                alerts.append({
                    "type": "api_performance",
                    "level": alert.level.value,
                    "message": alert.message,
                    "timestamp": alert.timestamp.isoformat()
                })
        except Exception as e:
            logger.error(f"Failed to get API alerts: {e}")
        
        return alerts
    
    def _get_system_warnings(
        self, 
        components: Dict[str, Dict[str, Any]], 
        system_metrics: Dict[str, Any]
    ) -> List[str]:
        """Get system warnings based on current state."""
        warnings = []
        
        # Check for component warnings
        for component_name, component_data in components.items():
            status = component_data.get("status", ComponentStatus.UNKNOWN)
            if isinstance(status, str):
                status = ComponentStatus(status)
            
            if status == ComponentStatus.WARNING:
                warnings.append(f"{component_name.replace('_', ' ').title()} is in warning state")
            elif status == ComponentStatus.CRITICAL:
                warnings.append(f"{component_name.replace('_', ' ').title()} is in critical state")
            elif status == ComponentStatus.UNKNOWN:
                warnings.append(f"{component_name.replace('_', ' ').title()} status is unknown")
        
        # Check system resource warnings
        cpu = system_metrics.get("cpu_usage", 0)
        memory = system_metrics.get("memory_usage", 0)
        disk = system_metrics.get("disk_usage", 0)
        
        if cpu >= self.health_thresholds["cpu_warning"]:
            warnings.append(f"High CPU usage: {cpu:.1f}%")
        
        if memory >= self.health_thresholds["memory_warning"]:
            warnings.append(f"High memory usage: {memory:.1f}%")
        
        if disk >= self.health_thresholds["disk_warning"]:
            warnings.append(f"High disk usage: {disk:.1f}%")
        
        return warnings
    
    def _start_health_monitoring(self) -> None:
        """Start background health monitoring tasks."""
        async def health_monitoring_loop():
            while True:
                try:
                    # Perform periodic health checks
                    health = await self.get_system_health()
                    
                    # Log critical issues
                    if health.overall_status == ComponentStatus.CRITICAL:
                        logger.critical(f"System health critical! Score: {health.health_score}")
                    elif health.overall_status == ComponentStatus.WARNING:
                        logger.warning(f"System health warning. Score: {health.health_score}")
                    
                    await asyncio.sleep(60)  # Check every minute
                    
                except Exception as e:
                    logger.error(f"Health monitoring loop failed: {e}")
                    await asyncio.sleep(300)  # Wait longer on error
        
        # Start monitoring task
        asyncio.create_task(health_monitoring_loop())


# Global health dashboard instance
_health_dashboard: Optional[HealthDashboard] = None


def get_health_dashboard() -> HealthDashboard:
    """Get the global health dashboard instance."""
    global _health_dashboard
    if _health_dashboard is None:
        _health_dashboard = HealthDashboard()
    return _health_dashboard
