"""
Git Operations Tools for Pydantic AI Migration

This module provides standalone git operation tools that can be registered
with Pydantic AI agents, avoiding circular import issues.
"""

import logging
from typing import Optional, List

import logfire
from pydantic_ai import RunContext

from ..dependencies import CodingDependencies
from ..models import GitOperationResult

logger = logging.getLogger(__name__)


async def git_status(
    ctx: RunContext[CodingDependencies],
    repo_path: str
) -> GitOperationResult:
    """
    Get git status for the repository.
    
    Args:
        ctx: Run context with dependencies
        repo_path: Path to the git repository
        
    Returns:
        GitOperationResult with git status information
    """
    with logfire.span("git_status", repo_path=repo_path):
        try:
            result = await ctx.deps.git_operations.get_git_status(repo_path)
            
            if result['status'] == 'success':
                logfire.info("Git status retrieved successfully")
                return GitOperationResult(
                    operation="git_status",
                    success=True,
                    output=result.get('output', ''),
                    files_changed=result.get('changed_files', [])
                )
            else:
                logfire.error("Git status failed", error=result.get('error'))
                return GitOperationResult(
                    operation="git_status",
                    success=False,
                    output="",
                    error_message=result.get('error')
                )
                
        except Exception as e:
            logger.error(f"Failed to get git status for {repo_path}: {e}")
            logfire.error("Git status exception", error=str(e))
            return GitOperationResult(
                operation="git_status",
                success=False,
                output="",
                error_message=str(e)
            )


async def git_diff(
    ctx: RunContext[CodingDependencies],
    repo_path: str,
    file_path: Optional[str] = None,
    staged: bool = False
) -> GitOperationResult:
    """
    Get git diff for the repository or specific file.
    
    Args:
        ctx: Run context with dependencies
        repo_path: Path to the git repository
        file_path: Optional specific file to diff
        staged: Whether to show staged changes
        
    Returns:
        GitOperationResult with diff output
    """
    with logfire.span("git_diff", repo_path=repo_path, file_path=file_path, staged=staged):
        try:
            result = await ctx.deps.git_operations.get_git_diff(repo_path, file_path, staged)
            
            if result['status'] == 'success':
                logfire.info("Git diff retrieved successfully")
                return GitOperationResult(
                    operation="git_diff",
                    success=True,
                    output=result.get('diff', ''),
                    files_changed=result.get('files_changed', [])
                )
            else:
                logfire.error("Git diff failed", error=result.get('error'))
                return GitOperationResult(
                    operation="git_diff",
                    success=False,
                    output="",
                    error_message=result.get('error')
                )
                
        except Exception as e:
            logger.error(f"Failed to get git diff for {repo_path}: {e}")
            logfire.error("Git diff exception", error=str(e))
            return GitOperationResult(
                operation="git_diff",
                success=False,
                output="",
                error_message=str(e)
            )


async def git_commit(
    ctx: RunContext[CodingDependencies],
    repo_path: str,
    message: str,
    add_all: bool = True,
    files: Optional[List[str]] = None
) -> GitOperationResult:
    """
    Commit changes to the git repository.
    
    Args:
        ctx: Run context with dependencies
        repo_path: Path to the git repository
        message: Commit message
        add_all: Whether to add all changes before committing
        files: Optional list of specific files to commit
        
    Returns:
        GitOperationResult with commit information
    """
    with logfire.span("git_commit", repo_path=repo_path, message=message, add_all=add_all):
        try:
            result = await ctx.deps.git_operations.git_commit(repo_path, message, add_all, files)
            
            if result['status'] == 'success':
                commit_hash = result.get('commit_hash')
                logfire.info("Git commit successful", commit_hash=commit_hash)
                return GitOperationResult(
                    operation="git_commit",
                    success=True,
                    output=result.get('output', ''),
                    commit_hash=commit_hash,
                    files_changed=result.get('files_committed', [])
                )
            else:
                logfire.error("Git commit failed", error=result.get('error'))
                return GitOperationResult(
                    operation="git_commit",
                    success=False,
                    output="",
                    error_message=result.get('error')
                )
                
        except Exception as e:
            logger.error(f"Failed to commit to {repo_path}: {e}")
            logfire.error("Git commit exception", error=str(e))
            return GitOperationResult(
                operation="git_commit",
                success=False,
                output="",
                error_message=str(e)
            )


async def git_push(
    ctx: RunContext[CodingDependencies],
    repo_path: str,
    remote: str = "origin",
    branch: Optional[str] = None
) -> GitOperationResult:
    """
    Push changes to remote repository.
    
    Args:
        ctx: Run context with dependencies
        repo_path: Path to the git repository
        remote: Remote name (default: origin)
        branch: Branch to push (default: current branch)
        
    Returns:
        GitOperationResult with push information
    """
    with logfire.span("git_push", repo_path=repo_path, remote=remote, branch=branch):
        try:
            result = await ctx.deps.git_operations.git_push(repo_path, remote, branch)
            
            if result['status'] == 'success':
                logfire.info("Git push successful")
                return GitOperationResult(
                    operation="git_push",
                    success=True,
                    output=result.get('output', '')
                )
            else:
                logfire.error("Git push failed", error=result.get('error'))
                return GitOperationResult(
                    operation="git_push",
                    success=False,
                    output="",
                    error_message=result.get('error')
                )
                
        except Exception as e:
            logger.error(f"Failed to push {repo_path}: {e}")
            logfire.error("Git push exception", error=str(e))
            return GitOperationResult(
                operation="git_push",
                success=False,
                output="",
                error_message=str(e)
            )


async def git_branch(
    ctx: RunContext[CodingDependencies],
    repo_path: str,
    branch_name: Optional[str] = None,
    create: bool = False,
    checkout: bool = False
) -> GitOperationResult:
    """
    Manage git branches (list, create, checkout).
    
    Args:
        ctx: Run context with dependencies
        repo_path: Path to the git repository
        branch_name: Name of the branch (for create/checkout operations)
        create: Whether to create a new branch
        checkout: Whether to checkout the branch
        
    Returns:
        GitOperationResult with branch information
    """
    with logfire.span("git_branch", repo_path=repo_path, branch_name=branch_name, create=create, checkout=checkout):
        try:
            if create and branch_name:
                result = await ctx.deps.git_operations.create_branch(repo_path, branch_name, checkout)
                operation = "git_create_branch"
            elif checkout and branch_name:
                result = await ctx.deps.git_operations.checkout_branch(repo_path, branch_name)
                operation = "git_checkout_branch"
            else:
                result = await ctx.deps.git_operations.list_branches(repo_path)
                operation = "git_list_branches"
            
            if result['status'] == 'success':
                logfire.info(f"{operation} successful")
                return GitOperationResult(
                    operation=operation,
                    success=True,
                    output=result.get('output', ''),
                    files_changed=result.get('branches', []) if 'branches' in result else []
                )
            else:
                logfire.error(f"{operation} failed", error=result.get('error'))
                return GitOperationResult(
                    operation=operation,
                    success=False,
                    output="",
                    error_message=result.get('error')
                )
                
        except Exception as e:
            logger.error(f"Failed git branch operation for {repo_path}: {e}")
            logfire.error("Git branch exception", error=str(e))
            return GitOperationResult(
                operation="git_branch",
                success=False,
                output="",
                error_message=str(e)
            )


async def git_log(
    ctx: RunContext[CodingDependencies],
    repo_path: str,
    max_count: int = 10,
    file_path: Optional[str] = None
) -> GitOperationResult:
    """
    Get git commit history.
    
    Args:
        ctx: Run context with dependencies
        repo_path: Path to the git repository
        max_count: Maximum number of commits to retrieve
        file_path: Optional specific file to get history for
        
    Returns:
        GitOperationResult with commit history
    """
    with logfire.span("git_log", repo_path=repo_path, max_count=max_count, file_path=file_path):
        try:
            result = await ctx.deps.git_operations.get_commit_history(repo_path, max_count, file_path)
            
            if result['status'] == 'success':
                commits = result.get('commits', [])
                logfire.info("Git log retrieved successfully", commit_count=len(commits))
                return GitOperationResult(
                    operation="git_log",
                    success=True,
                    output=result.get('output', ''),
                    files_changed=commits
                )
            else:
                logfire.error("Git log failed", error=result.get('error'))
                return GitOperationResult(
                    operation="git_log",
                    success=False,
                    output="",
                    error_message=result.get('error')
                )
                
        except Exception as e:
            logger.error(f"Failed to get git log for {repo_path}: {e}")
            logfire.error("Git log exception", error=str(e))
            return GitOperationResult(
                operation="git_log",
                success=False,
                output="",
                error_message=str(e)
            )
