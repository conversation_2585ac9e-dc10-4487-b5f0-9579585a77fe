"""
Pydantic AI Migration for DeepNexus AI Coder Agent - Phase 7

This module contains the complete Pydantic AI implementation that replaces
the existing Phase 5 TaskRunner system with modern, type-safe AI agents.

Features:
- Dependency injection architecture
- Decorator-based tool registration
- Structured outputs with Pydantic models
- Built-in error handling and retry logic
- Comprehensive testing with TestModel/FunctionModel
- Logfire monitoring and instrumentation
- Multi-agent workflows
- CLI integration
"""

from .agents import coding_agent, vision_agent
from .dependencies import CodingDependencies, VisionDependencies, create_dependencies
from .models import (
    TaskResult,
    CodeAnalysisResult,
    FileOperationResult,
    GitOperationResult,
    ChangeTrackingResult
)

__all__ = [
    'coding_agent',
    'vision_agent',
    'CodingDependencies',
    'VisionDependencies',
    'create_dependencies',
    'TaskResult',
    'CodeAnalysisResult',
    'FileOperationResult',
    'GitOperationResult',
    'ChangeTrackingResult'
]

__version__ = "7.0.0"
