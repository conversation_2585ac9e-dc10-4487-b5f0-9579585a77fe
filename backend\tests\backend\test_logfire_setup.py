#!/usr/bin/env python3
"""
Logfire Setup Test Script

This script validates that Logfire is properly configured and working
with your DeepNexus project token.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def print_status(test_name: str, success: bool, details: str = ""):
    """Print test status with color coding."""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} - {test_name}")
    if details:
        print(f"    {details}")


async def test_logfire_setup():
    """Test Logfire configuration and connectivity."""
    print("🔥 Testing Logfire Setup for DeepNexus Project")
    print("=" * 60)
    
    all_tests_passed = True
    
    try:
        # Test 1: Import Logfire
        print("\n📦 Testing Logfire import...")
        try:
            import logfire
            print_status("Logfire import", True, "Logfire library imported successfully")
        except ImportError as e:
            print_status("Logfire import", False, f"Import error: {e}")
            all_tests_passed = False
            return all_tests_passed
        
        # Test 2: Check environment variable
        print("\n🌍 Testing environment configuration...")
        token = os.getenv('LOGFIRE_TOKEN')
        if token and token.startswith('pylf_v1_eu_'):
            print_status("Environment token", True, f"Token found: {token[:20]}...")
        else:
            print_status("Environment token", False, "LOGFIRE_TOKEN not found or invalid")
            all_tests_passed = False
        
        # Test 3: Test our monitoring module
        print("\n🔧 Testing monitoring module...")
        try:
            from app.pydantic_ai.monitoring import configure_logfire, log_phase7_milestone
            print_status("Monitoring module import", True, "Monitoring module imported successfully")
        except ImportError as e:
            print_status("Monitoring module import", False, f"Import error: {e}")
            all_tests_passed = False
            return all_tests_passed
        
        # Test 4: Test basic Logfire configuration
        print("\n⚙️ Testing Logfire configuration...")
        try:
            configure_logfire(
                token=token,
                project_name='deepnexus',
                environment='test'
            )
            print_status("Logfire configuration", True, "Logfire configured successfully")
        except Exception as e:
            print_status("Logfire configuration", False, f"Configuration error: {e}")
            all_tests_passed = False
        
        # Test 5: Test basic logging
        print("\n📝 Testing basic logging...")
        try:
            logfire.info("🧪 Test log from DeepNexus Phase 7A setup", test=True, phase="7A")
            print_status("Basic logging", True, "Test log sent successfully")
        except Exception as e:
            print_status("Basic logging", False, f"Logging error: {e}")
            all_tests_passed = False
        
        # Test 6: Test milestone logging
        print("\n🎯 Testing milestone logging...")
        try:
            log_phase7_milestone("7A", "logfire_test", "completed", "Logfire setup validation successful")
            print_status("Milestone logging", True, "Milestone logged successfully")
        except Exception as e:
            print_status("Milestone logging", False, f"Milestone logging error: {e}")
            all_tests_passed = False
        
        # Test 7: Test span creation
        print("\n🔄 Testing span creation...")
        try:
            with logfire.span("test_span", operation="logfire_validation"):
                logfire.info("Inside test span", span_test=True)
                # Simulate some work
                await asyncio.sleep(0.1)
            print_status("Span creation", True, "Spans created and logged successfully")
        except Exception as e:
            print_status("Span creation", False, f"Span creation error: {e}")
            all_tests_passed = False
        
        # Test 8: Test agent import with monitoring
        print("\n🤖 Testing agent import with monitoring...")
        try:
            from app.pydantic_ai.agents import coding_agent, vision_agent
            print_status("Agent import with monitoring", True, "Agents imported with monitoring enabled")
        except Exception as e:
            print_status("Agent import with monitoring", False, f"Agent import error: {e}")
            all_tests_passed = False
        
    except Exception as e:
        logger.error(f"Unexpected error during Logfire testing: {e}")
        all_tests_passed = False
    
    # Summary
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 Logfire Setup: ALL TESTS PASSED")
        print("✅ Logfire is properly configured and connected to your DeepNexus project")
        print("🔗 Check your Logfire dashboard: https://logfire.pydantic.dev/")
        print("📊 You should see test logs and spans appearing in the Live View")
    else:
        print("❌ Logfire Setup: SOME TESTS FAILED")
        print("🔧 Please check the errors above and fix the configuration")
    
    return all_tests_passed


async def main():
    """Main test function."""
    try:
        success = await test_logfire_setup()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
