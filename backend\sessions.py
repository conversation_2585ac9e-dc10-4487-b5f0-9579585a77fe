# backend/sessions.py
import sqlite3
import json
import uuid
import time
import logging
from typing import Optional, Dict, Any, List

# Import local modules with fallback for direct execution
try:
    from .config import settings
except ImportError:
    # For direct execution
    from config import settings

logger = logging.getLogger(__name__)

def init_db():
    """Initialize the SQLite database with the sessions table."""
    try:
        # Ensure the data directory exists
        import os
        os.makedirs(os.path.dirname(settings.sqlite_db_path), exist_ok=True)
        
        conn = sqlite3.connect(settings.sqlite_db_path, check_same_thread=False)
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sessions (
                session_id     TEXT PRIMARY KEY,
                created_at     REAL NOT NULL,
                updated_at     REAL NOT NULL,
                chat_summary   TEXT DEFAULT '',
                last_commit    TEXT DEFAULT '',
                qdrant_vector_ids TEXT DEFAULT '[]',
                repo_url       TEXT DEFAULT '',
                branch         TEXT DEFAULT 'main',
                status         TEXT DEFAULT 'created'
            );
        """)
        conn.commit()
        conn.close()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise

def create_session(repo_url: str = "", branch: str = "main") -> str:
    """Create a new session and return the session ID."""
    session_id = str(uuid.uuid4())
    now = time.time()
    
    try:
        conn = sqlite3.connect(settings.sqlite_db_path, check_same_thread=False)
        cursor = conn.cursor()
        cursor.execute(
            """INSERT INTO sessions 
               (session_id, created_at, updated_at, chat_summary, last_commit, 
                qdrant_vector_ids, repo_url, branch, status) 
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
            (session_id, now, now, "", "", json.dumps([]), repo_url, branch, "created")
        )
        conn.commit()
        conn.close()
        logger.info(f"Created session {session_id}")
        return session_id
    except Exception as e:
        logger.error(f"Failed to create session: {e}")
        raise

def get_session(session_id: str) -> Optional[Dict[str, Any]]:
    """Retrieve a session by ID."""
    try:
        conn = sqlite3.connect(settings.sqlite_db_path, check_same_thread=False)
        cursor = conn.cursor()
        cursor.execute(
            """SELECT session_id, created_at, updated_at, chat_summary, last_commit, 
                      qdrant_vector_ids, repo_url, branch, status 
               FROM sessions WHERE session_id = ?""", 
            (session_id,)
        )
        row = cursor.fetchone()
        conn.close()
        
        if not row:
            return None
            
        return {
            "session_id": row[0],
            "created_at": row[1],
            "updated_at": row[2],
            "chat_summary": row[3],
            "last_commit": row[4],
            "qdrant_vector_ids": json.loads(row[5] or "[]"),
            "repo_url": row[6],
            "branch": row[7],
            "status": row[8]
        }
    except Exception as e:
        logger.error(f"Failed to get session {session_id}: {e}")
        return None

def update_session(session_id: str, **kwargs) -> bool:
    """
    Update session fields.
    Allowed kwargs: chat_summary, last_commit, qdrant_vector_ids, repo_url, branch, status
    """
    allowed_fields = {
        "chat_summary", "last_commit", "qdrant_vector_ids", 
        "repo_url", "branch", "status"
    }
    
    # Filter out invalid fields
    updates = {k: v for k, v in kwargs.items() if k in allowed_fields}
    
    if not updates:
        logger.warning(f"No valid fields to update for session {session_id}")
        return False
    
    try:
        fields = []
        values = []
        
        for field, value in updates.items():
            if field == "qdrant_vector_ids" and isinstance(value, list):
                fields.append(f"{field} = ?")
                values.append(json.dumps(value))
            else:
                fields.append(f"{field} = ?")
                values.append(value)
        
        # Always update updated_at
        fields.append("updated_at = ?")
        values.append(time.time())
        values.append(session_id)
        
        conn = sqlite3.connect(settings.sqlite_db_path, check_same_thread=False)
        cursor = conn.cursor()
        cursor.execute(
            f"UPDATE sessions SET {', '.join(fields)} WHERE session_id = ?", 
            values
        )
        conn.commit()
        conn.close()
        
        logger.info(f"Updated session {session_id} with fields: {list(updates.keys())}")
        return True
    except Exception as e:
        logger.error(f"Failed to update session {session_id}: {e}")
        return False

def list_sessions(limit: int = 50) -> List[Dict[str, Any]]:
    """List recent sessions."""
    try:
        conn = sqlite3.connect(settings.sqlite_db_path, check_same_thread=False)
        cursor = conn.cursor()
        cursor.execute(
            """SELECT session_id, created_at, updated_at, chat_summary, last_commit, 
                      qdrant_vector_ids, repo_url, branch, status 
               FROM sessions 
               ORDER BY updated_at DESC 
               LIMIT ?""", 
            (limit,)
        )
        rows = cursor.fetchall()
        conn.close()
        
        sessions = []
        for row in rows:
            sessions.append({
                "session_id": row[0],
                "created_at": row[1],
                "updated_at": row[2],
                "chat_summary": row[3],
                "last_commit": row[4],
                "qdrant_vector_ids": json.loads(row[5] or "[]"),
                "repo_url": row[6],
                "branch": row[7],
                "status": row[8]
            })
        
        return sessions
    except Exception as e:
        logger.error(f"Failed to list sessions: {e}")
        return []

def delete_session(session_id: str) -> bool:
    """Delete a session."""
    try:
        conn = sqlite3.connect(settings.sqlite_db_path, check_same_thread=False)
        cursor = conn.cursor()
        cursor.execute("DELETE FROM sessions WHERE session_id = ?", (session_id,))
        deleted = cursor.rowcount > 0
        conn.commit()
        conn.close()
        
        if deleted:
            logger.info(f"Deleted session {session_id}")
        else:
            logger.warning(f"Session {session_id} not found for deletion")
        
        return deleted
    except Exception as e:
        logger.error(f"Failed to delete session {session_id}: {e}")
        return False
