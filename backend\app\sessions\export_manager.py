"""
📤 Export Manager - Export conversations and results in multiple formats

This module provides comprehensive export capabilities:
- Export conversations as markdown, JSON, HTML, and text
- Export session data and statistics
- Batch export for multiple sessions
- Custom export templates and formatting
- Integration with session management and conversation history
"""

import json
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field
import logging

from .session_manager import SessionManager, Session
from .conversation_history import ConversationHistory, Message, MessageRole, MessageType

logger = logging.getLogger(__name__)


class ExportFormat(str, Enum):
    """Export format enumeration."""
    MARKDOWN = "markdown"
    JSON = "json"
    HTML = "html"
    TEXT = "text"
    CSV = "csv"
    ZIP = "zip"


class ExportConfig(BaseModel):
    """Export configuration settings."""
    
    # Format settings
    format: ExportFormat = Field(..., description="Export format")
    include_metadata: bool = Field(default=True, description="Include message metadata")
    include_timestamps: bool = Field(default=True, description="Include timestamps")
    include_token_counts: bool = Field(default=False, description="Include token counts")
    
    # Content filters
    include_system_messages: bool = Field(default=True, description="Include system messages")
    include_tool_messages: bool = Field(default=True, description="Include tool messages")
    include_code_blocks: bool = Field(default=True, description="Include code blocks")
    
    # Formatting options
    pretty_format: bool = Field(default=True, description="Use pretty formatting")
    include_session_info: bool = Field(default=True, description="Include session information")
    include_statistics: bool = Field(default=False, description="Include conversation statistics")
    
    # File options
    single_file: bool = Field(default=True, description="Export as single file")
    compress: bool = Field(default=False, description="Compress export files")
    
    # Custom options
    custom_template: Optional[str] = Field(None, description="Custom export template")
    date_format: str = Field(default="%Y-%m-%d %H:%M:%S", description="Date format string")


class ExportResult(BaseModel):
    """Result of export operation."""
    
    # Export details
    export_id: str = Field(..., description="Unique export identifier")
    session_ids: List[str] = Field(..., description="Exported session IDs")
    format: ExportFormat = Field(..., description="Export format used")
    
    # File information
    file_path: Optional[str] = Field(None, description="Path to exported file")
    file_size: int = Field(default=0, description="Size of exported file in bytes")
    
    # Export statistics
    messages_exported: int = Field(default=0, description="Number of messages exported")
    sessions_exported: int = Field(default=0, description="Number of sessions exported")
    
    # Timing information
    export_start_time: datetime = Field(default_factory=datetime.now, description="Export start time")
    export_end_time: Optional[datetime] = Field(None, description="Export completion time")
    export_duration: Optional[float] = Field(None, description="Export duration in seconds")
    
    # Status and errors
    success: bool = Field(default=False, description="Whether export was successful")
    errors: List[str] = Field(default_factory=list, description="Export errors")
    warnings: List[str] = Field(default_factory=list, description="Export warnings")
    
    def complete_export(self, success: bool = True):
        """Mark export as completed."""
        self.export_end_time = datetime.now()
        self.export_duration = (
            self.export_end_time - self.export_start_time
        ).total_seconds()
        self.success = success


class ExportManager:
    """
    📤 Advanced Export Manager
    
    Provides comprehensive export capabilities for conversations and session data
    with support for multiple formats and customizable output options.
    """
    
    def __init__(
        self,
        session_manager: SessionManager,
        conversation_history: ConversationHistory,
        export_dir: str = "data/exports"
    ):
        self.session_manager = session_manager
        self.conversation_history = conversation_history
        self.export_dir = Path(export_dir)
        self.export_dir.mkdir(parents=True, exist_ok=True)
        
        # Export templates
        self.templates = self._load_export_templates()
    
    def _load_export_templates(self) -> Dict[str, str]:
        """Load export templates for different formats."""
        return {
            "markdown_header": """# Conversation Export
**Session:** {session_name}
**Project:** {project_id}
**Exported:** {export_time}
**Messages:** {message_count}

---

""",
            "html_header": """<!DOCTYPE html>
<html>
<head>
    <title>Conversation Export - {session_name}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .message {{ margin: 10px 0; padding: 10px; border-left: 3px solid #ccc; }}
        .user {{ border-left-color: #007bff; }}
        .assistant {{ border-left-color: #28a745; }}
        .system {{ border-left-color: #ffc107; }}
        .tool {{ border-left-color: #6c757d; }}
        .timestamp {{ color: #666; font-size: 0.9em; }}
        .metadata {{ color: #999; font-size: 0.8em; }}
        pre {{ background: #f8f9fa; padding: 10px; border-radius: 4px; }}
    </style>
</head>
<body>
    <h1>Conversation Export</h1>
    <div class="session-info">
        <p><strong>Session:</strong> {session_name}</p>
        <p><strong>Project:</strong> {project_id}</p>
        <p><strong>Exported:</strong> {export_time}</p>
        <p><strong>Messages:</strong> {message_count}</p>
    </div>
    <hr>
""",
            "html_footer": """</body>
</html>"""
        }
    
    async def export_session(
        self,
        session_id: str,
        config: ExportConfig,
        output_path: Optional[str] = None
    ) -> ExportResult:
        """
        Export a single session.
        
        Args:
            session_id: Session to export
            config: Export configuration
            output_path: Optional custom output path
        
        Returns:
            ExportResult with export details
        """
        export_result = ExportResult(
            export_id=f"export_{session_id}_{int(datetime.now().timestamp())}",
            session_ids=[session_id],
            format=config.format
        )
        
        try:
            logger.info(f"Starting export for session {session_id}")
            
            # Get session data
            session = await self.session_manager.get_session(session_id)
            if not session:
                export_result.errors.append(f"Session {session_id} not found")
                export_result.complete_export(False)
                return export_result
            
            # Get conversation messages
            messages = await self.conversation_history.get_session_messages(
                session_id, limit=10000  # Large limit to get all messages
            )
            
            # Filter messages based on config
            filtered_messages = self._filter_messages(messages, config)
            
            # Generate export content
            export_content = await self._generate_export_content(
                session, filtered_messages, config
            )
            
            # Determine output path
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{session.name}_{timestamp}.{config.format.value}"
                output_path = self.export_dir / filename
            else:
                output_path = Path(output_path)
            
            # Write export file
            await self._write_export_file(export_content, output_path, config)
            
            # Update export result
            export_result.file_path = str(output_path)
            export_result.file_size = output_path.stat().st_size if output_path.exists() else 0
            export_result.messages_exported = len(filtered_messages)
            export_result.sessions_exported = 1
            export_result.complete_export(True)
            
            logger.info(f"Successfully exported session {session_id} to {output_path}")
            return export_result
            
        except Exception as e:
            logger.error(f"Export failed for session {session_id}: {e}")
            export_result.errors.append(f"Export failed: {str(e)}")
            export_result.complete_export(False)
            return export_result
    
    async def export_multiple_sessions(
        self,
        session_ids: List[str],
        config: ExportConfig,
        output_path: Optional[str] = None
    ) -> ExportResult:
        """Export multiple sessions."""
        export_result = ExportResult(
            export_id=f"export_multi_{int(datetime.now().timestamp())}",
            session_ids=session_ids,
            format=config.format
        )
        
        try:
            logger.info(f"Starting batch export for {len(session_ids)} sessions")
            
            if config.single_file:
                # Export all sessions to a single file
                return await self._export_sessions_single_file(session_ids, config, output_path)
            else:
                # Export each session to separate files
                return await self._export_sessions_separate_files(session_ids, config, output_path)
                
        except Exception as e:
            logger.error(f"Batch export failed: {e}")
            export_result.errors.append(f"Batch export failed: {str(e)}")
            export_result.complete_export(False)
            return export_result
    
    async def export_project_sessions(
        self,
        project_id: str,
        config: ExportConfig,
        output_path: Optional[str] = None
    ) -> ExportResult:
        """Export all sessions for a project."""
        try:
            # Get all sessions for the project
            sessions = await self.session_manager.get_project_sessions(project_id)
            session_ids = [session.session_id for session in sessions]
            
            if not session_ids:
                export_result = ExportResult(
                    export_id=f"export_project_{project_id}_{int(datetime.now().timestamp())}",
                    session_ids=[],
                    format=config.format
                )
                export_result.errors.append(f"No sessions found for project {project_id}")
                export_result.complete_export(False)
                return export_result
            
            # Export all project sessions
            return await self.export_multiple_sessions(session_ids, config, output_path)
            
        except Exception as e:
            logger.error(f"Project export failed for {project_id}: {e}")
            export_result = ExportResult(
                export_id=f"export_project_{project_id}_{int(datetime.now().timestamp())}",
                session_ids=[],
                format=config.format
            )
            export_result.errors.append(f"Project export failed: {str(e)}")
            export_result.complete_export(False)
            return export_result
    
    def _filter_messages(self, messages: List[Message], config: ExportConfig) -> List[Message]:
        """Filter messages based on export configuration."""
        filtered = []
        
        for message in messages:
            # Filter by role
            if not config.include_system_messages and message.role == MessageRole.SYSTEM:
                continue
            if not config.include_tool_messages and message.role == MessageRole.TOOL:
                continue
            
            # Filter by message type
            if not config.include_code_blocks and message.message_type == MessageType.CODE:
                continue
            
            filtered.append(message)
        
        return filtered

    async def _generate_export_content(
        self,
        session: Session,
        messages: List[Message],
        config: ExportConfig
    ) -> str:
        """Generate export content based on format."""
        if config.format == ExportFormat.MARKDOWN:
            return self._generate_markdown_content(session, messages, config)
        elif config.format == ExportFormat.JSON:
            return self._generate_json_content(session, messages, config)
        elif config.format == ExportFormat.HTML:
            return self._generate_html_content(session, messages, config)
        elif config.format == ExportFormat.TEXT:
            return self._generate_text_content(session, messages, config)
        elif config.format == ExportFormat.CSV:
            return self._generate_csv_content(session, messages, config)
        else:
            raise ValueError(f"Unsupported export format: {config.format}")

    def _generate_markdown_content(
        self,
        session: Session,
        messages: List[Message],
        config: ExportConfig
    ) -> str:
        """Generate markdown export content."""
        content = []

        # Add header
        if config.include_session_info:
            header = self.templates["markdown_header"].format(
                session_name=session.name,
                project_id=session.project_id,
                export_time=datetime.now().strftime(config.date_format),
                message_count=len(messages)
            )
            content.append(header)

        # Add messages
        for message in messages:
            content.append(self._format_message_markdown(message, config))

        # Add statistics
        if config.include_statistics:
            stats = self._generate_conversation_stats(messages)
            content.append("\n## Conversation Statistics\n")
            for key, value in stats.items():
                content.append(f"- **{key.replace('_', ' ').title()}:** {value}")

        return "\n".join(content)

    def _format_message_markdown(self, message: Message, config: ExportConfig) -> str:
        """Format a single message as markdown."""
        lines = []

        # Message header
        role_emoji = {
            MessageRole.USER: "👤",
            MessageRole.ASSISTANT: "🤖",
            MessageRole.SYSTEM: "⚙️",
            MessageRole.TOOL: "🔧"
        }

        header = f"## {role_emoji.get(message.role, '💬')} {message.role.value.title()}"

        if config.include_timestamps:
            timestamp = message.timestamp.strftime(config.date_format)
            header += f" - {timestamp}"

        lines.append(header)

        # Message content
        content = message.content
        if message.message_type == MessageType.CODE and message.code_language:
            content = f"```{message.code_language}\n{content}\n```"

        lines.append(content)

        # Metadata
        if config.include_metadata:
            metadata_lines = []
            if message.tool_name:
                metadata_lines.append(f"**Tool:** {message.tool_name}")
            if message.file_path:
                metadata_lines.append(f"**File:** {message.file_path}")
            if config.include_token_counts and message.token_count > 0:
                metadata_lines.append(f"**Tokens:** {message.token_count}")

            if metadata_lines:
                lines.append("\n*" + " | ".join(metadata_lines) + "*")

        lines.append("\n---\n")
        return "\n".join(lines)

    def _generate_json_content(
        self,
        session: Session,
        messages: List[Message],
        config: ExportConfig
    ) -> str:
        """Generate JSON export content."""
        export_data = {
            "export_info": {
                "export_time": datetime.now().isoformat(),
                "format": config.format.value,
                "config": config.model_dump()
            }
        }

        if config.include_session_info:
            export_data["session"] = session.model_dump()

        # Convert messages to dict
        messages_data = []
        for message in messages:
            message_dict = message.model_dump()
            if not config.include_metadata:
                # Remove metadata fields
                message_dict.pop("metadata", None)
                message_dict.pop("search_keywords", None)
            if not config.include_token_counts:
                message_dict.pop("token_count", None)

            messages_data.append(message_dict)

        export_data["messages"] = messages_data

        if config.include_statistics:
            export_data["statistics"] = self._generate_conversation_stats(messages)

        if config.pretty_format:
            return json.dumps(export_data, indent=2, default=str)
        else:
            return json.dumps(export_data, default=str)

    def _generate_html_content(
        self,
        session: Session,
        messages: List[Message],
        config: ExportConfig
    ) -> str:
        """Generate HTML export content."""
        content = []

        # Add header
        if config.include_session_info:
            header = self.templates["html_header"].format(
                session_name=session.name,
                project_id=session.project_id,
                export_time=datetime.now().strftime(config.date_format),
                message_count=len(messages)
            )
            content.append(header)

        # Add messages
        for message in messages:
            content.append(self._format_message_html(message, config))

        # Add statistics
        if config.include_statistics:
            stats = self._generate_conversation_stats(messages)
            content.append("<h2>Conversation Statistics</h2>")
            content.append("<ul>")
            for key, value in stats.items():
                content.append(f"<li><strong>{key.replace('_', ' ').title()}:</strong> {value}</li>")
            content.append("</ul>")

        # Add footer
        content.append(self.templates["html_footer"])

        return "\n".join(content)

    def _format_message_html(self, message: Message, config: ExportConfig) -> str:
        """Format a single message as HTML."""
        role_class = message.role.value.lower()

        html = f'<div class="message {role_class}">'

        # Message header
        html += f'<div class="role"><strong>{message.role.value.title()}</strong>'

        if config.include_timestamps:
            timestamp = message.timestamp.strftime(config.date_format)
            html += f' <span class="timestamp">- {timestamp}</span>'

        html += '</div>'

        # Message content
        content = message.content.replace('\n', '<br>')
        if message.message_type == MessageType.CODE:
            content = f'<pre><code>{content}</code></pre>'

        html += f'<div class="content">{content}</div>'

        # Metadata
        if config.include_metadata:
            metadata_parts = []
            if message.tool_name:
                metadata_parts.append(f"Tool: {message.tool_name}")
            if message.file_path:
                metadata_parts.append(f"File: {message.file_path}")
            if config.include_token_counts and message.token_count > 0:
                metadata_parts.append(f"Tokens: {message.token_count}")

            if metadata_parts:
                html += f'<div class="metadata">{" | ".join(metadata_parts)}</div>'

        html += '</div>'
        return html

    def _generate_text_content(
        self,
        session: Session,
        messages: List[Message],
        config: ExportConfig
    ) -> str:
        """Generate plain text export content."""
        content = []

        # Add header
        if config.include_session_info:
            content.append(f"Conversation Export")
            content.append(f"Session: {session.name}")
            content.append(f"Project: {session.project_id}")
            content.append(f"Exported: {datetime.now().strftime(config.date_format)}")
            content.append(f"Messages: {len(messages)}")
            content.append("=" * 50)
            content.append("")

        # Add messages
        for i, message in enumerate(messages, 1):
            content.append(f"[{i}] {message.role.value.upper()}")

            if config.include_timestamps:
                timestamp = message.timestamp.strftime(config.date_format)
                content.append(f"Time: {timestamp}")

            if config.include_metadata and message.tool_name:
                content.append(f"Tool: {message.tool_name}")

            content.append("")
            content.append(message.content)
            content.append("-" * 30)
            content.append("")

        return "\n".join(content)

    def _generate_csv_content(
        self,
        session: Session,  # Used for potential future enhancements
        messages: List[Message],
        config: ExportConfig
    ) -> str:
        """Generate CSV export content."""
        import csv
        from io import StringIO

        output = StringIO()
        writer = csv.writer(output)

        # Write header
        headers = ["timestamp", "role", "message_type", "content"]
        if config.include_metadata:
            headers.extend(["tool_name", "file_path", "token_count"])

        writer.writerow(headers)

        # Write messages
        for message in messages:
            row = [
                message.timestamp.strftime(config.date_format),
                message.role.value,
                message.message_type.value,
                message.content.replace('\n', '\\n')  # Escape newlines
            ]

            if config.include_metadata:
                row.extend([
                    message.tool_name or "",
                    message.file_path or "",
                    message.token_count if config.include_token_counts else ""
                ])

            writer.writerow(row)

        return output.getvalue()

    def _generate_conversation_stats(self, messages: List[Message]) -> Dict[str, Any]:
        """Generate conversation statistics."""
        if not messages:
            return {}

        stats = {
            "total_messages": len(messages),
            "user_messages": sum(1 for m in messages if m.role == MessageRole.USER),
            "assistant_messages": sum(1 for m in messages if m.role == MessageRole.ASSISTANT),
            "system_messages": sum(1 for m in messages if m.role == MessageRole.SYSTEM),
            "tool_messages": sum(1 for m in messages if m.role == MessageRole.TOOL),
            "code_messages": sum(1 for m in messages if m.message_type == MessageType.CODE),
            "total_tokens": sum(m.token_count for m in messages),
            "conversation_start": messages[0].timestamp.strftime("%Y-%m-%d %H:%M:%S"),
            "conversation_end": messages[-1].timestamp.strftime("%Y-%m-%d %H:%M:%S"),
            "duration_hours": (messages[-1].timestamp - messages[0].timestamp).total_seconds() / 3600
        }

        return stats

    async def _write_export_file(
        self,
        content: str,
        output_path: Path,
        config: ExportConfig
    ) -> None:
        """Write export content to file."""
        output_path.parent.mkdir(parents=True, exist_ok=True)

        if config.compress and config.format != ExportFormat.ZIP:
            # Write compressed file
            import gzip
            with gzip.open(f"{output_path}.gz", 'wt', encoding='utf-8') as f:
                f.write(content)
        else:
            # Write regular file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(content)

    async def _export_sessions_single_file(
        self,
        session_ids: List[str],
        config: ExportConfig,
        output_path: Optional[str]
    ) -> ExportResult:
        """Export multiple sessions to a single file."""
        export_result = ExportResult(
            export_id=f"export_multi_{int(datetime.now().timestamp())}",
            session_ids=session_ids,
            format=config.format
        )

        try:
            all_content = []
            total_messages = 0

            for session_id in session_ids:
                # Get session data
                session = await self.session_manager.get_session(session_id)
                if not session:
                    export_result.warnings.append(f"Session {session_id} not found")
                    continue

                # Get messages
                messages = await self.conversation_history.get_session_messages(session_id, limit=10000)
                filtered_messages = self._filter_messages(messages, config)

                # Generate content for this session
                session_content = await self._generate_export_content(session, filtered_messages, config)
                all_content.append(f"\n\n{'='*60}\n")
                all_content.append(session_content)

                total_messages += len(filtered_messages)

            # Combine all content
            combined_content = "\n".join(all_content)

            # Determine output path
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"multi_session_export_{timestamp}.{config.format.value}"
                output_path = self.export_dir / filename
            else:
                output_path = Path(output_path)

            # Write combined file
            await self._write_export_file(combined_content, output_path, config)

            # Update export result
            export_result.file_path = str(output_path)
            export_result.file_size = output_path.stat().st_size if output_path.exists() else 0
            export_result.messages_exported = total_messages
            export_result.sessions_exported = len([sid for sid in session_ids if sid not in [w.split()[1] for w in export_result.warnings if "not found" in w]])
            export_result.complete_export(True)

            return export_result

        except Exception as e:
            logger.error(f"Single file export failed: {e}")
            export_result.errors.append(f"Single file export failed: {str(e)}")
            export_result.complete_export(False)
            return export_result

    async def _export_sessions_separate_files(
        self,
        session_ids: List[str],
        config: ExportConfig,
        output_path: Optional[str]
    ) -> ExportResult:
        """Export multiple sessions to separate files."""
        export_result = ExportResult(
            export_id=f"export_separate_{int(datetime.now().timestamp())}",
            session_ids=session_ids,
            format=config.format
        )

        try:
            total_messages = 0
            successful_exports = 0

            # Create output directory
            if output_path:
                output_dir = Path(output_path)
            else:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_dir = self.export_dir / f"separate_exports_{timestamp}"

            output_dir.mkdir(parents=True, exist_ok=True)

            for session_id in session_ids:
                try:
                    # Export individual session
                    session_config = config.model_copy()  # Copy config for individual session
                    session_output_path = output_dir / f"session_{session_id}.{config.format.value}"

                    individual_result = await self.export_session(
                        session_id, session_config, str(session_output_path)
                    )

                    if individual_result.success:
                        total_messages += individual_result.messages_exported
                        successful_exports += 1
                    else:
                        export_result.warnings.extend(individual_result.errors)

                except Exception as e:
                    export_result.warnings.append(f"Failed to export session {session_id}: {str(e)}")

            # Update export result
            export_result.file_path = str(output_dir)
            export_result.file_size = sum(f.stat().st_size for f in output_dir.iterdir() if f.is_file())
            export_result.messages_exported = total_messages
            export_result.sessions_exported = successful_exports
            export_result.complete_export(successful_exports > 0)

            return export_result

        except Exception as e:
            logger.error(f"Separate files export failed: {e}")
            export_result.errors.append(f"Separate files export failed: {str(e)}")
            export_result.complete_export(False)
            return export_result


# Global export manager instance
_export_manager: Optional[ExportManager] = None


def get_export_manager() -> ExportManager:
    """Get the global export manager instance."""
    global _export_manager
    if _export_manager is None:
        from .session_manager import get_session_manager
        from .conversation_history import ConversationHistory

        session_manager = get_session_manager()
        conversation_history = ConversationHistory()
        _export_manager = ExportManager(session_manager, conversation_history)

    return _export_manager
