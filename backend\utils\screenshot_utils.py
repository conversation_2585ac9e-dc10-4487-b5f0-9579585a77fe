#!/usr/bin/env python3
"""
Screenshot Utilities for AI Coder Agent - Phase 6
Provides secure screenshot capture using Puppeteer with proper cleanup.

Features:
- Puppeteer integration with headless Chrome
- Dynamic port detection for local servers
- Timeout handling for browser operations
- Proper resource cleanup and error handling
- Screenshot optimization and compression
- Multiple format support (PNG, JPEG, WebP)
"""

import asyncio
import json
import logging
import os
import tempfile
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import subprocess
from concurrent.futures import ThreadPoolExecutor
import base64
from PIL import Image
import io

logger = logging.getLogger(__name__)

class ScreenshotUtils:
    """Utilities for capturing screenshots of web applications."""
    
    def __init__(self, workspace_root: Union[str, Path] = "/app/workspace"):
        self.workspace_root = Path(workspace_root)
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.browser_processes = {}  # Track browser processes for cleanup
        
        # Default screenshot settings
        self.default_settings = {
            'width': 1920,
            'height': 1080,
            'format': 'png',
            'quality': 90,
            'full_page': True,
            'timeout': 30000,  # 30 seconds
            'wait_for_selector': None,
            'wait_for_network_idle': True
        }
    
    def _validate_path(self, path: Union[str, Path]) -> Path:
        """Validate and resolve path within workspace."""
        path = Path(path).resolve()
        
        # Ensure path is within workspace
        try:
            path.relative_to(self.workspace_root.resolve())
        except ValueError:
            raise ValueError(f"Path {path} is outside workspace {self.workspace_root}")
        
        return path
    
    async def detect_local_server_port(self, project_path: Union[str, Path],
                                     common_ports: List[int] = None) -> Optional[int]:
        """
        Detect if a local development server is running.
        
        Args:
            project_path: Path to project directory
            common_ports: List of ports to check
            
        Returns:
            Port number if server is detected, None otherwise
        """
        try:
            if common_ports is None:
                common_ports = [3000, 3001, 8000, 8080, 5000, 5173, 4200, 9000]
            
            import httpx
            
            for port in common_ports:
                try:
                    async with httpx.AsyncClient(timeout=2.0) as client:
                        response = await client.get(f"http://localhost:{port}")
                        if response.status_code == 200:
                            logger.info(f"Detected local server on port {port}")
                            return port
                except:
                    continue
            
            logger.info("No local development server detected")
            return None
            
        except Exception as e:
            logger.error(f"Failed to detect local server: {e}")
            return None
    
    async def capture_screenshot(self, url: str, 
                               output_path: Union[str, Path],
                               settings: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Capture screenshot of a web page using Puppeteer.
        
        Args:
            url: URL to capture
            output_path: Path to save screenshot
            settings: Screenshot settings override
            
        Returns:
            Dict with capture results
        """
        try:
            output_path = self._validate_path(output_path)
            
            # Merge settings with defaults
            capture_settings = {**self.default_settings}
            if settings:
                capture_settings.update(settings)
            
            # Create output directory if needed
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Create Puppeteer script
            script_content = self._generate_puppeteer_script(
                url, str(output_path), capture_settings
            )
            
            # Write script to temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False) as f:
                f.write(script_content)
                script_path = f.name
            
            try:
                # Execute Puppeteer script
                result = await self._execute_puppeteer_script(script_path, capture_settings)
                
                # Verify screenshot was created
                if output_path.exists():
                    file_size = output_path.stat().st_size
                    
                    # Optimize screenshot if needed
                    if capture_settings.get('optimize', True):
                        await self._optimize_screenshot(output_path, capture_settings)
                    
                    return {
                        'status': 'success',
                        'url': url,
                        'output_path': str(output_path),
                        'file_size': file_size,
                        'format': capture_settings['format'],
                        'dimensions': f"{capture_settings['width']}x{capture_settings['height']}",
                        'execution_time': result.get('execution_time', 0)
                    }
                else:
                    return {
                        'status': 'error',
                        'error': 'Screenshot file was not created',
                        'url': url,
                        'output_path': str(output_path)
                    }
                    
            finally:
                # Clean up temporary script
                try:
                    os.unlink(script_path)
                except:
                    pass
            
        except Exception as e:
            logger.error(f"Failed to capture screenshot of {url}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__,
                'url': url
            }
    
    def _generate_puppeteer_script(self, url: str, output_path: str, 
                                 settings: Dict[str, Any]) -> str:
        """Generate Puppeteer JavaScript script for screenshot capture."""
        
        wait_conditions = []
        
        if settings.get('wait_for_selector'):
            wait_conditions.append(f"await page.waitForSelector('{settings['wait_for_selector']}', {{ timeout: {settings['timeout']} }});")
        
        if settings.get('wait_for_network_idle', True):
            wait_conditions.append("await page.waitForLoadState('networkidle');")
        
        wait_code = '\n    '.join(wait_conditions)
        
        script = f"""
const {{ chromium }} = require('playwright');

(async () => {{
    let browser;
    try {{
        // Launch browser
        browser = await chromium.launch({{
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-default-apps'
            ]
        }});
        
        const page = await browser.newPage();
        
        // Set viewport
        await page.setViewportSize({{
            width: {settings['width']},
            height: {settings['height']}
        }});
        
        // Navigate to URL
        await page.goto('{url}', {{
            waitUntil: 'domcontentloaded',
            timeout: {settings['timeout']}
        }});
        
        // Wait for conditions
        {wait_code}
        
        // Take screenshot
        await page.screenshot({{
            path: '{output_path}',
            type: '{settings['format']}',
            quality: {settings.get('quality', 90) if settings['format'] in ['jpeg', 'webp'] else 'undefined'},
            fullPage: {str(settings.get('full_page', True)).lower()}
        }});
        
        console.log('Screenshot captured successfully');
        
    }} catch (error) {{
        console.error('Screenshot failed:', error.message);
        process.exit(1);
    }} finally {{
        if (browser) {{
            await browser.close();
        }}
    }}
}})();
"""
        return script
    
    async def _execute_puppeteer_script(self, script_path: str, 
                                      settings: Dict[str, Any]) -> Dict[str, Any]:
        """Execute Puppeteer script with timeout handling."""
        
        def _run_script():
            start_time = time.time()
            try:
                # Use npx to run the script with playwright
                result = subprocess.run(
                    ['npx', 'playwright', 'install', 'chromium'],
                    capture_output=True,
                    text=True,
                    timeout=60,  # 1 minute for installation
                    shell=False
                )
                
                if result.returncode != 0:
                    logger.warning(f"Playwright install warning: {result.stderr}")
                
                # Run the actual script
                result = subprocess.run(
                    ['node', script_path],
                    capture_output=True,
                    text=True,
                    timeout=settings.get('timeout', 30000) / 1000 + 10,  # Add 10s buffer
                    shell=False
                )
                
                execution_time = time.time() - start_time
                
                return {
                    'returncode': result.returncode,
                    'stdout': result.stdout,
                    'stderr': result.stderr,
                    'execution_time': execution_time
                }
                
            except subprocess.TimeoutExpired:
                return {
                    'returncode': -1,
                    'stdout': '',
                    'stderr': 'Screenshot capture timed out',
                    'execution_time': settings.get('timeout', 30000) / 1000,
                    'timeout': True
                }
            except FileNotFoundError:
                return {
                    'returncode': -1,
                    'stdout': '',
                    'stderr': 'Node.js or Playwright not found. Please install Node.js and Playwright.',
                    'execution_time': 0,
                    'command_not_found': True
                }
        
        result = await asyncio.get_event_loop().run_in_executor(self.executor, _run_script)
        
        if result['returncode'] != 0:
            error_msg = result['stderr'] or result['stdout'] or 'Unknown error'
            raise Exception(f"Puppeteer script failed: {error_msg}")
        
        return result
    
    async def _optimize_screenshot(self, image_path: Path, 
                                 settings: Dict[str, Any]) -> None:
        """Optimize screenshot file size while maintaining quality."""
        try:
            def _optimize():
                with Image.open(image_path) as img:
                    # Convert to RGB if necessary
                    if img.mode in ('RGBA', 'LA', 'P'):
                        if settings['format'].lower() == 'jpeg':
                            # Create white background for JPEG
                            background = Image.new('RGB', img.size, (255, 255, 255))
                            if img.mode == 'P':
                                img = img.convert('RGBA')
                            background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                            img = background
                        elif settings['format'].lower() == 'png':
                            img = img.convert('RGBA')
                    
                    # Save optimized image
                    save_kwargs = {
                        'format': settings['format'].upper(),
                        'optimize': True
                    }
                    
                    if settings['format'].lower() in ['jpeg', 'webp']:
                        save_kwargs['quality'] = settings.get('quality', 90)
                    
                    if settings['format'].lower() == 'png':
                        save_kwargs['compress_level'] = 6
                    
                    img.save(image_path, **save_kwargs)
            
            await asyncio.get_event_loop().run_in_executor(self.executor, _optimize)
            
        except Exception as e:
            logger.warning(f"Failed to optimize screenshot: {e}")
    
    async def capture_application_screenshot(self, project_path: Union[str, Path],
                                           output_filename: str = "app_screenshot.png",
                                           settings: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Capture screenshot of a local web application.
        
        Args:
            project_path: Path to project directory
            output_filename: Name of output screenshot file
            settings: Screenshot settings override
            
        Returns:
            Dict with capture results
        """
        try:
            project_path = self._validate_path(project_path)
            
            # Detect local server
            port = await self.detect_local_server_port(project_path)
            if not port:
                return {
                    'status': 'error',
                    'error': 'No local development server detected',
                    'project_path': str(project_path),
                    'suggestion': 'Start your development server first'
                }
            
            # Capture screenshot
            url = f"http://localhost:{port}"
            output_path = project_path / "screenshots" / output_filename
            
            result = await self.capture_screenshot(url, output_path, settings)
            result['port'] = port
            result['project_path'] = str(project_path)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to capture application screenshot: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__,
                'project_path': str(project_path)
            }
    
    async def capture_multiple_screenshots(self, urls: List[str],
                                         output_dir: Union[str, Path],
                                         settings: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Capture multiple screenshots concurrently.
        
        Args:
            urls: List of URLs to capture
            output_dir: Directory to save screenshots
            settings: Screenshot settings override
            
        Returns:
            Dict with capture results for all URLs
        """
        try:
            output_dir = self._validate_path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Create tasks for concurrent execution
            tasks = []
            for i, url in enumerate(urls):
                filename = f"screenshot_{i+1:03d}.png"
                output_path = output_dir / filename
                task = self.capture_screenshot(url, output_path, settings)
                tasks.append(task)
            
            # Execute all screenshots concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            successful = 0
            failed = 0
            capture_results = []
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    capture_results.append({
                        'url': urls[i],
                        'status': 'error',
                        'error': str(result)
                    })
                    failed += 1
                else:
                    capture_results.append(result)
                    if result.get('status') == 'success':
                        successful += 1
                    else:
                        failed += 1
            
            return {
                'status': 'success',
                'total_urls': len(urls),
                'successful': successful,
                'failed': failed,
                'results': capture_results,
                'output_directory': str(output_dir)
            }
            
        except Exception as e:
            logger.error(f"Failed to capture multiple screenshots: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }
    
    def cleanup(self):
        """Clean up resources and browser processes."""
        try:
            # Clean up any remaining browser processes
            for process_id, process in self.browser_processes.items():
                try:
                    process.terminate()
                except:
                    pass
            
            self.browser_processes.clear()
            
            # Shutdown executor
            self.executor.shutdown(wait=False)
            
        except Exception as e:
            logger.error(f"Cleanup failed: {e}")


# Global instance
screenshot_utils = ScreenshotUtils()
