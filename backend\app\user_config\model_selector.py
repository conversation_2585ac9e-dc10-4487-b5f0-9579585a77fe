"""
🤖 Model Selector - Runtime model selection interface

This module provides comprehensive AI model selection with:
- Dynamic model configuration
- Provider-specific settings
- Performance optimization
- Fallback model support
- Cost and usage tracking
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple
from pydantic import BaseModel, Field, validator
import logging

logger = logging.getLogger(__name__)


class ModelProvider(str, Enum):
    """AI model providers."""
    OPENROUTER = "openrouter"
    OLLAMA = "ollama"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    HUGGINGFACE = "huggingface"


class ModelCapability(str, Enum):
    """Model capabilities."""
    TEXT_GENERATION = "text_generation"
    CODE_GENERATION = "code_generation"
    VISION = "vision"
    EMBEDDING = "embedding"
    FUNCTION_CALLING = "function_calling"
    REASONING = "reasoning"
    MULTIMODAL = "multimodal"


class ModelConfig(BaseModel):
    """AI model configuration."""
    
    model_id: str = Field(..., description="Unique model identifier")
    model_name: str = Field(..., description="Human-readable model name")
    provider: ModelProvider = Field(..., description="Model provider")
    
    # Model capabilities
    capabilities: List[ModelCapability] = Field(default_factory=list, description="Model capabilities")
    
    # Configuration parameters
    max_tokens: int = Field(default=8000, description="Maximum tokens per request")
    temperature: float = Field(default=0.7, description="Sampling temperature")
    top_p: float = Field(default=1.0, description="Top-p sampling parameter")
    frequency_penalty: float = Field(default=0.0, description="Frequency penalty")
    presence_penalty: float = Field(default=0.0, description="Presence penalty")
    
    # Performance settings
    timeout_seconds: int = Field(default=60, description="Request timeout in seconds")
    retry_attempts: int = Field(default=3, description="Number of retry attempts")
    enable_streaming: bool = Field(default=True, description="Enable streaming responses")
    
    # Cost and usage
    cost_per_1k_tokens: float = Field(default=0.0, description="Cost per 1000 tokens")
    rate_limit_rpm: int = Field(default=60, description="Rate limit requests per minute")
    rate_limit_tpm: int = Field(default=100000, description="Rate limit tokens per minute")
    
    # Model metadata
    context_window: int = Field(default=8000, description="Model context window size")
    supports_system_messages: bool = Field(default=True, description="Supports system messages")
    supports_function_calling: bool = Field(default=False, description="Supports function calling")
    supports_vision: bool = Field(default=False, description="Supports vision/image input")
    
    # Availability
    is_available: bool = Field(default=True, description="Model is currently available")
    is_deprecated: bool = Field(default=False, description="Model is deprecated")
    
    @validator('temperature')
    def validate_temperature(cls, v):
        if not 0.0 <= v <= 2.0:
            raise ValueError("Temperature must be between 0.0 and 2.0")
        return v
    
    @validator('top_p')
    def validate_top_p(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError("Top-p must be between 0.0 and 1.0")
        return v


class ModelSelector:
    """
    🤖 Advanced Model Selector
    
    Provides intelligent model selection based on task requirements,
    user preferences, and performance characteristics.
    """
    
    def __init__(self):
        # Available models registry
        self.available_models: Dict[str, ModelConfig] = {}
        
        # Model usage statistics
        self.usage_stats: Dict[str, Dict[str, Any]] = {}
        
        # Initialize default models
        self._initialize_default_models()
    
    def _initialize_default_models(self) -> None:
        """Initialize default model configurations."""
        default_models = [
            # DeepSeek models for coding
            ModelConfig(
                model_id="deepseek/deepseek-r1-0528:free",
                model_name="DeepSeek R1 (Free)",
                provider=ModelProvider.OPENROUTER,
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.CODE_GENERATION,
                    ModelCapability.REASONING
                ],
                max_tokens=8000,
                temperature=0.7,
                context_window=32000,
                supports_function_calling=False,
                cost_per_1k_tokens=0.0,
                rate_limit_rpm=20
            ),
            
            # Google Gemini for vision
            ModelConfig(
                model_id="google/gemini-2.0-flash-exp:free",
                model_name="Google Gemini 2.0 Flash (Free)",
                provider=ModelProvider.OPENROUTER,
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.VISION,
                    ModelCapability.MULTIMODAL
                ],
                max_tokens=8000,
                temperature=0.7,
                context_window=32000,
                supports_vision=True,
                cost_per_1k_tokens=0.0,
                rate_limit_rpm=15
            ),
            
            # BGE-M3 for embeddings
            ModelConfig(
                model_id="bge-m3",
                model_name="BGE-M3 Embeddings",
                provider=ModelProvider.OLLAMA,
                capabilities=[ModelCapability.EMBEDDING],
                max_tokens=512,
                context_window=8192,
                supports_system_messages=False,
                cost_per_1k_tokens=0.0,
                rate_limit_rpm=100
            ),
            
            # Mistral for tool calling
            ModelConfig(
                model_id="mistralai/mistral-7b-instruct:free",
                model_name="Mistral 7B Instruct (Free)",
                provider=ModelProvider.OPENROUTER,
                capabilities=[
                    ModelCapability.TEXT_GENERATION,
                    ModelCapability.FUNCTION_CALLING
                ],
                max_tokens=8000,
                temperature=0.7,
                context_window=32000,
                supports_function_calling=True,
                cost_per_1k_tokens=0.0,
                rate_limit_rpm=20
            )
        ]
        
        for model in default_models:
            self.register_model(model)
    
    def register_model(self, model_config: ModelConfig) -> None:
        """Register a new model configuration."""
        self.available_models[model_config.model_id] = model_config
        
        # Initialize usage stats
        if model_config.model_id not in self.usage_stats:
            self.usage_stats[model_config.model_id] = {
                "total_requests": 0,
                "total_tokens": 0,
                "total_cost": 0.0,
                "success_rate": 100.0,
                "avg_response_time": 0.0,
                "last_used": None
            }
        
        logger.info(f"Registered model: {model_config.model_name} ({model_config.model_id})")
    
    def get_model_config(self, model_id: str) -> Optional[ModelConfig]:
        """Get model configuration by ID."""
        return self.available_models.get(model_id)
    
    def get_available_models(
        self,
        capability: Optional[ModelCapability] = None,
        provider: Optional[ModelProvider] = None,
        available_only: bool = True
    ) -> List[ModelConfig]:
        """Get list of available models with optional filtering."""
        models = list(self.available_models.values())
        
        # Filter by availability
        if available_only:
            models = [m for m in models if m.is_available and not m.is_deprecated]
        
        # Filter by capability
        if capability:
            models = [m for m in models if capability in m.capabilities]
        
        # Filter by provider
        if provider:
            models = [m for m in models if m.provider == provider]
        
        return models
    
    def select_best_model(
        self,
        capability: ModelCapability,
        max_cost: Optional[float] = None,
        min_context_window: Optional[int] = None,
        prefer_free: bool = True
    ) -> Optional[ModelConfig]:
        """Select the best model for a specific capability."""
        candidates = self.get_available_models(capability=capability)
        
        if not candidates:
            return None
        
        # Filter by cost
        if max_cost is not None:
            candidates = [m for m in candidates if m.cost_per_1k_tokens <= max_cost]
        
        # Filter by context window
        if min_context_window is not None:
            candidates = [m for m in candidates if m.context_window >= min_context_window]
        
        if not candidates:
            return None
        
        # Scoring function
        def score_model(model: ModelConfig) -> float:
            score = 0.0
            
            # Prefer free models
            if prefer_free and model.cost_per_1k_tokens == 0.0:
                score += 100
            
            # Prefer models with higher context windows
            score += model.context_window / 1000
            
            # Prefer models with better success rates
            stats = self.usage_stats.get(model.model_id, {})
            success_rate = stats.get("success_rate", 100.0)
            score += success_rate
            
            # Prefer models with faster response times
            avg_response_time = stats.get("avg_response_time", 1.0)
            score += max(0, 10 - avg_response_time)  # Prefer sub-10 second responses
            
            # Penalty for deprecated models
            if model.is_deprecated:
                score -= 50
            
            return score
        
        # Select model with highest score
        best_model = max(candidates, key=score_model)
        return best_model
    
    def get_model_for_task(self, task_type: str, **kwargs) -> Optional[ModelConfig]:
        """Get the best model for a specific task type."""
        task_capability_map = {
            "coding": ModelCapability.CODE_GENERATION,
            "text": ModelCapability.TEXT_GENERATION,
            "vision": ModelCapability.VISION,
            "embedding": ModelCapability.EMBEDDING,
            "function_calling": ModelCapability.FUNCTION_CALLING,
            "reasoning": ModelCapability.REASONING,
            "multimodal": ModelCapability.MULTIMODAL
        }
        
        capability = task_capability_map.get(task_type.lower())
        if not capability:
            logger.warning(f"Unknown task type: {task_type}")
            return None
        
        return self.select_best_model(capability, **kwargs)
    
    def record_model_usage(
        self,
        model_id: str,
        tokens_used: int,
        response_time: float,
        success: bool
    ) -> None:
        """Record model usage statistics."""
        if model_id not in self.usage_stats:
            self.usage_stats[model_id] = {
                "total_requests": 0,
                "total_tokens": 0,
                "total_cost": 0.0,
                "success_rate": 100.0,
                "avg_response_time": 0.0,
                "last_used": None
            }
        
        stats = self.usage_stats[model_id]
        model_config = self.available_models.get(model_id)
        
        # Update statistics
        stats["total_requests"] += 1
        stats["total_tokens"] += tokens_used
        stats["last_used"] = datetime.now().isoformat()
        
        # Update cost
        if model_config:
            cost = (tokens_used / 1000) * model_config.cost_per_1k_tokens
            stats["total_cost"] += cost
        
        # Update success rate
        if success:
            successful_requests = stats["total_requests"] * (stats["success_rate"] / 100)
            stats["success_rate"] = ((successful_requests + 1) / stats["total_requests"]) * 100
        else:
            successful_requests = stats["total_requests"] * (stats["success_rate"] / 100)
            stats["success_rate"] = (successful_requests / stats["total_requests"]) * 100
        
        # Update average response time
        current_avg = stats["avg_response_time"]
        total_requests = stats["total_requests"]
        stats["avg_response_time"] = ((current_avg * (total_requests - 1)) + response_time) / total_requests
    
    def get_model_recommendations(self, user_preferences: Dict[str, Any]) -> List[Tuple[str, ModelConfig]]:
        """Get model recommendations based on user preferences."""
        recommendations = []
        
        # Get preferred models from user preferences
        preferred_coding = user_preferences.get("ai_models", {}).get("default_coding_model")
        preferred_vision = user_preferences.get("ai_models", {}).get("default_vision_model")
        preferred_embedding = user_preferences.get("ai_models", {}).get("default_embedding_model")
        
        # Recommend coding model
        if preferred_coding and preferred_coding in self.available_models:
            recommendations.append(("coding", self.available_models[preferred_coding]))
        else:
            best_coding = self.select_best_model(ModelCapability.CODE_GENERATION)
            if best_coding:
                recommendations.append(("coding", best_coding))
        
        # Recommend vision model
        if preferred_vision and preferred_vision in self.available_models:
            recommendations.append(("vision", self.available_models[preferred_vision]))
        else:
            best_vision = self.select_best_model(ModelCapability.VISION)
            if best_vision:
                recommendations.append(("vision", best_vision))
        
        # Recommend embedding model
        if preferred_embedding and preferred_embedding in self.available_models:
            recommendations.append(("embedding", self.available_models[preferred_embedding]))
        else:
            best_embedding = self.select_best_model(ModelCapability.EMBEDDING)
            if best_embedding:
                recommendations.append(("embedding", best_embedding))
        
        return recommendations
    
    def get_usage_statistics(self) -> Dict[str, Any]:
        """Get model usage statistics."""
        total_requests = sum(stats["total_requests"] for stats in self.usage_stats.values())
        total_tokens = sum(stats["total_tokens"] for stats in self.usage_stats.values())
        total_cost = sum(stats["total_cost"] for stats in self.usage_stats.values())
        
        # Most used models
        most_used = sorted(
            self.usage_stats.items(),
            key=lambda x: x[1]["total_requests"],
            reverse=True
        )[:5]
        
        return {
            "total_models": len(self.available_models),
            "total_requests": total_requests,
            "total_tokens": total_tokens,
            "total_cost": total_cost,
            "most_used_models": [
                {
                    "model_id": model_id,
                    "model_name": self.available_models.get(model_id, {}).model_name,
                    "requests": stats["total_requests"],
                    "tokens": stats["total_tokens"]
                }
                for model_id, stats in most_used
            ]
        }


# Global model selector instance
_model_selector: Optional[ModelSelector] = None


def get_model_selector() -> ModelSelector:
    """Get the global model selector instance."""
    global _model_selector
    if _model_selector is None:
        _model_selector = ModelSelector()
    return _model_selector
