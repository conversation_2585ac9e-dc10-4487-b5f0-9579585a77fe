"""
💬 Conversation History - Advanced chat history with search and filtering

This module provides comprehensive conversation history management with:
- Persistent message storage
- Advanced search and filtering capabilities
- Message threading and context preservation
- Performance optimization for large conversations
- Full integration with session management
"""

import json
import re
import sqlite3
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)


class MessageRole(str, Enum):
    """Message role enumeration."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    TOOL = "tool"


class MessageType(str, Enum):
    """Message type enumeration."""
    TEXT = "text"
    CODE = "code"
    ERROR = "error"
    TOOL_CALL = "tool_call"
    TOOL_RESULT = "tool_result"
    IMAGE = "image"
    FILE = "file"


class Message(BaseModel):
    """Individual message in conversation history."""
    
    # Core identifiers
    message_id: str = Field(..., description="Unique message identifier")
    session_id: str = Field(..., description="Associated session ID")
    
    # Message content
    role: MessageRole = Field(..., description="Message role")
    content: str = Field(..., description="Message content")
    message_type: MessageType = Field(default=MessageType.TEXT, description="Message type")
    
    # Metadata
    timestamp: datetime = Field(default_factory=datetime.now, description="Message timestamp")
    token_count: int = Field(default=0, description="Token count for this message")
    
    # Context and threading
    parent_message_id: Optional[str] = Field(None, description="Parent message ID for threading")
    thread_id: Optional[str] = Field(None, description="Thread identifier")
    
    # Tool and code information
    tool_name: Optional[str] = Field(None, description="Tool name if tool message")
    file_path: Optional[str] = Field(None, description="File path if file-related")
    code_language: Optional[str] = Field(None, description="Programming language if code")
    
    # Additional metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional message metadata")
    
    # Search and indexing
    search_keywords: List[str] = Field(default_factory=list, description="Extracted keywords for search")
    
    def extract_keywords(self) -> List[str]:
        """Extract keywords from message content for search indexing."""
        # Extract code blocks
        code_blocks = re.findall(r'```[\w]*\n(.*?)\n```', self.content, re.DOTALL)
        
        # Extract file paths
        file_paths = re.findall(r'[a-zA-Z0-9_/\\.-]+\.[a-zA-Z]{2,4}', self.content)
        
        # Extract function/class names
        identifiers = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', self.content)
        
        # Combine and filter keywords
        keywords = []
        keywords.extend([block[:50] for block in code_blocks])  # First 50 chars of code
        keywords.extend(file_paths)
        keywords.extend([id for id in identifiers if len(id) > 2])  # Filter short identifiers
        
        # Add tool and file info
        if self.tool_name:
            keywords.append(self.tool_name)
        if self.file_path:
            keywords.append(self.file_path)
        
        return list(set(keywords))  # Remove duplicates


class ConversationFilter(BaseModel):
    """Filter criteria for conversation search."""
    
    # Time-based filters
    start_date: Optional[datetime] = Field(None, description="Start date filter")
    end_date: Optional[datetime] = Field(None, description="End date filter")
    
    # Content filters
    roles: Optional[List[MessageRole]] = Field(None, description="Filter by message roles")
    message_types: Optional[List[MessageType]] = Field(None, description="Filter by message types")
    
    # Search filters
    search_query: Optional[str] = Field(None, description="Text search query")
    keywords: Optional[List[str]] = Field(None, description="Keyword filters")
    
    # Tool and file filters
    tool_names: Optional[List[str]] = Field(None, description="Filter by tool names")
    file_paths: Optional[List[str]] = Field(None, description="Filter by file paths")
    
    # Pagination
    limit: int = Field(default=100, description="Maximum number of results")
    offset: int = Field(default=0, description="Result offset for pagination")


class SearchResult(BaseModel):
    """Search result with relevance scoring."""
    
    message: Message = Field(..., description="Found message")
    relevance_score: float = Field(..., description="Relevance score (0.0-1.0)")
    match_highlights: List[str] = Field(default_factory=list, description="Highlighted matching text")
    context_messages: List[Message] = Field(default_factory=list, description="Surrounding context messages")


class ConversationHistory:
    """
    💬 Advanced Conversation History Manager
    
    Provides comprehensive conversation history management with search,
    filtering, and performance optimization capabilities.
    """
    
    def __init__(self, db_path: str = "data/conversation_history.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize database
        self._init_database()
        
        # In-memory cache for recent messages
        self.message_cache: Dict[str, List[Message]] = {}  # session_id -> messages
        self.cache_size = 100  # Messages per session to cache
    
    def _init_database(self) -> None:
        """Initialize conversation history database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS messages (
                    message_id TEXT PRIMARY KEY,
                    session_id TEXT NOT NULL,
                    role TEXT NOT NULL,
                    content TEXT NOT NULL,
                    message_type TEXT NOT NULL,
                    timestamp TIMESTAMP NOT NULL,
                    token_count INTEGER DEFAULT 0,
                    parent_message_id TEXT,
                    thread_id TEXT,
                    tool_name TEXT,
                    file_path TEXT,
                    code_language TEXT,
                    metadata_json TEXT DEFAULT '{}',
                    search_keywords_json TEXT DEFAULT '[]'
                )
            """)
            
            # Create indexes for performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_messages_session_id ON messages(session_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages(timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_messages_role ON messages(role)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_messages_type ON messages(message_type)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_messages_tool ON messages(tool_name)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_messages_file ON messages(file_path)")
            
            # Full-text search index
            conn.execute("""
                CREATE VIRTUAL TABLE IF NOT EXISTS messages_fts USING fts5(
                    message_id UNINDEXED,
                    content,
                    search_keywords,
                    content='messages',
                    content_rowid='rowid'
                )
            """)
    
    async def add_message(self, message: Message) -> bool:
        """Add a message to conversation history."""
        try:
            # Extract keywords for search
            message.search_keywords = message.extract_keywords()
            
            # Save to database
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO messages (
                        message_id, session_id, role, content, message_type,
                        timestamp, token_count, parent_message_id, thread_id,
                        tool_name, file_path, code_language, metadata_json,
                        search_keywords_json
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    message.message_id,
                    message.session_id,
                    message.role.value,
                    message.content,
                    message.message_type.value,
                    message.timestamp.isoformat(),
                    message.token_count,
                    message.parent_message_id,
                    message.thread_id,
                    message.tool_name,
                    message.file_path,
                    message.code_language,
                    json.dumps(message.metadata),
                    json.dumps(message.search_keywords)
                ))
                
                # Update FTS index
                conn.execute("""
                    INSERT INTO messages_fts (message_id, content, search_keywords)
                    VALUES (?, ?, ?)
                """, (
                    message.message_id,
                    message.content,
                    ' '.join(message.search_keywords)
                ))
            
            # Update cache
            if message.session_id not in self.message_cache:
                self.message_cache[message.session_id] = []
            
            self.message_cache[message.session_id].append(message)
            
            # Limit cache size
            if len(self.message_cache[message.session_id]) > self.cache_size:
                self.message_cache[message.session_id] = self.message_cache[message.session_id][-self.cache_size:]
            
            logger.debug(f"Added message {message.message_id} to session {message.session_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to add message: {e}")
            return False

    async def get_session_messages(
        self,
        session_id: str,
        limit: int = 100,
        offset: int = 0
    ) -> List[Message]:
        """Get messages for a session."""
        try:
            # Check cache first for recent messages
            if session_id in self.message_cache and offset == 0 and limit <= len(self.message_cache[session_id]):
                return self.message_cache[session_id][-limit:]

            # Load from database
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM messages
                    WHERE session_id = ?
                    ORDER BY timestamp DESC
                    LIMIT ? OFFSET ?
                """, (session_id, limit, offset))

                messages = []
                for row in cursor.fetchall():
                    message = self._row_to_message(row)
                    messages.append(message)

                return list(reversed(messages))  # Return in chronological order

        except Exception as e:
            logger.error(f"Failed to get session messages: {e}")
            return []

    async def search_messages(
        self,
        session_id: str,
        filter_criteria: ConversationFilter
    ) -> List[SearchResult]:
        """Search messages with advanced filtering and relevance scoring."""
        try:
            # Build SQL query based on filter criteria
            where_conditions = ["session_id = ?"]
            params = [session_id]

            # Time-based filters
            if filter_criteria.start_date:
                where_conditions.append("timestamp >= ?")
                params.append(filter_criteria.start_date.isoformat())

            if filter_criteria.end_date:
                where_conditions.append("timestamp <= ?")
                params.append(filter_criteria.end_date.isoformat())

            # Role filters
            if filter_criteria.roles:
                role_placeholders = ",".join("?" * len(filter_criteria.roles))
                where_conditions.append(f"role IN ({role_placeholders})")
                params.extend([role.value for role in filter_criteria.roles])

            # Message type filters
            if filter_criteria.message_types:
                type_placeholders = ",".join("?" * len(filter_criteria.message_types))
                where_conditions.append(f"message_type IN ({type_placeholders})")
                params.extend([msg_type.value for msg_type in filter_criteria.message_types])

            # Tool filters
            if filter_criteria.tool_names:
                tool_placeholders = ",".join("?" * len(filter_criteria.tool_names))
                where_conditions.append(f"tool_name IN ({tool_placeholders})")
                params.extend(filter_criteria.tool_names)

            # File path filters
            if filter_criteria.file_paths:
                file_placeholders = ",".join("?" * len(filter_criteria.file_paths))
                where_conditions.append(f"file_path IN ({file_placeholders})")
                params.extend(filter_criteria.file_paths)

            # Full-text search
            if filter_criteria.search_query:
                return await self._full_text_search(session_id, filter_criteria)

            # Execute query
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row

                query = f"""
                    SELECT * FROM messages
                    WHERE {' AND '.join(where_conditions)}
                    ORDER BY timestamp DESC
                    LIMIT ? OFFSET ?
                """
                params.extend([filter_criteria.limit, filter_criteria.offset])

                cursor = conn.execute(query, params)

                results = []
                for row in cursor.fetchall():
                    message = self._row_to_message(row)

                    # Calculate relevance score based on keyword matches
                    relevance_score = self._calculate_relevance(message, filter_criteria)

                    # Get context messages
                    context_messages = await self._get_context_messages(message.message_id, session_id)

                    result = SearchResult(
                        message=message,
                        relevance_score=relevance_score,
                        context_messages=context_messages
                    )
                    results.append(result)

                return results

        except Exception as e:
            logger.error(f"Failed to search messages: {e}")
            return []

    async def _full_text_search(
        self,
        session_id: str,
        filter_criteria: ConversationFilter
    ) -> List[SearchResult]:
        """Perform full-text search using FTS5."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row

                # Use FTS5 for full-text search
                cursor = conn.execute("""
                    SELECT m.*, fts.rank
                    FROM messages_fts fts
                    JOIN messages m ON fts.message_id = m.message_id
                    WHERE m.session_id = ? AND fts MATCH ?
                    ORDER BY fts.rank
                    LIMIT ? OFFSET ?
                """, (
                    session_id,
                    filter_criteria.search_query,
                    filter_criteria.limit,
                    filter_criteria.offset
                ))

                results = []
                for row in cursor.fetchall():
                    message = self._row_to_message(row)

                    # Use FTS rank as relevance score (normalized)
                    relevance_score = min(abs(row['rank']) / 10.0, 1.0)

                    # Highlight matches
                    highlights = self._highlight_matches(message.content, filter_criteria.search_query)

                    # Get context messages
                    context_messages = await self._get_context_messages(message.message_id, session_id)

                    result = SearchResult(
                        message=message,
                        relevance_score=relevance_score,
                        match_highlights=highlights,
                        context_messages=context_messages
                    )
                    results.append(result)

                return results

        except Exception as e:
            logger.error(f"Full-text search failed: {e}")
            return []

    def _row_to_message(self, row: sqlite3.Row) -> Message:
        """Convert database row to Message object."""
        metadata = json.loads(row['metadata_json'] or '{}')
        search_keywords = json.loads(row['search_keywords_json'] or '[]')

        return Message(
            message_id=row['message_id'],
            session_id=row['session_id'],
            role=MessageRole(row['role']),
            content=row['content'],
            message_type=MessageType(row['message_type']),
            timestamp=datetime.fromisoformat(row['timestamp']),
            token_count=row['token_count'],
            parent_message_id=row['parent_message_id'],
            thread_id=row['thread_id'],
            tool_name=row['tool_name'],
            file_path=row['file_path'],
            code_language=row['code_language'],
            metadata=metadata,
            search_keywords=search_keywords
        )

    def _calculate_relevance(self, message: Message, filter_criteria: ConversationFilter) -> float:
        """Calculate relevance score for a message."""
        score = 0.0

        # Keyword matching
        if filter_criteria.keywords:
            keyword_matches = sum(1 for keyword in filter_criteria.keywords
                                if keyword.lower() in message.content.lower())
            score += (keyword_matches / len(filter_criteria.keywords)) * 0.5

        # Recent messages get higher scores
        age_hours = (datetime.now() - message.timestamp).total_seconds() / 3600
        recency_score = max(0, 1 - (age_hours / 168))  # Decay over a week
        score += recency_score * 0.3

        # Tool and code messages get higher scores
        if message.message_type in [MessageType.CODE, MessageType.TOOL_CALL]:
            score += 0.2

        return min(score, 1.0)

    def _highlight_matches(self, content: str, query: str) -> List[str]:
        """Highlight matching text in content."""
        highlights = []
        query_words = query.lower().split()

        for word in query_words:
            pattern = re.compile(re.escape(word), re.IGNORECASE)
            matches = pattern.finditer(content)

            for match in matches:
                start = max(0, match.start() - 20)
                end = min(len(content), match.end() + 20)
                highlight = content[start:end]
                highlights.append(f"...{highlight}...")

        return highlights[:5]  # Limit to 5 highlights

    async def _get_context_messages(
        self,
        message_id: str,
        session_id: str,
        context_size: int = 2
    ) -> List[Message]:
        """Get context messages around a specific message."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row

                # Get the timestamp of the target message
                cursor = conn.execute(
                    "SELECT timestamp FROM messages WHERE message_id = ?",
                    (message_id,)
                )
                row = cursor.fetchone()
                if not row:
                    return []

                target_timestamp = row['timestamp']

                # Get context messages before and after
                cursor = conn.execute("""
                    (SELECT * FROM messages
                     WHERE session_id = ? AND timestamp < ?
                     ORDER BY timestamp DESC LIMIT ?)
                    UNION ALL
                    (SELECT * FROM messages
                     WHERE session_id = ? AND timestamp > ?
                     ORDER BY timestamp ASC LIMIT ?)
                    ORDER BY timestamp
                """, (session_id, target_timestamp, context_size,
                      session_id, target_timestamp, context_size))

                context_messages = []
                for row in cursor.fetchall():
                    message = self._row_to_message(row)
                    context_messages.append(message)

                return context_messages

        except Exception as e:
            logger.error(f"Failed to get context messages: {e}")
            return []

    async def get_conversation_stats(self, session_id: str) -> Dict[str, Any]:
        """Get conversation statistics for a session."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT
                        COUNT(*) as total_messages,
                        SUM(token_count) as total_tokens,
                        COUNT(DISTINCT role) as unique_roles,
                        COUNT(CASE WHEN message_type = 'code' THEN 1 END) as code_messages,
                        COUNT(CASE WHEN tool_name IS NOT NULL THEN 1 END) as tool_messages,
                        MIN(timestamp) as first_message,
                        MAX(timestamp) as last_message
                    FROM messages
                    WHERE session_id = ?
                """, (session_id,))

                row = cursor.fetchone()
                if row:
                    return {
                        "total_messages": row[0],
                        "total_tokens": row[1] or 0,
                        "unique_roles": row[2],
                        "code_messages": row[3],
                        "tool_messages": row[4],
                        "first_message": row[5],
                        "last_message": row[6],
                        "conversation_duration": (
                            datetime.fromisoformat(row[6]) - datetime.fromisoformat(row[5])
                        ).total_seconds() / 3600 if row[5] and row[6] else 0
                    }

        except Exception as e:
            logger.error(f"Failed to get conversation stats: {e}")

        return {}
