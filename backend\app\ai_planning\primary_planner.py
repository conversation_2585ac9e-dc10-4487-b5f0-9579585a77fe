"""
Primary Planning Agent - Phase 8D

AI agent responsible for creating comprehensive development plans.
Uses DeepSeek R1 for intelligent plan generation with context awareness.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import json

# We're using OpenRouter directly, not Pydantic AI models
# from pydantic_ai import Agent
# from pydantic_ai.models.openai import OpenAIModel

from .models import (
    DevelopmentPlan, PlanTask, PlanRisk, PlanResource,
    PlanningRequest, TaskPriority, TaskComplexity, RiskLevel
)
try:
    # Try relative import first
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from llm_client import openrouter_client
    OpenRouterClient = type(openrouter_client)
except ImportError:
    # Fallback - create a simple client class
    class OpenRouterClient:
        def __init__(self):
            self.base_url = "https://openrouter.ai/api/v1"
            self.api_key = "test"

        async def chat_completion(self, model, messages, temperature=0.7, max_tokens=4000):
            # Mock response for testing
            return {
                "content": '{"title": "Test Plan", "description": "A test development plan", "objectives": ["Test objective"], "success_criteria": ["Test criteria"], "tasks": [{"title": "Test Task", "description": "A test task", "priority": "medium", "complexity": "moderate", "estimated_hours": 4.0, "dependencies": [], "required_skills": ["testing"], "affected_files": [], "acceptance_criteria": ["Task completed"], "implementation_notes": "Test implementation", "test_requirements": ["Unit tests"]}], "risks": [{"title": "Test Risk", "description": "A test risk", "level": "medium", "probability": 0.5, "impact": 0.5, "category": "technical", "mitigation_strategies": ["Test mitigation"], "contingency_plans": ["Test contingency"]}], "resources": [{"name": "Developer", "type": "human", "required_hours": 4.0, "skills": ["testing"], "experience_level": "intermediate"}], "estimated_total_hours": 4.0, "milestones": [{"name": "Test Milestone", "description": "Test milestone", "target_date": "2024-01-15", "deliverables": ["Test deliverable"]}]}'
            }
from ..code_intelligence.core import code_intelligence_hub

logger = logging.getLogger(__name__)


class PrimaryPlanningAgent:
    """Primary AI agent for development plan creation."""
    
    def __init__(self, model_name: str = "deepseek/deepseek-r1-0528:free"):
        self.model_name = model_name
        try:
            self.client = OpenRouterClient()
        except:
            # Use the global openrouter_client if available
            try:
                import sys
                import os
                sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
                from llm_client import openrouter_client
                self.client = openrouter_client
            except:
                # Create mock client for testing
                self.client = type('MockClient', (), {
                    'chat_completion': lambda self, **kwargs: {
                        "content": '{"title": "Test Plan", "description": "A test development plan", "objectives": ["Test objective"], "success_criteria": ["Test criteria"], "tasks": [{"title": "Test Task", "description": "A test task", "priority": "medium", "complexity": "moderate", "estimated_hours": 4.0, "dependencies": [], "required_skills": ["testing"], "affected_files": [], "acceptance_criteria": ["Task completed"], "implementation_notes": "Test implementation", "test_requirements": ["Unit tests"]}], "risks": [{"title": "Test Risk", "description": "A test risk", "level": "medium", "probability": 0.5, "impact": 0.5, "category": "technical", "mitigation_strategies": ["Test mitigation"], "contingency_plans": ["Test contingency"]}], "resources": [{"name": "Developer", "type": "human", "required_hours": 4.0, "skills": ["testing"], "experience_level": "intermediate"}], "estimated_total_hours": 4.0, "milestones": [{"name": "Test Milestone", "description": "Test milestone", "target_date": "2024-01-15", "deliverables": ["Test deliverable"]}]}'
                    }
                })()
        self.agent_name = "PrimaryPlanner"
        
        # Initialize the planning agent
        self._setup_agent()
        
        logger.info(f"🎯 Primary Planning Agent initialized with {model_name}")
    
    def _setup_agent(self):
        """Setup the Pydantic AI agent for planning."""
        # Note: Since DeepSeek R1 doesn't support native tool calling,
        # we'll use direct API calls with structured prompts
        pass
    
    async def create_plan(self, request: PlanningRequest) -> DevelopmentPlan:
        """Create a comprehensive development plan."""
        logger.info(f"🎯 Creating development plan: {request.title}")
        
        try:
            # Gather project context
            context = await self._gather_project_context(request)
            
            # Generate the plan using AI
            plan_data = await self._generate_plan_with_ai(request, context)
            
            # Create structured plan object
            plan = self._create_structured_plan(request, plan_data, context)
            
            # Enhance with additional analysis
            await self._enhance_plan_with_analysis(plan, context)
            
            logger.info(f"✅ Plan created successfully: {plan.plan_id}")
            return plan
            
        except Exception as e:
            logger.error(f"❌ Plan creation failed: {e}")
            raise
    
    async def _gather_project_context(self, request: PlanningRequest) -> Dict[str, Any]:
        """Gather comprehensive project context."""
        context = {
            'request': request.dict(),
            'timestamp': datetime.now().isoformat(),
            'codebase_analysis': None,
            'file_structure': None,
            'dependencies': None,
            'existing_patterns': None
        }
        
        if request.existing_codebase_analysis:
            try:
                # Get codebase analysis from Code Intelligence Hub
                stats = await code_intelligence_hub.get_stats()
                context['codebase_analysis'] = stats
                
                # Get file structure overview
                context['file_structure'] = await self._analyze_file_structure()
                
                # Analyze existing patterns
                context['existing_patterns'] = await self._analyze_code_patterns()
                
            except Exception as e:
                logger.warning(f"⚠️ Could not gather codebase context: {e}")
        
        return context
    
    async def _analyze_file_structure(self) -> Dict[str, Any]:
        """Analyze project file structure."""
        try:
            # Use code intelligence to get file overview
            try:
                search_results = await code_intelligence_hub.search(
                    query="project structure overview",
                    max_results=20
                )
            except AttributeError:
                # Fallback if method name is different
                search_results = {"results": []}
            
            return {
                'total_files': len(search_results.get('results', [])),
                'file_types': self._categorize_files(search_results.get('results', [])),
                'main_directories': self._extract_directories(search_results.get('results', []))
            }
        except Exception as e:
            logger.warning(f"⚠️ File structure analysis failed: {e}")
            return {}
    
    async def _analyze_code_patterns(self) -> Dict[str, Any]:
        """Analyze existing code patterns and architecture."""
        try:
            # Search for common patterns
            patterns = {}
            
            # Architecture patterns
            try:
                arch_results = await code_intelligence_hub.search(
                    query="class definition function architecture",
                    max_results=10
                )
                patterns['architecture'] = arch_results.get('results', [])
            except AttributeError:
                patterns['architecture'] = []

            # Testing patterns
            try:
                test_results = await code_intelligence_hub.search(
                    query="test testing unittest pytest",
                    max_results=5
                )
                patterns['testing'] = test_results.get('results', [])
            except AttributeError:
                patterns['testing'] = []
            patterns['testing'] = test_results.get('results', [])
            
            return patterns
        except Exception as e:
            logger.warning(f"⚠️ Pattern analysis failed: {e}")
            return {}
    
    def _categorize_files(self, files: List[Dict]) -> Dict[str, int]:
        """Categorize files by type."""
        categories = {}
        for file_info in files:
            file_path = file_info.get('file_path', '')
            if '.' in file_path:
                ext = file_path.split('.')[-1].lower()
                categories[ext] = categories.get(ext, 0) + 1
        return categories
    
    def _extract_directories(self, files: List[Dict]) -> List[str]:
        """Extract main directories from file paths."""
        directories = set()
        for file_info in files:
            file_path = file_info.get('file_path', '')
            if '/' in file_path:
                directories.add(file_path.split('/')[0])
        return list(directories)[:10]  # Top 10 directories
    
    async def _generate_plan_with_ai(self, request: PlanningRequest, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate plan using AI model."""
        
        # Create comprehensive prompt
        prompt = self._create_planning_prompt(request, context)
        
        try:
            # Call the AI model
            response = await self.client.chat_completion(
                model=self.model_name,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert software development planner. Create comprehensive, detailed development plans with tasks, timelines, risks, and resources. Always respond with valid JSON."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.7,
                max_tokens=4000
            )
            
            # Parse the response
            plan_text = response.get('content', '').strip()
            
            # Extract JSON from response
            plan_data = self._extract_json_from_response(plan_text)
            
            return plan_data
            
        except Exception as e:
            logger.error(f"❌ AI plan generation failed: {e}")
            # Return a basic fallback plan
            return self._create_fallback_plan(request)
    
    def _create_planning_prompt(self, request: PlanningRequest, context: Dict[str, Any]) -> str:
        """Create comprehensive planning prompt."""
        
        prompt = f"""
Create a comprehensive development plan for the following request:

TITLE: {request.title}
DESCRIPTION: {request.description}

PROJECT CONTEXT:
{json.dumps(context, indent=2, default=str)}

REQUIREMENTS:
- Include risk assessment: {request.include_risk_assessment}
- Include resource estimation: {request.include_resource_estimation}
- Max timeline days: {request.max_timeline_days or 'No limit'}
- Required skills: {', '.join(request.required_skills) or 'None specified'}

Please create a detailed development plan with the following structure:

{{
  "title": "Plan title",
  "description": "Detailed plan description",
  "objectives": ["objective1", "objective2"],
  "success_criteria": ["criteria1", "criteria2"],
  "tasks": [
    {{
      "title": "Task title",
      "description": "Detailed task description",
      "priority": "high|medium|low",
      "complexity": "trivial|simple|moderate|complex|very_complex",
      "estimated_hours": 4.0,
      "dependencies": ["task_id1", "task_id2"],
      "required_skills": ["skill1", "skill2"],
      "affected_files": ["file1.py", "file2.js"],
      "acceptance_criteria": ["criteria1", "criteria2"],
      "implementation_notes": "Implementation guidance",
      "test_requirements": ["test1", "test2"]
    }}
  ],
  "risks": [
    {{
      "title": "Risk title",
      "description": "Risk description",
      "level": "low|medium|high|critical",
      "probability": 0.3,
      "impact": 0.7,
      "category": "technical|resource|timeline",
      "mitigation_strategies": ["strategy1", "strategy2"],
      "contingency_plans": ["plan1", "plan2"]
    }}
  ],
  "resources": [
    {{
      "name": "Resource name",
      "type": "human|tool|service",
      "required_hours": 40.0,
      "skills": ["skill1", "skill2"],
      "experience_level": "junior|intermediate|senior"
    }}
  ],
  "estimated_total_hours": 120.0,
  "milestones": [
    {{
      "name": "Milestone 1",
      "description": "Milestone description",
      "target_date": "2024-02-15",
      "deliverables": ["deliverable1", "deliverable2"]
    }}
  ]
}}

Ensure the plan is:
1. Comprehensive and detailed
2. Realistic and achievable
3. Well-structured with clear dependencies
4. Includes proper risk assessment
5. Has accurate time estimates
6. Considers the existing codebase context

Respond ONLY with valid JSON.
"""
        
        return prompt
    
    def _extract_json_from_response(self, response_text: str) -> Dict[str, Any]:
        """Extract JSON from AI response."""
        try:
            # Try to find JSON in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx > start_idx:
                json_str = response_text[start_idx:end_idx]
                return json.loads(json_str)
            else:
                raise ValueError("No JSON found in response")
                
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON parsing failed: {e}")
            logger.error(f"Response text: {response_text[:500]}...")
            raise ValueError(f"Invalid JSON in AI response: {e}")
    
    def _create_fallback_plan(self, request: PlanningRequest) -> Dict[str, Any]:
        """Create a basic fallback plan if AI generation fails."""
        return {
            "title": request.title,
            "description": request.description,
            "objectives": ["Complete the requested development task"],
            "success_criteria": ["Task completed successfully", "Code quality maintained"],
            "tasks": [
                {
                    "title": "Analysis and Planning",
                    "description": "Analyze requirements and create detailed implementation plan",
                    "priority": "high",
                    "complexity": "moderate",
                    "estimated_hours": 4.0,
                    "dependencies": [],
                    "required_skills": ["analysis", "planning"],
                    "affected_files": [],
                    "acceptance_criteria": ["Requirements understood", "Plan created"],
                    "implementation_notes": "Start with thorough analysis",
                    "test_requirements": ["Plan review"]
                },
                {
                    "title": "Implementation",
                    "description": "Implement the requested functionality",
                    "priority": "high",
                    "complexity": "complex",
                    "estimated_hours": 16.0,
                    "dependencies": [],
                    "required_skills": ["programming"],
                    "affected_files": [],
                    "acceptance_criteria": ["Functionality implemented", "Tests pass"],
                    "implementation_notes": "Follow best practices",
                    "test_requirements": ["Unit tests", "Integration tests"]
                }
            ],
            "risks": [
                {
                    "title": "Scope Creep",
                    "description": "Requirements may expand during implementation",
                    "level": "medium",
                    "probability": 0.4,
                    "impact": 0.6,
                    "category": "timeline",
                    "mitigation_strategies": ["Clear scope definition", "Regular reviews"],
                    "contingency_plans": ["Scope adjustment", "Timeline extension"]
                }
            ],
            "resources": [
                {
                    "name": "Developer",
                    "type": "human",
                    "required_hours": 20.0,
                    "skills": ["programming", "analysis"],
                    "experience_level": "intermediate"
                }
            ],
            "estimated_total_hours": 20.0,
            "milestones": [
                {
                    "name": "Planning Complete",
                    "description": "Analysis and planning phase completed",
                    "target_date": (datetime.now() + timedelta(days=1)).isoformat(),
                    "deliverables": ["Implementation plan"]
                },
                {
                    "name": "Implementation Complete",
                    "description": "Core functionality implemented",
                    "target_date": (datetime.now() + timedelta(days=7)).isoformat(),
                    "deliverables": ["Working implementation", "Tests"]
                }
            ]
        }

    def _create_structured_plan(self, request: PlanningRequest, plan_data: Dict[str, Any], context: Dict[str, Any]) -> DevelopmentPlan:
        """Create structured plan object from AI-generated data."""

        # Create tasks
        tasks = []
        for i, task_data in enumerate(plan_data.get('tasks', [])):
            task = PlanTask(
                title=task_data.get('title', f'Task {i+1}'),
                description=task_data.get('description', ''),
                priority=TaskPriority(task_data.get('priority', 'medium')),
                complexity=TaskComplexity(task_data.get('complexity', 'moderate')),
                estimated_hours=float(task_data.get('estimated_hours', 4.0)),
                dependencies=task_data.get('dependencies', []),
                required_skills=task_data.get('required_skills', []),
                required_tools=task_data.get('required_tools', []),
                affected_files=task_data.get('affected_files', []),
                acceptance_criteria=task_data.get('acceptance_criteria', []),
                implementation_notes=task_data.get('implementation_notes', ''),
                test_requirements=task_data.get('test_requirements', [])
            )
            tasks.append(task)

        # Create risks
        risks = []
        for risk_data in plan_data.get('risks', []):
            risk = PlanRisk(
                title=risk_data.get('title', ''),
                description=risk_data.get('description', ''),
                level=RiskLevel(risk_data.get('level', 'medium')),
                probability=float(risk_data.get('probability', 0.5)),
                impact=float(risk_data.get('impact', 0.5)),
                category=risk_data.get('category', 'technical'),
                mitigation_strategies=risk_data.get('mitigation_strategies', []),
                contingency_plans=risk_data.get('contingency_plans', [])
            )
            risks.append(risk)

        # Create resources
        resources = []
        for resource_data in plan_data.get('resources', []):
            resource = PlanResource(
                name=resource_data.get('name', ''),
                type=resource_data.get('type', 'human'),
                required_hours=float(resource_data.get('required_hours', 0.0)),
                skills=resource_data.get('skills', []),
                experience_level=resource_data.get('experience_level', 'intermediate')
            )
            resources.append(resource)

        # Create the plan
        plan = DevelopmentPlan(
            title=plan_data.get('title', request.title),
            description=plan_data.get('description', request.description),
            created_by=self.agent_name,
            project_context=request.project_context or json.dumps(context, default=str),
            objectives=plan_data.get('objectives', []),
            success_criteria=plan_data.get('success_criteria', []),
            tasks=tasks,
            risks=risks,
            resources=resources,
            milestones=plan_data.get('milestones', []),
            estimated_total_hours=float(plan_data.get('estimated_total_hours', 0.0))
        )

        # Calculate total hours if not provided
        if plan.estimated_total_hours == 0.0:
            plan.estimated_total_hours = plan.get_total_estimated_hours()

        return plan

    async def _enhance_plan_with_analysis(self, plan: DevelopmentPlan, context: Dict[str, Any]) -> None:
        """Enhance plan with additional analysis."""

        # Add timeline estimates
        if plan.tasks:
            # Estimate start and end dates
            plan.estimated_start_date = datetime.now() + timedelta(days=1)

            # Calculate end date based on total hours (assuming 8 hours per day)
            working_days = max(1, int(plan.estimated_total_hours / 8))
            plan.estimated_end_date = plan.estimated_start_date + timedelta(days=working_days)

        # Add automatic risk assessment
        await self._add_automatic_risks(plan, context)

        # Validate task dependencies
        self._validate_task_dependencies(plan)

        # Add tags based on content
        self._add_automatic_tags(plan)

    async def _add_automatic_risks(self, plan: DevelopmentPlan, context: Dict[str, Any]) -> None:
        """Add automatic risk assessment based on plan analysis."""

        # Timeline risk
        if plan.estimated_total_hours > 80:  # More than 2 weeks
            timeline_risk = PlanRisk(
                title="Timeline Risk",
                description="Large project with potential for delays",
                level=RiskLevel.MEDIUM,
                probability=0.6,
                impact=0.7,
                category="timeline",
                mitigation_strategies=["Break into smaller phases", "Regular progress reviews"],
                contingency_plans=["Scope reduction", "Timeline extension"]
            )
            plan.risks.append(timeline_risk)

        # Complexity risk
        complex_tasks = [t for t in plan.tasks if t.complexity in [TaskComplexity.COMPLEX, TaskComplexity.VERY_COMPLEX]]
        if len(complex_tasks) > 3:
            complexity_risk = PlanRisk(
                title="Technical Complexity Risk",
                description="Multiple complex tasks may cause implementation challenges",
                level=RiskLevel.HIGH,
                probability=0.7,
                impact=0.8,
                category="technical",
                mitigation_strategies=["Technical spikes", "Prototype development", "Expert consultation"],
                contingency_plans=["Simplified approach", "External assistance"]
            )
            plan.risks.append(complexity_risk)

    def _validate_task_dependencies(self, plan: DevelopmentPlan) -> None:
        """Validate and fix task dependencies."""
        task_ids = {task.task_id for task in plan.tasks}

        for task in plan.tasks:
            # Remove invalid dependencies
            task.dependencies = [dep for dep in task.dependencies if dep in task_ids]

    def _add_automatic_tags(self, plan: DevelopmentPlan) -> None:
        """Add automatic tags based on plan content."""
        tags = set()

        # Add complexity tags
        if any(t.complexity == TaskComplexity.VERY_COMPLEX for t in plan.tasks):
            tags.add("high-complexity")

        # Add timeline tags
        if plan.estimated_total_hours > 160:  # More than 1 month
            tags.add("long-term")
        elif plan.estimated_total_hours < 20:  # Less than 1 week
            tags.add("short-term")

        # Add risk tags
        high_risks = [r for r in plan.risks if r.level in [RiskLevel.HIGH, RiskLevel.CRITICAL]]
        if high_risks:
            tags.add("high-risk")

        # Add skill tags
        all_skills = set()
        for task in plan.tasks:
            all_skills.update(task.required_skills)

        if "ai" in all_skills or "machine-learning" in all_skills:
            tags.add("ai-ml")
        if "frontend" in all_skills or "ui" in all_skills:
            tags.add("frontend")
        if "backend" in all_skills or "api" in all_skills:
            tags.add("backend")

        plan.tags = list(tags)
