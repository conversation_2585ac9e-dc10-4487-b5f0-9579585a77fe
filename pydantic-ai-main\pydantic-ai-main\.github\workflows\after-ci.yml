name: After CI

on:
  workflow_run:
    workflows: [CI]
    types: [completed]

permissions:
  statuses: write
  pull-requests: write

jobs:
  smokeshow:
    runs-on: ubuntu-latest

    steps:
      - uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          python-version: '3.12'

      - uses: dawidd6/action-download-artifact@v6
        with:
          workflow: ci.yml
          name: '(diff-)?coverage-html.*'
          name_is_regexp: true
          commit: ${{ github.event.workflow_run.head_sha }}
          allow_forks: true
          workflow_conclusion: completed
          if_no_artifact_found: warn

      - run: uvx smokeshow upload coverage-html
        if: hashFiles('coverage-html/*.html') != ''
        env:
          SMOKESHOW_GITHUB_STATUS_DESCRIPTION: Coverage {coverage-percentage}
          SMOKESHOW_GITHUB_COVERAGE_THRESHOLD: 95
          SMOKESHOW_GITHUB_CONTEXT: coverage
          SMOKESHOW_GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SMOKESHOW_GITHUB_PR_HEAD_SHA: ${{ github.event.workflow_run.head_sha }}
          SMOKESHOW_AUTH_KEY: ${{ secrets.SMOKESHOW_AUTH_KEY }}

      - run: uvx smokeshow upload diff-coverage-html
        if: hashFiles('diff-coverage-html/*.html') != ''
        env:
          SMOKESHOW_GITHUB_STATUS_DESCRIPTION: Diff coverage {coverage-percentage}
          SMOKESHOW_GITHUB_COVERAGE_THRESHOLD: 95
          SMOKESHOW_GITHUB_CONTEXT: diff-coverage
          SMOKESHOW_GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SMOKESHOW_GITHUB_PR_HEAD_SHA: ${{ github.event.workflow_run.head_sha }}
          SMOKESHOW_AUTH_KEY: ${{ secrets.SMOKESHOW_AUTH_KEY }}

  deploy-docs-preview:
    runs-on: ubuntu-latest
    if: github.event.workflow_run.event == 'pull_request'
    environment:
      name: deploy-docs-preview

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
      - run: npm install
        working-directory: docs-site

      - uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          python-version: '3.12'

      - uses: dawidd6/action-download-artifact@v6
        with:
          workflow: ci.yml
          name: site
          path: site
          commit: ${{ github.event.workflow_run.head_sha }}
          allow_forks: true
          workflow_conclusion: completed

      - uses: cloudflare/wrangler-action@v3
        id: deploy
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          environment: previews
          workingDirectory: docs-site
          command: >
            deploy
            --var GIT_COMMIT_SHA:${{ github.event.workflow_run.head_sha }}
            --var GIT_BRANCH:${{ github.event.workflow_run.head_branch }}

      - run: echo "$GITHUB_EVENT_JSON"
        env:
          GITHUB_EVENT_JSON: ${{ toJSON(github.event) }}

      - name: Set preview URL
        run: uv run --no-project --with httpx .github/set_docs_pr_preview_url.py
        env:
          DEPLOY_OUTPUT: ${{ steps.deploy.outputs.command-output }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          REPOSITORY: ${{ github.repository }}
          PULL_REQUEST_NUMBER: ${{ github.event.workflow_run.pull_requests[0].number }}
          REF: ${{ github.event.workflow_run.head_sha }}
