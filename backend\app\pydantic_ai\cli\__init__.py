"""
CLI Integration for Pydantic AI

This module provides command-line interface integration for the Pydantic AI system,
allowing users to interact with agents, run tests, and manage workflows from the command line.
"""

from .commands import (
    run_agent_command,
    test_command,
    benchmark_command,
    evaluate_command
)

from .interface import (
    CLIInterface,
    create_cli_app,
    run_interactive_session
)

from .utils import (
    format_output,
    parse_agent_args,
    load_cli_config,
    save_cli_config,
    create_results_directory
)

__all__ = [
    # Commands
    'run_agent_command',
    'test_command',
    'benchmark_command',
    'evaluate_command',
    
    # Interface
    'CLIInterface',
    'create_cli_app',
    'run_interactive_session',
    
    # Utils
    'format_output',
    'parse_agent_args',
    'load_cli_config',
    'save_cli_config',
    'create_results_directory'
]
