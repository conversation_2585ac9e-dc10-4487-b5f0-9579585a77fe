#!/usr/bin/env pwsh
# Local Development Setup Script for AI Coder Agent

param(
    [string]$Mode = "docker-dev",  # Options: docker-dev, local, hybrid
    [switch]$SkipDeps = $false,
    [switch]$Help = $false
)

if ($Help) {
    Write-Host @"
🚀 AI Coder Agent - Development Setup

Usage: .\setup_local_dev.ps1 -Mode <mode> [options]

Modes:
  docker-dev  - Use Docker with volume mounting (recommended)
  local       - Run everything locally (fastest)
  hybrid      - Run backend locally, services in Docker

Options:
  -SkipDeps   - Skip dependency installation
  -Help       - Show this help message

Examples:
  .\setup_local_dev.ps1 -Mode docker-dev
  .\setup_local_dev.ps1 -Mode local
  .\setup_local_dev.ps1 -Mode hybrid -SkipDeps
"@
    exit 0
}

Write-Host "🚀 Setting up AI Coder Agent for development..." -ForegroundColor Green
Write-Host "Mode: $Mode" -ForegroundColor Yellow

switch ($Mode) {
    "docker-dev" {
        Write-Host "`n📦 Setting up Docker Development Environment..." -ForegroundColor Cyan
        
        # Build only once, then use volume mounting
        Write-Host "Building Docker images (one-time setup)..."
        docker-compose -f docker-compose.dev.yml build
        
        Write-Host "Starting services with volume mounting..."
        docker-compose -f docker-compose.dev.yml up -d
        
        Write-Host @"

✅ Docker Development Environment Ready!

Your code is now mounted as volumes:
- Backend: ./backend -> /app (auto-reload enabled)
- Frontend: ./frontend -> /app (hot reload enabled)

Commands:
  Start:    docker-compose -f docker-compose.dev.yml up -d
  Stop:     docker-compose -f docker-compose.dev.yml down
  Logs:     docker-compose -f docker-compose.dev.yml logs -f backend
  Restart:  docker-compose -f docker-compose.dev.yml restart backend

URLs:
  Backend:  http://localhost:8000
  Frontend: http://localhost:3000
  Qdrant:   http://localhost:6333

"@ -ForegroundColor Green
    }
    
    "local" {
        Write-Host "`n💻 Setting up Local Development Environment..." -ForegroundColor Cyan
        
        if (-not $SkipDeps) {
            Write-Host "Installing Python dependencies..."
            Set-Location backend
            python -m pip install --upgrade pip
            pip install -r requirements.txt
            Set-Location ..
            
            Write-Host "Installing Node.js dependencies..."
            Set-Location frontend
            npm install
            Set-Location ..
        }
        
        # Start only external services in Docker
        Write-Host "Starting external services (Qdrant, Ollama)..."
        docker-compose up -d qdrant ollama
        
        # Create local environment file
        @"
# Local Development Environment
OPENROUTER_API_KEY=$env:OPENROUTER_API_KEY
OLLAMA_HOST=http://localhost:11434
QDRANT_URL=http://localhost:6333
LOG_LEVEL=DEBUG
PYTHONPATH=.
"@ | Out-File -FilePath "backend/.env.local" -Encoding UTF8
        
        Write-Host @"

✅ Local Development Environment Ready!

To start development:

Terminal 1 (Backend):
  cd backend
  python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload

Terminal 2 (Frontend):
  cd frontend
  npm run dev

URLs:
  Backend:  http://localhost:8000
  Frontend: http://localhost:3000
  Qdrant:   http://localhost:6333
  Ollama:   http://localhost:11434

"@ -ForegroundColor Green
    }
    
    "hybrid" {
        Write-Host "`n🔄 Setting up Hybrid Development Environment..." -ForegroundColor Cyan
        
        if (-not $SkipDeps) {
            Write-Host "Installing Python dependencies..."
            Set-Location backend
            python -m pip install --upgrade pip
            pip install -r requirements.txt
            Set-Location ..
        }
        
        # Start only external services
        Write-Host "Starting external services..."
        docker-compose up -d qdrant ollama
        
        # Create hybrid environment file
        @"
# Hybrid Development Environment
OPENROUTER_API_KEY=$env:OPENROUTER_API_KEY
OLLAMA_HOST=http://localhost:11434
QDRANT_URL=http://localhost:6333
LOG_LEVEL=DEBUG
PYTHONPATH=.
"@ | Out-File -FilePath "backend/.env.hybrid" -Encoding UTF8
        
        Write-Host @"

✅ Hybrid Development Environment Ready!

External services running in Docker:
- Qdrant: http://localhost:6333
- Ollama: http://localhost:11434

To start backend locally:
  cd backend
  python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload

Frontend can run in Docker:
  docker-compose up -d frontend

"@ -ForegroundColor Green
    }
    
    default {
        Write-Host "❌ Invalid mode: $Mode" -ForegroundColor Red
        Write-Host "Valid modes: docker-dev, local, hybrid" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host "`n🎯 Development Tips:" -ForegroundColor Magenta
Write-Host "- Use 'docker-dev' mode for the best balance of speed and consistency"
Write-Host "- Use 'local' mode for fastest iteration (requires Python 3.11+ and Node.js)"
Write-Host "- Use 'hybrid' mode if you want to debug backend locally but keep services in Docker"
Write-Host "- All modes support hot reload/auto-reload for instant code changes"
