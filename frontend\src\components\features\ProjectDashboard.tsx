import React, { useState, useEffect } from 'react';
import { ProjectCreationWizard } from './ProjectCreationWizard';
import { ProjectAnalytics } from './ProjectAnalytics';

interface Project {
  id: string;
  name: string;
  description: string;
  type: 'web_app' | 'mobile_app' | 'api' | 'library' | 'data_science' | 'other';
  status: 'active' | 'paused' | 'completed' | 'archived';
  health_score: number;
  created_at: Date;
  updated_at: Date;
  metadata: {
    language?: string;
    framework?: string;
    lines_of_code?: number;
    complexity_score?: number;
    test_coverage?: number;
    dependencies_count?: number;
  };
  ai_features: {
    auto_planning: boolean;
    code_analysis: boolean;
    optimization: boolean;
    testing: boolean;
  };
}

interface ProjectStats {
  total_projects: number;
  active_projects: number;
  completed_projects: number;
  avg_health_score: number;
  total_lines_of_code: number;
}

type ViewMode = 'grid' | 'list' | 'tree';
type SortBy = 'name' | 'created_at' | 'updated_at' | 'health_score' | 'status';

export const ProjectDashboard: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [projectStats, setProjectStats] = useState<ProjectStats>({
    total_projects: 0,
    active_projects: 0,
    completed_projects: 0,
    avg_health_score: 0,
    total_lines_of_code: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [sortBy, setSortBy] = useState<SortBy>('updated_at');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateWizard, setShowCreateWizard] = useState(false);
  const [showAnalytics, setShowAnalytics] = useState<string | null>(null);

  useEffect(() => {
    loadProjects();
    loadProjectStats();
  }, []);

  const loadProjects = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/projects/');
      if (response.ok) {
        const data = await response.json();
        const projectsData = (data.projects || []).map((project: any) => ({
          id: project.id,
          name: project.name,
          description: project.description || '',
          type: project.type || 'other',
          status: project.status || 'active',
          health_score: project.health_score || Math.random() * 100,
          created_at: new Date(project.created_at || Date.now()),
          updated_at: new Date(project.updated_at || Date.now()),
          metadata: project.metadata || {},
          ai_features: project.ai_features || {
            auto_planning: false,
            code_analysis: false,
            optimization: false,
            testing: false
          }
        }));
        setProjects(projectsData);
      }
    } catch (error) {
      console.error('Failed to load projects:', error);
      // Mock data for development
      setProjects([
        {
          id: '1',
          name: 'DeepNexus Frontend',
          description: 'Revolutionary AI coding interface with nature-inspired design',
          type: 'web_app',
          status: 'active',
          health_score: 95,
          created_at: new Date('2024-01-15'),
          updated_at: new Date(),
          metadata: {
            language: 'TypeScript',
            framework: 'React',
            lines_of_code: 15420,
            complexity_score: 7.2,
            test_coverage: 85,
            dependencies_count: 42
          },
          ai_features: {
            auto_planning: true,
            code_analysis: true,
            optimization: true,
            testing: true
          }
        },
        {
          id: '2',
          name: 'AI Backend Services',
          description: 'Comprehensive backend with 150+ API endpoints',
          type: 'api',
          status: 'active',
          health_score: 88,
          created_at: new Date('2024-01-10'),
          updated_at: new Date(Date.now() - 3600000),
          metadata: {
            language: 'Python',
            framework: 'FastAPI',
            lines_of_code: 28750,
            complexity_score: 8.5,
            test_coverage: 92,
            dependencies_count: 67
          },
          ai_features: {
            auto_planning: true,
            code_analysis: true,
            optimization: false,
            testing: true
          }
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const loadProjectStats = async () => {
    try {
      const response = await fetch('/api/projects/statistics/overview');
      if (response.ok) {
        const data = await response.json();
        setProjectStats(data);
      }
    } catch (error) {
      console.error('Failed to load project stats:', error);
      // Mock stats
      setProjectStats({
        total_projects: 12,
        active_projects: 8,
        completed_projects: 3,
        avg_health_score: 87.5,
        total_lines_of_code: 156420
      });
    }
  };

  const getProjectIcon = (type: string) => {
    switch (type) {
      case 'web_app': return '🌐';
      case 'mobile_app': return '📱';
      case 'api': return '🔌';
      case 'library': return '📚';
      case 'data_science': return '📊';
      default: return '💻';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-growth-accent';
      case 'paused': return 'text-bloom-highlight';
      case 'completed': return 'text-golden-success';
      case 'archived': return 'text-earth-base/60';
      default: return 'text-earth-base';
    }
  };

  const getHealthColor = (score: number) => {
    if (score >= 80) return 'text-growth-accent';
    if (score >= 60) return 'text-bloom-highlight';
    return 'text-crimson-alert';
  };

  const filteredAndSortedProjects = projects
    .filter(project => {
      if (filterStatus !== 'all' && project.status !== filterStatus) return false;
      if (searchQuery && !project.name.toLowerCase().includes(searchQuery.toLowerCase())) return false;
      return true;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name': return a.name.localeCompare(b.name);
        case 'created_at': return b.created_at.getTime() - a.created_at.getTime();
        case 'updated_at': return b.updated_at.getTime() - a.updated_at.getTime();
        case 'health_score': return b.health_score - a.health_score;
        case 'status': return a.status.localeCompare(b.status);
        default: return 0;
      }
    });

  const renderProjectCard = (project: Project) => (
    <div
      key={project.id}
      className="bg-crystal-clear rounded-lg p-6 border border-growth-accent/20 hover-bloom transition-all cursor-pointer group"
    >
      {/* Project Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">{getProjectIcon(project.type)}</span>
          <div>
            <h3 className="text-lg font-semibold text-forest-primary group-hover:text-growth-accent transition-colors">
              {project.name}
            </h3>
            <p className="text-sm text-earth-base/70 capitalize">{project.type.replace('_', ' ')}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getStatusColor(project.status)} bg-current/10`}>
            {project.status}
          </span>
        </div>
      </div>

      {/* Project Description */}
      <p className="text-sm text-earth-base/80 mb-4 line-clamp-2">
        {project.description}
      </p>

      {/* Health Score */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-earth-base/70">Health Score</span>
          <span className={`text-sm font-bold ${getHealthColor(project.health_score)}`}>
            {project.health_score.toFixed(1)}%
          </span>
        </div>
        <div className="w-full bg-frost-light rounded-full h-2">
          <div 
            className={`h-2 rounded-full transition-all duration-500 ${
              project.health_score >= 80 ? 'bg-growth-gradient' :
              project.health_score >= 60 ? 'bg-gradient-to-r from-bloom-highlight to-golden-success' :
              'bg-gradient-to-r from-crimson-alert to-amber-data'
            }`}
            style={{ width: `${project.health_score}%` }}
          />
        </div>
      </div>

      {/* Metadata */}
      <div className="grid grid-cols-2 gap-3 mb-4 text-xs">
        {project.metadata.language && (
          <div className="flex items-center space-x-1">
            <span className="text-earth-base/60">Language:</span>
            <span className="font-medium">{project.metadata.language}</span>
          </div>
        )}
        {project.metadata.lines_of_code && (
          <div className="flex items-center space-x-1">
            <span className="text-earth-base/60">LOC:</span>
            <span className="font-medium">{project.metadata.lines_of_code.toLocaleString()}</span>
          </div>
        )}
        {project.metadata.test_coverage && (
          <div className="flex items-center space-x-1">
            <span className="text-earth-base/60">Coverage:</span>
            <span className="font-medium">{project.metadata.test_coverage}%</span>
          </div>
        )}
        {project.metadata.complexity_score && (
          <div className="flex items-center space-x-1">
            <span className="text-earth-base/60">Complexity:</span>
            <span className="font-medium">{project.metadata.complexity_score}/10</span>
          </div>
        )}
      </div>

      {/* AI Features */}
      <div className="flex items-center space-x-2 mb-4">
        <span className="text-xs text-earth-base/60">AI Features:</span>
        <div className="flex space-x-1">
          {Object.entries(project.ai_features).map(([feature, enabled]) => (
            <span
              key={feature}
              className={`w-2 h-2 rounded-full ${
                enabled ? 'bg-growth-accent animate-breathe' : 'bg-earth-base/20'
              }`}
              title={feature.replace('_', ' ')}
            />
          ))}
        </div>
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between text-xs text-earth-base/60">
        <span>Updated {project.updated_at.toLocaleDateString()}</span>
        <div className="flex space-x-2">
          <button
            onClick={() => setShowAnalytics(project.id)}
            className="hover:text-growth-accent transition-colors"
            title="View Analytics"
          >
            📊
          </button>
          <button className="hover:text-growth-accent transition-colors" title="Settings">⚙️</button>
          <button className="hover:text-growth-accent transition-colors" title="Deploy">🚀</button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-h2 text-forest-primary">🏗️ Project Forest</h1>
          <p className="text-earth-base/70">Manage your digital ecosystem</p>
        </div>
        
        <button
          onClick={() => setShowCreateWizard(true)}
          className="px-6 py-3 bg-growth-gradient text-crystal-clear rounded-lg font-medium hover-bloom transition-all"
        >
          🌱 New Project
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {[
          { label: 'Total Projects', value: projectStats.total_projects, icon: '📁', color: 'text-forest-primary' },
          { label: 'Active', value: projectStats.active_projects, icon: '🌱', color: 'text-growth-accent' },
          { label: 'Completed', value: projectStats.completed_projects, icon: '✅', color: 'text-golden-success' },
          { label: 'Avg Health', value: `${projectStats.avg_health_score}%`, icon: '💚', color: 'text-growth-accent' },
          { label: 'Total LOC', value: projectStats.total_lines_of_code.toLocaleString(), icon: '📝', color: 'text-bloom-highlight' }
        ].map((stat) => (
          <div key={stat.label} className="bg-crystal-clear rounded-lg p-4 border border-growth-accent/20 hover-bloom">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-earth-base/60">{stat.label}</p>
                <p className={`text-lg font-bold ${stat.color}`}>{stat.value}</p>
              </div>
              <span className="text-2xl">{stat.icon}</span>
            </div>
          </div>
        ))}
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between bg-crystal-clear rounded-lg p-4 border border-growth-accent/20">
        <div className="flex items-center space-x-4">
          {/* Search */}
          <div className="relative">
            <input
              type="text"
              placeholder="Search projects..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8 pr-4 py-2 border border-growth-accent/20 rounded-lg bg-mist-gradient focus:outline-none focus:ring-2 focus:ring-growth-accent/50"
            />
            <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-earth-base/60">🔍</span>
          </div>

          {/* Filter */}
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-3 py-2 border border-growth-accent/20 rounded-lg bg-mist-gradient focus:outline-none focus:ring-2 focus:ring-growth-accent/50"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="paused">Paused</option>
            <option value="completed">Completed</option>
            <option value="archived">Archived</option>
          </select>

          {/* Sort */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as SortBy)}
            className="px-3 py-2 border border-growth-accent/20 rounded-lg bg-mist-gradient focus:outline-none focus:ring-2 focus:ring-growth-accent/50"
          >
            <option value="updated_at">Last Updated</option>
            <option value="name">Name</option>
            <option value="created_at">Created</option>
            <option value="health_score">Health Score</option>
            <option value="status">Status</option>
          </select>
        </div>

        {/* View Mode */}
        <div className="flex items-center space-x-2">
          {(['grid', 'list', 'tree'] as ViewMode[]).map((mode) => (
            <button
              key={mode}
              onClick={() => setViewMode(mode)}
              className={`p-2 rounded-lg transition-all hover-bloom ${
                viewMode === mode 
                  ? 'bg-growth-accent text-crystal-clear' 
                  : 'bg-mist-gradient text-earth-base hover:bg-growth-accent/10'
              }`}
              title={`${mode} view`}
            >
              {mode === 'grid' ? '⊞' : mode === 'list' ? '☰' : '🌳'}
            </button>
          ))}
        </div>
      </div>

      {/* Projects Grid */}
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-growth-accent rounded-full animate-bounce"></div>
            <div className="w-3 h-3 bg-growth-accent rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
            <div className="w-3 h-3 bg-growth-accent rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
            <span className="text-earth-base/60 ml-3">Growing your project forest...</span>
          </div>
        </div>
      ) : (
        <div className={`${
          viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' :
          viewMode === 'list' ? 'space-y-4' :
          'space-y-2'
        }`}>
          {filteredAndSortedProjects.map(renderProjectCard)}
        </div>
      )}

      {filteredAndSortedProjects.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🌱</div>
          <h3 className="text-xl font-semibold text-forest-primary mb-2">No Projects Found</h3>
          <p className="text-earth-base/60 mb-4">
            {searchQuery || filterStatus !== 'all' 
              ? 'Try adjusting your search or filters' 
              : 'Start by creating your first project'}
          </p>
          <button
            onClick={() => setShowCreateWizard(true)}
            className="px-6 py-3 bg-growth-gradient text-crystal-clear rounded-lg font-medium hover-bloom transition-all"
          >
            🌱 Create First Project
          </button>
        </div>
      )}

      {/* Project Creation Wizard */}
      <ProjectCreationWizard
        isOpen={showCreateWizard}
        onClose={() => setShowCreateWizard(false)}
        onProjectCreated={(project) => {
          console.log('New project created:', project);
          loadProjects(); // Refresh the project list
        }}
      />

      {/* Project Analytics */}
      {showAnalytics && (
        <ProjectAnalytics
          projectId={showAnalytics}
          onClose={() => setShowAnalytics(null)}
        />
      )}
    </div>
  );
};
