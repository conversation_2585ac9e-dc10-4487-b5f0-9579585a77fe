# backend/app/agent/context_manager.py
import logging
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
import tiktoken

try:
    from ...config import settings
    from ...llm_client import openrouter_client
except ImportError:
    # For direct execution
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
    from config import settings
    from llm_client import openrouter_client

# Import Phase 3 modules for enhanced context
try:
    from .code_analyzer import code_analyzer
    from .repo_context import repo_context
    from .change_tracker import change_tracker
except ImportError:
    # Graceful fallback if Phase 3 modules aren't available
    code_analyzer = None
    repo_context = None
    change_tracker = None

logger = logging.getLogger(__name__)


class ContextManager:
    """
    Manages conversation context, token counting, and summarization.
    Handles context window limits and prompt building.
    """
    
    def __init__(self):
        self.max_context_tokens = settings.max_token_limit
        self.summarization_trigger_ratio = settings.summarization_trigger
        self.post_summary_target_tokens = int(settings.max_token_limit * 0.3)  # 30% of max tokens
        
        # Initialize tokenizer for the code model
        try:
            # Try to get encoding for the specific model
            self.tokenizer = tiktoken.encoding_for_model("gpt-4")  # Fallback encoding
        except KeyError:
            # Use a default encoding if model-specific one isn't available
            self.tokenizer = tiktoken.get_encoding("cl100k_base")
        
        logger.info(f"Initialized ContextManager with max_tokens={self.max_context_tokens}")
    
    def count_tokens(self, text: str) -> int:
        """
        Count tokens in a text string.
        
        Args:
            text: Text to count tokens for
            
        Returns:
            Number of tokens
        """
        try:
            return len(self.tokenizer.encode(text))
        except Exception as e:
            logger.error(f"Failed to count tokens: {e}")
            # Fallback: rough estimation (4 chars per token)
            return len(text) // 4
    
    def count_messages_tokens(self, messages: List[Dict[str, str]]) -> int:
        """
        Count total tokens in a list of messages.
        
        Args:
            messages: List of message dictionaries
            
        Returns:
            Total token count
        """
        total_tokens = 0
        
        for message in messages:
            # Count tokens for role and content
            role_tokens = self.count_tokens(message.get("role", ""))
            content_tokens = self.count_tokens(message.get("content", ""))
            
            # Add some overhead for message formatting
            message_tokens = role_tokens + content_tokens + 4  # 4 tokens overhead per message
            total_tokens += message_tokens
        
        return total_tokens
    
    def build_system_prompt(self, task_context: str = "") -> str:
        """
        Build the system prompt for the AI agent.
        
        Args:
            task_context: Additional context about the current task
            
        Returns:
            System prompt string
        """
        base_prompt = """You are an AI coding assistant that helps developers analyze, modify, and test code repositories.

Your capabilities include:
- Analyzing code structure and dependencies
- Generating code changes and improvements
- Running tests and fixing issues
- Providing explanations and documentation
- Working with Git repositories

Guidelines:
- Always write clean, well-documented code
- Follow best practices and coding standards
- Test your changes thoroughly
- Provide clear explanations for your decisions
- Ask for clarification when requirements are unclear"""

        if task_context:
            base_prompt += f"\n\nCurrent task context:\n{task_context}"
        
        return base_prompt
    
    def should_summarize(self, messages: List[Dict[str, str]]) -> bool:
        """
        Determine if conversation should be summarized based on token count.
        
        Args:
            messages: Current conversation messages
            
        Returns:
            True if summarization is needed
        """
        current_tokens = self.count_messages_tokens(messages)
        trigger_threshold = int(self.max_context_tokens * self.summarization_trigger_ratio)
        
        should_summarize = current_tokens >= trigger_threshold
        
        if should_summarize:
            logger.info(
                f"Summarization triggered: {current_tokens} tokens >= "
                f"{trigger_threshold} threshold ({self.summarization_trigger_ratio * 100}%)"
            )
        
        return should_summarize
    
    async def summarize_conversation(
        self, 
        messages: List[Dict[str, str]],
        preserve_recent: int = 3
    ) -> Tuple[List[Dict[str, str]], str]:
        """
        Summarize conversation history to reduce token count.
        
        Args:
            messages: Full conversation history
            preserve_recent: Number of recent messages to preserve
            
        Returns:
            Tuple of (new_messages_list, summary_text)
        """
        try:
            if len(messages) <= preserve_recent + 1:  # +1 for system message
                logger.info("Not enough messages to summarize")
                return messages, ""
            
            # Separate system message, messages to summarize, and recent messages
            system_message = messages[0] if messages and messages[0].get("role") == "system" else None
            recent_messages = messages[-preserve_recent:] if preserve_recent > 0 else []
            
            # Messages to summarize (everything except system and recent)
            start_idx = 1 if system_message else 0
            end_idx = len(messages) - preserve_recent if preserve_recent > 0 else len(messages)
            messages_to_summarize = messages[start_idx:end_idx]
            
            if not messages_to_summarize:
                logger.info("No messages to summarize")
                return messages, ""
            
            # Build conversation text for summarization
            conversation_text = ""
            for msg in messages_to_summarize:
                role = msg.get("role", "unknown")
                content = msg.get("content", "")
                conversation_text += f"{role.upper()}: {content}\n\n"
            
            logger.info(f"Summarizing {len(messages_to_summarize)} messages")
            
            # Generate summary
            summary = await openrouter_client.summarize_text(
                conversation_text,
                max_length=self.post_summary_target_tokens
            )
            
            # Build new message list
            new_messages = []
            
            # Add system message if it exists
            if system_message:
                new_messages.append(system_message)
            
            # Add summary as a system message
            summary_message = {
                "role": "system",
                "content": f"Previous conversation summary:\n{summary}"
            }
            new_messages.append(summary_message)
            
            # Add recent messages
            new_messages.extend(recent_messages)
            
            # Verify token reduction
            old_tokens = self.count_messages_tokens(messages)
            new_tokens = self.count_messages_tokens(new_messages)
            
            logger.info(
                f"Summarization complete: {old_tokens} -> {new_tokens} tokens "
                f"({((old_tokens - new_tokens) / old_tokens * 100):.1f}% reduction)"
            )
            
            return new_messages, summary
            
        except Exception as e:
            logger.error(f"Failed to summarize conversation: {e}")
            # Return original messages if summarization fails
            return messages, ""
    
    def validate_context_size(self, messages: List[Dict[str, str]]) -> bool:
        """
        Validate that the context size is within limits.
        
        Args:
            messages: Messages to validate
            
        Returns:
            True if context size is acceptable
        """
        token_count = self.count_messages_tokens(messages)
        is_valid = token_count <= self.max_context_tokens
        
        if not is_valid:
            logger.warning(
                f"Context size validation failed: {token_count} tokens > "
                f"{self.max_context_tokens} limit"
            )
        
        return is_valid
    
    async def prepare_context(
        self, 
        messages: List[Dict[str, str]],
        task_context: str = "",
        auto_summarize: bool = True
    ) -> List[Dict[str, str]]:
        """
        Prepare conversation context for LLM request.
        
        Args:
            messages: Current conversation messages
            task_context: Additional task context
            auto_summarize: Whether to auto-summarize if needed
            
        Returns:
            Prepared messages list ready for LLM
        """
        try:
            # Ensure we have a system message
            if not messages or messages[0].get("role") != "system":
                system_prompt = self.build_system_prompt(task_context)
                messages = [{"role": "system", "content": system_prompt}] + messages
            elif task_context:
                # Update existing system message with task context
                current_system = messages[0]["content"]
                updated_system = f"{current_system}\n\nCurrent task context:\n{task_context}"
                messages[0]["content"] = updated_system
            
            # Check if summarization is needed
            if auto_summarize and self.should_summarize(messages):
                messages, _ = await self.summarize_conversation(messages)
            
            # Final validation
            if not self.validate_context_size(messages):
                logger.error("Context size still too large after preparation")
                # Emergency truncation: keep only system message and last few messages
                if len(messages) > 4:
                    messages = [messages[0]] + messages[-3:]
                    logger.warning("Applied emergency context truncation")
            
            return messages
            
        except Exception as e:
            logger.error(f"Failed to prepare context: {e}")
            # Return minimal context on failure
            system_prompt = self.build_system_prompt(task_context)
            return [{"role": "system", "content": system_prompt}]

    # Phase 5 Enhanced Methods

    async def integrate_semantic_search(self, query: str, project_path: Union[str, Path],
                                       max_results: int = 5) -> Dict[str, Any]:
        """
        Integrate semantic code search for relevant context.

        Args:
            query: Search query
            project_path: Path to project directory
            max_results: Maximum number of results to return

        Returns:
            Dict with search results
        """
        try:
            if not code_analyzer:
                return {
                    'status': 'skipped',
                    'reason': 'Code analyzer not available'
                }

            # Perform semantic search
            search_result = await code_analyzer.search_similar_code(
                query, project_path, limit=max_results
            )

            if search_result['status'] != 'success':
                return search_result

            # Format results for context inclusion
            relevant_code = []
            for result in search_result.get('results', []):
                relevant_code.append({
                    'file': result.get('file_path', 'unknown'),
                    'score': result.get('score', 0),
                    'content': result.get('content', ''),
                    'line_start': result.get('line_start', 0),
                    'line_end': result.get('line_end', 0)
                })

            return {
                'status': 'success',
                'query': query,
                'relevant_code': relevant_code,
                'total_results': len(relevant_code)
            }

        except Exception as e:
            logger.error(f"Failed to integrate semantic search: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def build_task_context(self, project_path: Union[str, Path],
                                task_description: str,
                                include_changes: bool = True) -> Dict[str, Any]:
        """
        Build comprehensive task context with file change tracking.

        Args:
            project_path: Path to project directory
            task_description: Description of the task
            include_changes: Whether to include recent file changes

        Returns:
            Dict with task context
        """
        try:
            context = {
                'task_description': task_description,
                'project_path': str(project_path),
                'timestamp': time.time(),
                'project_summary': {},
                'relevant_files': [],
                'recent_changes': [],
                'semantic_context': {}
            }

            # Get project summary if repo_context is available
            if repo_context:
                summary_result = await repo_context.generate_project_summary(project_path)
                if summary_result['status'] == 'success':
                    context['project_summary'] = summary_result.get('summary', {})

            # Get task-specific context
            if repo_context:
                task_context_result = await repo_context.generate_task_context(
                    project_path, task_description
                )
                if task_context_result['status'] == 'success':
                    context['relevant_files'] = task_context_result.get('relevant_files', [])

            # Include recent changes if requested
            if include_changes and change_tracker:
                changes_result = await change_tracker.get_recent_changes(limit=10)
                if changes_result['status'] == 'success':
                    context['recent_changes'] = changes_result.get('statistics', {})

            # Get semantic context using search
            semantic_result = await self.integrate_semantic_search(
                task_description, project_path, max_results=3
            )
            if semantic_result['status'] == 'success':
                context['semantic_context'] = semantic_result.get('relevant_code', [])

            return {
                'status': 'success',
                'context': context
            }

        except Exception as e:
            logger.error(f"Failed to build task context: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def retrieve_relevant_code(self, query: str, project_path: Union[str, Path],
                                   similarity_threshold: float = 0.7) -> Dict[str, Any]:
        """
        Retrieve relevant code using embeddings and similarity.

        Args:
            query: Query to search for
            project_path: Path to project directory
            similarity_threshold: Minimum similarity score

        Returns:
            Dict with relevant code snippets
        """
        try:
            # Use semantic search to find relevant code
            search_result = await self.integrate_semantic_search(
                query, project_path, max_results=10
            )

            if search_result['status'] != 'success':
                return search_result

            # Filter by similarity threshold
            relevant_code = []
            for result in search_result.get('relevant_code', []):
                if result.get('score', 0) >= similarity_threshold:
                    relevant_code.append(result)

            # Sort by relevance score
            relevant_code.sort(key=lambda x: x.get('score', 0), reverse=True)

            return {
                'status': 'success',
                'query': query,
                'threshold': similarity_threshold,
                'relevant_code': relevant_code,
                'total_found': len(relevant_code)
            }

        except Exception as e:
            logger.error(f"Failed to retrieve relevant code: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def maintain_conversation_memory(self, messages: List[Dict[str, str]],
                                         task_id: str,
                                         max_memory_turns: int = 50) -> List[Dict[str, str]]:
        """
        Maintain conversation memory with task persistence.

        Args:
            messages: Current conversation messages
            task_id: Task ID for persistence
            max_memory_turns: Maximum conversation turns to remember

        Returns:
            Updated messages with memory management
        """
        try:
            # Limit conversation length
            if len(messages) > max_memory_turns:
                # Keep system message and recent messages
                system_msg = messages[0] if messages and messages[0].get('role') == 'system' else None
                recent_messages = messages[-(max_memory_turns-1):] if max_memory_turns > 1 else []

                if system_msg:
                    messages = [system_msg] + recent_messages
                else:
                    messages = recent_messages

                logger.info(f"Task {task_id}: Trimmed conversation to {len(messages)} messages")

            # Add task context to system message if not present
            if messages and messages[0].get('role') == 'system':
                content = messages[0]['content']
                if f"Task ID: {task_id}" not in content:
                    messages[0]['content'] = f"{content}\n\nTask ID: {task_id}"

            return messages

        except Exception as e:
            logger.error(f"Failed to maintain conversation memory: {e}")
            return messages

    def optimize_context_window(self, messages: List[Dict[str, str]],
                               target_tokens: Optional[int] = None) -> List[Dict[str, str]]:
        """
        Optimize context window usage for better LLM performance.

        Args:
            messages: Messages to optimize
            target_tokens: Target token count (defaults to 80% of max)

        Returns:
            Optimized messages list
        """
        try:
            if target_tokens is None:
                target_tokens = int(self.max_context_tokens * 0.8)  # 80% of max

            current_tokens = self.count_messages_tokens(messages)

            if current_tokens <= target_tokens:
                return messages

            logger.info(f"Optimizing context: {current_tokens} -> target {target_tokens} tokens")

            # Keep system message and progressively remove older messages
            if not messages:
                return messages

            system_msg = messages[0] if messages[0].get('role') == 'system' else None
            other_messages = messages[1:] if system_msg else messages

            # Binary search for optimal message count
            left, right = 1, len(other_messages)
            best_messages = messages

            while left <= right:
                mid = (left + right) // 2
                test_messages = ([system_msg] if system_msg else []) + other_messages[-mid:]
                test_tokens = self.count_messages_tokens(test_messages)

                if test_tokens <= target_tokens:
                    best_messages = test_messages
                    left = mid + 1
                else:
                    right = mid - 1

            optimized_tokens = self.count_messages_tokens(best_messages)
            logger.info(f"Context optimized: {current_tokens} -> {optimized_tokens} tokens")

            return best_messages

        except Exception as e:
            logger.error(f"Failed to optimize context window: {e}")
            return messages


# Global context manager instance
context_manager = ContextManager()
