"""
🛡️ Advanced Rate Limiter - Intelligent Request Throttling

This module provides sophisticated rate limiting with:
- Multiple rate limiting algorithms (Token Bucket, Sliding Window, Fixed Window)
- Adaptive rate limiting based on system load
- User-specific and endpoint-specific limits
- Burst handling and grace periods
- Rate limit analytics and monitoring
- Distributed rate limiting support
"""

import asyncio
import time
from collections import defaultdict, deque
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)


class RateLimitStrategy(str, Enum):
    """Rate limiting strategies."""
    TOKEN_BUCKET = "token_bucket"          # Token bucket algorithm
    SLIDING_WINDOW = "sliding_window"      # Sliding window counter
    FIXED_WINDOW = "fixed_window"          # Fixed window counter
    ADAPTIVE = "adaptive"                  # Adaptive based on system load


class RateLimitRule(BaseModel):
    """Rate limiting rule configuration."""
    
    rule_id: str = Field(..., description="Unique rule identifier")
    name: str = Field(..., description="Rule name")
    strategy: RateLimitStrategy = Field(..., description="Rate limiting strategy")
    
    # Rate limits
    requests_per_minute: int = Field(..., description="Requests per minute limit")
    requests_per_hour: int = Field(..., description="Requests per hour limit")
    requests_per_day: int = Field(..., description="Requests per day limit")
    
    # Burst handling
    burst_limit: int = Field(..., description="Maximum burst requests")
    burst_window_seconds: int = Field(default=60, description="Burst window in seconds")
    
    # Targeting
    applies_to_users: List[str] = Field(default_factory=list, description="Specific users (empty = all)")
    applies_to_endpoints: List[str] = Field(default_factory=list, description="Specific endpoints (empty = all)")
    applies_to_ips: List[str] = Field(default_factory=list, description="Specific IPs (empty = all)")
    
    # Behavior
    block_on_exceed: bool = Field(default=True, description="Block requests when limit exceeded")
    grace_period_seconds: int = Field(default=0, description="Grace period before blocking")
    
    # Priority and status
    priority: int = Field(default=100, description="Rule priority (lower = higher priority)")
    enabled: bool = Field(default=True, description="Whether rule is enabled")
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.now, description="Rule creation time")
    updated_at: datetime = Field(default_factory=datetime.now, description="Rule last update time")


class RateLimitResult(BaseModel):
    """Rate limit check result."""
    
    allowed: bool = Field(..., description="Whether request is allowed")
    rule_matched: Optional[str] = Field(None, description="Matched rule ID")
    
    # Current usage
    current_minute_count: int = Field(default=0, description="Current minute request count")
    current_hour_count: int = Field(default=0, description="Current hour request count")
    current_day_count: int = Field(default=0, description="Current day request count")
    
    # Limits
    minute_limit: int = Field(default=0, description="Minute limit")
    hour_limit: int = Field(default=0, description="Hour limit")
    day_limit: int = Field(default=0, description="Day limit")
    
    # Timing
    reset_time: datetime = Field(..., description="When limits reset")
    retry_after_seconds: int = Field(default=0, description="Retry after seconds")
    
    # Additional info
    burst_used: int = Field(default=0, description="Burst capacity used")
    grace_period_remaining: int = Field(default=0, description="Grace period remaining seconds")
    
    # Headers for response
    headers: Dict[str, str] = Field(default_factory=dict, description="Rate limit headers")


class TokenBucket:
    """Token bucket implementation for rate limiting."""
    
    def __init__(self, capacity: int, refill_rate: float):
        self.capacity = capacity
        self.refill_rate = refill_rate  # tokens per second
        self.tokens = capacity
        self.last_refill = time.time()
    
    def consume(self, tokens: int = 1) -> bool:
        """Try to consume tokens from bucket."""
        now = time.time()
        
        # Refill tokens
        time_passed = now - self.last_refill
        self.tokens = min(self.capacity, self.tokens + time_passed * self.refill_rate)
        self.last_refill = now
        
        # Check if we have enough tokens
        if self.tokens >= tokens:
            self.tokens -= tokens
            return True
        
        return False
    
    def get_tokens(self) -> float:
        """Get current token count."""
        now = time.time()
        time_passed = now - self.last_refill
        return min(self.capacity, self.tokens + time_passed * self.refill_rate)


class SlidingWindow:
    """Sliding window counter for rate limiting."""
    
    def __init__(self, window_size_seconds: int, max_requests: int):
        self.window_size = window_size_seconds
        self.max_requests = max_requests
        self.requests: deque = deque()
    
    def add_request(self) -> bool:
        """Add request and check if within limit."""
        now = time.time()
        
        # Remove old requests outside window
        while self.requests and self.requests[0] <= now - self.window_size:
            self.requests.popleft()
        
        # Check if we're at limit
        if len(self.requests) >= self.max_requests:
            return False
        
        # Add current request
        self.requests.append(now)
        return True
    
    def get_count(self) -> int:
        """Get current request count in window."""
        now = time.time()
        
        # Remove old requests
        while self.requests and self.requests[0] <= now - self.window_size:
            self.requests.popleft()
        
        return len(self.requests)


class RateLimiter:
    """
    🛡️ Advanced Rate Limiter
    
    Provides sophisticated rate limiting with multiple algorithms,
    adaptive behavior, and comprehensive monitoring.
    """
    
    def __init__(self):
        # Rate limiting rules
        self.rules: Dict[str, RateLimitRule] = {}
        
        # Token buckets for different keys
        self.token_buckets: Dict[str, TokenBucket] = {}
        
        # Sliding windows for different keys
        self.sliding_windows: Dict[str, SlidingWindow] = {}
        
        # Fixed window counters
        self.fixed_windows: Dict[str, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        self.window_resets: Dict[str, datetime] = {}
        
        # Request tracking
        self.request_history: Dict[str, List[datetime]] = defaultdict(list)
        self.blocked_requests: Dict[str, int] = defaultdict(int)
        
        # Grace periods
        self.grace_periods: Dict[str, datetime] = {}
        
        # System load tracking
        self.system_load = 0.0
        self.adaptive_multiplier = 1.0
        
        # Statistics
        self.stats = {
            "total_requests": 0,
            "blocked_requests": 0,
            "rules_triggered": defaultdict(int),
            "average_load": 0.0
        }
    
    async def check_rate_limit(
        self,
        identifier: str,
        endpoint: str = "",
        user_id: str = "",
        ip_address: str = "",
        request_weight: int = 1
    ) -> RateLimitResult:
        """Check if request is within rate limits."""
        self.stats["total_requests"] += 1
        
        # Find applicable rules
        applicable_rules = await self._find_applicable_rules(endpoint, user_id, ip_address)
        
        if not applicable_rules:
            # No rules apply, allow request
            return RateLimitResult(
                allowed=True,
                reset_time=datetime.now() + timedelta(minutes=1),
                headers=self._generate_headers(None, 0, 0, 0)
            )
        
        # Check each rule (highest priority first)
        for rule in sorted(applicable_rules, key=lambda r: r.priority):
            result = await self._check_rule(identifier, rule, request_weight)
            
            if not result.allowed:
                self.stats["blocked_requests"] += 1
                self.stats["rules_triggered"][rule.rule_id] += 1
                self.blocked_requests[identifier] += 1
                
                logger.warning(f"Rate limit exceeded for {identifier} on rule {rule.name}")
                return result
        
        # All rules passed, allow request
        best_rule = applicable_rules[0]  # Use highest priority rule for headers
        return await self._check_rule(identifier, best_rule, request_weight, consume=True)
    
    async def add_rule(self, rule: RateLimitRule) -> bool:
        """Add a new rate limiting rule."""
        try:
            self.rules[rule.rule_id] = rule
            logger.info(f"Added rate limiting rule: {rule.name}")
            return True
        except Exception as e:
            logger.error(f"Failed to add rate limiting rule: {e}")
            return False
    
    async def remove_rule(self, rule_id: str) -> bool:
        """Remove a rate limiting rule."""
        if rule_id in self.rules:
            del self.rules[rule_id]
            logger.info(f"Removed rate limiting rule: {rule_id}")
            return True
        return False
    
    async def update_rule(self, rule: RateLimitRule) -> bool:
        """Update an existing rate limiting rule."""
        if rule.rule_id in self.rules:
            rule.updated_at = datetime.now()
            self.rules[rule.rule_id] = rule
            logger.info(f"Updated rate limiting rule: {rule.name}")
            return True
        return False
    
    async def get_usage_stats(self, identifier: str) -> Dict[str, Any]:
        """Get usage statistics for identifier."""
        now = datetime.now()
        
        # Get request history
        history = self.request_history.get(identifier, [])
        
        # Calculate counts for different windows
        minute_count = len([req for req in history if now - req < timedelta(minutes=1)])
        hour_count = len([req for req in history if now - req < timedelta(hours=1)])
        day_count = len([req for req in history if now - req < timedelta(days=1)])
        
        return {
            "identifier": identifier,
            "requests_last_minute": minute_count,
            "requests_last_hour": hour_count,
            "requests_last_day": day_count,
            "total_blocked": self.blocked_requests.get(identifier, 0),
            "in_grace_period": identifier in self.grace_periods,
            "grace_period_expires": self.grace_periods.get(identifier),
            "applicable_rules": [rule.name for rule in await self._find_applicable_rules("", "", identifier)]
        }
    
    async def reset_limits(self, identifier: str) -> bool:
        """Reset rate limits for identifier."""
        try:
            # Clear token bucket
            if identifier in self.token_buckets:
                del self.token_buckets[identifier]
            
            # Clear sliding window
            if identifier in self.sliding_windows:
                del self.sliding_windows[identifier]
            
            # Clear fixed windows
            if identifier in self.fixed_windows:
                del self.fixed_windows[identifier]
            
            # Clear history
            if identifier in self.request_history:
                del self.request_history[identifier]
            
            # Clear blocked count
            if identifier in self.blocked_requests:
                del self.blocked_requests[identifier]
            
            # Clear grace period
            if identifier in self.grace_periods:
                del self.grace_periods[identifier]
            
            logger.info(f"Reset rate limits for {identifier}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to reset rate limits for {identifier}: {e}")
            return False
    
    async def update_system_load(self, load: float) -> None:
        """Update system load for adaptive rate limiting."""
        self.system_load = load
        
        # Calculate adaptive multiplier
        if load > 0.9:
            self.adaptive_multiplier = 0.5  # Reduce limits by 50%
        elif load > 0.8:
            self.adaptive_multiplier = 0.7  # Reduce limits by 30%
        elif load > 0.7:
            self.adaptive_multiplier = 0.85  # Reduce limits by 15%
        else:
            self.adaptive_multiplier = 1.0  # Normal limits
        
        # Update average load
        self.stats["average_load"] = (self.stats["average_load"] * 0.9 + load * 0.1)
    
    async def _find_applicable_rules(
        self,
        endpoint: str,
        user_id: str,
        ip_address: str
    ) -> List[RateLimitRule]:
        """Find rules that apply to the request."""
        applicable_rules = []
        
        for rule in self.rules.values():
            if not rule.enabled:
                continue
            
            # Check endpoint filter
            if rule.applies_to_endpoints and endpoint not in rule.applies_to_endpoints:
                continue
            
            # Check user filter
            if rule.applies_to_users and user_id not in rule.applies_to_users:
                continue
            
            # Check IP filter
            if rule.applies_to_ips and ip_address not in rule.applies_to_ips:
                continue
            
            applicable_rules.append(rule)
        
        return applicable_rules
    
    async def _check_rule(
        self,
        identifier: str,
        rule: RateLimitRule,
        request_weight: int,
        consume: bool = False
    ) -> RateLimitResult:
        """Check a specific rule against identifier."""
        now = datetime.now()
        
        # Apply adaptive multiplier for adaptive strategy
        if rule.strategy == RateLimitStrategy.ADAPTIVE:
            minute_limit = int(rule.requests_per_minute * self.adaptive_multiplier)
            hour_limit = int(rule.requests_per_hour * self.adaptive_multiplier)
            day_limit = int(rule.requests_per_day * self.adaptive_multiplier)
        else:
            minute_limit = rule.requests_per_minute
            hour_limit = rule.requests_per_hour
            day_limit = rule.requests_per_day
        
        # Check grace period
        if identifier in self.grace_periods:
            if now < self.grace_periods[identifier]:
                grace_remaining = int((self.grace_periods[identifier] - now).total_seconds())
                return RateLimitResult(
                    allowed=True,
                    rule_matched=rule.rule_id,
                    minute_limit=minute_limit,
                    hour_limit=hour_limit,
                    day_limit=day_limit,
                    reset_time=now + timedelta(minutes=1),
                    grace_period_remaining=grace_remaining,
                    headers=self._generate_headers(rule, 0, 0, 0)
                )
            else:
                del self.grace_periods[identifier]
        
        # Check based on strategy
        if rule.strategy == RateLimitStrategy.TOKEN_BUCKET:
            return await self._check_token_bucket(identifier, rule, minute_limit, request_weight, consume)
        
        elif rule.strategy == RateLimitStrategy.SLIDING_WINDOW:
            return await self._check_sliding_window(identifier, rule, minute_limit, request_weight, consume)
        
        elif rule.strategy in [RateLimitStrategy.FIXED_WINDOW, RateLimitStrategy.ADAPTIVE]:
            return await self._check_fixed_window(identifier, rule, minute_limit, hour_limit, day_limit, request_weight, consume)
        
        # Default to allowing
        return RateLimitResult(
            allowed=True,
            rule_matched=rule.rule_id,
            reset_time=now + timedelta(minutes=1),
            headers=self._generate_headers(rule, 0, 0, 0)
        )
    
    async def _check_token_bucket(
        self,
        identifier: str,
        rule: RateLimitRule,
        minute_limit: int,
        request_weight: int,
        consume: bool
    ) -> RateLimitResult:
        """Check token bucket rate limit."""
        bucket_key = f"{identifier}:{rule.rule_id}"
        
        if bucket_key not in self.token_buckets:
            # Create new bucket
            refill_rate = minute_limit / 60.0  # tokens per second
            self.token_buckets[bucket_key] = TokenBucket(minute_limit, refill_rate)
        
        bucket = self.token_buckets[bucket_key]
        
        if consume:
            allowed = bucket.consume(request_weight)
        else:
            allowed = bucket.get_tokens() >= request_weight
        
        current_tokens = bucket.get_tokens()
        
        return RateLimitResult(
            allowed=allowed,
            rule_matched=rule.rule_id,
            current_minute_count=minute_limit - int(current_tokens),
            minute_limit=minute_limit,
            reset_time=datetime.now() + timedelta(minutes=1),
            retry_after_seconds=60 if not allowed else 0,
            headers=self._generate_headers(rule, minute_limit - int(current_tokens), 0, 0)
        )
    
    async def _check_sliding_window(
        self,
        identifier: str,
        rule: RateLimitRule,
        minute_limit: int,
        request_weight: int,
        consume: bool
    ) -> RateLimitResult:
        """Check sliding window rate limit."""
        window_key = f"{identifier}:{rule.rule_id}"
        
        if window_key not in self.sliding_windows:
            self.sliding_windows[window_key] = SlidingWindow(60, minute_limit)
        
        window = self.sliding_windows[window_key]
        
        if consume:
            allowed = window.add_request()
        else:
            allowed = window.get_count() < minute_limit
        
        current_count = window.get_count()
        
        return RateLimitResult(
            allowed=allowed,
            rule_matched=rule.rule_id,
            current_minute_count=current_count,
            minute_limit=minute_limit,
            reset_time=datetime.now() + timedelta(minutes=1),
            retry_after_seconds=60 if not allowed else 0,
            headers=self._generate_headers(rule, current_count, 0, 0)
        )
    
    async def _check_fixed_window(
        self,
        identifier: str,
        rule: RateLimitRule,
        minute_limit: int,
        hour_limit: int,
        day_limit: int,
        request_weight: int,
        consume: bool
    ) -> RateLimitResult:
        """Check fixed window rate limit."""
        now = datetime.now()
        window_key = f"{identifier}:{rule.rule_id}"
        
        # Initialize windows if needed
        if window_key not in self.fixed_windows:
            self.fixed_windows[window_key] = defaultdict(int)
            self.window_resets[window_key] = now
        
        # Reset windows if needed
        reset_time = self.window_resets[window_key]
        if now - reset_time >= timedelta(minutes=1):
            self.fixed_windows[window_key]["minute"] = 0
            self.window_resets[window_key] = now
        
        if now - reset_time >= timedelta(hours=1):
            self.fixed_windows[window_key]["hour"] = 0
        
        if now - reset_time >= timedelta(days=1):
            self.fixed_windows[window_key]["day"] = 0
        
        # Get current counts
        minute_count = self.fixed_windows[window_key]["minute"]
        hour_count = self.fixed_windows[window_key]["hour"]
        day_count = self.fixed_windows[window_key]["day"]
        
        # Check limits
        allowed = (
            minute_count + request_weight <= minute_limit and
            hour_count + request_weight <= hour_limit and
            day_count + request_weight <= day_limit
        )
        
        # Consume if allowed and consuming
        if allowed and consume:
            self.fixed_windows[window_key]["minute"] += request_weight
            self.fixed_windows[window_key]["hour"] += request_weight
            self.fixed_windows[window_key]["day"] += request_weight
            
            # Update request history
            self.request_history[identifier].append(now)
            
            # Clean old history
            cutoff = now - timedelta(days=1)
            self.request_history[identifier] = [
                req for req in self.request_history[identifier] if req > cutoff
            ]
        
        # Calculate retry after
        retry_after = 0
        if not allowed:
            if minute_count >= minute_limit:
                retry_after = 60
            elif hour_count >= hour_limit:
                retry_after = 3600
            elif day_count >= day_limit:
                retry_after = 86400
        
        return RateLimitResult(
            allowed=allowed,
            rule_matched=rule.rule_id,
            current_minute_count=minute_count,
            current_hour_count=hour_count,
            current_day_count=day_count,
            minute_limit=minute_limit,
            hour_limit=hour_limit,
            day_limit=day_limit,
            reset_time=now + timedelta(minutes=1),
            retry_after_seconds=retry_after,
            headers=self._generate_headers(rule, minute_count, hour_count, day_count)
        )
    
    def _generate_headers(
        self,
        rule: Optional[RateLimitRule],
        minute_count: int,
        hour_count: int,
        day_count: int
    ) -> Dict[str, str]:
        """Generate rate limit headers for response."""
        if rule is None:
            return {}
        
        return {
            "X-RateLimit-Limit-Minute": str(rule.requests_per_minute),
            "X-RateLimit-Limit-Hour": str(rule.requests_per_hour),
            "X-RateLimit-Limit-Day": str(rule.requests_per_day),
            "X-RateLimit-Remaining-Minute": str(max(0, rule.requests_per_minute - minute_count)),
            "X-RateLimit-Remaining-Hour": str(max(0, rule.requests_per_hour - hour_count)),
            "X-RateLimit-Remaining-Day": str(max(0, rule.requests_per_day - day_count)),
            "X-RateLimit-Reset": str(int((datetime.now() + timedelta(minutes=1)).timestamp())),
            "X-RateLimit-Rule": rule.name
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get rate limiting statistics."""
        return {
            **self.stats,
            "active_rules": len([r for r in self.rules.values() if r.enabled]),
            "total_rules": len(self.rules),
            "active_buckets": len(self.token_buckets),
            "active_windows": len(self.sliding_windows),
            "tracked_identifiers": len(self.request_history),
            "system_load": self.system_load,
            "adaptive_multiplier": self.adaptive_multiplier
        }


# Global rate limiter instance
_rate_limiter: Optional[RateLimiter] = None


def get_rate_limiter() -> RateLimiter:
    """Get the global rate limiter instance."""
    global _rate_limiter
    if _rate_limiter is None:
        _rate_limiter = RateLimiter()
    return _rate_limiter
