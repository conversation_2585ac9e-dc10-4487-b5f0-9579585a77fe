"""
API endpoints for Enhanced Error Recovery System

Provides REST API access to error handling functionality, notifications,
and system health monitoring.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional

from fastapi import APIRouter, HTTPException, Query, Path
from pydantic import BaseModel, Field

from ..error_handling import (
    get_error_integration,
    ErrorCategory,
    NotificationLevel,
    RetryConfig
)
from ..error_handling.retry_manager import RetryStrategy
from ..pydantic_ai.agents import get_agent_error_health

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/error-handling", tags=["Error Handling"])

# Helper function to get error integration instance
def _get_error_integration():
    """Get the error integration instance."""
    return get_error_integration()


# Request/Response Models
class ErrorHandlingHealthResponse(BaseModel):
    """Response model for error handling health status."""
    overall_health: str = Field(..., description="Overall health status")
    error_handling_stats: Dict = Field(..., description="Error handling statistics")
    component_health: Dict = Field(..., description="Component health details")
    recommendations: List[str] = Field(..., description="Health recommendations")
    timestamp: datetime = Field(default_factory=datetime.now)


class NotificationResponse(BaseModel):
    """Response model for notifications."""
    id: str
    title: str
    message: str
    level: str
    type: str
    timestamp: str
    operation: str
    suggested_actions: List[str]
    user_actions_required: List[str]
    help_links: List[str]
    recovery_in_progress: bool
    recovery_action: Optional[str]
    estimated_recovery_time: Optional[int]
    technical_details: Dict
    session_id: Optional[str]


class RetryConfigRequest(BaseModel):
    """Request model for retry configuration."""
    max_attempts: int = Field(default=3, ge=1, le=10)
    strategy: str = Field(default="exponential")
    base_delay: float = Field(default=1.0, ge=0.1, le=60.0)
    max_delay: float = Field(default=60.0, ge=1.0, le=300.0)
    jitter: bool = Field(default=True)
    use_circuit_breaker: bool = Field(default=True)


class ErrorTestRequest(BaseModel):
    """Request model for error testing."""
    error_type: str = Field(..., description="Type of error to simulate")
    operation: str = Field(default="test_operation")
    session_id: Optional[str] = None
    user_id: Optional[str] = None


# Health and Status Endpoints
@router.get("/health", response_model=ErrorHandlingHealthResponse)
async def get_error_handling_health():
    """Get comprehensive error handling system health."""
    try:
        health_data = get_agent_error_health()
        return ErrorHandlingHealthResponse(**health_data)
    except Exception as e:
        logger.error(f"Failed to get error handling health: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve health status")


@router.get("/statistics")
async def get_error_statistics():
    """Get detailed error handling statistics."""
    try:
        error_integration = get_error_integration()

        # Get statistics from all components
        retry_stats = error_integration.retry_manager.retry_stats
        categorizer_stats = error_integration.error_categorizer.get_statistics()
        recovery_stats = error_integration.recovery_strategies.get_recovery_statistics()
        notification_stats = error_integration.notification_manager.get_statistics()

        return {
            "retry_manager": {
                "total_contexts": len(retry_stats),
                "context_details": retry_stats
            },
            "error_categorizer": categorizer_stats,
            "recovery_strategies": recovery_stats,
            "notification_manager": notification_stats,
            "integration_stats": {
                "total_errors_handled": error_integration.handled_errors,
                "successful_recoveries": error_integration.successful_recoveries,
                "failed_recoveries": error_integration.failed_recoveries,
                "recovery_rate": (
                    error_integration.successful_recoveries / error_integration.handled_errors
                    if error_integration.handled_errors > 0 else 0
                )
            }
        }
    except Exception as e:
        logger.error(f"Failed to get error statistics: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve statistics")


# Notification Management Endpoints
@router.get("/notifications", response_model=List[NotificationResponse])
async def get_notifications(
    session_id: Optional[str] = Query(None, description="Filter by session ID"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    active_only: bool = Query(False, description="Return only active notifications"),
    limit: int = Query(50, ge=1, le=200, description="Maximum number of notifications")
):
    """Get notifications with optional filtering."""
    try:
        error_integration = _get_error_integration()

        if active_only:
            notifications = error_integration.notification_manager.get_active_notifications(
                session_id=session_id,
                user_id=user_id
            )
        else:
            notifications = error_integration.notification_manager.get_notification_history(
                limit=limit,
                session_id=session_id,
                user_id=user_id
            )
        
        return [NotificationResponse(**notification.to_dict()) for notification in notifications]
    except Exception as e:
        logger.error(f"Failed to get notifications: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve notifications")


@router.delete("/notifications/{notification_id}")
async def dismiss_notification(notification_id: str = Path(..., description="Notification ID to dismiss")):
    """Dismiss a specific notification."""
    try:
        error_integration = _get_error_integration()
        success = error_integration.notification_manager.dismiss_notification(notification_id)
        if success:
            return {"message": "Notification dismissed successfully", "notification_id": notification_id}
        else:
            raise HTTPException(status_code=404, detail="Notification not found")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to dismiss notification {notification_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to dismiss notification")


# Configuration Endpoints
@router.get("/retry-config")
async def get_default_retry_config():
    """Get default retry configuration."""
    return {
        "coding_tasks": {
            "max_attempts": 3,
            "strategy": "exponential",
            "base_delay": 1.0,
            "max_delay": 30.0,
            "model_retry_attempts": 2,
            "tool_retry_attempts": 3,
            "use_circuit_breaker": True
        },
        "vision_tasks": {
            "max_attempts": 3,
            "strategy": "exponential",
            "base_delay": 2.0,
            "max_delay": 60.0,
            "model_retry_attempts": 2,
            "tool_retry_attempts": 2,
            "use_circuit_breaker": True,
            "rate_limit_delay": 45.0
        }
    }


@router.post("/test-error-handling")
async def test_error_handling(request: ErrorTestRequest):
    """Test the error handling system with simulated errors."""
    try:
        # Create appropriate error based on type
        if request.error_type == "model_retry":
            from pydantic_ai import ModelRetry
            error = ModelRetry("Test model retry error")
        elif request.error_type == "connection_error":
            error = ConnectionError("Test connection error")
        elif request.error_type == "timeout_error":
            error = TimeoutError("Test timeout error")
        elif request.error_type == "rate_limit":
            from pydantic_ai.exceptions import ModelHTTPError
            error = ModelHTTPError("Rate limit exceeded (429)")
        elif request.error_type == "validation_error":
            error = ValueError("Test validation error")
        else:
            error = Exception(f"Test {request.error_type} error")
        
        # Handle the error with the recovery system
        error_integration = _get_error_integration()
        result = await error_integration.handle_error_with_recovery(
            error=error,
            operation=request.operation,
            session_id=request.session_id,
            user_id=request.user_id,
            show_technical_details=True
        )
        
        return {
            "test_completed": True,
            "error_type": request.error_type,
            "handling_result": result
        }
    except Exception as e:
        logger.error(f"Error handling test failed: {e}")
        raise HTTPException(status_code=500, detail=f"Test failed: {str(e)}")


@router.post("/run-system-test")
async def run_comprehensive_system_test():
    """Run comprehensive error handling system test."""
    try:
        error_integration = _get_error_integration()
        test_results = await error_integration.test_error_handling_system()
        return test_results
    except Exception as e:
        logger.error(f"System test failed: {e}")
        raise HTTPException(status_code=500, detail=f"System test failed: {str(e)}")


# Circuit Breaker Management
@router.get("/circuit-breakers")
async def get_circuit_breaker_status():
    """Get status of all circuit breakers."""
    try:
        error_integration = _get_error_integration()
        circuit_breakers = error_integration.retry_manager.circuit_breakers
        status = {}
        
        for key, breaker in circuit_breakers.items():
            status[key] = {
                "state": breaker.state.value,
                "failure_count": breaker.failure_count,
                "success_count": breaker.success_count,
                "last_failure_time": breaker.last_failure_time,
                "is_request_allowed": breaker.is_request_allowed()
            }
        
        return {
            "circuit_breakers": status,
            "total_breakers": len(circuit_breakers)
        }
    except Exception as e:
        logger.error(f"Failed to get circuit breaker status: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve circuit breaker status")


@router.post("/circuit-breakers/{breaker_key}/reset")
async def reset_circuit_breaker(breaker_key: str = Path(..., description="Circuit breaker key to reset")):
    """Reset a specific circuit breaker."""
    try:
        error_integration = _get_error_integration()
        circuit_breakers = error_integration.retry_manager.circuit_breakers
        if breaker_key in circuit_breakers:
            breaker = circuit_breakers[breaker_key]
            breaker.failure_count = 0
            breaker.success_count = 0
            breaker.state = breaker.state.CLOSED  # Reset to closed state
            
            return {
                "message": f"Circuit breaker '{breaker_key}' reset successfully",
                "breaker_key": breaker_key,
                "new_state": "CLOSED"
            }
        else:
            raise HTTPException(status_code=404, detail="Circuit breaker not found")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to reset circuit breaker {breaker_key}: {e}")
        raise HTTPException(status_code=500, detail="Failed to reset circuit breaker")


# Error Categories and Patterns
@router.get("/error-categories")
async def get_error_categories():
    """Get available error categories and their descriptions."""
    categories = {}
    for category in ErrorCategory:
        categories[category.value] = {
            "name": category.value,
            "description": category.name.replace('_', ' ').title()
        }
    
    return {"categories": categories}


@router.get("/error-patterns")
async def get_error_patterns():
    """Get configured error patterns."""
    try:
        error_integration = _get_error_integration()
        patterns = []
        for pattern in error_integration.error_categorizer.patterns:
            patterns.append({
                "name": pattern.name,
                "category": pattern.category.value,
                "severity": pattern.severity.value,
                "recoverability": pattern.recoverability.value,
                "description": pattern.description,
                "suggested_actions": pattern.suggested_actions,
                "retry_recommended": pattern.retry_recommended,
                "max_retries": pattern.max_retries
            })
        
        return {"patterns": patterns, "total_patterns": len(patterns)}
    except Exception as e:
        logger.error(f"Failed to get error patterns: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve error patterns")
