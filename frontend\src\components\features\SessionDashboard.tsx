import React, { useState, useEffect } from 'react';

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  tokens?: number;
}

interface SessionData {
  id: string;
  name: string;
  project_id?: string;
  status: 'active' | 'paused' | 'completed' | 'archived';
  created_at: Date;
  updated_at: Date;
  message_count: number;
  total_tokens: number;
  duration: number; // in minutes
  tags: string[];
  summary?: string;
  ai_insights: {
    main_topics: string[];
    complexity_score: number;
    productivity_score: number;
    suggestions: string[];
  };
}

interface SessionStats {
  total_sessions: number;
  active_sessions: number;
  total_messages: number;
  total_tokens: number;
  avg_session_duration: number;
  most_productive_hour: number;
}

export const SessionDashboard: React.FC = () => {
  const [sessions, setSessions] = useState<SessionData[]>([]);
  const [stats, setStats] = useState<SessionStats>({
    total_sessions: 0,
    active_sessions: 0,
    total_messages: 0,
    total_tokens: 0,
    avg_session_duration: 0,
    most_productive_hour: 0
  });
  const [selectedSession, setSelectedSession] = useState<SessionData | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'updated_at' | 'created_at' | 'duration' | 'tokens'>('updated_at');

  useEffect(() => {
    loadSessions();
    loadStats();
  }, []);

  const loadSessions = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/sessions');
      if (response.ok) {
        const data = await response.json();
        const sessionsData = (data.sessions || []).map((session: any) => ({
          id: session.id,
          name: session.name || `Session ${session.id.slice(0, 8)}`,
          project_id: session.project_id,
          status: session.status || 'active',
          created_at: new Date(session.created_at || Date.now()),
          updated_at: new Date(session.updated_at || Date.now()),
          message_count: session.message_count || 0,
          total_tokens: session.total_tokens || 0,
          duration: session.duration || Math.random() * 120,
          tags: session.tags || [],
          summary: session.summary,
          ai_insights: session.ai_insights || {
            main_topics: ['AI Development', 'Frontend Design'],
            complexity_score: Math.random() * 10,
            productivity_score: Math.random() * 10,
            suggestions: ['Consider breaking down complex topics', 'Add more specific examples']
          }
        }));
        setSessions(sessionsData);
      } else {
        // Mock data for development
        setSessions([
          {
            id: '1',
            name: 'DeepNexus Frontend Development',
            project_id: 'project-1',
            status: 'active',
            created_at: new Date(Date.now() - 2 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 10 * 60 * 1000),
            message_count: 45,
            total_tokens: 12500,
            duration: 120,
            tags: ['frontend', 'react', 'design'],
            summary: 'Working on revolutionary nature-inspired UI components',
            ai_insights: {
              main_topics: ['React Components', 'Design System', 'API Integration'],
              complexity_score: 8.5,
              productivity_score: 9.2,
              suggestions: ['Consider component optimization', 'Add more TypeScript types']
            }
          },
          {
            id: '2',
            name: 'AI Planning System',
            project_id: 'project-1',
            status: 'completed',
            created_at: new Date(Date.now() - 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 4 * 60 * 60 * 1000),
            message_count: 32,
            total_tokens: 8900,
            duration: 85,
            tags: ['ai', 'planning', 'backend'],
            summary: 'Implemented intelligent task orchestration',
            ai_insights: {
              main_topics: ['Task Management', 'AI Integration', 'Data Structures'],
              complexity_score: 7.8,
              productivity_score: 8.7,
              suggestions: ['Add error handling', 'Implement caching']
            }
          }
        ]);
      }
    } catch (error) {
      console.error('Failed to load sessions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const response = await fetch('/api/sessions/system/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      } else {
        // Mock stats
        setStats({
          total_sessions: 24,
          active_sessions: 3,
          total_messages: 1247,
          total_tokens: 345600,
          avg_session_duration: 67,
          most_productive_hour: 14
        });
      }
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  };

  const loadSessionMessages = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/sessions/${sessionId}/messages`);
      if (response.ok) {
        const data = await response.json();
        setMessages(data.messages || []);
      }
    } catch (error) {
      console.error('Failed to load session messages:', error);
    }
  };

  const searchMessages = async (sessionId: string, query: string) => {
    try {
      const response = await fetch(`/api/sessions/${sessionId}/search`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query, limit: 50 })
      });
      if (response.ok) {
        const data = await response.json();
        return data.results || [];
      }
    } catch (error) {
      console.error('Failed to search messages:', error);
    }
    return [];
  };

  const exportSessions = async (format: 'json' | 'csv' | 'markdown') => {
    try {
      const response = await fetch('/api/sessions/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          format,
          session_ids: sessions.map(s => s.id),
          include_messages: true
        })
      });
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `sessions_export.${format}`;
        a.click();
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Failed to export sessions:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-growth-accent bg-growth-accent/10';
      case 'completed': return 'text-golden-success bg-golden-success/10';
      case 'paused': return 'text-bloom-highlight bg-bloom-highlight/10';
      case 'archived': return 'text-earth-base/60 bg-earth-base/10';
      default: return 'text-earth-base bg-mist-gradient';
    }
  };

  const getComplexityColor = (score: number) => {
    if (score >= 8) return 'text-crimson-alert';
    if (score >= 6) return 'text-bloom-highlight';
    return 'text-growth-accent';
  };

  const filteredAndSortedSessions = sessions
    .filter(session => {
      if (filterStatus !== 'all' && session.status !== filterStatus) return false;
      if (searchQuery && !session.name.toLowerCase().includes(searchQuery.toLowerCase())) return false;
      return true;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'created_at': return b.created_at.getTime() - a.created_at.getTime();
        case 'updated_at': return b.updated_at.getTime() - a.updated_at.getTime();
        case 'duration': return b.duration - a.duration;
        case 'tokens': return b.total_tokens - a.total_tokens;
        default: return 0;
      }
    });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-h2 text-forest-primary">💬 Session Garden</h1>
          <p className="text-earth-base/70">Manage your AI conversation history</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={() => exportSessions('json')}
            className="px-4 py-2 border border-growth-accent/20 rounded-lg text-earth-base hover:bg-growth-accent/5 transition-all hover-bloom"
          >
            📥 Export
          </button>
          <button className="px-6 py-3 bg-growth-gradient text-crystal-clear rounded-lg font-medium hover-bloom transition-all">
            💬 New Session
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        {[
          { label: 'Total Sessions', value: stats.total_sessions, icon: '💬', color: 'text-forest-primary' },
          { label: 'Active', value: stats.active_sessions, icon: '🟢', color: 'text-growth-accent' },
          { label: 'Messages', value: stats.total_messages.toLocaleString(), icon: '💭', color: 'text-bloom-highlight' },
          { label: 'Tokens', value: `${(stats.total_tokens / 1000).toFixed(1)}K`, icon: '🔤', color: 'text-amber-data' },
          { label: 'Avg Duration', value: `${stats.avg_session_duration}m`, icon: '⏱️', color: 'text-golden-success' },
          { label: 'Peak Hour', value: `${stats.most_productive_hour}:00`, icon: '📈', color: 'text-crimson-alert' }
        ].map((stat) => (
          <div key={stat.label} className="bg-crystal-clear rounded-lg p-4 border border-growth-accent/20 hover-bloom">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-earth-base/60">{stat.label}</p>
                <p className={`text-lg font-bold ${stat.color}`}>{stat.value}</p>
              </div>
              <span className="text-2xl">{stat.icon}</span>
            </div>
          </div>
        ))}
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between bg-crystal-clear rounded-lg p-4 border border-growth-accent/20">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Search sessions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8 pr-4 py-2 border border-growth-accent/20 rounded-lg bg-mist-gradient focus:outline-none focus:ring-2 focus:ring-growth-accent/50"
            />
            <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-earth-base/60">🔍</span>
          </div>

          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-3 py-2 border border-growth-accent/20 rounded-lg bg-mist-gradient focus:outline-none focus:ring-2 focus:ring-growth-accent/50"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="completed">Completed</option>
            <option value="paused">Paused</option>
            <option value="archived">Archived</option>
          </select>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-2 border border-growth-accent/20 rounded-lg bg-mist-gradient focus:outline-none focus:ring-2 focus:ring-growth-accent/50"
          >
            <option value="updated_at">Last Updated</option>
            <option value="created_at">Created</option>
            <option value="duration">Duration</option>
            <option value="tokens">Tokens</option>
          </select>
        </div>
      </div>

      {/* Sessions Grid */}
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-growth-accent rounded-full animate-bounce"></div>
            <div className="w-3 h-3 bg-growth-accent rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
            <div className="w-3 h-3 bg-growth-accent rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
            <span className="text-earth-base/60 ml-3">Loading sessions...</span>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredAndSortedSessions.map((session) => (
            <div
              key={session.id}
              className="bg-crystal-clear rounded-lg p-6 border border-growth-accent/20 hover-bloom transition-all cursor-pointer group"
              onClick={() => {
                setSelectedSession(session);
                loadSessionMessages(session.id);
              }}
            >
              {/* Session Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h3 className="font-semibold text-forest-primary group-hover:text-growth-accent transition-colors line-clamp-1">
                    {session.name}
                  </h3>
                  <p className="text-sm text-earth-base/70 mt-1 line-clamp-2">
                    {session.summary || 'No summary available'}
                  </p>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}>
                  {session.status}
                </span>
              </div>

              {/* Session Metrics */}
              <div className="grid grid-cols-2 gap-3 mb-4 text-sm">
                <div className="flex items-center space-x-1">
                  <span className="text-earth-base/60">💭</span>
                  <span>{session.message_count} messages</span>
                </div>
                <div className="flex items-center space-x-1">
                  <span className="text-earth-base/60">🔤</span>
                  <span>{(session.total_tokens / 1000).toFixed(1)}K tokens</span>
                </div>
                <div className="flex items-center space-x-1">
                  <span className="text-earth-base/60">⏱️</span>
                  <span>{session.duration}m</span>
                </div>
                <div className="flex items-center space-x-1">
                  <span className="text-earth-base/60">🧩</span>
                  <span className={getComplexityColor(session.ai_insights.complexity_score)}>
                    {session.ai_insights.complexity_score.toFixed(1)}/10
                  </span>
                </div>
              </div>

              {/* AI Insights */}
              <div className="mb-4">
                <div className="text-xs text-earth-base/60 mb-1">Main Topics</div>
                <div className="flex flex-wrap gap-1">
                  {session.ai_insights.main_topics.slice(0, 3).map((topic, index) => (
                    <span key={index} className="text-xs bg-growth-accent/10 text-growth-accent px-2 py-1 rounded">
                      {topic}
                    </span>
                  ))}
                  {session.ai_insights.main_topics.length > 3 && (
                    <span className="text-xs text-earth-base/60">+{session.ai_insights.main_topics.length - 3}</span>
                  )}
                </div>
              </div>

              {/* Tags */}
              {session.tags.length > 0 && (
                <div className="mb-4">
                  <div className="flex flex-wrap gap-1">
                    {session.tags.map((tag, index) => (
                      <span key={index} className="text-xs bg-mist-gradient text-earth-base px-2 py-1 rounded">
                        #{tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Footer */}
              <div className="flex items-center justify-between text-xs text-earth-base/60">
                <span>Updated {session.updated_at.toLocaleDateString()}</span>
                <div className="flex space-x-2">
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      // Resume session
                    }}
                    className="hover:text-growth-accent transition-colors"
                    title="Resume Session"
                  >
                    ▶️
                  </button>
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      // Export session
                    }}
                    className="hover:text-growth-accent transition-colors"
                    title="Export Session"
                  >
                    📥
                  </button>
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      // Archive session
                    }}
                    className="hover:text-growth-accent transition-colors"
                    title="Archive Session"
                  >
                    📦
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {filteredAndSortedSessions.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">💬</div>
          <h3 className="text-xl font-semibold text-forest-primary mb-2">No Sessions Found</h3>
          <p className="text-earth-base/60 mb-4">
            {searchQuery || filterStatus !== 'all' 
              ? 'Try adjusting your search or filters' 
              : 'Start your first AI conversation'}
          </p>
          <button className="px-6 py-3 bg-growth-gradient text-crystal-clear rounded-lg font-medium hover-bloom transition-all">
            💬 Start New Session
          </button>
        </div>
      )}

      {/* Session Detail Modal */}
      {selectedSession && (
        <div className="fixed inset-0 bg-shadow-deep/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-crystal-clear rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto animate-bloom">
            <div className="p-6 border-b border-growth-accent/20">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-forest-primary">{selectedSession.name}</h2>
                  <p className="text-earth-base/70">{selectedSession.summary}</p>
                </div>
                <button
                  onClick={() => setSelectedSession(null)}
                  className="p-2 hover:bg-growth-accent/10 rounded-lg transition-all hover-bloom"
                >
                  ✕
                </button>
              </div>
            </div>
            
            <div className="p-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-growth-accent">{selectedSession.message_count}</div>
                  <div className="text-sm text-earth-base/60">Messages</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-bloom-highlight">{(selectedSession.total_tokens / 1000).toFixed(1)}K</div>
                  <div className="text-sm text-earth-base/60">Tokens</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-golden-success">{selectedSession.duration}m</div>
                  <div className="text-sm text-earth-base/60">Duration</div>
                </div>
                <div className="text-center">
                  <div className={`text-2xl font-bold ${getComplexityColor(selectedSession.ai_insights.productivity_score)}`}>
                    {selectedSession.ai_insights.productivity_score.toFixed(1)}
                  </div>
                  <div className="text-sm text-earth-base/60">Productivity</div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-forest-primary mb-3">🎯 Main Topics</h3>
                  <div className="space-y-2">
                    {selectedSession.ai_insights.main_topics.map((topic, index) => (
                      <div key={index} className="bg-mist-gradient rounded px-3 py-2 text-sm">
                        {topic}
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-forest-primary mb-3">💡 AI Suggestions</h3>
                  <div className="space-y-2">
                    {selectedSession.ai_insights.suggestions.map((suggestion, index) => (
                      <div key={index} className="bg-growth-accent/10 rounded px-3 py-2 text-sm text-growth-accent">
                        {suggestion}
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Messages Preview */}
              {messages.length > 0 && (
                <div className="mt-6">
                  <h3 className="font-semibold text-forest-primary mb-3">💬 Recent Messages ({messages.length})</h3>
                  <div className="max-h-40 overflow-y-auto space-y-2">
                    {messages.slice(0, 5).map((message, index) => (
                      <div key={index} className="bg-mist-gradient rounded px-3 py-2 text-sm">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="text-xs text-earth-base/60">{message.role}</span>
                          <span className="text-xs text-earth-base/40">•</span>
                          <span className="text-xs text-earth-base/60">{new Date(message.timestamp).toLocaleTimeString()}</span>
                        </div>
                        <div className="text-earth-base line-clamp-2">{message.content}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="mt-6 flex space-x-3">
                <button className="px-4 py-2 bg-growth-gradient text-crystal-clear rounded-lg hover-bloom transition-all">
                  ▶️ Resume Session
                </button>
                <button className="px-4 py-2 border border-growth-accent/20 rounded-lg text-earth-base hover:bg-growth-accent/5 transition-all">
                  📥 Export
                </button>
                <button
                  onClick={async () => {
                    const query = prompt('Enter search query:');
                    if (query && selectedSession) {
                      const results = await searchMessages(selectedSession.id, query);
                      setMessages(results);
                    }
                  }}
                  className="px-4 py-2 border border-growth-accent/20 rounded-lg text-earth-base hover:bg-growth-accent/5 transition-all"
                >
                  🔍 Search Messages
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
