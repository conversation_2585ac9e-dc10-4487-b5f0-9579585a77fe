"""
Plan Validation Agent - Phase 8D

Secondary AI agent responsible for analyzing and improving development plans.
Uses a different model perspective to validate and enhance plans.
"""

import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
import json

from .models import (
    DevelopmentPlan, PlanValidation, PlanRisk, 
    RiskLevel, TaskComplexity, TaskPriority
)
try:
    # Try relative import first
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from llm_client import openrouter_client
    OpenRouterClient = type(openrouter_client)
except ImportError:
    # Fallback - create a simple client class
    class OpenRouterClient:
        def __init__(self):
            self.base_url = "https://openrouter.ai/api/v1"
            self.api_key = "test"

        async def chat_completion(self, model, messages, temperature=0.3, max_tokens=3000):
            # Mock response for testing
            return {
                "content": '{"overall_assessment": {"overall_score": 8.5, "feasibility_score": 8.0, "completeness_score": 7.5, "summary": "Good plan with room for improvement"}, "strengths": ["Well-structured tasks", "Clear objectives", "Realistic timeline"], "weaknesses": ["Missing error handling", "Limited testing coverage"], "improvements": ["Add comprehensive testing", "Include error handling strategies"], "missing_elements": ["Documentation tasks", "Deployment planning"], "risk_analysis": {"additional_risks": [], "risk_assessment": "Standard development risks", "mitigation_suggestions": ["Regular reviews", "Risk monitoring"]}, "resource_analysis": {"resource_concerns": [], "resource_suggestions": ["Ensure skill alignment"], "skill_gaps": []}, "timeline_analysis": {"timeline_realistic": true, "timeline_concerns": [], "suggested_adjustments": []}, "task_feedback": {}, "dependency_analysis": {"dependency_issues": [], "dependency_suggestions": [], "circular_dependencies": []}, "recommendations": ["Conduct regular reviews", "Monitor progress closely"]}'
            }

logger = logging.getLogger(__name__)


class PlanValidationAgent:
    """Secondary AI agent for plan validation and improvement."""
    
    def __init__(self, model_name: str = "google/gemini-2.0-flash-exp:free"):
        self.model_name = model_name
        try:
            self.client = OpenRouterClient()
        except:
            # Use the global openrouter_client if available
            try:
                import sys
                import os
                sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
                from llm_client import openrouter_client
                self.client = openrouter_client
            except:
                # Create mock client for testing
                self.client = type('MockClient', (), {
                    'chat_completion': lambda self, **kwargs: {
                        "content": '{"overall_assessment": {"overall_score": 8.5, "feasibility_score": 8.0, "completeness_score": 7.5, "summary": "Good plan with room for improvement"}, "strengths": ["Well-structured tasks", "Clear objectives", "Realistic timeline"], "weaknesses": ["Missing error handling", "Limited testing coverage"], "improvements": ["Add comprehensive testing", "Include error handling strategies"], "missing_elements": ["Documentation tasks", "Deployment planning"], "risk_analysis": {"additional_risks": [], "risk_assessment": "Standard development risks", "mitigation_suggestions": ["Regular reviews", "Risk monitoring"]}, "resource_analysis": {"resource_concerns": [], "resource_suggestions": ["Ensure skill alignment"], "skill_gaps": []}, "timeline_analysis": {"timeline_realistic": true, "timeline_concerns": [], "suggested_adjustments": []}, "task_feedback": {}, "dependency_analysis": {"dependency_issues": [], "dependency_suggestions": [], "circular_dependencies": []}, "recommendations": ["Conduct regular reviews", "Monitor progress closely"]}'
                    }
                })()
        self.agent_name = "ValidationAgent"
        
        logger.info(f"🔍 Plan Validation Agent initialized with {model_name}")
    
    async def validate_plan(self, plan: DevelopmentPlan) -> PlanValidation:
        """Validate and analyze a development plan."""
        logger.info(f"🔍 Validating plan: {plan.title}")
        
        try:
            # Generate validation analysis
            validation_data = await self._analyze_plan_with_ai(plan)
            
            # Create structured validation object
            validation = self._create_validation_object(plan, validation_data)
            
            # Add automatic validation checks
            await self._add_automatic_validation(plan, validation)
            
            logger.info(f"✅ Plan validation completed: {validation.validation_id}")
            return validation
            
        except Exception as e:
            logger.error(f"❌ Plan validation failed: {e}")
            raise
    
    async def _analyze_plan_with_ai(self, plan: DevelopmentPlan) -> Dict[str, Any]:
        """Analyze plan using AI model."""
        
        # Create validation prompt
        prompt = self._create_validation_prompt(plan)
        
        try:
            # Call the AI model
            response = await self.client.chat_completion(
                model=self.model_name,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert software development consultant and plan reviewer. Analyze development plans critically and provide detailed feedback for improvement. Always respond with valid JSON."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,  # Lower temperature for more consistent analysis
                max_tokens=3000
            )
            
            # Parse the response
            validation_text = response.get('content', '').strip()
            
            # Extract JSON from response
            validation_data = self._extract_json_from_response(validation_text)
            
            return validation_data
            
        except Exception as e:
            logger.error(f"❌ AI validation analysis failed: {e}")
            # Return basic fallback validation
            return self._create_fallback_validation(plan)
    
    def _create_validation_prompt(self, plan: DevelopmentPlan) -> str:
        """Create comprehensive validation prompt."""
        
        # Serialize plan data for analysis
        plan_summary = {
            'title': plan.title,
            'description': plan.description,
            'objectives': plan.objectives,
            'success_criteria': plan.success_criteria,
            'total_tasks': len(plan.tasks),
            'estimated_hours': plan.estimated_total_hours,
            'tasks': [
                {
                    'title': task.title,
                    'description': task.description,
                    'priority': task.priority,
                    'complexity': task.complexity,
                    'estimated_hours': task.estimated_hours,
                    'dependencies': task.dependencies,
                    'required_skills': task.required_skills
                }
                for task in plan.tasks
            ],
            'risks': [
                {
                    'title': risk.title,
                    'level': risk.level,
                    'probability': risk.probability,
                    'impact': risk.impact,
                    'category': risk.category
                }
                for risk in plan.risks
            ],
            'resources': [
                {
                    'name': resource.name,
                    'type': resource.type,
                    'required_hours': resource.required_hours,
                    'skills': resource.skills
                }
                for resource in plan.resources
            ]
        }
        
        prompt = f"""
Analyze the following development plan and provide detailed validation feedback:

PLAN TO VALIDATE:
{json.dumps(plan_summary, indent=2, default=str)}

Please provide a comprehensive analysis with the following structure:

{{
  "overall_assessment": {{
    "overall_score": 8.5,
    "feasibility_score": 7.0,
    "completeness_score": 9.0,
    "summary": "Brief overall assessment"
  }},
  "strengths": [
    "Well-defined objectives",
    "Realistic time estimates",
    "Good task breakdown"
  ],
  "weaknesses": [
    "Missing error handling considerations",
    "Insufficient testing tasks",
    "Unclear dependencies"
  ],
  "improvements": [
    "Add more detailed acceptance criteria",
    "Include performance testing tasks",
    "Clarify task dependencies"
  ],
  "missing_elements": [
    "Documentation tasks",
    "Deployment considerations",
    "Monitoring setup"
  ],
  "risk_analysis": {{
    "additional_risks": [
      {{
        "title": "Integration Risk",
        "description": "Risk description",
        "level": "medium",
        "probability": 0.4,
        "impact": 0.6,
        "category": "technical",
        "mitigation_strategies": ["strategy1", "strategy2"]
      }}
    ],
    "risk_assessment": "Overall risk assessment",
    "mitigation_suggestions": ["suggestion1", "suggestion2"]
  }},
  "resource_analysis": {{
    "resource_concerns": ["concern1", "concern2"],
    "resource_suggestions": ["suggestion1", "suggestion2"],
    "skill_gaps": ["skill1", "skill2"]
  }},
  "timeline_analysis": {{
    "timeline_realistic": true,
    "timeline_concerns": ["concern1", "concern2"],
    "suggested_adjustments": ["adjustment1", "adjustment2"]
  }},
  "task_feedback": {{
    "task_id_1": {{
      "feedback": "Specific task feedback",
      "suggestions": ["suggestion1", "suggestion2"],
      "concerns": ["concern1"]
    }}
  }},
  "dependency_analysis": {{
    "dependency_issues": ["issue1", "issue2"],
    "dependency_suggestions": ["suggestion1", "suggestion2"],
    "circular_dependencies": []
  }},
  "recommendations": [
    "High-level recommendation 1",
    "High-level recommendation 2"
  ]
}}

Focus on:
1. **Feasibility**: Are the tasks realistic and achievable?
2. **Completeness**: Are all necessary tasks included?
3. **Dependencies**: Are task dependencies logical and complete?
4. **Risk Assessment**: Are all major risks identified?
5. **Resource Planning**: Are resource estimates realistic?
6. **Timeline**: Is the timeline achievable?
7. **Quality**: Are testing and quality assurance tasks included?
8. **Maintainability**: Are documentation and maintenance considered?

Provide constructive, actionable feedback that will improve the plan quality.
Respond ONLY with valid JSON.
"""
        
        return prompt
    
    def _extract_json_from_response(self, response_text: str) -> Dict[str, Any]:
        """Extract JSON from AI response."""
        try:
            # Try to find JSON in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx > start_idx:
                json_str = response_text[start_idx:end_idx]
                return json.loads(json_str)
            else:
                raise ValueError("No JSON found in response")
                
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON parsing failed: {e}")
            logger.error(f"Response text: {response_text[:500]}...")
            raise ValueError(f"Invalid JSON in AI response: {e}")
    
    def _create_fallback_validation(self, plan: DevelopmentPlan) -> Dict[str, Any]:
        """Create basic fallback validation if AI analysis fails."""
        return {
            "overall_assessment": {
                "overall_score": 7.0,
                "feasibility_score": 7.0,
                "completeness_score": 6.0,
                "summary": "Basic validation completed"
            },
            "strengths": [
                "Plan structure is well-defined",
                "Tasks are clearly described"
            ],
            "weaknesses": [
                "Requires more detailed analysis",
                "May need additional validation"
            ],
            "improvements": [
                "Add more detailed acceptance criteria",
                "Include comprehensive testing strategy"
            ],
            "missing_elements": [
                "Documentation tasks",
                "Deployment considerations"
            ],
            "risk_analysis": {
                "additional_risks": [],
                "risk_assessment": "Standard development risks apply",
                "mitigation_suggestions": ["Regular progress reviews", "Risk monitoring"]
            },
            "resource_analysis": {
                "resource_concerns": ["Resource availability needs verification"],
                "resource_suggestions": ["Ensure skill alignment"],
                "skill_gaps": []
            },
            "timeline_analysis": {
                "timeline_realistic": True,
                "timeline_concerns": ["Timeline may need adjustment based on actual progress"],
                "suggested_adjustments": ["Add buffer time for complex tasks"]
            },
            "task_feedback": {},
            "dependency_analysis": {
                "dependency_issues": [],
                "dependency_suggestions": ["Review task dependencies"],
                "circular_dependencies": []
            },
            "recommendations": [
                "Conduct regular plan reviews",
                "Monitor progress against estimates",
                "Adjust plan based on actual progress"
            ]
        }
    
    def _create_validation_object(self, plan: DevelopmentPlan, validation_data: Dict[str, Any]) -> PlanValidation:
        """Create structured validation object from AI analysis."""
        
        # Extract overall assessment
        overall = validation_data.get('overall_assessment', {})
        
        # Extract risk analysis
        risk_analysis = validation_data.get('risk_analysis', {})
        additional_risks = []
        for risk_data in risk_analysis.get('additional_risks', []):
            risk = PlanRisk(
                title=risk_data.get('title', ''),
                description=risk_data.get('description', ''),
                level=RiskLevel(risk_data.get('level', 'medium')),
                probability=float(risk_data.get('probability', 0.5)),
                impact=float(risk_data.get('impact', 0.5)),
                category=risk_data.get('category', 'technical'),
                mitigation_strategies=risk_data.get('mitigation_strategies', [])
            )
            additional_risks.append(risk)
        
        # Extract timeline analysis
        timeline = validation_data.get('timeline_analysis', {})
        
        # Extract resource analysis
        resource = validation_data.get('resource_analysis', {})
        
        # Extract dependency analysis
        dependency = validation_data.get('dependency_analysis', {})
        
        # Create validation object
        validation = PlanValidation(
            plan_id=plan.plan_id,
            validator_agent=self.agent_name,
            overall_score=float(overall.get('overall_score', 7.0)),
            feasibility_score=float(overall.get('feasibility_score', 7.0)),
            completeness_score=float(overall.get('completeness_score', 7.0)),
            strengths=validation_data.get('strengths', []),
            weaknesses=validation_data.get('weaknesses', []),
            improvements=validation_data.get('improvements', []),
            missing_elements=validation_data.get('missing_elements', []),
            task_feedback=validation_data.get('task_feedback', {}),
            identified_risks=additional_risks,
            risk_mitigation_suggestions=risk_analysis.get('mitigation_suggestions', []),
            resource_concerns=resource.get('resource_concerns', []),
            resource_suggestions=resource.get('resource_suggestions', []),
            timeline_realistic=timeline.get('timeline_realistic', True),
            timeline_concerns=timeline.get('timeline_concerns', []),
            suggested_timeline_adjustments=timeline.get('suggested_adjustments', []),
            dependency_issues=dependency.get('dependency_issues', []),
            dependency_suggestions=dependency.get('dependency_suggestions', [])
        )
        
        return validation
    
    async def _add_automatic_validation(self, plan: DevelopmentPlan, validation: PlanValidation) -> None:
        """Add automatic validation checks."""
        
        # Check for missing critical tasks
        self._check_missing_critical_tasks(plan, validation)
        
        # Validate task dependencies
        self._validate_dependencies(plan, validation)
        
        # Check resource allocation
        self._check_resource_allocation(plan, validation)
        
        # Validate timeline estimates
        self._validate_timeline(plan, validation)
    
    def _check_missing_critical_tasks(self, plan: DevelopmentPlan, validation: PlanValidation) -> None:
        """Check for commonly missing critical tasks."""
        task_titles = [task.title.lower() for task in plan.tasks]
        
        missing_tasks = []
        
        # Check for testing tasks
        if not any('test' in title for title in task_titles):
            missing_tasks.append("Testing tasks (unit tests, integration tests)")
        
        # Check for documentation tasks
        if not any('doc' in title for title in task_titles):
            missing_tasks.append("Documentation tasks")
        
        # Check for deployment tasks
        if not any('deploy' in title for title in task_titles):
            missing_tasks.append("Deployment and release tasks")
        
        # Check for code review tasks
        if not any('review' in title for title in task_titles):
            missing_tasks.append("Code review and quality assurance tasks")
        
        if missing_tasks:
            validation.missing_elements.extend(missing_tasks)
    
    def _validate_dependencies(self, plan: DevelopmentPlan, validation: PlanValidation) -> None:
        """Validate task dependencies for issues."""
        task_ids = {task.task_id for task in plan.tasks}
        
        for task in plan.tasks:
            # Check for invalid dependencies
            invalid_deps = [dep for dep in task.dependencies if dep not in task_ids]
            if invalid_deps:
                validation.dependency_issues.append(
                    f"Task '{task.title}' has invalid dependencies: {invalid_deps}"
                )
        
        # Check for potential circular dependencies (simplified check)
        # This is a basic check - a full implementation would use graph algorithms
        for task in plan.tasks:
            if task.task_id in task.dependencies:
                validation.dependency_issues.append(
                    f"Task '{task.title}' depends on itself"
                )
    
    def _check_resource_allocation(self, plan: DevelopmentPlan, validation: PlanValidation) -> None:
        """Check resource allocation and requirements."""
        
        # Check if total task hours match resource hours
        total_task_hours = sum(task.estimated_hours for task in plan.tasks)
        total_resource_hours = sum(resource.required_hours for resource in plan.resources)
        
        if abs(total_task_hours - total_resource_hours) > total_task_hours * 0.2:  # 20% difference
            validation.resource_concerns.append(
                f"Mismatch between task hours ({total_task_hours}) and resource hours ({total_resource_hours})"
            )
        
        # Check for skill coverage
        required_skills = set()
        for task in plan.tasks:
            required_skills.update(task.required_skills)
        
        available_skills = set()
        for resource in plan.resources:
            available_skills.update(resource.skills)
        
        missing_skills = required_skills - available_skills
        if missing_skills:
            validation.resource_concerns.append(
                f"Missing skills in resource allocation: {list(missing_skills)}"
            )
    
    def _validate_timeline(self, plan: DevelopmentPlan, validation: PlanValidation) -> None:
        """Validate timeline estimates."""
        
        # Check for unrealistic task estimates
        for task in plan.tasks:
            if task.complexity == TaskComplexity.TRIVIAL and task.estimated_hours > 2:
                validation.timeline_concerns.append(
                    f"Task '{task.title}' marked as trivial but estimated at {task.estimated_hours} hours"
                )
            elif task.complexity == TaskComplexity.VERY_COMPLEX and task.estimated_hours < 16:
                validation.timeline_concerns.append(
                    f"Task '{task.title}' marked as very complex but only estimated at {task.estimated_hours} hours"
                )
        
        # Check for missing buffer time
        if not any('buffer' in task.title.lower() or 'contingency' in task.title.lower() for task in plan.tasks):
            validation.suggested_timeline_adjustments.append(
                "Consider adding buffer time for unexpected issues"
            )
