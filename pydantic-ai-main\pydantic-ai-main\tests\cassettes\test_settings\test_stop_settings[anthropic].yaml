interactions:
- request:
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '193'
      content-type:
      - application/json
      host:
      - api.anthropic.com
    method: POST
    parsed_body:
      max_tokens: 1024
      messages:
      - content:
        - text: What is the capital of France?
          type: text
        role: user
      model: claude-3-5-sonnet-latest
      stop_sequences:
      - Paris
      stream: false
    uri: https://api.anthropic.com/v1/messages?beta=true
  response:
    headers:
      connection:
      - keep-alive
      content-length:
      - '359'
      content-type:
      - application/json
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      transfer-encoding:
      - chunked
    parsed_body:
      content:
      - text: 'The capital of France is '
        type: text
      id: msg_01SVFZqkSLhUPkAA4w6rhzFD
      model: claude-3-5-sonnet-20241022
      role: assistant
      stop_reason: stop_sequence
      stop_sequence: Paris
      type: message
      usage:
        cache_creation_input_tokens: 0
        cache_read_input_tokens: 0
        input_tokens: 14
        output_tokens: 6
        service_tier: standard
    status:
      code: 200
      message: OK
version: 1
