"""
Enhanced Retry Manager with Pydantic AI Integration

Provides intelligent retry mechanisms that work seamlessly with Pydantic AI's ModelRetry
exceptions and the existing resilience system.
"""

import asyncio
import logging
import random
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Type, Union

from pydantic import BaseModel

# Import Pydantic AI exceptions with fallback for testing
try:
    from pydantic_ai import ModelRetry
    from pydantic_ai.exceptions import UnexpectedModelBehavior, UsageLimitExceeded, ModelHTTPError
except ImportError:
    # Fallback for testing or when pydantic_ai is not available
    class ModelRetry(Exception):
        def __init__(self, message: str):
            self.message = message
            super().__init__(message)

    class UnexpectedModelBehavior(Exception):
        pass

    class UsageLimitExceeded(Exception):
        pass

    class ModelHTTPError(Exception):
        pass

# Import AdaptiveCircuitBreaker with fallback
try:
    from ..core.resilience import AdaptiveCircuitBreaker
except ImportError:
    # Fallback implementation for testing
    class AdaptiveCircuitBreaker:
        def __init__(self, failure_threshold=5, recovery_timeout=60.0, adaptive=True):
            self.failure_threshold = failure_threshold
            self.recovery_timeout = recovery_timeout
            self.adaptive = adaptive
            self.failure_count = 0
            self.success_count = 0
            self.last_failure_time = 0.0
            self.state = type('State', (), {'CLOSED': 'closed'})()

        def is_request_allowed(self):
            return True

        def record_success(self):
            self.success_count += 1

        def record_failure(self, error_type="unknown"):
            self.failure_count += 1

logger = logging.getLogger(__name__)


class RetryReason(Enum):
    """Reasons for retry attempts."""
    NETWORK_ERROR = "network_error"
    RATE_LIMIT = "rate_limit"
    MODEL_ERROR = "model_error"
    VALIDATION_ERROR = "validation_error"
    TIMEOUT = "timeout"
    CIRCUIT_BREAKER = "circuit_breaker"
    USER_RETRY = "user_retry"
    SYSTEM_ERROR = "system_error"


class RetryStrategy(Enum):
    """Available retry strategies."""
    EXPONENTIAL = "exponential"
    LINEAR = "linear"
    FIBONACCI = "fibonacci"
    FIXED = "fixed"
    ADAPTIVE = "adaptive"


@dataclass
class RetryConfig:
    """Configuration for retry behavior."""
    max_attempts: int = 3
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL
    base_delay: float = 1.0
    max_delay: float = 60.0
    jitter: bool = True
    backoff_multiplier: float = 2.0
    
    # Pydantic AI specific
    model_retry_attempts: int = 2
    tool_retry_attempts: int = 3
    
    # Error-specific configurations
    rate_limit_delay: float = 30.0
    network_error_delay: float = 5.0
    
    # Circuit breaker integration
    use_circuit_breaker: bool = True
    circuit_breaker_threshold: int = 5
    circuit_breaker_timeout: float = 60.0


@dataclass
class RetryAttempt:
    """Information about a single retry attempt."""
    attempt_number: int
    reason: RetryReason
    delay: float
    timestamp: datetime
    error: Optional[Exception] = None
    context: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RetryResult:
    """Result of a retry operation."""
    success: bool = False
    final_result: Any = None
    final_error: Optional[Exception] = None
    attempts: List[RetryAttempt] = field(default_factory=list)
    total_duration: float = 0.0
    recovery_applied: bool = False
    
    @property
    def attempt_count(self) -> int:
        return len(self.attempts)
    
    @property
    def last_attempt(self) -> Optional[RetryAttempt]:
        return self.attempts[-1] if self.attempts else None


class EnhancedRetryManager:
    """Enhanced retry manager with Pydantic AI integration."""
    
    def __init__(self):
        self.circuit_breakers: Dict[str, AdaptiveCircuitBreaker] = {}
        self.retry_stats: Dict[str, List[RetryResult]] = {}
        self.active_retries: Dict[str, RetryResult] = {}
    
    def get_circuit_breaker(self, key: str, config: RetryConfig) -> AdaptiveCircuitBreaker:
        """Get or create circuit breaker for a specific key."""
        if key not in self.circuit_breakers:
            self.circuit_breakers[key] = AdaptiveCircuitBreaker(
                failure_threshold=config.circuit_breaker_threshold,
                recovery_timeout=config.circuit_breaker_timeout,
                adaptive=True
            )
        return self.circuit_breakers[key]
    
    def calculate_delay(
        self, 
        attempt: int, 
        config: RetryConfig, 
        reason: RetryReason
    ) -> float:
        """Calculate delay for retry attempt based on strategy and reason."""
        
        # Special handling for specific error types
        if reason == RetryReason.RATE_LIMIT:
            base_delay = config.rate_limit_delay
        elif reason == RetryReason.NETWORK_ERROR:
            base_delay = config.network_error_delay
        else:
            base_delay = config.base_delay
        
        # Apply strategy
        if config.strategy == RetryStrategy.EXPONENTIAL:
            delay = min(base_delay * (config.backoff_multiplier ** attempt), config.max_delay)
        elif config.strategy == RetryStrategy.LINEAR:
            delay = min(base_delay + (attempt * base_delay), config.max_delay)
        elif config.strategy == RetryStrategy.FIBONACCI:
            delay = min(self._fibonacci_delay(attempt, base_delay), config.max_delay)
        elif config.strategy == RetryStrategy.ADAPTIVE:
            delay = self._adaptive_delay(attempt, config, reason)
        else:  # FIXED
            delay = base_delay
        
        # Add jitter if enabled
        if config.jitter:
            jitter_range = delay * 0.25
            delay += random.uniform(-jitter_range, jitter_range)
        
        return max(0, delay)
    
    def _fibonacci_delay(self, attempt: int, base_delay: float) -> float:
        """Calculate Fibonacci-based delay."""
        def fib(n):
            if n <= 1:
                return n
            return fib(n-1) + fib(n-2)
        
        return base_delay * fib(attempt + 1)
    
    def _adaptive_delay(self, attempt: int, config: RetryConfig, reason: RetryReason) -> float:
        """Calculate adaptive delay based on historical success rates."""
        # This could be enhanced with ML-based predictions
        base = config.base_delay * (1.5 ** attempt)
        
        # Adjust based on reason
        multipliers = {
            RetryReason.RATE_LIMIT: 2.0,
            RetryReason.NETWORK_ERROR: 1.5,
            RetryReason.MODEL_ERROR: 1.2,
            RetryReason.TIMEOUT: 1.8,
        }
        
        multiplier = multipliers.get(reason, 1.0)
        return min(base * multiplier, config.max_delay)
    
    def categorize_error(self, error: Exception) -> RetryReason:
        """Categorize error to determine retry strategy."""
        if isinstance(error, ModelRetry):
            return RetryReason.MODEL_ERROR
        elif isinstance(error, ModelHTTPError):
            if "rate limit" in str(error).lower() or "429" in str(error):
                return RetryReason.RATE_LIMIT
            return RetryReason.NETWORK_ERROR
        elif isinstance(error, UnexpectedModelBehavior):
            return RetryReason.MODEL_ERROR
        elif isinstance(error, UsageLimitExceeded):
            return RetryReason.RATE_LIMIT
        elif isinstance(error, (ConnectionError, TimeoutError)):
            return RetryReason.NETWORK_ERROR
        elif "timeout" in str(error).lower():
            return RetryReason.TIMEOUT
        else:
            return RetryReason.SYSTEM_ERROR
    
    async def execute_with_retry(
        self,
        func: Callable,
        config: RetryConfig,
        context_key: str = "default",
        context: Optional[Dict[str, Any]] = None,
        *args,
        **kwargs
    ) -> RetryResult:
        """Execute function with intelligent retry logic."""
        
        start_time = time.time()
        result = RetryResult()
        context = context or {}
        
        # Check circuit breaker if enabled
        if config.use_circuit_breaker:
            circuit_breaker = self.get_circuit_breaker(context_key, config)
            if not circuit_breaker.is_request_allowed():
                result.final_error = Exception("Circuit breaker is open")
                return result
        
        for attempt in range(config.max_attempts):
            try:
                # Execute the function
                if asyncio.iscoroutinefunction(func):
                    final_result = await func(*args, **kwargs)
                else:
                    final_result = func(*args, **kwargs)
                
                # Success!
                result.success = True
                result.final_result = final_result
                
                # Record success in circuit breaker
                if config.use_circuit_breaker:
                    circuit_breaker.record_success()
                
                break
                
            except Exception as error:
                reason = self.categorize_error(error)
                
                # Create retry attempt record
                retry_attempt = RetryAttempt(
                    attempt_number=attempt + 1,
                    reason=reason,
                    delay=0.0,
                    timestamp=datetime.now(),
                    error=error,
                    context=context.copy()
                )
                
                result.attempts.append(retry_attempt)
                
                # Check if we should retry
                if attempt < config.max_attempts - 1:
                    delay = self.calculate_delay(attempt, config, reason)
                    retry_attempt.delay = delay
                    
                    logger.warning(
                        f"Attempt {attempt + 1} failed for {context_key}: {error}. "
                        f"Retrying in {delay:.2f}s (reason: {reason.value})"
                    )
                    
                    await asyncio.sleep(delay)
                else:
                    # Final attempt failed
                    result.final_error = error
                    
                    # Record failure in circuit breaker
                    if config.use_circuit_breaker:
                        circuit_breaker.record_failure(type(error).__name__)
                    
                    logger.error(
                        f"All {config.max_attempts} attempts failed for {context_key}. "
                        f"Final error: {error}"
                    )
        
        result.total_duration = time.time() - start_time
        
        # Store result for analytics
        if context_key not in self.retry_stats:
            self.retry_stats[context_key] = []
        self.retry_stats[context_key].append(result)
        
        return result
