#!/usr/bin/env python3
"""
Git Operations Module for AI Coder Agent - Phase 3
Provides safe git operations with branch protection and validation.

Features:
- Git status and diff operations
- Safe branch management with protection
- Commit operations with validation
- History and blame functionality
- Stash operations and conflict detection
- Repository initialization and validation
"""

import os
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Union, Any, Tuple
import logging
import asyncio
from concurrent.futures import ThreadPoolExecutor
import git
from git import Repo, InvalidGitRepositoryError, GitCommandError

# Import configuration
try:
    from ...config import settings
except ImportError:
    from config import settings

logger = logging.getLogger(__name__)

class GitOperationError(Exception):
    """Custom exception for git operation errors."""
    pass

class GitSecurityError(GitOperationError):
    """Exception for git security-related errors."""
    pass

class GitOperations:
    """Safe git operations with workspace boundary enforcement."""
    
    def __init__(self):
        self.workspace_root = Path(getattr(settings, 'workspace_root', '/app/workspace'))
        self.git_safe_mode = getattr(settings, 'git_safe_mode', True)
        self.protected_branches = getattr(settings, 'protected_branches', ['main', 'master', 'production'])
        self.max_commit_message_length = getattr(settings, 'max_commit_message_length', 72)
        self.executor = ThreadPoolExecutor(max_workers=2)
        
        logger.info(f"GitOperations initialized with workspace: {self.workspace_root}")
    
    def _validate_repo_path(self, path: Union[str, Path]) -> Path:
        """
        Validate repository path for security and workspace boundaries.
        
        Args:
            path: Repository path to validate
            
        Returns:
            Validated Path object
            
        Raises:
            GitSecurityError: If path is outside workspace
            GitOperationError: If path is invalid
        """
        try:
            repo_path = Path(path).resolve()
            
            # Check if path is within workspace
            try:
                repo_path.relative_to(self.workspace_root.resolve())
            except ValueError:
                raise GitSecurityError(f"Repository path outside workspace: {path}")
            
            return repo_path
            
        except Exception as e:
            if isinstance(e, GitSecurityError):
                raise
            raise GitOperationError(f"Invalid repository path: {path} - {e}")
    
    def _get_repo(self, path: Union[str, Path]) -> Repo:
        """Get git repository object with validation."""
        repo_path = self._validate_repo_path(path)
        
        try:
            return Repo(repo_path)
        except InvalidGitRepositoryError:
            raise GitOperationError(f"Not a git repository: {repo_path}")
        except Exception as e:
            raise GitOperationError(f"Failed to access repository: {e}")
    
    def _validate_branch_name(self, branch_name: str) -> bool:
        """Validate branch name for safety."""
        if not branch_name or not isinstance(branch_name, str):
            return False
        
        # Check for dangerous characters
        dangerous_chars = ['..', '~', '^', ':', '?', '*', '[', '\\', ' ']
        if any(char in branch_name for char in dangerous_chars):
            return False
        
        # Check for protected branches in safe mode
        if self.git_safe_mode and branch_name in self.protected_branches:
            return False
        
        return True
    
    def _validate_commit_message(self, message: str) -> Tuple[bool, str]:
        """Validate commit message format and content."""
        if not message or not message.strip():
            return False, "Commit message cannot be empty"
        
        message = message.strip()
        
        # Check length
        if len(message) > self.max_commit_message_length:
            return False, f"Commit message too long (max {self.max_commit_message_length} chars)"
        
        # Check for basic format
        if message.startswith(' ') or message.endswith(' '):
            return False, "Commit message should not start or end with spaces"
        
        return True, "Valid commit message"
    
    async def get_git_status(self, repo_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Get git repository status with staged/unstaged/untracked files.
        
        Args:
            repo_path: Path to git repository
            
        Returns:
            Dict with git status information
        """
        try:
            def _get_status():
                repo = self._get_repo(repo_path)
                
                # Get status information
                status = {
                    'staged': [],
                    'unstaged': [],
                    'untracked': [],
                    'conflicts': [],
                    'current_branch': None,
                    'ahead': 0,
                    'behind': 0,
                    'is_dirty': False
                }
                
                # Current branch
                try:
                    status['current_branch'] = repo.active_branch.name
                except TypeError:
                    status['current_branch'] = 'HEAD (detached)'
                
                # File status
                for item in repo.index.diff(None):  # Unstaged changes
                    status['unstaged'].append({
                        'file': item.a_path,
                        'change_type': item.change_type
                    })
                
                for item in repo.index.diff('HEAD'):  # Staged changes
                    status['staged'].append({
                        'file': item.a_path,
                        'change_type': item.change_type
                    })
                
                # Untracked files
                status['untracked'] = list(repo.untracked_files)
                
                # Check for conflicts
                try:
                    conflicts = repo.git.diff('--name-only', '--diff-filter=U').split('\n')
                    status['conflicts'] = [f for f in conflicts if f.strip()]
                except GitCommandError:
                    pass
                
                # Repository state
                status['is_dirty'] = repo.is_dirty(untracked_files=True)
                
                # Ahead/behind tracking
                try:
                    if repo.active_branch.tracking_branch():
                        ahead_behind = repo.git.rev_list(
                            '--left-right', '--count',
                            f'{repo.active_branch.tracking_branch()}...{repo.active_branch}'
                        ).split('\t')
                        status['behind'] = int(ahead_behind[0])
                        status['ahead'] = int(ahead_behind[1])
                except (TypeError, GitCommandError, IndexError):
                    pass
                
                return status
            
            status = await asyncio.get_event_loop().run_in_executor(self.executor, _get_status)
            
            return {
                'status': 'success',
                'repository': str(self._validate_repo_path(repo_path)),
                **status
            }
            
        except Exception as e:
            logger.error(f"Failed to get git status for {repo_path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }
    
    async def get_git_diff(self, repo_path: Union[str, Path], 
                          file_path: Optional[str] = None,
                          staged: bool = False,
                          commit_range: Optional[str] = None) -> Dict[str, Any]:
        """
        Get git diff with file-specific and commit-range options.
        
        Args:
            repo_path: Path to git repository
            file_path: Optional specific file to diff
            staged: Whether to show staged changes
            commit_range: Optional commit range (e.g., 'HEAD~1..HEAD')
            
        Returns:
            Dict with diff information
        """
        try:
            def _get_diff():
                repo = self._get_repo(repo_path)
                
                diff_args = []
                
                if commit_range:
                    diff_args.append(commit_range)
                elif staged:
                    diff_args.append('--cached')
                
                if file_path:
                    # Validate file path is within repository
                    full_file_path = Path(repo.working_dir) / file_path
                    try:
                        full_file_path.relative_to(Path(repo.working_dir))
                    except ValueError:
                        raise GitSecurityError(f"File path outside repository: {file_path}")
                    
                    diff_args.append('--')
                    diff_args.append(file_path)
                
                # Get diff
                diff_text = repo.git.diff(*diff_args)
                
                # Parse diff statistics
                try:
                    stat_args = ['--stat'] + diff_args
                    diff_stat = repo.git.diff(*stat_args)
                except GitCommandError:
                    diff_stat = ""
                
                return {
                    'diff': diff_text,
                    'stats': diff_stat,
                    'has_changes': bool(diff_text.strip())
                }
            
            diff_result = await asyncio.get_event_loop().run_in_executor(self.executor, _get_diff)
            
            return {
                'status': 'success',
                'repository': str(self._validate_repo_path(repo_path)),
                'file_path': file_path,
                'staged': staged,
                'commit_range': commit_range,
                **diff_result
            }
            
        except Exception as e:
            logger.error(f"Failed to get git diff for {repo_path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def git_commit(self, repo_path: Union[str, Path], message: str,
                        files: Optional[List[str]] = None,
                        add_all: bool = False) -> Dict[str, Any]:
        """
        Commit changes with message validation and staging.

        Args:
            repo_path: Path to git repository
            message: Commit message
            files: Optional list of specific files to commit
            add_all: Whether to add all changes before committing

        Returns:
            Dict with commit operation result
        """
        try:
            # Validate commit message
            is_valid, validation_msg = self._validate_commit_message(message)
            if not is_valid:
                return {
                    'status': 'error',
                    'error': validation_msg,
                    'error_type': 'ValidationError'
                }

            def _commit():
                repo = self._get_repo(repo_path)

                # Check if repository is clean
                if not repo.is_dirty(untracked_files=True) and not files:
                    return {
                        'committed': False,
                        'reason': 'No changes to commit'
                    }

                # Stage files
                if add_all:
                    repo.git.add('.')
                elif files:
                    for file_path in files:
                        # Validate file path
                        full_file_path = Path(repo.working_dir) / file_path
                        try:
                            full_file_path.relative_to(Path(repo.working_dir))
                        except ValueError:
                            raise GitSecurityError(f"File path outside repository: {file_path}")

                        repo.git.add(file_path)

                # Create commit
                commit = repo.index.commit(message)

                return {
                    'committed': True,
                    'commit_hash': commit.hexsha,
                    'commit_message': commit.message.strip(),
                    'author': f"{commit.author.name} <{commit.author.email}>",
                    'timestamp': commit.committed_date
                }

            result = await asyncio.get_event_loop().run_in_executor(self.executor, _commit)

            return {
                'status': 'success',
                'repository': str(self._validate_repo_path(repo_path)),
                **result
            }

        except Exception as e:
            logger.error(f"Failed to commit in {repo_path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def git_branch_operations(self, repo_path: Union[str, Path],
                                   operation: str,
                                   branch_name: Optional[str] = None,
                                   force: bool = False) -> Dict[str, Any]:
        """
        Git branch operations (create, switch, list, delete).

        Args:
            repo_path: Path to git repository
            operation: Operation type ('create', 'switch', 'list', 'delete')
            branch_name: Branch name for operations that require it
            force: Force operation (use with caution)

        Returns:
            Dict with operation result
        """
        try:
            if operation not in ['create', 'switch', 'list', 'delete']:
                return {
                    'status': 'error',
                    'error': f'Invalid operation: {operation}',
                    'error_type': 'ValidationError'
                }

            # Validate branch name for operations that need it
            if operation in ['create', 'switch', 'delete'] and branch_name:
                if not self._validate_branch_name(branch_name):
                    return {
                        'status': 'error',
                        'error': f'Invalid or protected branch name: {branch_name}',
                        'error_type': 'ValidationError'
                    }

            def _branch_operation():
                repo = self._get_repo(repo_path)

                if operation == 'list':
                    branches = {
                        'local': [branch.name for branch in repo.branches],
                        'remote': [branch.name for branch in repo.remote().refs],
                        'current': repo.active_branch.name if repo.active_branch else None
                    }
                    return {'branches': branches}

                elif operation == 'create':
                    if not branch_name:
                        raise GitOperationError("Branch name required for create operation")

                    # Check if branch already exists
                    if branch_name in [branch.name for branch in repo.branches]:
                        return {
                            'created': False,
                            'reason': f'Branch already exists: {branch_name}'
                        }

                    new_branch = repo.create_head(branch_name)
                    return {
                        'created': True,
                        'branch_name': branch_name,
                        'commit_hash': new_branch.commit.hexsha
                    }

                elif operation == 'switch':
                    if not branch_name:
                        raise GitOperationError("Branch name required for switch operation")

                    # Check for uncommitted changes
                    if repo.is_dirty() and not force:
                        return {
                            'switched': False,
                            'reason': 'Uncommitted changes present. Use force=True to override.'
                        }

                    repo.git.checkout(branch_name)
                    return {
                        'switched': True,
                        'branch_name': branch_name,
                        'previous_branch': repo.active_branch.name
                    }

                elif operation == 'delete':
                    if not branch_name:
                        raise GitOperationError("Branch name required for delete operation")

                    # Safety check for current branch
                    if repo.active_branch and repo.active_branch.name == branch_name:
                        return {
                            'deleted': False,
                            'reason': 'Cannot delete current branch'
                        }

                    # Delete branch
                    repo.delete_head(branch_name, force=force)
                    return {
                        'deleted': True,
                        'branch_name': branch_name
                    }

            result = await asyncio.get_event_loop().run_in_executor(self.executor, _branch_operation)

            return {
                'status': 'success',
                'repository': str(self._validate_repo_path(repo_path)),
                'operation': operation,
                **result
            }

        except Exception as e:
            logger.error(f"Failed branch operation {operation} in {repo_path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def get_commit_history(self, repo_path: Union[str, Path],
                                max_count: int = 50,
                                branch: Optional[str] = None,
                                file_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Get commit history with filtering and pagination.

        Args:
            repo_path: Path to git repository
            max_count: Maximum number of commits to return
            branch: Optional branch to get history from
            file_path: Optional file path to filter commits

        Returns:
            Dict with commit history
        """
        try:
            def _get_history():
                repo = self._get_repo(repo_path)

                # Prepare arguments
                kwargs = {'max_count': max_count}
                if branch:
                    kwargs['rev'] = branch
                if file_path:
                    # Validate file path
                    full_file_path = Path(repo.working_dir) / file_path
                    try:
                        full_file_path.relative_to(Path(repo.working_dir))
                    except ValueError:
                        raise GitSecurityError(f"File path outside repository: {file_path}")
                    kwargs['paths'] = file_path

                # Get commits
                commits = []
                for commit in repo.iter_commits(**kwargs):
                    commits.append({
                        'hash': commit.hexsha,
                        'short_hash': commit.hexsha[:8],
                        'message': commit.message.strip(),
                        'author': f"{commit.author.name} <{commit.author.email}>",
                        'timestamp': commit.committed_date,
                        'files_changed': len(commit.stats.files),
                        'insertions': commit.stats.total['insertions'],
                        'deletions': commit.stats.total['deletions']
                    })

                return {
                    'commits': commits,
                    'total_count': len(commits),
                    'branch': branch or repo.active_branch.name if repo.active_branch else 'HEAD'
                }

            result = await asyncio.get_event_loop().run_in_executor(self.executor, _get_history)

            return {
                'status': 'success',
                'repository': str(self._validate_repo_path(repo_path)),
                **result
            }

        except Exception as e:
            logger.error(f"Failed to get commit history for {repo_path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def get_file_blame(self, repo_path: Union[str, Path], file_path: str) -> Dict[str, Any]:
        """
        Get line-by-line authorship information for a file.

        Args:
            repo_path: Path to git repository
            file_path: File path to get blame for

        Returns:
            Dict with blame information
        """
        try:
            def _get_blame():
                repo = self._get_repo(repo_path)

                # Validate file path
                full_file_path = Path(repo.working_dir) / file_path
                try:
                    full_file_path.relative_to(Path(repo.working_dir))
                except ValueError:
                    raise GitSecurityError(f"File path outside repository: {file_path}")

                if not full_file_path.exists():
                    raise GitOperationError(f"File not found: {file_path}")

                # Get blame information
                blame_data = []
                for commit, lines in repo.blame('HEAD', file_path):
                    for line_no, line_content in enumerate(lines, 1):
                        blame_data.append({
                            'line_number': line_no,
                            'content': line_content.rstrip('\n\r'),
                            'commit_hash': commit.hexsha,
                            'short_hash': commit.hexsha[:8],
                            'author': f"{commit.author.name} <{commit.author.email}>",
                            'timestamp': commit.committed_date,
                            'message': commit.message.strip().split('\n')[0]  # First line only
                        })

                return {
                    'file_path': file_path,
                    'lines': blame_data,
                    'total_lines': len(blame_data)
                }

            result = await asyncio.get_event_loop().run_in_executor(self.executor, _get_blame)

            return {
                'status': 'success',
                'repository': str(self._validate_repo_path(repo_path)),
                **result
            }

        except Exception as e:
            logger.error(f"Failed to get blame for {file_path} in {repo_path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def git_stash_operations(self, repo_path: Union[str, Path],
                                  operation: str,
                                  message: Optional[str] = None,
                                  stash_index: Optional[int] = None) -> Dict[str, Any]:
        """
        Git stash operations for temporary changes.

        Args:
            repo_path: Path to git repository
            operation: Operation type ('save', 'pop', 'list', 'drop', 'apply')
            message: Optional message for stash save
            stash_index: Optional stash index for pop/drop/apply operations

        Returns:
            Dict with stash operation result
        """
        try:
            if operation not in ['save', 'pop', 'list', 'drop', 'apply']:
                return {
                    'status': 'error',
                    'error': f'Invalid stash operation: {operation}',
                    'error_type': 'ValidationError'
                }

            def _stash_operation():
                repo = self._get_repo(repo_path)

                if operation == 'save':
                    if not repo.is_dirty():
                        return {
                            'saved': False,
                            'reason': 'No changes to stash'
                        }

                    stash_msg = message or f"WIP on {repo.active_branch.name}: {repo.head.commit.hexsha[:8]}"
                    repo.git.stash('save', stash_msg)

                    return {
                        'saved': True,
                        'message': stash_msg
                    }

                elif operation == 'list':
                    stash_list = repo.git.stash('list').split('\n') if repo.git.stash('list') else []
                    stashes = []
                    for i, stash_entry in enumerate(stash_list):
                        if stash_entry.strip():
                            stashes.append({
                                'index': i,
                                'description': stash_entry.strip()
                            })

                    return {
                        'stashes': stashes,
                        'count': len(stashes)
                    }

                elif operation in ['pop', 'drop', 'apply']:
                    stash_ref = f'stash@{{{stash_index}}}' if stash_index is not None else 'stash@{0}'

                    try:
                        if operation == 'pop':
                            repo.git.stash('pop', stash_ref)
                        elif operation == 'drop':
                            repo.git.stash('drop', stash_ref)
                        elif operation == 'apply':
                            repo.git.stash('apply', stash_ref)

                        return {
                            'success': True,
                            'operation': operation,
                            'stash_ref': stash_ref
                        }
                    except GitCommandError as e:
                        if 'No stash entries found' in str(e):
                            return {
                                'success': False,
                                'reason': 'No stash entries found'
                            }
                        raise

            result = await asyncio.get_event_loop().run_in_executor(self.executor, _stash_operation)

            return {
                'status': 'success',
                'repository': str(self._validate_repo_path(repo_path)),
                'operation': operation,
                **result
            }

        except Exception as e:
            logger.error(f"Failed stash operation {operation} in {repo_path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    def cleanup(self):
        """Clean up resources."""
        try:
            self.executor.shutdown(wait=True)
            logger.info("GitOperations cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Global instance
git_operations = GitOperations()
