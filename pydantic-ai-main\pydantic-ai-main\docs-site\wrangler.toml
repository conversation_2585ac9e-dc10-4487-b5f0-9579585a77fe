#:schema node_modules/wrangler/config-schema.json
name = "pydantic-ai"
compatibility_date = "2025-01-24"
routes = ["ai.pydantic.dev/*"]
main = "src/index.ts"
workers_dev = false

[assets]
directory = "../site"
binding = "ASSETS"
# so we can rewrite the HTML to insert the version notice
run_worker_first = true

[[kv_namespaces]]
binding = "KV"
id = "4e49a275426244488a79d572da40eb69"

[env.previews]
workers_dev = true
routes = []

[[env.previews.kv_namespaces]]
binding = "KV"
id = "95eb127177234122896b4f0a80412f1b"
