#!/usr/bin/env python3
"""
Test Script for Enhanced Error Recovery System

This script validates the functionality of the enhanced error recovery system
including retry mechanisms, error categorization, recovery strategies, and
user notifications.
"""

import asyncio
import logging
import sys
import time
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

# Import error handling components directly to avoid circular imports
from app.error_handling.retry_manager import EnhancedRetryManager, RetryConfig
from app.error_handling.error_categorizer import <PERSON>rrorCategorizer, ErrorCategory
from app.error_handling.recovery_strategies import RecoveryStrategies
from app.error_handling.user_notifications import UserNotificationManager, NotificationLevel
from app.error_handling.integration import ErrorHandlingIntegration

# Create mock exceptions for testing
class MockModelRetry(Exception):
    """Mock ModelRetry exception for testing."""
    def __init__(self, message: str):
        self.message = message
        super().__init__(message)

class MockModelHTTPError(Exception):
    """Mock ModelHTTPError exception for testing."""
    pass

class MockUnexpectedModelBehavior(Exception):
    """Mock UnexpectedModelBehavior exception for testing."""
    pass

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TestErrorRecoverySystem:
    """Comprehensive test suite for the enhanced error recovery system."""
    
    def __init__(self):
        self.retry_manager = EnhancedRetryManager()
        self.error_categorizer = ErrorCategorizer()
        self.recovery_strategies = RecoveryStrategies()
        self.notification_manager = UserNotificationManager()
        self.error_integration = ErrorHandlingIntegration(
            retry_manager=self.retry_manager,
            error_categorizer=self.error_categorizer,
            recovery_strategies=self.recovery_strategies,
            notification_manager=self.notification_manager
        )
        
        self.test_results = {}
        self.total_tests = 0
        self.passed_tests = 0
    
    def log_test_result(self, test_name: str, passed: bool, details: str = ""):
        """Log test result."""
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
            logger.info(f"✅ {test_name}: PASSED {details}")
        else:
            logger.error(f"❌ {test_name}: FAILED {details}")
        
        self.test_results[test_name] = {
            'passed': passed,
            'details': details
        }
    
    async def test_retry_manager(self):
        """Test the enhanced retry manager."""
        logger.info("🔄 Testing Enhanced Retry Manager...")
        
        # Test 1: Basic retry functionality
        attempt_count = 0
        
        async def failing_function():
            nonlocal attempt_count
            attempt_count += 1
            if attempt_count < 3:
                raise ConnectionError("Simulated connection error")
            return "Success after retries"
        
        config = RetryConfig(max_attempts=3, base_delay=0.1)
        result = await self.retry_manager.execute_with_retry(
            failing_function, config, "test_retry"
        )
        
        self.log_test_result(
            "retry_manager_basic_retry",
            result.success and result.attempt_count == 3,
            f"Attempts: {result.attempt_count}, Success: {result.success}"
        )
        
        # Test 2: Error categorization in retry manager
        try:
            raise MockModelRetry("Test model retry")
        except Exception as e:
            reason = self.retry_manager.categorize_error(e)
            self.log_test_result(
                "retry_manager_error_categorization",
                reason.value == "system_error",  # Mock will be categorized as system error
                f"Categorized as: {reason.value}"
            )
        
        # Test 3: Circuit breaker integration
        circuit_breaker = self.retry_manager.get_circuit_breaker("test_service", config)
        self.log_test_result(
            "retry_manager_circuit_breaker",
            circuit_breaker is not None,
            "Circuit breaker created successfully"
        )
    
    async def test_error_categorizer(self):
        """Test the error categorizer."""
        logger.info("🏷️ Testing Error Categorizer...")
        
        # Test different error types
        test_errors = [
            (MockModelRetry("Test retry"), ErrorCategory.UNKNOWN),  # Mock will be unknown
            (ConnectionError("Connection failed"), ErrorCategory.NETWORK_CONNECTION),
            (TimeoutError("Request timeout"), ErrorCategory.NETWORK_TIMEOUT),
            (ValueError("Invalid input"), ErrorCategory.USER_INPUT),
        ]
        
        for error, expected_category in test_errors:
            context = self.error_categorizer.classify_error(error)
            self.log_test_result(
                f"error_categorizer_{expected_category.value}",
                context.category == expected_category,
                f"Expected: {expected_category.value}, Got: {context.category.value}"
            )
        
        # Test statistics
        stats = self.error_categorizer.get_statistics()
        self.log_test_result(
            "error_categorizer_statistics",
            stats.get('total_classifications', 0) > 0,
            f"Total classifications: {stats.get('total_classifications', 0)}"
        )
    
    async def test_recovery_strategies(self):
        """Test recovery strategies."""
        logger.info("🔧 Testing Recovery Strategies...")
        
        # Test recovery action selection
        mock_error_context = type('MockErrorContext', (), {
            'category': ErrorCategory.API_RATE_LIMIT,
            'severity': type('MockSeverity', (), {'HIGH': 'high'})(),
            'max_retries': 3,
            'error': Exception("Test error")
        })()
        
        action = self.recovery_strategies._select_recovery_action(mock_error_context)
        self.log_test_result(
            "recovery_strategies_action_selection",
            action.value == "wait_and_retry",
            f"Selected action: {action.value}"
        )
        
        # Test recovery application
        recovery_result = await self.recovery_strategies.apply_recovery(mock_error_context)
        self.log_test_result(
            "recovery_strategies_application",
            recovery_result is not None,
            f"Recovery status: {recovery_result.status.value if recovery_result else 'None'}"
        )
        
        # Test statistics
        stats = self.recovery_strategies.get_recovery_statistics()
        self.log_test_result(
            "recovery_strategies_statistics",
            stats.get('total_attempts', 0) > 0,
            f"Total attempts: {stats.get('total_attempts', 0)}"
        )
    
    async def test_user_notifications(self):
        """Test user notification manager."""
        logger.info("📢 Testing User Notification Manager...")
        
        # Create a mock error context
        mock_error_context = type('MockErrorContext', (), {
            'category': ErrorCategory.MODEL_ERROR,
            'severity': type('MockSeverity', (), {'MEDIUM': 'medium'})(),
            'error': Exception("Test error"),
            'suggested_actions': ["Test action"],
            'confidence': 0.9,
            'timestamp': type('MockTimestamp', (), {'isoformat': lambda: '2024-01-01T00:00:00'})(),
            'session_id': 'test_session',
            'user_id': 'test_user'
        })()
        
        # Test error notification creation
        notification = self.notification_manager.create_error_notification(
            error_context=mock_error_context,
            operation="test_operation"
        )
        
        self.log_test_result(
            "user_notifications_error_creation",
            notification.level == NotificationLevel.WARNING,
            f"Notification level: {notification.level.value}"
        )
        
        # Test active notifications
        active = self.notification_manager.get_active_notifications()
        self.log_test_result(
            "user_notifications_active_list",
            len(active) > 0,
            f"Active notifications: {len(active)}"
        )
        
        # Test notification dismissal
        dismissed = self.notification_manager.dismiss_notification(notification.notification_id)
        self.log_test_result(
            "user_notifications_dismissal",
            dismissed,
            "Notification dismissed successfully"
        )
    
    async def test_integration_system(self):
        """Test the integrated error handling system."""
        logger.info("🔗 Testing Integration System...")
        
        # Test comprehensive error handling
        try:
            raise MockModelRetry("Integration test error")
        except Exception as e:
            result = await self.error_integration.handle_error_with_recovery(
                error=e,
                operation="integration_test",
                session_id="test_session",
                show_technical_details=True
            )
            
            self.log_test_result(
                "integration_error_handling",
                result['error_handled'],
                f"Category: {result['error_category']}"
            )
        
        # Test function execution with error handling
        async def test_function():
            raise ConnectionError("Test connection error")
        
        result = await self.error_integration.execute_with_enhanced_error_handling(
            func=test_function,
            operation="test_function_execution"
        )
        
        self.log_test_result(
            "integration_function_execution",
            result['error_handled'],
            f"Success: {result['success']}"
        )
        
        # Test system health
        health = self.error_integration.get_system_health()
        self.log_test_result(
            "integration_system_health",
            'overall_health' in health,
            f"Health status: {health.get('overall_health', 'unknown')}"
        )
        
        # Test built-in system test
        system_test = await self.error_integration.test_error_handling_system()
        self.log_test_result(
            "integration_system_test",
            system_test['system_ready'],
            f"Test success rate: {system_test['test_summary']['success_rate']:.2f}"
        )
    
    async def test_decorator_functionality(self):
        """Test the error handling decorator."""
        logger.info("🎭 Testing Decorator Functionality...")
        
        # Create decorator
        @self.error_integration.create_error_handling_decorator(
            operation="decorated_function_test",
            retry_config=RetryConfig(max_attempts=2)
        )
        async def decorated_function():
            raise ValueError("Decorator test error")
        
        # Test decorated function
        result = await decorated_function()
        
        self.log_test_result(
            "decorator_functionality",
            result['error_handled'],
            f"Error handled: {result['error_handled']}"
        )
    
    async def test_performance_metrics(self):
        """Test performance and metrics collection."""
        logger.info("📊 Testing Performance Metrics...")
        
        start_time = time.time()
        
        # Generate multiple errors to test performance
        for i in range(10):
            try:
                if i % 3 == 0:
                    raise MockModelRetry(f"Test error {i}")
                elif i % 3 == 1:
                    raise ConnectionError(f"Connection error {i}")
                else:
                    raise ValueError(f"Value error {i}")
            except Exception as e:
                await self.error_integration.handle_error_with_recovery(
                    error=e,
                    operation=f"performance_test_{i}"
                )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        self.log_test_result(
            "performance_metrics",
            processing_time < 5.0,  # Should handle 10 errors in under 5 seconds
            f"Processing time: {processing_time:.2f}s for 10 errors"
        )
        
        # Test statistics collection
        health = self.error_integration.get_system_health()
        error_count = health['error_handling_stats']['total_errors_handled']
        
        self.log_test_result(
            "metrics_collection",
            error_count >= 10,
            f"Total errors handled: {error_count}"
        )
    
    async def run_all_tests(self):
        """Run all test suites."""
        logger.info("🚀 Starting Enhanced Error Recovery System Tests...")
        
        test_suites = [
            self.test_retry_manager,
            self.test_error_categorizer,
            self.test_recovery_strategies,
            self.test_user_notifications,
            self.test_integration_system,
            self.test_decorator_functionality,
            self.test_performance_metrics
        ]
        
        for test_suite in test_suites:
            try:
                await test_suite()
            except Exception as e:
                logger.error(f"Test suite {test_suite.__name__} failed with exception: {e}")
                self.log_test_result(test_suite.__name__, False, f"Exception: {e}")
        
        # Print final results
        self.print_test_summary()
    
    def print_test_summary(self):
        """Print comprehensive test summary."""
        logger.info("=" * 60)
        logger.info("📋 ENHANCED ERROR RECOVERY SYSTEM TEST SUMMARY")
        logger.info("=" * 60)
        
        success_rate = (self.passed_tests / self.total_tests) * 100 if self.total_tests > 0 else 0
        
        logger.info(f"Total Tests: {self.total_tests}")
        logger.info(f"Passed Tests: {self.passed_tests}")
        logger.info(f"Failed Tests: {self.total_tests - self.passed_tests}")
        logger.info(f"Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 90:
            logger.info("🎉 EXCELLENT: System is ready for production!")
        elif success_rate >= 75:
            logger.info("✅ GOOD: System is functional with minor issues")
        elif success_rate >= 50:
            logger.info("⚠️ FAIR: System needs improvements")
        else:
            logger.info("❌ POOR: System requires significant fixes")
        
        # Print failed tests
        failed_tests = [name for name, result in self.test_results.items() if not result['passed']]
        if failed_tests:
            logger.info("\n❌ Failed Tests:")
            for test_name in failed_tests:
                details = self.test_results[test_name]['details']
                logger.info(f"  - {test_name}: {details}")
        
        logger.info("=" * 60)


async def main():
    """Main test execution function."""
    test_system = TestErrorRecoverySystem()
    await test_system.run_all_tests()
    
    # Return exit code based on test results
    success_rate = (test_system.passed_tests / test_system.total_tests) * 100 if test_system.total_tests > 0 else 0
    return 0 if success_rate >= 75 else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
