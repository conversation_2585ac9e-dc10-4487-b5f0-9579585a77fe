services:
  frontend:
    container_name: deepnexus_frontend
    build: ./frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend
    environment:
      - VITE_BACKEND_URL=http://localhost:8000
    networks:
      - deepnexus_network

  backend:
    container_name: deepnexus_backend
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      # Pydantic AI Configuration
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - LOGFIRE_TOKEN=${LOGFIRE_TOKEN:-}
      - CODING_MODEL=${CODING_MODEL:-deepseek/deepseek-r1-0528:free}
      - VISION_MODEL=${VISION_MODEL:-google/gemini-2.0-flash-exp:free}
      - EMBEDDING_MODEL=${EMBEDDING_MODEL:-bge-m3}

      # Infrastructure
      - OLLAMA_HOST=${OLLAMA_HOST:-http://ollama:11434}
      - QDRANT_URL=${QDRANT_URL:-http://qdrant:6333}
      - BACKEND_WORKERS=${BACKEND_WORKERS:-4}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - WORKSPACE_ROOT=${WORKSPACE_ROOT:-/app/workspace}
    volumes:
      - ./backend:/app  # Mount entire backend directory for fast development
      - ./backend/data:/app/data
      - ./backend/workspace:/app/workspace
    depends_on:
      - qdrant
      - ollama
    networks:
      - deepnexus_network
    # Enable auto-reload for development with Pydantic AI
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  # Ollama service for BGE-M3 embeddings
  ollama:
    container_name: deepnexus_ollama
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ./ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    healthcheck:
      test: ["CMD", "ollama", "list"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - deepnexus_network

  # Self-hosted Qdrant vector database for code embeddings
  qdrant:
    container_name: deepnexus_qdrant
    image: qdrant/qdrant:v1.8.3
    ports:
      - "6333:6333"
      - "6334:6334"  # gRPC port
    environment:
      - QDRANT__SERVICE__GRPC_PORT=6334
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__LOG_LEVEL=info
    volumes:
      - ./qdrant_storage:/qdrant/storage
    healthcheck:
      test: ["CMD", "/qdrant/qdrant", "--version"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - deepnexus_network

networks:
  deepnexus_network:
    driver: bridge

volumes:
  backend_data:
  backend_workspace:
  ollama_data:
  qdrant_storage:
