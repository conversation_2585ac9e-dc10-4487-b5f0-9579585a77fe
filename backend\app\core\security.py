"""
Enhanced Security and Validation System

Provides comprehensive security features including:
- Input validation and sanitization
- Rate limiting and throttling
- Authentication and authorization
- Audit logging and monitoring
- Data encryption and protection
- Security policy enforcement
"""

import asyncio
import hashlib
import hmac
import logging
import time
import re
import json
from typing import Dict, Any, Optional, List, Callable, Union, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict, deque
import secrets
import base64
from cryptography.fernet import Ferne<PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

logger = logging.getLogger(__name__)


@dataclass
class SecurityPolicy:
    """Security policy configuration."""
    max_file_size_mb: int = 10
    allowed_file_extensions: Set[str] = field(default_factory=lambda: {
        '.py', '.js', '.ts', '.jsx', '.tsx', '.html', '.css', '.json', 
        '.yaml', '.yml', '.md', '.txt', '.sql', '.sh', '.bat'
    })
    blocked_patterns: List[str] = field(default_factory=lambda: [
        r'<script[^>]*>.*?</script>',  # Script tags
        r'javascript:',  # JavaScript URLs
        r'on\w+\s*=',  # Event handlers
        r'eval\s*\(',  # eval() calls
        r'exec\s*\(',  # exec() calls
        r'__import__',  # Python imports
        r'subprocess',  # Subprocess calls
        r'os\.system',  # OS system calls
    ])
    rate_limit_per_minute: int = 100
    session_timeout_minutes: int = 60
    require_authentication: bool = False
    enable_audit_logging: bool = True


class InputValidator:
    """Advanced input validation and sanitization."""
    
    def __init__(self, policy: SecurityPolicy):
        self.policy = policy
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in policy.blocked_patterns]
    
    def validate_file_upload(self, filename: str, content: bytes) -> Dict[str, Any]:
        """Validate file upload."""
        result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Check file size
        size_mb = len(content) / (1024 * 1024)
        if size_mb > self.policy.max_file_size_mb:
            result['valid'] = False
            result['errors'].append(f"File size {size_mb:.2f}MB exceeds limit of {self.policy.max_file_size_mb}MB")
        
        # Check file extension
        file_path = Path(filename)
        if file_path.suffix.lower() not in self.policy.allowed_file_extensions:
            result['valid'] = False
            result['errors'].append(f"File extension {file_path.suffix} not allowed")
        
        # Check content for malicious patterns
        try:
            content_str = content.decode('utf-8', errors='ignore')
            for pattern in self.compiled_patterns:
                if pattern.search(content_str):
                    result['valid'] = False
                    result['errors'].append(f"Content contains blocked pattern: {pattern.pattern}")
        except Exception as e:
            result['warnings'].append(f"Content validation warning: {e}")
        
        return result
    
    def sanitize_input(self, text: str) -> str:
        """Sanitize text input."""
        if not isinstance(text, str):
            return str(text)
        
        # Remove null bytes
        text = text.replace('\x00', '')
        
        # Limit length
        if len(text) > 10000:
            text = text[:10000]
        
        # Remove potentially dangerous patterns
        for pattern in self.compiled_patterns:
            text = pattern.sub('', text)
        
        return text.strip()
    
    def validate_path(self, path: str, allowed_base_paths: List[str]) -> bool:
        """Validate file path to prevent directory traversal."""
        try:
            # Normalize path
            normalized_path = Path(path).resolve()
            
            # Check against allowed base paths
            for base_path in allowed_base_paths:
                base_resolved = Path(base_path).resolve()
                try:
                    normalized_path.relative_to(base_resolved)
                    return True
                except ValueError:
                    continue
            
            return False
        except Exception:
            return False


class RateLimiter:
    """Advanced rate limiting with multiple strategies."""
    
    def __init__(self):
        self.requests = defaultdict(deque)
        self.blocked_ips = defaultdict(datetime)
        self.global_stats = {
            'total_requests': 0,
            'blocked_requests': 0,
            'unique_ips': set()
        }
    
    async def check_rate_limit(
        self,
        identifier: str,
        limit: int = 100,
        window_minutes: int = 1,
        block_duration_minutes: int = 5
    ) -> Dict[str, Any]:
        """Check if request is within rate limits."""
        current_time = datetime.now()
        window_start = current_time - timedelta(minutes=window_minutes)
        
        # Check if IP is currently blocked
        if identifier in self.blocked_ips:
            if current_time < self.blocked_ips[identifier]:
                return {
                    'allowed': False,
                    'reason': 'IP temporarily blocked',
                    'retry_after': (self.blocked_ips[identifier] - current_time).total_seconds()
                }
            else:
                # Unblock IP
                del self.blocked_ips[identifier]
        
        # Clean old requests
        request_times = self.requests[identifier]
        while request_times and request_times[0] < window_start:
            request_times.popleft()
        
        # Check rate limit
        if len(request_times) >= limit:
            # Block IP
            self.blocked_ips[identifier] = current_time + timedelta(minutes=block_duration_minutes)
            self.global_stats['blocked_requests'] += 1
            
            return {
                'allowed': False,
                'reason': 'Rate limit exceeded',
                'requests_in_window': len(request_times),
                'limit': limit,
                'retry_after': block_duration_minutes * 60
            }
        
        # Allow request
        request_times.append(current_time)
        self.global_stats['total_requests'] += 1
        self.global_stats['unique_ips'].add(identifier)
        
        return {
            'allowed': True,
            'requests_in_window': len(request_times),
            'limit': limit,
            'remaining': limit - len(request_times)
        }


class EncryptionManager:
    """Data encryption and decryption management."""
    
    def __init__(self, master_key: Optional[str] = None):
        if master_key:
            self.key = master_key.encode()
        else:
            self.key = Fernet.generate_key()
        
        self.cipher = Fernet(self.key)
    
    def encrypt_data(self, data: Union[str, bytes, Dict[str, Any]]) -> str:
        """Encrypt data and return base64 encoded string."""
        if isinstance(data, dict):
            data = json.dumps(data)
        
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        encrypted = self.cipher.encrypt(data)
        return base64.b64encode(encrypted).decode('utf-8')
    
    def decrypt_data(self, encrypted_data: str) -> bytes:
        """Decrypt base64 encoded encrypted data."""
        encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
        return self.cipher.decrypt(encrypted_bytes)
    
    def decrypt_to_string(self, encrypted_data: str) -> str:
        """Decrypt and return as string."""
        return self.decrypt_data(encrypted_data).decode('utf-8')
    
    def decrypt_to_json(self, encrypted_data: str) -> Dict[str, Any]:
        """Decrypt and return as JSON object."""
        return json.loads(self.decrypt_to_string(encrypted_data))
    
    def generate_hash(self, data: str, salt: Optional[str] = None) -> str:
        """Generate secure hash of data."""
        if salt is None:
            salt = secrets.token_hex(16)
        
        # Use PBKDF2 for key derivation
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt.encode(),
            iterations=100000,
        )
        
        key = kdf.derive(data.encode())
        return f"{salt}:{base64.b64encode(key).decode()}"
    
    def verify_hash(self, data: str, hashed: str) -> bool:
        """Verify data against hash."""
        try:
            salt, hash_b64 = hashed.split(':', 1)
            expected_hash = base64.b64decode(hash_b64)
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt.encode(),
                iterations=100000,
            )
            
            kdf.verify(data.encode(), expected_hash)
            return True
        except Exception:
            return False


class AuditLogger:
    """Security audit logging system."""
    
    def __init__(self, log_file: str = "security_audit.log"):
        self.log_file = log_file
        self.events = deque(maxlen=10000)
        
        # Set up file logging
        self.file_handler = logging.FileHandler(log_file)
        self.file_handler.setFormatter(
            logging.Formatter('%(asctime)s - SECURITY - %(levelname)s - %(message)s')
        )
        
        self.audit_logger = logging.getLogger('security_audit')
        self.audit_logger.addHandler(self.file_handler)
        self.audit_logger.setLevel(logging.INFO)
    
    def log_event(
        self,
        event_type: str,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        severity: str = "INFO"
    ) -> None:
        """Log security event."""
        event = {
            'timestamp': datetime.now().isoformat(),
            'event_type': event_type,
            'user_id': user_id,
            'ip_address': ip_address,
            'details': details or {},
            'severity': severity
        }
        
        self.events.append(event)
        
        # Log to file
        log_message = f"{event_type} - User: {user_id} - IP: {ip_address} - Details: {json.dumps(details)}"
        
        if severity == "ERROR":
            self.audit_logger.error(log_message)
        elif severity == "WARNING":
            self.audit_logger.warning(log_message)
        else:
            self.audit_logger.info(log_message)
    
    def get_recent_events(self, limit: int = 100, event_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get recent security events."""
        events = list(self.events)
        
        if event_type:
            events = [e for e in events if e['event_type'] == event_type]
        
        return events[-limit:]
    
    def get_security_summary(self) -> Dict[str, Any]:
        """Get security summary statistics."""
        events = list(self.events)
        
        event_counts = defaultdict(int)
        severity_counts = defaultdict(int)
        unique_ips = set()
        unique_users = set()
        
        for event in events:
            event_counts[event['event_type']] += 1
            severity_counts[event['severity']] += 1
            
            if event['ip_address']:
                unique_ips.add(event['ip_address'])
            if event['user_id']:
                unique_users.add(event['user_id'])
        
        return {
            'total_events': len(events),
            'event_types': dict(event_counts),
            'severity_distribution': dict(severity_counts),
            'unique_ips': len(unique_ips),
            'unique_users': len(unique_users),
            'time_range': {
                'start': events[0]['timestamp'] if events else None,
                'end': events[-1]['timestamp'] if events else None
            }
        }


class SecurityManager:
    """Main security management system."""
    
    def __init__(self, policy: Optional[SecurityPolicy] = None):
        self.policy = policy or SecurityPolicy()
        self.validator = InputValidator(self.policy)
        self.rate_limiter = RateLimiter()
        self.encryption_manager = EncryptionManager()
        self.audit_logger = AuditLogger()
        
        # Security tokens and sessions
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.api_keys: Dict[str, Dict[str, Any]] = {}
    
    async def validate_request(
        self,
        request_data: Dict[str, Any],
        ip_address: str,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Comprehensive request validation."""
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Rate limiting
        rate_limit_result = await self.rate_limiter.check_rate_limit(
            ip_address,
            self.policy.rate_limit_per_minute
        )
        
        if not rate_limit_result['allowed']:
            validation_result['valid'] = False
            validation_result['errors'].append(rate_limit_result['reason'])
            
            # Log security event
            self.audit_logger.log_event(
                'RATE_LIMIT_EXCEEDED',
                user_id=user_id,
                ip_address=ip_address,
                details=rate_limit_result,
                severity='WARNING'
            )
        
        # Input validation
        for key, value in request_data.items():
            if isinstance(value, str):
                sanitized = self.validator.sanitize_input(value)
                if sanitized != value:
                    validation_result['warnings'].append(f"Input sanitized for field: {key}")
                    request_data[key] = sanitized
        
        # Log successful validation
        if validation_result['valid']:
            self.audit_logger.log_event(
                'REQUEST_VALIDATED',
                user_id=user_id,
                ip_address=ip_address,
                details={'request_size': len(str(request_data))}
            )
        
        return validation_result
    
    def generate_api_key(self, user_id: str, permissions: List[str]) -> str:
        """Generate secure API key."""
        api_key = secrets.token_urlsafe(32)
        
        self.api_keys[api_key] = {
            'user_id': user_id,
            'permissions': permissions,
            'created_at': datetime.now(),
            'last_used': None,
            'usage_count': 0
        }
        
        self.audit_logger.log_event(
            'API_KEY_GENERATED',
            user_id=user_id,
            details={'permissions': permissions}
        )
        
        return api_key
    
    def validate_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """Validate API key and return user info."""
        if api_key in self.api_keys:
            key_info = self.api_keys[api_key]
            key_info['last_used'] = datetime.now()
            key_info['usage_count'] += 1
            
            return {
                'user_id': key_info['user_id'],
                'permissions': key_info['permissions']
            }
        
        self.audit_logger.log_event(
            'INVALID_API_KEY',
            details={'api_key_prefix': api_key[:8] if api_key else None},
            severity='WARNING'
        )
        
        return None
    
    def get_security_status(self) -> Dict[str, Any]:
        """Get comprehensive security status."""
        return {
            'policy': {
                'max_file_size_mb': self.policy.max_file_size_mb,
                'allowed_extensions': list(self.policy.allowed_file_extensions),
                'rate_limit_per_minute': self.policy.rate_limit_per_minute,
                'authentication_required': self.policy.require_authentication,
                'audit_logging_enabled': self.policy.enable_audit_logging
            },
            'rate_limiting': {
                'total_requests': self.rate_limiter.global_stats['total_requests'],
                'blocked_requests': self.rate_limiter.global_stats['blocked_requests'],
                'unique_ips': len(self.rate_limiter.global_stats['unique_ips']),
                'currently_blocked': len(self.rate_limiter.blocked_ips)
            },
            'audit_summary': self.audit_logger.get_security_summary(),
            'active_sessions': len(self.active_sessions),
            'active_api_keys': len(self.api_keys)
        }


# Global security manager
security_manager = SecurityManager()


# Security decorators
def require_authentication(func):
    """Decorator to require authentication."""
    async def wrapper(*args, **kwargs):
        # Implementation would check for valid session/API key
        # This is a placeholder for the actual authentication logic
        return await func(*args, **kwargs)
    return wrapper


def audit_log(event_type: str, severity: str = "INFO"):
    """Decorator to automatically log function calls."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                result = await func(*args, **kwargs)
                security_manager.audit_logger.log_event(
                    event_type,
                    details={'function': func.__name__, 'success': True},
                    severity=severity
                )
                return result
            except Exception as e:
                security_manager.audit_logger.log_event(
                    f"{event_type}_FAILED",
                    details={'function': func.__name__, 'error': str(e)},
                    severity='ERROR'
                )
                raise
        return wrapper
    return decorator
