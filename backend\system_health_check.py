#!/usr/bin/env python3
"""
System Health Check - Comprehensive System Validation

Quick health check to identify any issues with the DeepNexus AI system
and verify all components are functioning correctly.
"""

import asyncio
import logging
import sys
import time
from pathlib import Path

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.WARNING,  # Reduce noise
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def check_basic_imports():
    """Check basic imports."""
    print("🔍 Checking basic imports...")
    
    try:
        # Core imports
        import pydantic_ai
        import logfire
        import httpx
        import openai
        
        print("✅ Core dependencies available")
        
        # App imports
        from app.pydantic_ai.agents import coding_agent, vision_agent
        from app.pydantic_ai.tools.registry import get_coding_tools, get_vision_tools
        
        print("✅ Agent imports successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import check failed: {e}")
        return False


async def check_tool_registry():
    """Check tool registry functionality."""
    print("🔧 Checking tool registry...")
    
    try:
        from app.pydantic_ai.tools.registry import get_coding_tools, get_vision_tools, get_tool_summary
        
        # Get tools
        coding_tools = get_coding_tools()
        vision_tools = get_vision_tools()
        
        print(f"✅ Tool registry: {len(coding_tools)} coding tools, {len(vision_tools)} vision tools")
        
        # Get summary
        summary = get_tool_summary()
        print(f"✅ Tool summary: {summary}")
        
        return True
        
    except Exception as e:
        print(f"❌ Tool registry check failed: {e}")
        return False


async def check_agents():
    """Check agent initialization."""
    print("🤖 Checking agents...")
    
    try:
        from app.pydantic_ai.agents import coding_agent, vision_agent
        
        if coding_agent:
            print("✅ Coding agent available")
        else:
            print("❌ Coding agent not available")
            return False
        
        if vision_agent:
            print("✅ Vision agent available")
        else:
            print("❌ Vision agent not available")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Agent check failed: {e}")
        return False


async def check_code_intelligence():
    """Check Code Intelligence Hub."""
    print("🧠 Checking Code Intelligence Hub...")
    
    try:
        from app.code_intelligence import code_intelligence_hub
        
        if code_intelligence_hub:
            print("✅ Code Intelligence Hub available")
            
            # Quick initialization test
            await code_intelligence_hub.initialize()
            print("✅ Code Intelligence Hub initialized")
            
            return True
        else:
            print("❌ Code Intelligence Hub not available")
            return False
        
    except Exception as e:
        print(f"❌ Code Intelligence check failed: {e}")
        return False


async def check_autonomous_continue():
    """Check Autonomous Continue Mode."""
    print("🔄 Checking Autonomous Continue Mode...")
    
    try:
        from app.autonomous_continue.core import autonomous_continue_engine
        from app.autonomous_continue.models import ContinueConfig, SafetyLimits
        
        if autonomous_continue_engine:
            print("✅ Autonomous Continue Engine available")
            
            # Quick initialization test
            init_result = await autonomous_continue_engine.initialize()
            if init_result['status'] == 'success':
                print("✅ Autonomous Continue Engine initialized")
            else:
                print(f"⚠️ Autonomous Continue Engine initialization warning: {init_result.get('error', 'Unknown')}")
            
            return True
        else:
            print("❌ Autonomous Continue Engine not available")
            return False
        
    except Exception as e:
        print(f"❌ Autonomous Continue check failed: {e}")
        return False


async def check_monitoring():
    """Check monitoring setup."""
    print("📊 Checking monitoring...")
    
    try:
        from app.pydantic_ai.monitoring.logfire_config import setup_monitoring
        
        print("✅ Monitoring configuration available")
        
        # Check if Logfire is configured
        import logfire
        print("✅ Logfire available")
        
        return True
        
    except Exception as e:
        print(f"❌ Monitoring check failed: {e}")
        return False


async def check_docker_services():
    """Check Docker services connectivity."""
    print("🐳 Checking Docker services...")
    
    try:
        # Check Qdrant
        from vector_db import VectorDB
        vector_db = VectorDB()
        print("✅ Qdrant connection available")
        
        # Check Ollama
        from ollama_client import OllamaClient
        ollama = OllamaClient()
        print("✅ Ollama connection available")
        
        return True
        
    except Exception as e:
        print(f"❌ Docker services check failed: {e}")
        return False


async def check_llm_clients():
    """Check LLM client functionality."""
    print("🧠 Checking LLM clients...")
    
    try:
        from llm_client import LLMClient
        
        client = LLMClient()
        print("✅ LLM client initialized")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM client check failed: {e}")
        return False


async def run_health_check():
    """Run comprehensive health check."""
    print("🚀 DeepNexus AI System Health Check")
    print("=" * 50)
    
    checks = [
        ("Basic Imports", check_basic_imports),
        ("Tool Registry", check_tool_registry),
        ("Agents", check_agents),
        ("Code Intelligence", check_code_intelligence),
        ("Autonomous Continue", check_autonomous_continue),
        ("Monitoring", check_monitoring),
        ("Docker Services", check_docker_services),
        ("LLM Clients", check_llm_clients),
    ]
    
    results = []
    
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            result = await check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} check failed with exception: {e}")
            results.append((check_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("🎯 HEALTH CHECK SUMMARY")
    print("="*50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {check_name}")
    
    print(f"\n📊 Results: {passed}/{total} checks passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL CHECKS PASSED! System is healthy!")
        return True
    else:
        print("⚠️ Some checks failed. Issues identified above.")
        return False


if __name__ == "__main__":
    # Run the health check
    success = asyncio.run(run_health_check())
    
    if success:
        print("\n🚀 System is ready for operation!")
        sys.exit(0)
    else:
        print("\n❌ System has issues that need attention.")
        sys.exit(1)
