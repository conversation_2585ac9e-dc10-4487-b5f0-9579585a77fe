"""
💾 Context Cache - Cache frequently used contexts

This module provides intelligent context caching with:
- LRU cache with intelligent eviction
- Context similarity detection
- Compression-aware caching
- Performance optimization
- Memory usage monitoring
"""

import hashlib
import pickle
import time
from collections import OrderedDict
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)


class CacheStrategy(str, Enum):
    """Cache strategies for different scenarios."""
    LRU = "lru"                    # Least Recently Used
    LFU = "lfu"                    # Least Frequently Used
    TTL = "ttl"                    # Time To Live
    ADAPTIVE = "adaptive"          # Adaptive based on usage patterns


class CacheEntry(BaseModel):
    """Cache entry with metadata."""
    
    cache_key: str = Field(..., description="Unique cache key")
    content: str = Field(..., description="Cached content")
    content_hash: str = Field(..., description="Content hash for integrity")
    
    # Cache metadata
    created_at: datetime = Field(default_factory=datetime.now, description="Creation timestamp")
    last_accessed: datetime = Field(default_factory=datetime.now, description="Last access timestamp")
    access_count: int = Field(default=1, description="Number of accesses")
    hit_count: int = Field(default=0, description="Cache hit count")
    
    # Content metadata
    content_type: str = Field(default="general", description="Type of cached content")
    compression_ratio: float = Field(default=0.0, description="Compression ratio if compressed")
    original_size: int = Field(..., description="Original content size in bytes")
    cached_size: int = Field(..., description="Cached content size in bytes")
    
    # Performance metadata
    generation_time: float = Field(default=0.0, description="Time to generate content")
    compression_time: float = Field(default=0.0, description="Time to compress content")
    
    # TTL settings
    ttl_seconds: Optional[int] = Field(None, description="Time to live in seconds")
    expires_at: Optional[datetime] = Field(None, description="Expiration timestamp")
    
    def is_expired(self) -> bool:
        """Check if cache entry is expired."""
        if self.expires_at is None:
            return False
        return datetime.now() > self.expires_at
    
    def update_access(self) -> None:
        """Update access metadata."""
        self.last_accessed = datetime.now()
        self.access_count += 1
        self.hit_count += 1


class ContextCache:
    """
    💾 Advanced Context Cache
    
    Provides intelligent caching of frequently used contexts with
    compression awareness and performance optimization.
    """
    
    def __init__(
        self,
        max_size: int = 1000,
        max_memory_mb: int = 100,
        default_ttl: int = 3600,  # 1 hour
        strategy: CacheStrategy = CacheStrategy.ADAPTIVE
    ):
        self.max_size = max_size
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.default_ttl = default_ttl
        self.strategy = strategy
        
        # Cache storage
        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()
        
        # Performance tracking
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "total_requests": 0,
            "memory_usage": 0,
            "average_generation_time": 0.0,
            "average_compression_time": 0.0
        }
        
        # Similarity detection
        self.similarity_threshold = 0.85
        self.content_hashes: Dict[str, str] = {}  # hash -> cache_key mapping
        
        # Adaptive strategy parameters
        self.access_patterns: Dict[str, List[datetime]] = {}
        self.popularity_scores: Dict[str, float] = {}
    
    def get(self, cache_key: str) -> Optional[str]:
        """Get content from cache."""
        self.cache_stats["total_requests"] += 1
        
        if cache_key not in self.cache:
            self.cache_stats["misses"] += 1
            return None
        
        entry = self.cache[cache_key]
        
        # Check if expired
        if entry.is_expired():
            self.remove(cache_key)
            self.cache_stats["misses"] += 1
            return None
        
        # Update access metadata
        entry.update_access()
        
        # Move to end for LRU
        if self.strategy in [CacheStrategy.LRU, CacheStrategy.ADAPTIVE]:
            self.cache.move_to_end(cache_key)
        
        # Update access patterns
        self._update_access_patterns(cache_key)
        
        self.cache_stats["hits"] += 1
        logger.debug(f"Cache hit for key: {cache_key}")
        
        return entry.content
    
    def put(
        self,
        cache_key: str,
        content: str,
        content_type: str = "general",
        compression_ratio: float = 0.0,
        generation_time: float = 0.0,
        compression_time: float = 0.0,
        ttl_seconds: Optional[int] = None
    ) -> bool:
        """Put content into cache."""
        try:
            # Calculate content hash
            content_hash = self._calculate_hash(content)
            
            # Check for similar content
            similar_key = self._find_similar_content(content_hash)
            if similar_key and similar_key != cache_key:
                logger.debug(f"Similar content found, linking {cache_key} to {similar_key}")
                # Create alias entry
                return self._create_alias(cache_key, similar_key)
            
            # Calculate sizes
            original_size = len(content.encode('utf-8'))
            cached_size = original_size
            
            # Set TTL
            ttl = ttl_seconds or self.default_ttl
            expires_at = datetime.now() + timedelta(seconds=ttl) if ttl > 0 else None
            
            # Create cache entry
            entry = CacheEntry(
                cache_key=cache_key,
                content=content,
                content_hash=content_hash,
                content_type=content_type,
                compression_ratio=compression_ratio,
                original_size=original_size,
                cached_size=cached_size,
                generation_time=generation_time,
                compression_time=compression_time,
                ttl_seconds=ttl,
                expires_at=expires_at
            )
            
            # Check memory limits before adding
            if not self._check_memory_limits(entry):
                self._evict_entries()
                if not self._check_memory_limits(entry):
                    logger.warning(f"Cannot cache entry {cache_key}: memory limit exceeded")
                    return False
            
            # Add to cache
            self.cache[cache_key] = entry
            self.content_hashes[content_hash] = cache_key
            
            # Update memory usage
            self._update_memory_usage()
            
            # Update statistics
            self._update_generation_stats(generation_time, compression_time)
            
            # Evict if necessary
            if len(self.cache) > self.max_size:
                self._evict_entries()
            
            logger.debug(f"Cached content with key: {cache_key}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cache content: {e}")
            return False
    
    def remove(self, cache_key: str) -> bool:
        """Remove entry from cache."""
        if cache_key not in self.cache:
            return False
        
        entry = self.cache[cache_key]
        
        # Remove from cache
        del self.cache[cache_key]
        
        # Remove from content hashes
        if entry.content_hash in self.content_hashes:
            del self.content_hashes[entry.content_hash]
        
        # Remove from access patterns
        if cache_key in self.access_patterns:
            del self.access_patterns[cache_key]
        
        if cache_key in self.popularity_scores:
            del self.popularity_scores[cache_key]
        
        # Update memory usage
        self._update_memory_usage()
        
        logger.debug(f"Removed cache entry: {cache_key}")
        return True
    
    def clear(self) -> None:
        """Clear all cache entries."""
        self.cache.clear()
        self.content_hashes.clear()
        self.access_patterns.clear()
        self.popularity_scores.clear()
        self._update_memory_usage()
        logger.info("Cache cleared")
    
    def get_similar_content(self, content: str, threshold: float = None) -> Optional[str]:
        """Find similar cached content."""
        threshold = threshold or self.similarity_threshold
        content_hash = self._calculate_hash(content)
        
        # Check for exact match first
        if content_hash in self.content_hashes:
            cache_key = self.content_hashes[content_hash]
            return self.get(cache_key)
        
        # Check for similar content (simplified similarity check)
        content_words = set(content.lower().split())
        
        for cached_hash, cache_key in self.content_hashes.items():
            if cache_key not in self.cache:
                continue
            
            cached_content = self.cache[cache_key].content
            cached_words = set(cached_content.lower().split())
            
            # Calculate Jaccard similarity
            if cached_words and content_words:
                intersection = content_words.intersection(cached_words)
                union = content_words.union(cached_words)
                similarity = len(intersection) / len(union)
                
                if similarity >= threshold:
                    logger.debug(f"Found similar content with {similarity:.2f} similarity")
                    return self.get(cache_key)
        
        return None
    
    def _find_similar_content(self, content_hash: str) -> Optional[str]:
        """Find similar content by hash."""
        return self.content_hashes.get(content_hash)
    
    def _create_alias(self, alias_key: str, target_key: str) -> bool:
        """Create an alias to existing cache entry."""
        if target_key not in self.cache:
            return False
        
        # Create a lightweight alias entry
        target_entry = self.cache[target_key]
        alias_entry = CacheEntry(
            cache_key=alias_key,
            content=target_entry.content,
            content_hash=target_entry.content_hash,
            content_type=target_entry.content_type,
            compression_ratio=target_entry.compression_ratio,
            original_size=target_entry.original_size,
            cached_size=0,  # Alias doesn't count toward memory
            generation_time=0.0,
            compression_time=0.0,
            ttl_seconds=target_entry.ttl_seconds,
            expires_at=target_entry.expires_at
        )
        
        self.cache[alias_key] = alias_entry
        return True
    
    def _check_memory_limits(self, new_entry: CacheEntry) -> bool:
        """Check if adding new entry would exceed memory limits."""
        current_memory = self.cache_stats["memory_usage"]
        new_memory = current_memory + new_entry.cached_size
        return new_memory <= self.max_memory_bytes
    
    def _evict_entries(self) -> None:
        """Evict entries based on strategy."""
        if not self.cache:
            return
        
        evicted_count = 0
        target_evictions = max(1, len(self.cache) // 10)  # Evict 10% at a time
        
        if self.strategy == CacheStrategy.LRU:
            # Evict least recently used
            while len(self.cache) > self.max_size - target_evictions and self.cache:
                oldest_key = next(iter(self.cache))
                self.remove(oldest_key)
                evicted_count += 1
        
        elif self.strategy == CacheStrategy.LFU:
            # Evict least frequently used
            sorted_entries = sorted(
                self.cache.items(),
                key=lambda x: x[1].access_count
            )
            for key, _ in sorted_entries[:target_evictions]:
                self.remove(key)
                evicted_count += 1
        
        elif self.strategy == CacheStrategy.TTL:
            # Evict expired entries first
            expired_keys = [
                key for key, entry in self.cache.items()
                if entry.is_expired()
            ]
            for key in expired_keys:
                self.remove(key)
                evicted_count += 1
        
        elif self.strategy == CacheStrategy.ADAPTIVE:
            # Adaptive eviction based on popularity and recency
            self._adaptive_eviction(target_evictions)
            evicted_count = target_evictions
        
        self.cache_stats["evictions"] += evicted_count
        logger.debug(f"Evicted {evicted_count} cache entries")
    
    def _adaptive_eviction(self, target_evictions: int) -> None:
        """Adaptive eviction strategy."""
        # Calculate popularity scores
        self._update_popularity_scores()
        
        # Sort by combined score (popularity + recency)
        scored_entries = []
        for key, entry in self.cache.items():
            popularity = self.popularity_scores.get(key, 0.0)
            recency = self._calculate_recency_score(entry.last_accessed)
            combined_score = popularity * 0.7 + recency * 0.3
            scored_entries.append((key, combined_score))
        
        # Sort by score (lowest first for eviction)
        scored_entries.sort(key=lambda x: x[1])
        
        # Evict lowest scoring entries
        for key, _ in scored_entries[:target_evictions]:
            self.remove(key)
    
    def _update_access_patterns(self, cache_key: str) -> None:
        """Update access patterns for adaptive strategy."""
        if cache_key not in self.access_patterns:
            self.access_patterns[cache_key] = []
        
        self.access_patterns[cache_key].append(datetime.now())
        
        # Keep only recent accesses (last 24 hours)
        cutoff = datetime.now() - timedelta(hours=24)
        self.access_patterns[cache_key] = [
            access_time for access_time in self.access_patterns[cache_key]
            if access_time > cutoff
        ]
    
    def _update_popularity_scores(self) -> None:
        """Update popularity scores based on access patterns."""
        for cache_key, accesses in self.access_patterns.items():
            if not accesses:
                self.popularity_scores[cache_key] = 0.0
                continue
            
            # Calculate frequency (accesses per hour)
            hours_span = max(1, (datetime.now() - accesses[0]).total_seconds() / 3600)
            frequency = len(accesses) / hours_span
            
            # Calculate recency bonus
            latest_access = max(accesses)
            hours_since_access = (datetime.now() - latest_access).total_seconds() / 3600
            recency_bonus = max(0, 1 - hours_since_access / 24)  # Decay over 24 hours
            
            # Combined popularity score
            self.popularity_scores[cache_key] = frequency * (1 + recency_bonus)
    
    def _calculate_recency_score(self, last_accessed: datetime) -> float:
        """Calculate recency score (0-1)."""
        hours_since = (datetime.now() - last_accessed).total_seconds() / 3600
        return max(0, 1 - hours_since / 24)  # Decay over 24 hours
    
    def _calculate_hash(self, content: str) -> str:
        """Calculate content hash."""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    def _update_memory_usage(self) -> None:
        """Update memory usage statistics."""
        total_memory = sum(entry.cached_size for entry in self.cache.values())
        self.cache_stats["memory_usage"] = total_memory
    
    def _update_generation_stats(self, generation_time: float, compression_time: float) -> None:
        """Update generation time statistics."""
        total_entries = len(self.cache)
        if total_entries == 0:
            return
        
        current_avg_gen = self.cache_stats["average_generation_time"]
        current_avg_comp = self.cache_stats["average_compression_time"]
        
        self.cache_stats["average_generation_time"] = (
            (current_avg_gen * (total_entries - 1) + generation_time) / total_entries
        )
        
        self.cache_stats["average_compression_time"] = (
            (current_avg_comp * (total_entries - 1) + compression_time) / total_entries
        )
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        hit_rate = (
            self.cache_stats["hits"] / self.cache_stats["total_requests"]
            if self.cache_stats["total_requests"] > 0 else 0
        )
        
        return {
            **self.cache_stats,
            "hit_rate": hit_rate,
            "cache_size": len(self.cache),
            "memory_usage_mb": self.cache_stats["memory_usage"] / (1024 * 1024),
            "memory_utilization": (
                self.cache_stats["memory_usage"] / self.max_memory_bytes
                if self.max_memory_bytes > 0 else 0
            ),
            "strategy": self.strategy.value,
            "similarity_threshold": self.similarity_threshold
        }
    
    def optimize_cache(self) -> Dict[str, Any]:
        """Optimize cache performance."""
        optimization_results = {
            "entries_before": len(self.cache),
            "memory_before": self.cache_stats["memory_usage"],
            "optimizations_applied": []
        }
        
        # Remove expired entries
        expired_keys = [
            key for key, entry in self.cache.items()
            if entry.is_expired()
        ]
        for key in expired_keys:
            self.remove(key)
        
        if expired_keys:
            optimization_results["optimizations_applied"].append(f"Removed {len(expired_keys)} expired entries")
        
        # Update popularity scores
        self._update_popularity_scores()
        optimization_results["optimizations_applied"].append("Updated popularity scores")
        
        # Update final stats
        optimization_results["entries_after"] = len(self.cache)
        optimization_results["memory_after"] = self.cache_stats["memory_usage"]
        optimization_results["memory_saved"] = optimization_results["memory_before"] - optimization_results["memory_after"]
        
        return optimization_results


# Global context cache instance
_context_cache: Optional[ContextCache] = None


def get_context_cache() -> ContextCache:
    """Get the global context cache instance."""
    global _context_cache
    if _context_cache is None:
        _context_cache = ContextCache()
    return _context_cache
