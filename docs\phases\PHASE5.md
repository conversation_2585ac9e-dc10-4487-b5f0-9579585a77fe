# 🚀 **Phase 5 Implementation Instructions for AI Coder Agent**

## 📋 **Current Status**
Phases 1-4 in @IMPLEMENTATION_PLAN.md are **COMPLETE** ✅. The foundation is rock-solid:
- ✅ **Phase 1-2**: Foundation and external service integration (OpenRouter, Ollama BGE-M3, Qdrant)
- ✅ **Phase 3**: Advanced code operations with 5 major modules and 30+ API endpoints
- ✅ **Phase 4**: Fast development workflow with volume mounting (no Docker rebuilds needed!)

## 🎯 **Phase 5 Objective**
Implement **Task Runner & Core Agent Logic** as defined in `IMPLEMENTATION_PLAN.md`:
- Lint and Test Utilities for code quality automation
- Core Task Runner for AI-driven coding workflows
- Enhanced Memory and Context Retrieval using Phase 3 capabilities

## 🔧 **Prerequisites Check**
Before starting, verify Phases 1-4 completion:
```bash
# 1. Ensure development environment is running (FAST METHOD!)
docker-compose -f docker-compose.dev.yml ps
# Expected: All containers showing as "healthy" with volume mounting

# 2. Run Phase 3 completion test
python test_phase3_complete.py
# Expected: "7/8 tests passed" (Module Imports failure is expected outside Docker)

# 3. Test Phase 3 integration endpoint
curl -X POST http://localhost:8000/api/test/phase3-integration
# Expected: All 5 modules (file_operations, git_operations, code_analyzer, change_tracker, repo_context) working

# 4. Verify fast development workflow
# Make a small change to backend/main.py and see it reload instantly (no rebuild!)
```

## 🚨 **CRITICAL: Use Fast Development Workflow**
**DO NOT use `docker-compose build` during development!** The previous AI set up a fast development system:

```bash
# Use the FAST development environment (volume mounting)
docker-compose -f docker-compose.dev.yml up -d

# Your code changes will be reflected INSTANTLY without rebuilds
# Backend: ./backend -> /app (auto-reload enabled)
# Frontend: ./frontend -> /app (hot reload enabled)

# Quick commands available:
. .\dev_commands.ps1
dev-start      # Start with volume mounting
dev-test       # Test Phase 3 functionality
dev-logs       # View backend logs
dev-restart    # Restart backend only
```

## 📝 **Phase 5 Implementation Tasks**

### **Task 5.1: Lint and Test Utilities**

1. **Create `backend/utils/lint_test_utils.py`**:
```python
# Required features from IMPLEMENTATION_PLAN.md:
# - run_python_linting() using flake8 with configurable rules
# - run_python_tests() using pytest with coverage reporting
# - run_javascript_linting() using eslint with project configs
# - run_javascript_tests() using npm test with timeout handling
# - parse_test_results() for structured output and error reporting
# - load_project_config() from .aiagent.json for custom commands
```

2. **Implementation Requirements**:
   - Use `subprocess` with `shell=False` for security
   - Support configurable linting rules via `.aiagent.json`
   - Implement timeout handling for long-running tests
   - Parse and structure test output for LLM consumption
   - Support both Python and JavaScript/TypeScript projects
   - Add comprehensive error handling and logging
   - Integrate with existing Phase 3 file operations

3. **Configuration Support**:
   - Create `.aiagent.json` schema for project-specific commands
   - Support custom lint commands, test commands, and timeouts
   - Allow override of default tools (flake8, pytest, eslint)
   - Enable/disable specific checks per project

### **Task 5.2: Core Task Runner**

1. **Create `backend/app/agent/task_runner.py`**:
```python
# Required features from IMPLEMENTATION_PLAN.md:
# - execute_task() main execution loop with error handling
# - parse_llm_response() with JSON validation and retry logic
# - execute_tool_calls() with Phase 3 integration
# - run_lint_test_cycle() iterative improvement loop
# - commit_changes() on successful completion
# - generate_task_context() using Phase 3 repo context
```

2. **Implementation Requirements**:
   - Integrate with existing OpenRouter LLM client
   - Use Phase 3 modules for file operations, git, and code analysis
   - Implement robust JSON parsing with fallback strategies
   - Add comprehensive error handling and recovery
   - Support iterative lint/test/fix cycles
   - Maintain detailed execution logs
   - Handle concurrent task execution safely

3. **Integration with Existing Systems**:
   - Use Phase 3 `repo_context` for task context generation
   - Leverage `file_operations` for code modifications
   - Integrate `git_operations` for version control
   - Use `code_analyzer` for semantic understanding
   - Connect with `change_tracker` for monitoring

### **Task 5.3: Enhanced Memory and Context Retrieval**

1. **Enhance `backend/app/agent/context_manager.py`**:
```python
# Required enhancements from IMPLEMENTATION_PLAN.md:
# - integrate_semantic_search() using Phase 3 code analyzer
# - build_task_context() with file change tracking
# - retrieve_relevant_code() using embeddings and similarity
# - maintain_conversation_memory() with task history
# - optimize_context_window() for LLM efficiency
```

2. **Implementation Requirements**:
   - Extend existing context manager with Phase 3 capabilities
   - Integrate semantic code search for relevant context
   - Use file change tracking for incremental context updates
   - Implement conversation memory with task persistence
   - Optimize context window usage for better LLM performance
   - Add context relevance scoring and filtering

## 🧪 **Testing Strategy**

### **Create Phase 5 Test Suite**

1. **Create `test_phase5_complete.py`** (following the pattern of `test_phase3_complete.py`):
```python
#!/usr/bin/env python3
"""
Comprehensive Phase 5 Completion Test for AI Coder Agent
Tests all Phase 5 requirements from IMPLEMENTATION_PLAN.md
"""

def test_lint_test_utilities():
    """Test lint and test utilities functionality."""
    # Test Python linting with flake8
    # Test Python testing with pytest
    # Test JavaScript linting with eslint
    # Test JavaScript testing with npm test
    # Test configuration loading from .aiagent.json
    # Test timeout handling and error reporting

def test_task_runner():
    """Test core task runner functionality."""
    # Test main execution loop
    # Test LLM JSON parsing with retry logic
    # Test tool execution with Phase 3 integration
    # Test lint/test iteration cycles
    # Test commit operations on success
    # Test comprehensive error handling

def test_enhanced_context():
    """Test enhanced memory and context retrieval."""
    # Test semantic search integration
    # Test file change tracking integration
    # Test task context generation
    # Test conversation memory persistence
    # Test context window optimization

def test_integration():
    """Test integration between all Phase 5 components."""
    # Test full task execution workflow
    # Test Phase 3 integration (all 5 modules)
    # Test LLM integration with OpenRouter
    # Test error recovery and retry mechanisms
```

2. **Add API endpoints for testing**:
```python
# Add to backend/main.py:
@app.post("/api/test/lint-test-utils")
async def test_lint_test_utilities_endpoint():
    """Test lint and test utilities functionality."""

@app.post("/api/test/task-runner")
async def test_task_runner_endpoint():
    """Test core task runner functionality."""

@app.post("/api/test/enhanced-context")
async def test_enhanced_context_endpoint():
    """Test enhanced memory and context retrieval."""

@app.post("/api/test/phase5-integration")
async def test_phase5_integration_endpoint():
    """Test Phase 5 integration and all components."""
```

## 🔄 **Development Workflow**

### **Step-by-Step Process**

1. **Setup Phase**:
```bash
# Ensure Phases 1-4 are complete
python test_phase3_complete.py
# Expected: 7/8 tests passed

# Start FAST development environment (NO REBUILDS!)
docker-compose -f docker-compose.dev.yml up -d
# Your code changes will be reflected instantly

# Verify Phase 3 modules are loaded
curl -X POST http://localhost:8000/api/test/phase3-integration
# Expected: All 5 modules working
```

2. **Dependencies Phase**:
```bash
# Add new dependencies to backend/requirements.txt:
# subprocess32>=3.5.4  # For secure subprocess handling
# jsonschema>=4.20.0   # For .aiagent.json validation

# NO REBUILD NEEDED! Volume mounting handles dependencies automatically
# Just restart the backend container:
docker-compose -f docker-compose.dev.yml restart backend
```

3. **Implementation Phase**:
```bash
# For each component:
# 1. Create the Python file in backend/utils/ or backend/app/agent/
# 2. Implement required methods with comprehensive error handling
# 3. Add imports to backend/main.py
# 4. Test individually with API endpoints

# Example for lint_test_utils:
# Create backend/utils/lint_test_utils.py
# Test: curl -X POST http://localhost:8000/api/test/lint-test-utils
```

4. **Integration Phase**:
```bash
# Test each component integration
curl -X POST http://localhost:8000/api/test/lint-test-utils
curl -X POST http://localhost:8000/api/test/task-runner
curl -X POST http://localhost:8000/api/test/enhanced-context

# Monitor Docker container health (should remain healthy)
docker-compose -f docker-compose.dev.yml ps
```

5. **Validation Phase**:
```bash
# Run comprehensive Phase 5 test
python test_phase5_complete.py

# Expected output: "🎉 Phase 5 is COMPLETE! All requirements satisfied."
```

## 📁 **Expected File Structure After Phase 5**

```
backend/
├── utils/
│   └── lint_test_utils.py             # NEW - Lint and test automation
├── app/
│   └── agent/
│       ├── context_manager.py         # ENHANCED - Phase 3 integration
│       ├── task_runner.py             # NEW - Core AI task execution
│       ├── file_operations.py         # EXISTING (Phase 3)
│       ├── git_operations.py          # EXISTING (Phase 3)
│       ├── code_analyzer.py           # EXISTING (Phase 3)
│       ├── change_tracker.py          # EXISTING (Phase 3)
│       └── repo_context.py            # EXISTING (Phase 3)
├── main.py                            # UPDATED with Phase 5 endpoints
├── config.py                          # UPDATED with Phase 5 settings
└── requirements.txt                   # UPDATED with new dependencies

# Root directory:
├── test_phase5_complete.py            # NEW - Phase 5 validation
├── test_phase3_complete.py            # EXISTING
├── .aiagent.json                      # NEW - Project configuration
└── IMPLEMENTATION_PLAN.md             # UPDATED with Phase 5 checkmarks
```

## ⚙️ **Configuration Updates**

### **Add to `backend/config.py`**:
```python
# Task Runner Settings
task_timeout_seconds: int = Field(300, description="Maximum task execution time (5 minutes)")
max_lint_test_cycles: int = Field(3, description="Maximum lint/test/fix iterations")
auto_commit_on_success: bool = Field(True, description="Automatically commit successful changes")
task_history_retention_days: int = Field(7, description="Days to retain task execution history")

# Lint and Test Settings
python_linter: str = Field("flake8", description="Python linting tool")
python_test_runner: str = Field("pytest", description="Python test runner")
javascript_linter: str = Field("eslint", description="JavaScript linting tool")
javascript_test_runner: str = Field("npm test", description="JavaScript test command")
lint_timeout_seconds: int = Field(60, description="Timeout for linting operations")
test_timeout_seconds: int = Field(180, description="Timeout for test operations")

# Context Enhancement Settings
max_context_files: int = Field(20, description="Maximum files to include in task context")
context_similarity_threshold: float = Field(0.7, description="Minimum similarity for context inclusion")
conversation_memory_limit: int = Field(50, description="Maximum conversation turns to remember")
```

### **Create `.aiagent.json` Schema**:
```json
{
  "version": "1.0",
  "project": {
    "name": "AI Coder Agent",
    "type": "python",
    "description": "AI-powered coding assistant"
  },
  "linting": {
    "python": {
      "enabled": true,
      "command": "flake8",
      "args": ["--max-line-length=88", "--ignore=E203,W503"],
      "timeout": 60
    },
    "javascript": {
      "enabled": true,
      "command": "eslint",
      "args": ["--ext", ".js,.jsx,.ts,.tsx"],
      "timeout": 60
    }
  },
  "testing": {
    "python": {
      "enabled": true,
      "command": "pytest",
      "args": ["-v", "--tb=short"],
      "timeout": 180
    },
    "javascript": {
      "enabled": true,
      "command": "npm test",
      "args": ["--", "--watchAll=false"],
      "timeout": 180
    }
  },
  "git": {
    "auto_commit": true,
    "commit_message_template": "AI Agent: {task_description}",
    "protected_branches": ["main", "master", "production"]
  }
}
```

## 🚨 **Important Implementation Notes**

### **Fast Development Workflow (CRITICAL)**
1. **NO Docker Rebuilds**: Use `docker-compose.dev.yml` with volume mounting
2. **Instant Code Changes**: Backend auto-reload and frontend hot reload enabled
3. **Quick Commands**: Use `dev_commands.ps1` for rapid development workflow
4. **Volume Mounting**: `./backend:/app` means changes are reflected immediately

### **Integration with Phase 3 (ESSENTIAL)**
1. **File Operations**: Use existing `file_operations` module for all file I/O
2. **Git Operations**: Leverage `git_operations` for version control
3. **Code Analysis**: Integrate `code_analyzer` for semantic understanding
4. **Change Tracking**: Connect with `change_tracker` for monitoring
5. **Repository Context**: Use `repo_context` for project understanding

### **Security Considerations**
1. **Subprocess Safety**: Always use `shell=False` and validate commands
2. **Timeout Handling**: Prevent hanging processes with proper timeouts
3. **Input Validation**: Sanitize all user inputs and LLM responses
4. **Error Isolation**: Ensure failures don't crash the entire system
5. **Resource Limits**: Prevent excessive CPU/memory usage

### **Performance Optimization**
1. **Async Operations**: Use async/await for I/O-bound operations
2. **Caching**: Cache lint/test results to avoid redundant operations
3. **Incremental Processing**: Only process changed files when possible
4. **Background Tasks**: Run heavy operations in background threads
5. **Context Optimization**: Efficiently manage LLM context windows

### **Error Handling**
1. **Graceful Degradation**: Continue operation even if some tools fail
2. **Retry Logic**: Implement exponential backoff for transient failures
3. **Comprehensive Logging**: Log all operations for debugging
4. **User Feedback**: Provide clear error messages and suggestions
5. **Recovery Mechanisms**: Implement rollback for failed operations

## ✅ **Success Criteria**

Phase 5 is complete when:
- [ ] Lint and test utilities work for Python and JavaScript projects
- [ ] Core task runner executes AI-driven coding workflows successfully
- [ ] Enhanced context management integrates Phase 3 capabilities
- [ ] LLM integration works with structured JSON responses
- [ ] Lint/test/fix iteration cycles complete successfully
- [ ] Git operations integrate seamlessly with task execution
- [ ] Configuration system supports project-specific settings
- [ ] Integration tests pass with 100% success rate
- [ ] `test_phase5_complete.py` shows all tests passing
- [ ] All API endpoints respond correctly
- [ ] Docker containers remain stable under load
- [ ] No breaking changes to Phase 1-4 functionality
- [ ] Fast development workflow continues to work perfectly

## 🎯 **Final Validation Commands**

```bash
# 1. Verify fast development environment is running
docker-compose -f docker-compose.dev.yml ps
# Expected: All containers showing "healthy" status

# 2. Test Phase 3 functionality still works
python test_phase3_complete.py
# Expected: 7/8 tests passed

# 3. Test new Phase 5 functionality
python test_phase5_complete.py
# Expected: All tests passing

# 4. Test full integration
curl -X POST http://localhost:8000/api/test/phase5-integration
# Expected: All components working together

# 5. Final validation
python test_phase5_complete.py && echo "✅ PHASE 5 COMPLETE - READY FOR PHASE 6"
```

## 🔗 **Dependencies to Add**

Update `backend/requirements.txt` with:
```
# Phase 5 Dependencies
jsonschema>=4.20.0        # For .aiagent.json validation
subprocess32>=3.5.4       # For secure subprocess handling (Python < 3.8)
```

## 🎯 **Key Integration Points**

1. **With Phase 3 Modules**: Use all 5 Phase 3 modules for comprehensive functionality
2. **With OpenRouter LLM**: Leverage existing LLM client for AI-driven tasks
3. **With BGE-M3 Embeddings**: Use semantic search for context retrieval
4. **With Qdrant**: Store and retrieve task-related embeddings
5. **With Fast Development**: Maintain instant code changes without rebuilds

---

**Next AI: Follow these instructions step-by-step to implement Phase 5. Start with Task 5.1 (Lint and Test Utilities) and work through each component systematically. CRITICAL: Use the fast development workflow with volume mounting - DO NOT rebuild Docker containers during development! Pay special attention to integrating with all Phase 3 modules and maintaining the excellent foundation that has been built. Create the comprehensive test script `test_phase5_complete.py` after implementation to validate everything works perfectly.**
