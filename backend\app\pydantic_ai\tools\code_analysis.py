"""
Code Analysis Tools for Pydantic AI Migration

This module provides standalone code analysis tools that can be registered
with Pydantic AI agents, avoiding circular import issues.
"""

import logging
from typing import Optional, List, Dict, Any

import logfire
from pydantic_ai import RunContext

from ..dependencies import CodingDependencies
from ..models import CodeAnalysisResult, LintTestResult

logger = logging.getLogger(__name__)


async def analyze_code(
    ctx: RunContext[CodingDependencies],
    file_path: str,
    include_complexity: bool = True,
    include_dependencies: bool = True
) -> CodeAnalysisResult:
    """
    Analyze code structure, complexity, and maintainability.
    
    Args:
        ctx: Run context with dependencies
        file_path: Path to the code file to analyze
        include_complexity: Whether to include complexity metrics
        include_dependencies: Whether to analyze dependencies
        
    Returns:
        CodeAnalysisResult with detailed analysis
    """
    with logfire.span("analyze_code", file_path=file_path):
        try:
            result = await ctx.deps.code_analyzer.analyze_file(
                file_path, include_complexity, include_dependencies
            )
            
            if result['status'] == 'success':
                analysis = result['analysis']
                logfire.info("Code analysis completed", 
                           complexity=analysis.get('complexity_score', 0),
                           maintainability=analysis.get('maintainability_index', 0))
                
                return CodeAnalysisResult(
                    file_path=file_path,
                    language=analysis.get('language', 'unknown'),
                    complexity_score=analysis.get('complexity_score', 0.0),
                    maintainability_index=analysis.get('maintainability_index', 0.0),
                    lines_of_code=analysis.get('lines_of_code', 0),
                    issues=analysis.get('issues', []),
                    suggestions=analysis.get('suggestions', []),
                    dependencies=analysis.get('dependencies', []),
                    functions=analysis.get('functions', []),
                    classes=analysis.get('classes', [])
                )
            else:
                logfire.error("Code analysis failed", error=result.get('error'))
                # Return minimal result with error information
                return CodeAnalysisResult(
                    file_path=file_path,
                    language='unknown',
                    complexity_score=0.0,
                    maintainability_index=0.0,
                    lines_of_code=0,
                    issues=[f"Analysis failed: {result.get('error')}"]
                )
                
        except Exception as e:
            logger.error(f"Failed to analyze code {file_path}: {e}")
            logfire.error("Code analysis exception", error=str(e))
            return CodeAnalysisResult(
                file_path=file_path,
                language='unknown',
                complexity_score=0.0,
                maintainability_index=0.0,
                lines_of_code=0,
                issues=[f"Analysis exception: {str(e)}"]
            )


async def search_code(
    ctx: RunContext[CodingDependencies],
    query: str,
    file_types: Optional[List[str]] = None,
    max_results: int = 10
) -> Dict[str, Any]:
    """
    Search code using semantic vector search.
    
    Args:
        ctx: Run context with dependencies
        query: Search query
        file_types: Optional list of file extensions to filter
        max_results: Maximum number of results to return
        
    Returns:
        Dictionary with search results
    """
    with logfire.span("search_code", query=query, max_results=max_results):
        try:
            result = await ctx.deps.code_analyzer.semantic_search(
                query, file_types, max_results
            )
            
            if result['status'] == 'success':
                results = result.get('results', [])
                logfire.info("Code search completed", result_count=len(results))
                return {
                    'status': 'success',
                    'query': query,
                    'results': results,
                    'total_results': len(results)
                }
            else:
                logfire.error("Code search failed", error=result.get('error'))
                return {
                    'status': 'error',
                    'query': query,
                    'error': result.get('error'),
                    'results': []
                }
                
        except Exception as e:
            logger.error(f"Failed to search code with query '{query}': {e}")
            logfire.error("Code search exception", error=str(e))
            return {
                'status': 'error',
                'query': query,
                'error': str(e),
                'results': []
            }


async def get_project_context(
    ctx: RunContext[CodingDependencies],
    project_path: str,
    include_structure: bool = True,
    include_dependencies: bool = True
) -> Dict[str, Any]:
    """
    Get comprehensive project context and structure.
    
    Args:
        ctx: Run context with dependencies
        project_path: Path to the project directory
        include_structure: Whether to include project structure
        include_dependencies: Whether to include dependency analysis
        
    Returns:
        Dictionary with project context information
    """
    with logfire.span("get_project_context", project_path=project_path):
        try:
            result = await ctx.deps.repo_context.get_project_summary(
                project_path, include_structure, include_dependencies
            )
            
            if result['status'] == 'success':
                logfire.info("Project context retrieved successfully")
                return result
            else:
                logfire.error("Project context retrieval failed", error=result.get('error'))
                return {
                    'status': 'error',
                    'error': result.get('error'),
                    'project_path': project_path
                }
                
        except Exception as e:
            logger.error(f"Failed to get project context for {project_path}: {e}")
            logfire.error("Project context exception", error=str(e))
            return {
                'status': 'error',
                'error': str(e),
                'project_path': project_path
            }


async def run_linting(
    ctx: RunContext[CodingDependencies],
    project_path: str,
    file_path: Optional[str] = None,
    language: Optional[str] = None
) -> LintTestResult:
    """
    Run linting tools on the project or specific file.
    
    Args:
        ctx: Run context with dependencies
        project_path: Path to the project directory
        file_path: Optional specific file to lint
        language: Optional language override for linting
        
    Returns:
        LintTestResult with linting results
    """
    with logfire.span("run_linting", project_path=project_path, file_path=file_path):
        try:
            if language == 'python' or (file_path and file_path.endswith('.py')):
                result = await ctx.deps.lint_test_utils.run_python_linting(project_path, file_path)
            elif language == 'javascript' or (file_path and file_path.endswith(('.js', '.ts'))):
                result = await ctx.deps.lint_test_utils.run_javascript_linting(project_path, file_path)
            else:
                # Auto-detect or run all linting
                result = await ctx.deps.lint_test_utils.run_all_linting(project_path)
            
            if result['status'] == 'success':
                lint_passed = result.get('lint_passed', False)
                logfire.info("Linting completed", lint_passed=lint_passed)
                return LintTestResult(
                    lint_passed=lint_passed,
                    tests_passed=True,  # Only linting, no tests run
                    lint_errors=result.get('errors', []),
                    test_errors=[],
                    execution_time=result.get('execution_time', 0.0)
                )
            else:
                logfire.error("Linting failed", error=result.get('error'))
                return LintTestResult(
                    lint_passed=False,
                    tests_passed=False,
                    lint_errors=[result.get('error', 'Unknown linting error')],
                    test_errors=[],
                    execution_time=0.0
                )
                
        except Exception as e:
            logger.error(f"Failed to run linting for {project_path}: {e}")
            logfire.error("Linting exception", error=str(e))
            return LintTestResult(
                lint_passed=False,
                tests_passed=False,
                lint_errors=[str(e)],
                test_errors=[],
                execution_time=0.0
            )


async def run_tests(
    ctx: RunContext[CodingDependencies],
    project_path: str,
    test_path: Optional[str] = None,
    language: Optional[str] = None
) -> LintTestResult:
    """
    Run tests on the project or specific test file.
    
    Args:
        ctx: Run context with dependencies
        project_path: Path to the project directory
        test_path: Optional specific test file or directory
        language: Optional language override for testing
        
    Returns:
        LintTestResult with test results
    """
    with logfire.span("run_tests", project_path=project_path, test_path=test_path):
        try:
            if language == 'python' or (test_path and test_path.endswith('.py')):
                result = await ctx.deps.lint_test_utils.run_python_tests(project_path, test_path)
            elif language == 'javascript' or (test_path and test_path.endswith(('.js', '.ts'))):
                result = await ctx.deps.lint_test_utils.run_javascript_tests(project_path, test_path)
            else:
                # Auto-detect or run all tests
                result = await ctx.deps.lint_test_utils.run_all_tests(project_path)
            
            if result['status'] == 'success':
                tests_passed = result.get('tests_passed', False)
                logfire.info("Tests completed", tests_passed=tests_passed, 
                           coverage=result.get('coverage'))
                return LintTestResult(
                    lint_passed=True,  # Only tests run, no linting
                    tests_passed=tests_passed,
                    lint_errors=[],
                    test_errors=result.get('errors', []),
                    coverage=result.get('coverage'),
                    execution_time=result.get('execution_time', 0.0)
                )
            else:
                logfire.error("Tests failed", error=result.get('error'))
                return LintTestResult(
                    lint_passed=False,
                    tests_passed=False,
                    lint_errors=[],
                    test_errors=[result.get('error', 'Unknown test error')],
                    execution_time=0.0
                )
                
        except Exception as e:
            logger.error(f"Failed to run tests for {project_path}: {e}")
            logfire.error("Testing exception", error=str(e))
            return LintTestResult(
                lint_passed=False,
                tests_passed=False,
                lint_errors=[],
                test_errors=[str(e)],
                execution_time=0.0
            )
