"""
🚀 Session Manager - Core session management with project integration

This module provides comprehensive session management that works seamlessly
with the workspace/project management system, allowing multiple sessions
per project with full persistence and recovery capabilities.
"""

import asyncio
import json
import sqlite3
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)


class SessionStatus(str, Enum):
    """Session status enumeration."""
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    RECOVERING = "recovering"


class SessionConfig(BaseModel):
    """Session configuration settings."""
    
    # Basic settings
    name: str = Field(..., description="Session display name")
    description: str = Field(default="", description="Session description")
    
    # Project integration
    project_id: str = Field(..., description="Associated project ID")
    workspace_path: str = Field(..., description="Workspace directory path")
    
    # Session behavior
    auto_save_interval: int = Field(default=30, description="Auto-save interval in seconds")
    max_history_size: int = Field(default=1000, description="Maximum conversation history size")
    enable_recovery: bool = Field(default=True, description="Enable automatic session recovery")
    
    # AI settings
    preferred_model: str = Field(default="deepseek/deepseek-r1-0528:free", description="Preferred AI model")
    context_window_size: int = Field(default=8000, description="Context window size in tokens")
    
    # Export settings
    auto_export: bool = Field(default=False, description="Auto-export conversations")
    export_format: str = Field(default="markdown", description="Default export format")


class Session(BaseModel):
    """Session data model with full project integration."""
    
    # Core identifiers
    session_id: str = Field(..., description="Unique session identifier")
    project_id: str = Field(..., description="Associated project ID")
    
    # Session metadata
    name: str = Field(..., description="Session display name")
    description: str = Field(default="", description="Session description")
    status: SessionStatus = Field(default=SessionStatus.ACTIVE, description="Current session status")
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.now, description="Session creation time")
    updated_at: datetime = Field(default_factory=datetime.now, description="Last update time")
    last_accessed: datetime = Field(default_factory=datetime.now, description="Last access time")
    
    # Session data
    workspace_path: str = Field(..., description="Workspace directory path")
    conversation_count: int = Field(default=0, description="Number of messages in conversation")
    
    # Configuration
    config: SessionConfig = Field(..., description="Session configuration")
    
    # Session state
    context_data: Dict[str, Any] = Field(default_factory=dict, description="Session context data")
    active_tools: List[str] = Field(default_factory=list, description="Currently active tools")
    
    # Statistics
    total_tokens_used: int = Field(default=0, description="Total tokens used in session")
    total_api_calls: int = Field(default=0, description="Total API calls made")
    
    def update_activity(self):
        """Update session activity timestamp."""
        self.last_accessed = datetime.now()
        self.updated_at = datetime.now()
    
    def is_expired(self, ttl_hours: int = 24) -> bool:
        """Check if session has expired."""
        expiry_time = self.last_accessed + timedelta(hours=ttl_hours)
        return datetime.now() > expiry_time


class SessionManager:
    """
    🚀 Advanced Session Manager with Project Integration
    
    Manages multiple concurrent sessions per project with full persistence,
    recovery capabilities, and seamless integration with the workspace system.
    """
    
    def __init__(self, db_path: str = "data/sessions.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # In-memory session cache for performance
        self.active_sessions: Dict[str, Session] = {}
        self.project_sessions: Dict[str, Set[str]] = {}  # project_id -> session_ids
        
        # Initialize database
        self._init_database()
        
        # Load active sessions from database
        self._load_active_sessions()
        
        # Start background tasks
        self._start_background_tasks()
    
    def _init_database(self) -> None:
        """Initialize the session database with proper schema."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id TEXT PRIMARY KEY,
                    project_id TEXT NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT,
                    status TEXT NOT NULL,
                    created_at TIMESTAMP NOT NULL,
                    updated_at TIMESTAMP NOT NULL,
                    last_accessed TIMESTAMP NOT NULL,
                    workspace_path TEXT NOT NULL,
                    conversation_count INTEGER DEFAULT 0,
                    config_json TEXT NOT NULL,
                    context_data_json TEXT DEFAULT '{}',
                    active_tools_json TEXT DEFAULT '[]',
                    total_tokens_used INTEGER DEFAULT 0,
                    total_api_calls INTEGER DEFAULT 0
                )
            """)
            
            # Create indexes for performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_sessions_project_id ON sessions(project_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_sessions_status ON sessions(status)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_sessions_last_accessed ON sessions(last_accessed)")
    
    def _load_active_sessions(self) -> None:
        """Load active sessions from database into memory."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM sessions 
                    WHERE status IN ('active', 'paused', 'recovering')
                    ORDER BY last_accessed DESC
                """)
                
                for row in cursor.fetchall():
                    session = self._row_to_session(row)
                    self.active_sessions[session.session_id] = session
                    
                    # Update project sessions mapping
                    if session.project_id not in self.project_sessions:
                        self.project_sessions[session.project_id] = set()
                    self.project_sessions[session.project_id].add(session.session_id)
                
                logger.info(f"Loaded {len(self.active_sessions)} active sessions")
                
        except Exception as e:
            logger.error(f"Failed to load active sessions: {e}")
    
    def _row_to_session(self, row: sqlite3.Row) -> Session:
        """Convert database row to Session object."""
        config_data = json.loads(row['config_json'])
        context_data = json.loads(row['context_data_json'])
        active_tools = json.loads(row['active_tools_json'])
        
        return Session(
            session_id=row['session_id'],
            project_id=row['project_id'],
            name=row['name'],
            description=row['description'] or "",
            status=SessionStatus(row['status']),
            created_at=datetime.fromisoformat(row['created_at']),
            updated_at=datetime.fromisoformat(row['updated_at']),
            last_accessed=datetime.fromisoformat(row['last_accessed']),
            workspace_path=row['workspace_path'],
            conversation_count=row['conversation_count'],
            config=SessionConfig(**config_data),
            context_data=context_data,
            active_tools=active_tools,
            total_tokens_used=row['total_tokens_used'],
            total_api_calls=row['total_api_calls']
        )
    
    def _start_background_tasks(self) -> None:
        """Start background maintenance tasks."""
        async def maintenance_loop():
            while True:
                try:
                    await self._cleanup_expired_sessions()
                    await self._auto_save_sessions()
                    await asyncio.sleep(300)  # Run every 5 minutes
                except Exception as e:
                    logger.error(f"Session maintenance failed: {e}")
                    await asyncio.sleep(60)
        
        # Start maintenance task
        asyncio.create_task(maintenance_loop())
    
    async def create_session(
        self,
        project_id: str,
        workspace_path: str,
        name: str,
        description: str = "",
        config: Optional[SessionConfig] = None
    ) -> Session:
        """
        Create a new session for a project.
        
        Args:
            project_id: The project this session belongs to
            workspace_path: Path to the project workspace
            name: Display name for the session
            description: Optional session description
            config: Optional session configuration
        
        Returns:
            Created Session object
        """
        session_id = str(uuid.uuid4())
        
        # Create default config if not provided
        if config is None:
            config = SessionConfig(
                name=name,
                description=description,
                project_id=project_id,
                workspace_path=workspace_path
            )
        
        # Create session object
        session = Session(
            session_id=session_id,
            project_id=project_id,
            name=name,
            description=description,
            workspace_path=workspace_path,
            config=config
        )
        
        # Save to database
        await self._save_session_to_db(session)
        
        # Add to memory cache
        self.active_sessions[session_id] = session
        
        # Update project sessions mapping
        if project_id not in self.project_sessions:
            self.project_sessions[project_id] = set()
        self.project_sessions[project_id].add(session_id)
        
        logger.info(f"Created session {session_id} for project {project_id}")
        return session

    async def get_session(self, session_id: str) -> Optional[Session]:
        """Get session by ID."""
        # Check memory cache first
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            session.update_activity()
            await self._save_session_to_db(session)
            return session

        # Load from database
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("SELECT * FROM sessions WHERE session_id = ?", (session_id,))
                row = cursor.fetchone()

                if row:
                    session = self._row_to_session(row)
                    session.update_activity()

                    # Add to cache if active
                    if session.status in [SessionStatus.ACTIVE, SessionStatus.PAUSED]:
                        self.active_sessions[session_id] = session

                    await self._save_session_to_db(session)
                    return session

        except Exception as e:
            logger.error(f"Failed to get session {session_id}: {e}")

        return None

    async def get_project_sessions(self, project_id: str) -> List[Session]:
        """Get all sessions for a project."""
        sessions = []

        # Get from cache first
        if project_id in self.project_sessions:
            for session_id in self.project_sessions[project_id]:
                if session_id in self.active_sessions:
                    sessions.append(self.active_sessions[session_id])

        # Load additional sessions from database
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(
                    "SELECT * FROM sessions WHERE project_id = ? ORDER BY last_accessed DESC",
                    (project_id,)
                )

                for row in cursor.fetchall():
                    session = self._row_to_session(row)
                    # Only add if not already in cache
                    if session.session_id not in [s.session_id for s in sessions]:
                        sessions.append(session)

        except Exception as e:
            logger.error(f"Failed to get project sessions for {project_id}: {e}")

        return sessions

    async def update_session(self, session: Session) -> bool:
        """Update session data."""
        try:
            session.update_activity()

            # Update cache
            self.active_sessions[session.session_id] = session

            # Save to database
            await self._save_session_to_db(session)

            logger.debug(f"Updated session {session.session_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to update session {session.session_id}: {e}")
            return False

    async def pause_session(self, session_id: str) -> bool:
        """Pause a session."""
        session = await self.get_session(session_id)
        if session:
            session.status = SessionStatus.PAUSED
            return await self.update_session(session)
        return False

    async def resume_session(self, session_id: str) -> bool:
        """Resume a paused session."""
        session = await self.get_session(session_id)
        if session and session.status == SessionStatus.PAUSED:
            session.status = SessionStatus.ACTIVE
            return await self.update_session(session)
        return False

    async def complete_session(self, session_id: str) -> bool:
        """Mark session as completed."""
        session = await self.get_session(session_id)
        if session:
            session.status = SessionStatus.COMPLETED
            success = await self.update_session(session)

            # Remove from active cache
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]

            return success
        return False

    async def delete_session(self, session_id: str) -> bool:
        """Delete a session completely."""
        try:
            # Remove from cache
            if session_id in self.active_sessions:
                session = self.active_sessions[session_id]
                project_id = session.project_id

                del self.active_sessions[session_id]

                # Update project sessions mapping
                if project_id in self.project_sessions:
                    self.project_sessions[project_id].discard(session_id)

            # Delete from database
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("DELETE FROM sessions WHERE session_id = ?", (session_id,))

            logger.info(f"Deleted session {session_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to delete session {session_id}: {e}")
            return False

    async def _save_session_to_db(self, session: Session) -> None:
        """Save session to database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO sessions (
                    session_id, project_id, name, description, status,
                    created_at, updated_at, last_accessed, workspace_path,
                    conversation_count, config_json, context_data_json,
                    active_tools_json, total_tokens_used, total_api_calls
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                session.session_id,
                session.project_id,
                session.name,
                session.description,
                session.status.value,
                session.created_at.isoformat(),
                session.updated_at.isoformat(),
                session.last_accessed.isoformat(),
                session.workspace_path,
                session.conversation_count,
                json.dumps(session.config.model_dump()),
                json.dumps(session.context_data),
                json.dumps(session.active_tools),
                session.total_tokens_used,
                session.total_api_calls
            ))

    async def _cleanup_expired_sessions(self) -> None:
        """Clean up expired sessions."""
        try:
            expired_sessions = []
            for session_id, session in self.active_sessions.items():
                if session.is_expired():
                    expired_sessions.append(session_id)

            for session_id in expired_sessions:
                await self.pause_session(session_id)
                logger.info(f"Paused expired session {session_id}")

        except Exception as e:
            logger.error(f"Session cleanup failed: {e}")

    async def _auto_save_sessions(self) -> None:
        """Auto-save all active sessions."""
        try:
            for session in self.active_sessions.values():
                await self._save_session_to_db(session)
        except Exception as e:
            logger.error(f"Auto-save failed: {e}")

    def get_session_stats(self) -> Dict[str, Any]:
        """Get session statistics."""
        total_sessions = len(self.active_sessions)
        active_count = sum(1 for s in self.active_sessions.values() if s.status == SessionStatus.ACTIVE)
        paused_count = sum(1 for s in self.active_sessions.values() if s.status == SessionStatus.PAUSED)

        return {
            "total_active_sessions": total_sessions,
            "active_sessions": active_count,
            "paused_sessions": paused_count,
            "projects_with_sessions": len(self.project_sessions),
            "total_tokens_used": sum(s.total_tokens_used for s in self.active_sessions.values()),
            "total_api_calls": sum(s.total_api_calls for s in self.active_sessions.values())
        }


# Global session manager instance
_session_manager: Optional[SessionManager] = None


def get_session_manager() -> SessionManager:
    """Get the global session manager instance."""
    global _session_manager
    if _session_manager is None:
        _session_manager = SessionManager()
    return _session_manager
