interactions:
- request:
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '140'
      content-type:
      - application/json
      host:
      - api.cohere.com
    method: POST
    parsed_body:
      messages:
      - content: What is the capital of France?
        role: user
      model: command-r-plus
      stop_sequences:
      - Paris
      stream: false
    uri: https://api.cohere.com/v2/chat
  response:
    headers:
      access-control-expose-headers:
      - X-Debug-Trace-ID
      alt-svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      cache-control:
      - no-cache, no-store, no-transform, must-revalidate, private, max-age=0
      content-length:
      - '280'
      content-type:
      - application/json
      expires:
      - Thu, 01 Jan 1970 00:00:00 UTC
      num_chars:
      - '1200'
      num_tokens:
      - '12'
      pragma:
      - no-cache
      vary:
      - Origin
    parsed_body:
      finish_reason: STOP_SEQUENCE
      id: 2ed6908c-e5cb-4063-b2a3-0ac4990d1b85
      message:
        content:
        - text: The capital of France is
          type: text
        role: assistant
      usage:
        billed_units:
          input_tokens: 7
          output_tokens: 5
        tokens:
          input_tokens: 200
          output_tokens: 7
    status:
      code: 200
      message: OK
version: 1
