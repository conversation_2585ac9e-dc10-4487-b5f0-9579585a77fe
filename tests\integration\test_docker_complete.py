#!/usr/bin/env python3
"""
Complete Docker Container Test Script
Tests that all Docker containers build and run correctly.
"""
import subprocess
import time
import requests
import json
import sys
from pathlib import Path

def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_status(test_name, success, details=""):
    """Print test status."""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} {test_name}")
    if details:
        print(f"    {details}")

def run_command(command, timeout=30):
    """Run a command and return result."""
    try:
        result = subprocess.run(
            command.split(), 
            capture_output=True, 
            text=True,
            timeout=timeout
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def test_docker_build():
    """Test Docker build process."""
    print_header("Testing Docker Build Process")
    
    # Test backend build
    print("Building backend container...")
    success, stdout, stderr = run_command("docker build -t ai-coder-backend ./backend", timeout=300)
    print_status("Backend Docker build", success, stderr if not success else "Build completed")
    
    if not success:
        return False
    
    # Test frontend build
    print("Building frontend container...")
    success, stdout, stderr = run_command("docker build -t ai-coder-frontend ./frontend", timeout=180)
    print_status("Frontend Docker build", success, stderr if not success else "Build completed")
    
    return success

def test_docker_compose_up():
    """Test docker-compose up."""
    print_header("Testing Docker Compose Startup")
    
    # Stop any existing containers
    run_command("docker-compose down", timeout=30)
    
    # Start services
    print("Starting all services with docker-compose...")
    success, stdout, stderr = run_command("docker-compose up -d", timeout=180)
    print_status("Docker Compose startup", success, stderr if not success else "All services started")
    
    if not success:
        return False
    
    # Wait for services to be ready
    print("Waiting for services to initialize...")
    time.sleep(10)
    
    return True

def test_container_health():
    """Test that all containers are running."""
    print_header("Testing Container Health")
    
    # Check container status
    success, stdout, stderr = run_command("docker-compose ps")
    print_status("Container status check", success, stderr if not success else "Status retrieved")
    
    if success:
        print("Container Status:")
        print(stdout)
    
    # Check individual containers
    containers = ["ai_coder_frontend", "ai_coder_backend", "ai_coder_ollama", "ai_coder_qdrant"]
    all_healthy = True
    
    for container in containers:
        success, stdout, stderr = run_command(f"docker ps --filter name={container} --format table")
        is_running = container in stdout if success else False
        print_status(f"Container running: {container}", is_running)
        if not is_running:
            all_healthy = False
    
    return all_healthy

def test_service_endpoints():
    """Test that all service endpoints are responding."""
    print_header("Testing Service Endpoints")
    
    endpoints = [
        ("Frontend", "http://localhost:3000", 200),
        ("Backend Health", "http://localhost:8000/health", 200),
        ("Backend API Health", "http://localhost:8000/api/health", 200),
        ("Backend Config Validation", "http://localhost:8000/api/config/validate", 200),
        ("Backend Database Health", "http://localhost:8000/api/database/health", 200),
        ("Qdrant Health", "http://localhost:6333/health", 200),
        ("Ollama API", "http://localhost:11434/api/tags", 200),
    ]
    
    all_responding = True
    
    for name, url, expected_status in endpoints:
        try:
            response = requests.get(url, timeout=10)
            success = response.status_code == expected_status
            details = f"Status: {response.status_code}"
            if success and name.startswith("Backend"):
                try:
                    data = response.json()
                    details += f", Response: {json.dumps(data, indent=2)}"
                except:
                    pass
        except requests.exceptions.RequestException as e:
            success = False
            details = f"Connection error: {e}"
        
        print_status(name, success, details)
        if not success:
            all_responding = False
    
    return all_responding

def test_service_communication():
    """Test inter-service communication."""
    print_header("Testing Service Communication")
    
    # Test backend can reach Qdrant
    try:
        response = requests.get("http://localhost:8000/api/config/validate", timeout=10)
        if response.status_code == 200:
            data = response.json()
            qdrant_configured = data.get("validation", {}).get("qdrant_configured", False)
            print_status("Backend → Qdrant communication", qdrant_configured, 
                        "Backend can reach Qdrant service" if qdrant_configured else "Backend cannot reach Qdrant")
        else:
            print_status("Backend → Qdrant communication", False, f"Config validation failed: {response.status_code}")
            return False
    except Exception as e:
        print_status("Backend → Qdrant communication", False, f"Error: {e}")
        return False
    
    # Test backend can reach Ollama
    try:
        response = requests.get("http://localhost:8000/api/config/validate", timeout=10)
        if response.status_code == 200:
            data = response.json()
            ollama_configured = data.get("validation", {}).get("ollama_configured", False)
            print_status("Backend → Ollama communication", ollama_configured,
                        "Backend can reach Ollama service" if ollama_configured else "Backend cannot reach Ollama")
        else:
            print_status("Backend → Ollama communication", False, f"Config validation failed: {response.status_code}")
            return False
    except Exception as e:
        print_status("Backend → Ollama communication", False, f"Error: {e}")
        return False
    
    return True

def test_docker_logs():
    """Check Docker logs for errors."""
    print_header("Checking Docker Logs")
    
    services = ["frontend", "backend", "ollama", "qdrant"]
    
    for service in services:
        success, stdout, stderr = run_command(f"docker-compose logs --tail=10 {service}")
        has_errors = "error" in stdout.lower() or "failed" in stdout.lower()
        print_status(f"{service} logs", not has_errors, 
                    "No errors found" if not has_errors else "Errors detected in logs")
        
        if has_errors:
            print(f"Recent {service} logs:")
            print(stdout[-500:])  # Show last 500 chars
    
    return True

def cleanup():
    """Clean up Docker containers."""
    print_header("Cleanup")
    
    print("Stopping all services...")
    success, stdout, stderr = run_command("docker-compose down")
    print_status("Docker Compose cleanup", success, "Services stopped" if success else stderr)

def main():
    """Run all Docker tests."""
    print_header("AI Coder Agent - Complete Docker Test Suite")
    print("Testing Docker containers and service communication...")
    
    tests = [
        ("Docker Build", test_docker_build),
        ("Docker Compose Startup", test_docker_compose_up),
        ("Container Health", test_container_health),
        ("Service Endpoints", test_service_endpoints),
        ("Service Communication", test_service_communication),
        ("Docker Logs", test_docker_logs),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n🔄 Running {test_name}...")
            result = test_func()
            results.append((test_name, result))
            
            if not result and test_name in ["Docker Build", "Docker Compose Startup"]:
                print(f"❌ Critical test '{test_name}' failed. Stopping further tests.")
                break
                
        except Exception as e:
            print_status(f"{test_name} (EXCEPTION)", False, f"Unexpected error: {e}")
            results.append((test_name, False))
    
    # Summary
    print_header("Docker Test Results Summary")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        print_status(test_name, result)
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL DOCKER TESTS PASSED!")
        print("✅ All containers build successfully")
        print("✅ All services start and respond correctly")
        print("✅ Inter-service communication works")
        print("✅ Phase 1 Docker setup is COMPLETE!")
        cleanup()
        return 0
    else:
        print(f"\n❌ {total - passed} Docker tests failed.")
        print("🔧 Please check the failing tests and fix issues.")
        print("\n💡 To debug:")
        print("   docker-compose logs <service_name>")
        print("   docker ps -a")
        print("   docker-compose down")
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
        cleanup()
        sys.exit(1)
