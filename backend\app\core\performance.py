"""
Performance Optimization Module

Provides system-wide performance optimizations including:
- Connection pooling and reuse
- Caching mechanisms
- Resource management
- Batch processing optimizations
- Memory management
"""

import asyncio
import logging
import time
import weakref
from typing import Dict, Any, Optional, List, Callable
from functools import wraps, lru_cache
from collections import defaultdict
import threading
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)


class ConnectionPool:
    """Generic connection pool for reusing expensive connections."""
    
    def __init__(self, max_size: int = 10, timeout: float = 30.0):
        self.max_size = max_size
        self.timeout = timeout
        self._pool = asyncio.Queue(maxsize=max_size)
        self._active_connections = set()
        self._lock = asyncio.Lock()
        
    async def get_connection(self, factory: Callable):
        """Get a connection from the pool or create a new one."""
        try:
            # Try to get from pool with timeout
            connection = await asyncio.wait_for(
                self._pool.get(), timeout=1.0
            )
            if await self._validate_connection(connection):
                self._active_connections.add(connection)
                return connection
        except asyncio.TimeoutError:
            pass
        
        # Create new connection if pool is empty or connections are invalid
        async with self._lock:
            if len(self._active_connections) < self.max_size:
                connection = await factory()
                self._active_connections.add(connection)
                return connection
        
        # Wait for a connection to be returned
        connection = await self._pool.get()
        self._active_connections.add(connection)
        return connection
    
    async def return_connection(self, connection):
        """Return a connection to the pool."""
        if connection in self._active_connections:
            self._active_connections.remove(connection)
            if await self._validate_connection(connection):
                try:
                    self._pool.put_nowait(connection)
                except asyncio.QueueFull:
                    # Pool is full, close the connection
                    await self._close_connection(connection)
            else:
                await self._close_connection(connection)
    
    async def _validate_connection(self, connection) -> bool:
        """Validate if a connection is still usable."""
        # Override in subclasses for specific validation
        return True
    
    async def _close_connection(self, connection):
        """Close a connection."""
        # Override in subclasses for specific cleanup
        pass


class CacheManager:
    """Advanced caching system with TTL and memory management."""
    
    def __init__(self, max_size: int = 1000, default_ttl: float = 300.0):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache = {}
        self._access_times = {}
        self._expiry_times = {}
        self._lock = threading.RLock()
        
        # Start cleanup task
        self._cleanup_task = asyncio.create_task(self._cleanup_expired())
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        with self._lock:
            if key not in self._cache:
                return None
            
            # Check if expired
            if time.time() > self._expiry_times.get(key, 0):
                self._remove_key(key)
                return None
            
            # Update access time
            self._access_times[key] = time.time()
            return self._cache[key]
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """Set value in cache."""
        with self._lock:
            if ttl is None:
                ttl = self.default_ttl
            
            # Remove oldest items if cache is full
            if len(self._cache) >= self.max_size and key not in self._cache:
                self._evict_lru()
            
            current_time = time.time()
            self._cache[key] = value
            self._access_times[key] = current_time
            self._expiry_times[key] = current_time + ttl
    
    def invalidate(self, key: str) -> None:
        """Remove key from cache."""
        with self._lock:
            self._remove_key(key)
    
    def clear(self) -> None:
        """Clear all cache entries."""
        with self._lock:
            self._cache.clear()
            self._access_times.clear()
            self._expiry_times.clear()
    
    def _remove_key(self, key: str) -> None:
        """Remove a key and its metadata."""
        self._cache.pop(key, None)
        self._access_times.pop(key, None)
        self._expiry_times.pop(key, None)
    
    def _evict_lru(self) -> None:
        """Evict least recently used item."""
        if not self._access_times:
            return
        
        lru_key = min(self._access_times.items(), key=lambda x: x[1])[0]
        self._remove_key(lru_key)
    
    async def _cleanup_expired(self) -> None:
        """Periodic cleanup of expired entries."""
        while True:
            try:
                await asyncio.sleep(60)  # Cleanup every minute
                current_time = time.time()
                
                with self._lock:
                    expired_keys = [
                        key for key, expiry in self._expiry_times.items()
                        if current_time > expiry
                    ]
                    
                    for key in expired_keys:
                        self._remove_key(key)
                
                if expired_keys:
                    logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
                    
            except Exception as e:
                logger.error(f"Cache cleanup failed: {e}")


class BatchProcessor:
    """Efficient batch processing for operations."""
    
    def __init__(self, batch_size: int = 100, flush_interval: float = 5.0):
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self._batches = defaultdict(list)
        self._processors = {}
        self._last_flush = defaultdict(float)
        self._lock = asyncio.Lock()
        
        # Start flush task
        self._flush_task = asyncio.create_task(self._periodic_flush())
    
    async def add_item(self, batch_key: str, item: Any, processor: Callable = None) -> None:
        """Add item to batch for processing."""
        async with self._lock:
            self._batches[batch_key].append(item)
            
            if processor:
                self._processors[batch_key] = processor
            
            # Flush if batch is full
            if len(self._batches[batch_key]) >= self.batch_size:
                await self._flush_batch(batch_key)
    
    async def flush_batch(self, batch_key: str) -> None:
        """Manually flush a specific batch."""
        async with self._lock:
            await self._flush_batch(batch_key)
    
    async def flush_all(self) -> None:
        """Flush all batches."""
        async with self._lock:
            for batch_key in list(self._batches.keys()):
                await self._flush_batch(batch_key)
    
    async def _flush_batch(self, batch_key: str) -> None:
        """Internal batch flush."""
        if not self._batches[batch_key]:
            return
        
        items = self._batches[batch_key].copy()
        self._batches[batch_key].clear()
        self._last_flush[batch_key] = time.time()
        
        processor = self._processors.get(batch_key)
        if processor:
            try:
                await processor(items)
                logger.debug(f"Processed batch {batch_key} with {len(items)} items")
            except Exception as e:
                logger.error(f"Batch processing failed for {batch_key}: {e}")
    
    async def _periodic_flush(self) -> None:
        """Periodic flush of batches."""
        while True:
            try:
                await asyncio.sleep(self.flush_interval)
                current_time = time.time()
                
                async with self._lock:
                    for batch_key in list(self._batches.keys()):
                        if (self._batches[batch_key] and 
                            current_time - self._last_flush[batch_key] >= self.flush_interval):
                            await self._flush_batch(batch_key)
                            
            except Exception as e:
                logger.error(f"Periodic flush failed: {e}")


class ResourceMonitor:
    """Monitor system resources and provide optimization hints."""
    
    def __init__(self):
        self.metrics = defaultdict(list)
        self._monitoring = False
        self._monitor_task = None
    
    async def start_monitoring(self, interval: float = 30.0) -> None:
        """Start resource monitoring."""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._monitor_task = asyncio.create_task(self._monitor_loop(interval))
        logger.info("Resource monitoring started")
    
    async def stop_monitoring(self) -> None:
        """Stop resource monitoring."""
        self._monitoring = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("Resource monitoring stopped")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current resource metrics."""
        try:
            import psutil
            
            return {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('/').percent,
                'active_connections': len(asyncio.all_tasks()),
                'timestamp': time.time()
            }
        except ImportError:
            return {'error': 'psutil not available'}
    
    def get_optimization_hints(self) -> List[str]:
        """Get optimization hints based on current metrics."""
        hints = []
        metrics = self.get_metrics()
        
        if metrics.get('memory_percent', 0) > 80:
            hints.append("High memory usage detected - consider clearing caches")
        
        if metrics.get('cpu_percent', 0) > 90:
            hints.append("High CPU usage detected - consider reducing batch sizes")
        
        if metrics.get('active_connections', 0) > 100:
            hints.append("Many active connections - consider connection pooling")
        
        return hints
    
    async def _monitor_loop(self, interval: float) -> None:
        """Main monitoring loop."""
        while self._monitoring:
            try:
                metrics = self.get_metrics()
                
                # Store metrics (keep last 100 entries)
                for key, value in metrics.items():
                    if isinstance(value, (int, float)):
                        self.metrics[key].append(value)
                        if len(self.metrics[key]) > 100:
                            self.metrics[key].pop(0)
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"Resource monitoring failed: {e}")
                await asyncio.sleep(interval)


# Performance decorators
def cached(ttl: float = 300.0, key_func: Optional[Callable] = None):
    """Decorator for caching function results."""
    def decorator(func):
        cache = CacheManager(default_ttl=ttl)
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # Try cache first
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            cache.set(cache_key, result)
            return result
        
        return wrapper
    return decorator


def rate_limited(calls_per_second: float = 10.0):
    """Decorator for rate limiting function calls."""
    def decorator(func):
        last_called = [0.0]
        min_interval = 1.0 / calls_per_second
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            now = time.time()
            time_since_last = now - last_called[0]
            
            if time_since_last < min_interval:
                await asyncio.sleep(min_interval - time_since_last)
            
            last_called[0] = time.time()
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


# Global instances
cache_manager = CacheManager()
batch_processor = BatchProcessor()
resource_monitor = ResourceMonitor()

# Start monitoring
asyncio.create_task(resource_monitor.start_monitoring())
