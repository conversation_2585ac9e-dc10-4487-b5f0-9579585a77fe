"""
Code Analyzer for Code Intelligence Hub - Phase 8B

Multi-language code analysis and chunk generation for semantic indexing.
"""

import ast
import re
import logging
from typing import List, Dict, Optional, Any, Tuple
from pathlib import Path

from .models import CodeFile, CodeChunk, CodeFunction, CodeClass, CodeLanguage, CodeElementType

logger = logging.getLogger(__name__)


class CodeAnalyzer:
    """
    Multi-language code analyzer for extracting structure and creating chunks.
    
    Analyzes code files to extract functions, classes, imports, and other
    structural elements, then creates semantic chunks for embedding.
    """
    
    def __init__(self):
        """Initialize the code analyzer."""
        self.chunk_size = 500  # Target chunk size in characters
        self.chunk_overlap = 50  # Overlap between chunks
        self.min_chunk_size = 100  # Minimum chunk size
        
        logger.info("🔬 Code analyzer initialized")
    
    async def analyze_file(self, code_file: CodeFile) -> Dict[str, Any]:
        """
        Analyze a code file and extract structural information.
        
        Args:
            code_file: CodeFile object to analyze
            
        Returns:
            Analysis results with functions, classes, imports, etc.
        """
        try:
            logger.debug(f"🔍 Analyzing file: {code_file.path}")
            
            # Read file content
            with open(code_file.absolute_path, 'r', encoding=code_file.encoding, errors='ignore') as f:
                content = f.read()
            
            # Analyze based on language
            if code_file.language == CodeLanguage.PYTHON:
                return await self._analyze_python(content, code_file)
            elif code_file.language in [CodeLanguage.JAVASCRIPT, CodeLanguage.TYPESCRIPT]:
                return await self._analyze_javascript(content, code_file)
            elif code_file.language == CodeLanguage.JAVA:
                return await self._analyze_java(content, code_file)
            elif code_file.language == CodeLanguage.GO:
                return await self._analyze_go(content, code_file)
            elif code_file.language == CodeLanguage.RUST:
                return await self._analyze_rust(content, code_file)
            else:
                return await self._analyze_generic(content, code_file)
                
        except Exception as e:
            logger.error(f"❌ Failed to analyze file {code_file.path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'functions': [],
                'classes': [],
                'imports': [],
                'dependencies': []
            }
    
    async def create_chunks(self, code_file: CodeFile) -> List[CodeChunk]:
        """
        Create semantic chunks from a code file.
        
        Args:
            code_file: CodeFile object to chunk
            
        Returns:
            List of CodeChunk objects
        """
        try:
            logger.debug(f"📝 Creating chunks for: {code_file.path}")
            
            # Read file content
            with open(code_file.absolute_path, 'r', encoding=code_file.encoding, errors='ignore') as f:
                content = f.read()
            
            chunks = []
            
            # Create function-level chunks first
            function_chunks = await self._create_function_chunks(content, code_file)
            chunks.extend(function_chunks)
            
            # Create class-level chunks
            class_chunks = await self._create_class_chunks(content, code_file)
            chunks.extend(class_chunks)
            
            # Create general content chunks for remaining code
            general_chunks = await self._create_general_chunks(content, code_file, chunks)
            chunks.extend(general_chunks)
            
            # Add chunk IDs
            for i, chunk in enumerate(chunks):
                chunk.chunk_id = f"{code_file.file_id}_chunk_{i}"
            
            logger.debug(f"📊 Created {len(chunks)} chunks for {code_file.path}")
            
            return chunks
            
        except Exception as e:
            logger.error(f"❌ Failed to create chunks for {code_file.path}: {e}")
            return []
    
    async def _analyze_python(self, content: str, code_file: CodeFile) -> Dict[str, Any]:
        """Analyze Python code using AST."""
        try:
            tree = ast.parse(content)
            
            functions = []
            classes = []
            imports = []
            dependencies = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    functions.append(node.name)
                elif isinstance(node, ast.ClassDef):
                    classes.append(node.name)
                elif isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append(alias.name)
                        dependencies.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        imports.append(node.module)
                        dependencies.append(node.module)
            
            return {
                'status': 'success',
                'functions': functions,
                'classes': classes,
                'imports': imports,
                'dependencies': dependencies
            }
            
        except Exception as e:
            logger.debug(f"Python AST analysis failed: {e}")
            return await self._analyze_generic(content, code_file)
    
    async def _analyze_javascript(self, content: str, code_file: CodeFile) -> Dict[str, Any]:
        """Analyze JavaScript/TypeScript code using regex patterns."""
        try:
            functions = []
            classes = []
            imports = []
            dependencies = []
            
            # Function patterns
            function_patterns = [
                r'function\s+(\w+)\s*\(',
                r'const\s+(\w+)\s*=\s*\([^)]*\)\s*=>',
                r'let\s+(\w+)\s*=\s*\([^)]*\)\s*=>',
                r'var\s+(\w+)\s*=\s*function',
                r'(\w+)\s*:\s*function\s*\(',
                r'async\s+function\s+(\w+)\s*\('
            ]
            
            for pattern in function_patterns:
                matches = re.finditer(pattern, content, re.MULTILINE)
                functions.extend([match.group(1) for match in matches])
            
            # Class patterns
            class_matches = re.finditer(r'class\s+(\w+)', content, re.MULTILINE)
            classes.extend([match.group(1) for match in class_matches])
            
            # Import patterns
            import_patterns = [
                r'import\s+.*?\s+from\s+[\'"]([^\'"]+)[\'"]',
                r'import\s+[\'"]([^\'"]+)[\'"]',
                r'require\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)'
            ]
            
            for pattern in import_patterns:
                matches = re.finditer(pattern, content, re.MULTILINE)
                for match in matches:
                    module = match.group(1)
                    imports.append(module)
                    if not module.startswith('.'):  # External dependency
                        dependencies.append(module.split('/')[0])
            
            return {
                'status': 'success',
                'functions': list(set(functions)),
                'classes': list(set(classes)),
                'imports': list(set(imports)),
                'dependencies': list(set(dependencies))
            }
            
        except Exception as e:
            logger.debug(f"JavaScript analysis failed: {e}")
            return await self._analyze_generic(content, code_file)
    
    async def _analyze_java(self, content: str, code_file: CodeFile) -> Dict[str, Any]:
        """Analyze Java code using regex patterns."""
        try:
            functions = []
            classes = []
            imports = []
            dependencies = []
            
            # Method patterns
            method_pattern = r'(?:public|private|protected|static|\s)+[\w<>\[\]]+\s+(\w+)\s*\([^)]*\)\s*(?:throws\s+[\w,\s]+)?\s*\{'
            method_matches = re.finditer(method_pattern, content, re.MULTILINE)
            functions.extend([match.group(1) for match in method_matches if match.group(1) not in ['if', 'for', 'while', 'switch']])
            
            # Class patterns
            class_pattern = r'(?:public|private|protected|\s)*class\s+(\w+)'
            class_matches = re.finditer(class_pattern, content, re.MULTILINE)
            classes.extend([match.group(1) for match in class_matches])
            
            # Import patterns
            import_pattern = r'import\s+(?:static\s+)?([^;]+);'
            import_matches = re.finditer(import_pattern, content, re.MULTILINE)
            for match in import_matches:
                import_stmt = match.group(1).strip()
                imports.append(import_stmt)
                # Extract package name
                if '.' in import_stmt:
                    package = '.'.join(import_stmt.split('.')[:-1])
                    dependencies.append(package)
            
            return {
                'status': 'success',
                'functions': list(set(functions)),
                'classes': list(set(classes)),
                'imports': list(set(imports)),
                'dependencies': list(set(dependencies))
            }
            
        except Exception as e:
            logger.debug(f"Java analysis failed: {e}")
            return await self._analyze_generic(content, code_file)
    
    async def _analyze_go(self, content: str, code_file: CodeFile) -> Dict[str, Any]:
        """Analyze Go code using regex patterns."""
        try:
            functions = []
            classes = []  # Go doesn't have classes, but has structs
            imports = []
            dependencies = []
            
            # Function patterns
            func_pattern = r'func\s+(?:\([^)]*\)\s+)?(\w+)\s*\('
            func_matches = re.finditer(func_pattern, content, re.MULTILINE)
            functions.extend([match.group(1) for match in func_matches])
            
            # Struct patterns (similar to classes)
            struct_pattern = r'type\s+(\w+)\s+struct'
            struct_matches = re.finditer(struct_pattern, content, re.MULTILINE)
            classes.extend([match.group(1) for match in struct_matches])
            
            # Import patterns
            import_pattern = r'import\s+(?:\(\s*([^)]+)\s*\)|"([^"]+)")'
            import_matches = re.finditer(import_pattern, content, re.MULTILINE | re.DOTALL)
            for match in import_matches:
                if match.group(1):  # Multi-line import
                    import_block = match.group(1)
                    for line in import_block.split('\n'):
                        line = line.strip()
                        if line and line.startswith('"') and line.endswith('"'):
                            module = line[1:-1]
                            imports.append(module)
                            dependencies.append(module.split('/')[0])
                elif match.group(2):  # Single import
                    module = match.group(2)
                    imports.append(module)
                    dependencies.append(module.split('/')[0])
            
            return {
                'status': 'success',
                'functions': list(set(functions)),
                'classes': list(set(classes)),
                'imports': list(set(imports)),
                'dependencies': list(set(dependencies))
            }
            
        except Exception as e:
            logger.debug(f"Go analysis failed: {e}")
            return await self._analyze_generic(content, code_file)
    
    async def _analyze_rust(self, content: str, code_file: CodeFile) -> Dict[str, Any]:
        """Analyze Rust code using regex patterns."""
        try:
            functions = []
            classes = []  # Rust has structs and enums
            imports = []
            dependencies = []
            
            # Function patterns
            func_pattern = r'(?:pub\s+)?fn\s+(\w+)\s*\('
            func_matches = re.finditer(func_pattern, content, re.MULTILINE)
            functions.extend([match.group(1) for match in func_matches])
            
            # Struct and enum patterns
            struct_pattern = r'(?:pub\s+)?struct\s+(\w+)'
            enum_pattern = r'(?:pub\s+)?enum\s+(\w+)'
            
            struct_matches = re.finditer(struct_pattern, content, re.MULTILINE)
            enum_matches = re.finditer(enum_pattern, content, re.MULTILINE)
            
            classes.extend([match.group(1) for match in struct_matches])
            classes.extend([match.group(1) for match in enum_matches])
            
            # Use patterns
            use_pattern = r'use\s+([^;]+);'
            use_matches = re.finditer(use_pattern, content, re.MULTILINE)
            for match in use_matches:
                use_stmt = match.group(1).strip()
                imports.append(use_stmt)
                # Extract crate name
                if '::' in use_stmt:
                    crate = use_stmt.split('::')[0]
                    dependencies.append(crate)
            
            return {
                'status': 'success',
                'functions': list(set(functions)),
                'classes': list(set(classes)),
                'imports': list(set(imports)),
                'dependencies': list(set(dependencies))
            }
            
        except Exception as e:
            logger.debug(f"Rust analysis failed: {e}")
            return await self._analyze_generic(content, code_file)
    
    async def _analyze_generic(self, content: str, code_file: CodeFile) -> Dict[str, Any]:
        """Generic analysis for unsupported languages."""
        try:
            # Basic pattern matching for common constructs
            functions = []
            classes = []
            imports = []
            dependencies = []
            
            # Generic function patterns
            func_patterns = [
                r'def\s+(\w+)\s*\(',  # Python-style
                r'function\s+(\w+)\s*\(',  # JavaScript-style
                r'(\w+)\s*\([^)]*\)\s*\{',  # C-style
            ]
            
            for pattern in func_patterns:
                matches = re.finditer(pattern, content, re.MULTILINE)
                functions.extend([match.group(1) for match in matches])
            
            return {
                'status': 'success',
                'functions': list(set(functions)),
                'classes': classes,
                'imports': imports,
                'dependencies': dependencies
            }
            
        except Exception as e:
            logger.debug(f"Generic analysis failed: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'functions': [],
                'classes': [],
                'imports': [],
                'dependencies': []
            }
    
    async def _create_function_chunks(self, content: str, code_file: CodeFile) -> List[CodeChunk]:
        """Create chunks for individual functions."""
        chunks = []
        
        try:
            lines = content.split('\n')
            
            # Language-specific function detection
            if code_file.language == CodeLanguage.PYTHON:
                chunks.extend(await self._extract_python_functions(lines, code_file))
            elif code_file.language in [CodeLanguage.JAVASCRIPT, CodeLanguage.TYPESCRIPT]:
                chunks.extend(await self._extract_js_functions(lines, code_file))
            # Add more language-specific extractors as needed
            
        except Exception as e:
            logger.debug(f"Function chunk creation failed: {e}")
        
        return chunks
    
    async def _create_class_chunks(self, content: str, code_file: CodeFile) -> List[CodeChunk]:
        """Create chunks for classes."""
        chunks = []
        
        try:
            lines = content.split('\n')
            
            # Language-specific class detection
            if code_file.language == CodeLanguage.PYTHON:
                chunks.extend(await self._extract_python_classes(lines, code_file))
            # Add more language-specific extractors as needed
            
        except Exception as e:
            logger.debug(f"Class chunk creation failed: {e}")
        
        return chunks
    
    async def _create_general_chunks(self, content: str, code_file: CodeFile, existing_chunks: List[CodeChunk]) -> List[CodeChunk]:
        """Create general content chunks for code not covered by function/class chunks."""
        chunks = []
        
        try:
            lines = content.split('\n')
            covered_lines = set()
            
            # Mark lines covered by existing chunks
            for chunk in existing_chunks:
                for line_num in range(chunk.start_line, chunk.end_line + 1):
                    covered_lines.add(line_num)
            
            # Create chunks for uncovered content
            current_chunk_lines = []
            current_start_line = None
            
            for i, line in enumerate(lines, 1):
                if i not in covered_lines and line.strip():  # Skip empty lines and covered lines
                    if current_start_line is None:
                        current_start_line = i
                    current_chunk_lines.append(line)
                    
                    # Check if chunk is large enough
                    chunk_content = '\n'.join(current_chunk_lines)
                    if len(chunk_content) >= self.chunk_size:
                        # Create chunk
                        chunk = CodeChunk(
                            chunk_id="",  # Will be set later
                            file_id=code_file.file_id,
                            content=chunk_content,
                            start_line=current_start_line,
                            end_line=i,
                            element_type=CodeElementType.CHUNK
                        )
                        chunks.append(chunk)
                        
                        # Start new chunk with overlap
                        overlap_lines = current_chunk_lines[-self.chunk_overlap//20:] if len(current_chunk_lines) > self.chunk_overlap//20 else []
                        current_chunk_lines = overlap_lines
                        current_start_line = i - len(overlap_lines) + 1 if overlap_lines else None
                else:
                    # End current chunk if we have content
                    if current_chunk_lines and len('\n'.join(current_chunk_lines)) >= self.min_chunk_size:
                        chunk = CodeChunk(
                            chunk_id="",  # Will be set later
                            file_id=code_file.file_id,
                            content='\n'.join(current_chunk_lines),
                            start_line=current_start_line,
                            end_line=i-1,
                            element_type=CodeElementType.CHUNK
                        )
                        chunks.append(chunk)
                    
                    current_chunk_lines = []
                    current_start_line = None
            
            # Handle remaining content
            if current_chunk_lines and len('\n'.join(current_chunk_lines)) >= self.min_chunk_size:
                chunk = CodeChunk(
                    chunk_id="",  # Will be set later
                    file_id=code_file.file_id,
                    content='\n'.join(current_chunk_lines),
                    start_line=current_start_line,
                    end_line=len(lines),
                    element_type=CodeElementType.CHUNK
                )
                chunks.append(chunk)
            
        except Exception as e:
            logger.debug(f"General chunk creation failed: {e}")
        
        return chunks
    
    async def _extract_python_functions(self, lines: List[str], code_file: CodeFile) -> List[CodeChunk]:
        """Extract Python function chunks."""
        chunks = []
        
        try:
            i = 0
            while i < len(lines):
                line = lines[i].strip()
                
                # Look for function definition
                if line.startswith('def ') or line.startswith('async def '):
                    func_start = i + 1  # 1-based line numbers
                    func_lines = [lines[i]]
                    
                    # Extract function name
                    func_name_match = re.search(r'def\s+(\w+)\s*\(', line)
                    func_name = func_name_match.group(1) if func_name_match else "unknown"
                    
                    # Find function end (next function or class, or end of file)
                    j = i + 1
                    indent_level = len(lines[i]) - len(lines[i].lstrip())
                    
                    while j < len(lines):
                        current_line = lines[j]
                        if current_line.strip():  # Non-empty line
                            current_indent = len(current_line) - len(current_line.lstrip())
                            if current_indent <= indent_level and (current_line.strip().startswith('def ') or 
                                                                 current_line.strip().startswith('class ') or
                                                                 current_line.strip().startswith('async def ')):
                                break
                        func_lines.append(current_line)
                        j += 1
                    
                    # Create function chunk
                    chunk = CodeChunk(
                        chunk_id="",  # Will be set later
                        file_id=code_file.file_id,
                        content='\n'.join(func_lines),
                        start_line=func_start,
                        end_line=j,
                        element_type=CodeElementType.FUNCTION,
                        function_name=func_name
                    )
                    chunks.append(chunk)
                    
                    i = j
                else:
                    i += 1
                    
        except Exception as e:
            logger.debug(f"Python function extraction failed: {e}")
        
        return chunks
    
    async def _extract_python_classes(self, lines: List[str], code_file: CodeFile) -> List[CodeChunk]:
        """Extract Python class chunks."""
        chunks = []
        
        try:
            i = 0
            while i < len(lines):
                line = lines[i].strip()
                
                # Look for class definition
                if line.startswith('class '):
                    class_start = i + 1  # 1-based line numbers
                    class_lines = [lines[i]]
                    
                    # Extract class name
                    class_name_match = re.search(r'class\s+(\w+)', line)
                    class_name = class_name_match.group(1) if class_name_match else "unknown"
                    
                    # Find class end
                    j = i + 1
                    indent_level = len(lines[i]) - len(lines[i].lstrip())
                    
                    while j < len(lines):
                        current_line = lines[j]
                        if current_line.strip():  # Non-empty line
                            current_indent = len(current_line) - len(current_line.lstrip())
                            if current_indent <= indent_level and current_line.strip().startswith('class '):
                                break
                        class_lines.append(current_line)
                        j += 1
                    
                    # Create class chunk
                    chunk = CodeChunk(
                        chunk_id="",  # Will be set later
                        file_id=code_file.file_id,
                        content='\n'.join(class_lines),
                        start_line=class_start,
                        end_line=j,
                        element_type=CodeElementType.CLASS,
                        class_name=class_name
                    )
                    chunks.append(chunk)
                    
                    i = j
                else:
                    i += 1
                    
        except Exception as e:
            logger.debug(f"Python class extraction failed: {e}")
        
        return chunks
    
    async def _extract_js_functions(self, lines: List[str], code_file: CodeFile) -> List[CodeChunk]:
        """Extract JavaScript/TypeScript function chunks."""
        chunks = []
        
        try:
            # This is a simplified implementation
            # A full implementation would need proper JavaScript parsing
            
            i = 0
            while i < len(lines):
                line = lines[i].strip()
                
                # Look for function definitions
                func_patterns = [
                    r'function\s+(\w+)\s*\(',
                    r'const\s+(\w+)\s*=\s*\([^)]*\)\s*=>',
                    r'(\w+)\s*:\s*function\s*\('
                ]
                
                func_name = None
                for pattern in func_patterns:
                    match = re.search(pattern, line)
                    if match:
                        func_name = match.group(1)
                        break
                
                if func_name:
                    func_start = i + 1
                    func_lines = [lines[i]]
                    
                    # Find function end (simplified - look for matching braces)
                    brace_count = line.count('{') - line.count('}')
                    j = i + 1
                    
                    while j < len(lines) and brace_count > 0:
                        current_line = lines[j]
                        brace_count += current_line.count('{') - current_line.count('}')
                        func_lines.append(current_line)
                        j += 1
                    
                    # Create function chunk
                    chunk = CodeChunk(
                        chunk_id="",  # Will be set later
                        file_id=code_file.file_id,
                        content='\n'.join(func_lines),
                        start_line=func_start,
                        end_line=j,
                        element_type=CodeElementType.FUNCTION,
                        function_name=func_name
                    )
                    chunks.append(chunk)
                    
                    i = j
                else:
                    i += 1
                    
        except Exception as e:
            logger.debug(f"JavaScript function extraction failed: {e}")
        
        return chunks
