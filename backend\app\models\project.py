"""
Project Management Models - Multi-Project Support

Provides data models for managing multiple isolated projects within the AI Coder Agent system.
Each project has its own workspace, embeddings, sessions, and configuration.
"""

from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from uuid import uuid4

from pydantic import BaseModel, Field, validator
import json


class ProjectConfig(BaseModel):
    """Project-specific configuration settings."""
    
    # Basic settings
    name: str = Field(..., description="Project display name")
    description: str = Field(default="", description="Project description")
    language: str = Field(default="python", description="Primary programming language")
    framework: str = Field(default="", description="Primary framework (e.g., FastAPI, React)")
    
    # AI settings
    preferred_code_model: str = Field(default="deepseek/deepseek-r1-0528:free", description="Preferred coding model")
    preferred_vision_model: str = Field(default="google/gemini-2.0-flash-exp:free", description="Preferred vision model")
    embedding_model: str = Field(default="bge-m3", description="Embedding model for this project")
    
    # Workspace settings
    max_file_size: int = Field(default=10_000_000, description="Maximum file size in bytes")
    allowed_extensions: List[str] = Field(
        default=[".py", ".js", ".ts", ".java", ".cpp", ".go", ".rs", ".md", ".txt", ".json", ".yaml", ".yml", ".html", ".css"],
        description="Allowed file extensions"
    )
    
    # Feature flags
    ai_planning_enabled: bool = Field(default=True, description="Enable AI planning for this project")
    autonomous_continue_enabled: bool = Field(default=True, description="Enable autonomous continue mode")
    code_intelligence_enabled: bool = Field(default=True, description="Enable code intelligence features")
    vision_analysis_enabled: bool = Field(default=True, description="Enable vision analysis features")
    
    # Custom settings
    custom_settings: Dict[str, Any] = Field(default_factory=dict, description="Custom project settings")


class ProjectMetadata(BaseModel):
    """Project metadata and statistics."""
    
    # File statistics
    total_files: int = Field(default=0, description="Total number of files")
    total_lines: int = Field(default=0, description="Total lines of code")
    file_types: Dict[str, int] = Field(default_factory=dict, description="File type counts")
    
    # Embedding statistics
    indexed_files: int = Field(default=0, description="Number of indexed files")
    embedding_count: int = Field(default=0, description="Total embeddings stored")
    last_indexed: Optional[datetime] = Field(default=None, description="Last indexing time")
    
    # Activity statistics
    sessions_count: int = Field(default=0, description="Total sessions created")
    plans_created: int = Field(default=0, description="AI plans created")
    tasks_completed: int = Field(default=0, description="Tasks completed")
    
    # Git integration (future)
    git_repository: Optional[str] = Field(default=None, description="Git repository URL")
    current_branch: Optional[str] = Field(default=None, description="Current git branch")
    last_commit: Optional[str] = Field(default=None, description="Last commit hash")


class Project(BaseModel):
    """Complete project model with all associated data."""
    
    # Core identification
    project_id: str = Field(default_factory=lambda: str(uuid4()), description="Unique project identifier")
    slug: str = Field(..., description="URL-friendly project identifier")
    
    # Basic information
    config: ProjectConfig = Field(..., description="Project configuration")
    metadata: ProjectMetadata = Field(default_factory=ProjectMetadata, description="Project metadata")
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.now, description="Project creation time")
    updated_at: datetime = Field(default_factory=datetime.now, description="Last update time")
    last_accessed: datetime = Field(default_factory=datetime.now, description="Last access time")
    
    # Status
    status: str = Field(default="active", description="Project status: active, archived, deleted")
    
    # Paths (computed)
    workspace_path: Optional[str] = Field(default=None, description="Absolute workspace path")
    config_path: Optional[str] = Field(default=None, description="Project config file path")
    
    @validator('slug')
    def validate_slug(cls, v):
        """Ensure slug is URL-friendly."""
        import re
        if not re.match(r'^[a-z0-9-_]+$', v):
            raise ValueError('Slug must contain only lowercase letters, numbers, hyphens, and underscores')
        return v
    
    def get_workspace_path(self, base_workspace: str = "/app/workspace") -> Path:
        """Get the absolute workspace path for this project."""
        return Path(base_workspace) / self.slug
    
    def get_config_path(self, base_workspace: str = "/app/workspace") -> Path:
        """Get the project config file path."""
        return self.get_workspace_path(base_workspace) / ".deepnexus" / "config.json"
    
    def get_embeddings_collection_name(self) -> str:
        """Get the Qdrant collection name for this project."""
        return f"project_{self.slug}_embeddings"
    
    def get_sessions_db_path(self, base_workspace: str = "/app/workspace") -> Path:
        """Get the project-specific sessions database path."""
        return self.get_workspace_path(base_workspace) / ".deepnexus" / "sessions.db"


class ProjectCreateRequest(BaseModel):
    """Request model for creating a new project."""
    
    name: str = Field(..., min_length=1, max_length=100, description="Project name")
    slug: str = Field(..., min_length=1, max_length=50, description="URL-friendly identifier")
    description: str = Field(default="", max_length=500, description="Project description")
    language: str = Field(default="python", description="Primary programming language")
    framework: str = Field(default="", description="Primary framework")
    
    # Template options (future)
    template: Optional[str] = Field(default=None, description="Project template to use")
    
    @validator('slug')
    def validate_slug(cls, v):
        """Ensure slug is URL-friendly."""
        import re
        if not re.match(r'^[a-z0-9-_]+$', v):
            raise ValueError('Slug must contain only lowercase letters, numbers, hyphens, and underscores')
        return v


class ProjectListResponse(BaseModel):
    """Response model for listing projects."""
    
    projects: List[Project] = Field(..., description="List of projects")
    total: int = Field(..., description="Total number of projects")
    active: int = Field(..., description="Number of active projects")
    archived: int = Field(..., description="Number of archived projects")


class ProjectResponse(BaseModel):
    """Response model for project operations."""
    
    status: str = Field(..., description="Operation status")
    message: str = Field(..., description="Status message")
    project: Optional[Project] = Field(default=None, description="Project data")
    error: Optional[str] = Field(default=None, description="Error message if failed")
