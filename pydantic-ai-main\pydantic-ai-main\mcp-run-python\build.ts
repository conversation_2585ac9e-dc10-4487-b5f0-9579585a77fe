// inline src/prepare_env.py into src/prepareEnvCode.js
import * as path from '@std/path'

if (!import.meta.dirname) {
  throw new Error('import.meta.dirname is not defined, unable to load prepare_env.py')
}
const src = path.join(import.meta.dirname, 'src/prepare_env.py')
const dst = path.join(import.meta.dirname, 'src/prepareEnvCode.ts')

let pythonCode = await Deno.readTextFile(src)
pythonCode = pythonCode.replace(/\\/g, '\\\\')
const jsCode = `\
// DO NOT EDIT THIS FILE DIRECTLY, INSTEAD RUN "deno run build"
export const preparePythonCode = \`${pythonCode}\`
`
await Deno.writeTextFile(dst, jsCode)
