"""
Core Agent Definitions for Pydantic AI Migration

This module defines the main AI agents that replace the TaskRunner system,
using Pydantic AI's decorator-based tool registration and dependency injection.

Enhanced with intelligent error recovery, retry mechanisms, and user notifications.
"""

import logging
import os

from pydantic_ai import Agent, RunContext
from pydantic_ai.providers.openrouter import OpenRouterProvider

from .dependencies import CodingDependencies, VisionDependencies
from .models import TaskResult
from .monitoring import setup_monitoring, log_phase7_milestone
from .tools import get_coding_tools, get_vision_tools
from .deepseek_model import DeepSeekR1Model
from .simple_vision_model import SimpleVisionModel
from .advanced_vision_model import AdvancedVisionModel

# Import enhanced error recovery system
from ..error_handling import (
    get_error_integration,
    RetryConfig,
    ErrorHandlingIntegration
)
from ..error_handling.retry_manager import RetryStrategy as ErrorRetryStrategy

# Initialize comprehensive monitoring
setup_monitoring()

# Log agent initialization
log_phase7_milestone("7A", "agent_initialization", "started", "Initializing coding and vision agents")

logger = logging.getLogger(__name__)

# Get API key from environment
OPENROUTER_API_KEY = os.getenv('OPENROUTER_API_KEY')
if not OPENROUTER_API_KEY:
    logger.warning("OPENROUTER_API_KEY not found in environment variables")

# Create OpenRouter provider
openrouter_provider = OpenRouterProvider(api_key=OPENROUTER_API_KEY)

# Create models for different tasks
# Using custom DeepSeek R1 model for coding tasks (with JSON-based tool calling)
coding_model = DeepSeekR1Model('deepseek/deepseek-r1-0528:free')

# Use advanced vision model with tool calling support (similar to DeepSeek approach)
vision_model = AdvancedVisionModel('google/gemini-2.0-flash-exp:free')

# Keep simple vision model for basic tasks
simple_vision_model = SimpleVisionModel('google/gemini-2.0-flash-exp:free')

# Enhanced retry configuration for coding tasks
coding_retry_config = RetryConfig(
    max_attempts=3,
    strategy=ErrorRetryStrategy.EXPONENTIAL,
    base_delay=1.0,
    max_delay=30.0,
    model_retry_attempts=2,
    tool_retry_attempts=3,
    use_circuit_breaker=True
)

# Main coding agent with tools and enhanced error recovery
coding_agent = Agent(
    model=coding_model,
    deps_type=CodingDependencies,
    output_type=TaskResult,
    tools=get_coding_tools(),  # Use tools from registry
    system_prompt="""
You are an expert AI coding assistant for the DeepNexus AI Coder Agent system.

Your capabilities include:
- Reading, writing, and analyzing code files
- Performing git operations (status, diff, commit, push)
- Running linting and testing tools
- Analyzing code complexity and maintainability
- Tracking file changes and project context
- Semantic code search using vector embeddings

You have access to comprehensive tools for:
1. File Operations: read_file, write_file, create_file, delete_file, list_directory
2. Git Operations: git_status, git_diff, git_commit, git_push, git_branch
3. Code Analysis: analyze_code, search_code, get_project_context
4. Testing: run_linting, run_tests
5. Change Tracking: track_changes, get_change_summary

Enhanced Error Recovery Features:
- Automatic retry with intelligent backoff for transient failures
- Smart error categorization and recovery strategies
- User-friendly error notifications with actionable guidance
- Circuit breaker protection against cascading failures

Always:
- Use structured outputs with proper typing
- Provide clear reasoning for each action
- Handle errors gracefully with detailed messages
- Follow best practices for code quality and security
- Test changes thoroughly before committing
- Leverage automatic error recovery when issues occur

Focus on delivering high-quality, maintainable code that follows project conventions.
The system will automatically handle and recover from common errors.
""",
    retries=3,  # Increased retries with enhanced recovery
    instrument=True  # Enable automatic Logfire instrumentation
)

# Enhanced retry configuration for vision tasks
vision_retry_config = RetryConfig(
    max_attempts=3,
    strategy=ErrorRetryStrategy.EXPONENTIAL,
    base_delay=2.0,  # Slightly longer delay for vision processing
    max_delay=60.0,
    model_retry_attempts=2,
    tool_retry_attempts=2,
    use_circuit_breaker=True,
    rate_limit_delay=45.0  # Longer delay for vision API rate limits
)

# Vision analysis agent with enhanced error recovery
vision_agent = Agent(
    model=vision_model,
    deps_type=VisionDependencies,
    # Removed output_type to allow free-form responses
    tools=get_vision_tools(),
    system_prompt="""
You are an expert vision analysis AI for the DeepNexus AI Coder Agent system.

Your capabilities include:
- Analyzing screenshots and UI images with professional-grade insights
- Identifying visual bugs, accessibility issues, and usability problems
- Providing comprehensive recommendations with specific implementation details
- Detecting design patterns, layout issues, and user experience problems
- Performing detailed accessibility audits with WCAG compliance analysis

You have access to advanced tools for:
1. AI-Powered Analysis: ai_analyze_image, detect_ui_elements
2. Accessibility Auditing: accessibility_audit
3. Visual Reporting: generate_visual_report, visual_regression_analysis
4. Basic Analysis: analyze_screenshot, analyze_ui_component, compare_screenshots

Enhanced Error Recovery Features:
- Intelligent retry mechanisms for vision processing failures
- Automatic fallback to simpler analysis methods when needed
- Smart handling of image processing timeouts and rate limits
- User-friendly notifications for vision analysis issues

When providing analysis, ALWAYS include:
- Detailed observations with specific locations and measurements
- Comprehensive recommendations with implementation steps
- Accessibility issues with WCAG guideline references
- Usability improvements with user impact analysis
- Design suggestions with visual hierarchy considerations
- Specific technical recommendations for developers
- Priority levels for each issue (Critical, High, Medium, Low)
- Confidence scores and detailed reasoning

Focus on delivering professional-grade visual analysis that provides actionable insights for development teams.
The system will automatically handle and recover from vision processing errors.
""",
    retries=3,  # Increased retries with enhanced recovery
    instrument=True  # Enable automatic Logfire instrumentation
)


@coding_agent.system_prompt
async def add_workspace_context(ctx: RunContext[CodingDependencies]) -> str:
    """Add current workspace context to the system prompt."""
    try:
        workspace_info = f"Current workspace: {ctx.deps.workspace_root}"
        
        # Get basic project context if available
        if hasattr(ctx.deps.repo_context, 'get_project_summary'):
            try:
                summary = await ctx.deps.repo_context.get_project_summary(str(ctx.deps.workspace_root))
                if summary.get('status') == 'success':
                    workspace_info += f"\nProject summary: {summary.get('summary', 'No summary available')}"
            except Exception as e:
                logger.warning(f"Failed to get project summary: {e}")
        
        return workspace_info
    except Exception as e:
        logger.error(f"Failed to add workspace context: {e}")
        return f"Current workspace: {ctx.deps.workspace_root}"


@vision_agent.system_prompt
async def add_vision_context(ctx: RunContext[VisionDependencies]) -> str:
    """Add vision analysis context to the system prompt."""
    return f"Current workspace: {ctx.deps.workspace_root}\nFocus on providing actionable visual feedback."


# Simple agents without tools for basic communication
simple_coding_agent = Agent(
    model=coding_model,
    system_prompt="""You are a helpful AI coding assistant.
    Respond naturally and conversationally. You can discuss code, provide explanations,
    and help with programming questions without needing to use tools."""
)

simple_vision_agent = Agent(
    model=simple_vision_model,
    system_prompt="""You are a helpful AI vision analysis assistant.
    Respond naturally and conversationally. You can analyze images and provide
    visual insights without needing to use tools."""
)

# Enhanced agent execution functions with error recovery
async def execute_coding_task_with_recovery(
    prompt: str,
    dependencies: CodingDependencies,
    session_id: str = None,
    user_id: str = None,
    show_technical_details: bool = False
) -> dict:
    """Execute coding task with enhanced error recovery."""

    error_integration = get_error_integration()

    @error_integration.create_error_handling_decorator(
        operation="coding_task_execution",
        retry_config=coding_retry_config,
        show_technical_details=show_technical_details
    )
    async def _execute_coding_task():
        return await coding_agent.run(
            prompt,
            deps=dependencies,
            _session_id=session_id,
            _user_id=user_id
        )

    return await _execute_coding_task()


async def execute_vision_task_with_recovery(
    prompt: str,
    dependencies: VisionDependencies,
    session_id: str = None,
    user_id: str = None,
    show_technical_details: bool = False
) -> dict:
    """Execute vision task with enhanced error recovery."""

    error_integration = get_error_integration()

    @error_integration.create_error_handling_decorator(
        operation="vision_task_execution",
        retry_config=vision_retry_config,
        show_technical_details=show_technical_details
    )
    async def _execute_vision_task():
        return await vision_agent.run(
            prompt,
            deps=dependencies,
            _session_id=session_id,
            _user_id=user_id
        )

    return await _execute_vision_task()


# Enhanced simple agent execution
async def execute_simple_coding_with_recovery(
    prompt: str,
    session_id: str = None,
    user_id: str = None
) -> dict:
    """Execute simple coding task with error recovery."""

    error_integration = get_error_integration()

    @error_integration.create_error_handling_decorator(
        operation="simple_coding_execution",
        retry_config=RetryConfig(max_attempts=2, base_delay=0.5)
    )
    async def _execute_simple_coding():
        return await simple_coding_agent.run(
            prompt,
            _session_id=session_id,
            _user_id=user_id
        )

    return await _execute_simple_coding()


async def execute_simple_vision_with_recovery(
    prompt: str,
    session_id: str = None,
    user_id: str = None
) -> dict:
    """Execute simple vision task with error recovery."""

    error_integration = get_error_integration()

    @error_integration.create_error_handling_decorator(
        operation="simple_vision_execution",
        retry_config=RetryConfig(max_attempts=2, base_delay=1.0)
    )
    async def _execute_simple_vision():
        return await simple_vision_agent.run(
            prompt,
            _session_id=session_id,
            _user_id=user_id
        )

    return await _execute_simple_vision()


# Get error handling system health
def get_agent_error_health() -> dict:
    """Get error handling health status for agents."""
    return get_error_integration().get_system_health()


# Log agent initialization completion
log_phase7_milestone("7A", "agent_initialization", "completed",
                    f"Agents configured with enhanced error recovery: coding_model={coding_model.model_name}, vision_model={vision_model.model_name}")

# Export agents and enhanced functions for use in other modules
__all__ = [
    'coding_agent', 'vision_agent', 'coding_model', 'vision_model',
    'simple_coding_agent', 'simple_vision_agent',
    'execute_coding_task_with_recovery', 'execute_vision_task_with_recovery',
    'execute_simple_coding_with_recovery', 'execute_simple_vision_with_recovery',
    'get_agent_error_health', 'coding_retry_config', 'vision_retry_config'
]
