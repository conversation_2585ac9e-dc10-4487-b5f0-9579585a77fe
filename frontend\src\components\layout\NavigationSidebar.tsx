import React, { useState } from 'react';

interface NavigationItem {
  id: string;
  label: string;
  icon: string;
  path: string;
  badge?: number;
  children?: NavigationItem[];
}

interface NavigationSidebarProps {
  collapsed: boolean;
  onToggle: () => void;
  currentLayout: string;
  onLayoutChange: (layout: 'chat' | 'code' | 'dashboard' | 'analytics' | 'projects') => void;
  systemHealth: {
    api: string;
    database: string;
    services: string;
  };
}

export const NavigationSidebar: React.FC<NavigationSidebarProps> = ({
  collapsed,
  onToggle,
  currentLayout,
  onLayoutChange,
  systemHealth
}) => {
  const [expandedItems, setExpandedItems] = useState<string[]>(['ai-tools']);

  const navigationItems: NavigationItem[] = [
    {
      id: 'projects',
      label: 'Project Forest',
      icon: '🌳',
      path: '/projects'
    },
    {
      id: 'chat',
      label: 'AI Chat',
      icon: '💬',
      path: '/chat'
    },
    {
      id: 'code',
      label: 'Code Editor',
      icon: '⚡',
      path: '/code'
    },
    {
      id: 'ai-tools',
      label: 'AI Tools',
      icon: '🧠',
      path: '/ai-tools',
      children: [
        { id: 'planning', label: 'AI Planning', icon: '📋', path: '/planning' },
        { id: 'analysis', label: 'Code Analysis', icon: '🔍', path: '/analysis' },
        { id: 'refactor', label: 'Refactoring', icon: '🛠️', path: '/refactor' },
        { id: 'debug', label: 'Debug Assistant', icon: '🐛', path: '/debug' },
        { id: 'tests', label: 'Test Generator', icon: '🧪', path: '/tests' }
      ]
    },
    {
      id: 'projects',
      label: 'Projects',
      icon: '📁',
      path: '/projects',
      children: [
        { id: 'current', label: 'Current Project', icon: '🎯', path: '/projects/current' },
        { id: 'templates', label: 'Templates', icon: '📄', path: '/projects/templates' },
        { id: 'archive', label: 'Archive', icon: '📦', path: '/projects/archive' }
      ]
    },
    {
      id: 'sessions',
      label: 'Sessions',
      icon: '💾',
      path: '/sessions',
      badge: 3
    },
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: '📊',
      path: '/dashboard'
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: '📈',
      path: '/analytics'
    },
    {
      id: 'security',
      label: 'Security',
      icon: '🔐',
      path: '/security',
      children: [
        { id: 'api-keys', label: 'API Keys', icon: '🔑', path: '/security/keys' },
        { id: 'threats', label: 'Threats', icon: '🚨', path: '/security/threats' },
        { id: 'rate-limits', label: 'Rate Limits', icon: '⚡', path: '/security/limits' }
      ]
    },
    {
      id: 'context',
      label: 'Context',
      icon: '🎯',
      path: '/context',
      children: [
        { id: 'optimize', label: 'Optimize', icon: '⚡', path: '/context/optimize' },
        { id: 'cache', label: 'Cache', icon: '💾', path: '/context/cache' },
        { id: 'summarize', label: 'Summarize', icon: '📝', path: '/context/summarize' }
      ]
    }
  ];

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const handleItemClick = (item: NavigationItem) => {
    if (item.children) {
      toggleExpanded(item.id);
    } else {
      // Handle navigation
      if (['chat', 'code', 'dashboard', 'analytics', 'projects'].includes(item.id)) {
        onLayoutChange(item.id as any);
      }
    }
  };

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    const isExpanded = expandedItems.includes(item.id);
    const isActive = currentLayout === item.id;
    const hasChildren = item.children && item.children.length > 0;

    return (
      <div key={item.id} className="mb-1">
        <button
          onClick={() => handleItemClick(item)}
          className={`w-full flex items-center justify-between p-3 rounded-lg transition-all duration-300 hover-bloom group ${
            isActive 
              ? 'bg-growth-gradient text-crystal-clear shadow-md' 
              : 'text-earth-base hover:bg-growth-accent/10'
          } ${level > 0 ? 'ml-4 text-sm' : ''}`}
        >
          <div className="flex items-center space-x-3">
            <span className={`text-lg transition-transform duration-300 ${
              isActive ? 'scale-110' : 'group-hover:scale-105'
            }`}>
              {item.icon}
            </span>
            
            {!collapsed && (
              <>
                <span className="font-medium">{item.label}</span>
                {item.badge && (
                  <span className="bg-crimson-alert text-crystal-clear text-xs rounded-full px-2 py-1 animate-breathe">
                    {item.badge}
                  </span>
                )}
              </>
            )}
          </div>

          {!collapsed && hasChildren && (
            <span className={`transition-transform duration-300 ${
              isExpanded ? 'rotate-90' : ''
            }`}>
              ▶
            </span>
          )}
        </button>

        {/* Children */}
        {!collapsed && hasChildren && isExpanded && (
          <div className="mt-2 space-y-1 animate-bloom">
            {item.children!.map(child => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <aside className={`fixed left-0 top-0 h-full bg-crystal-clear border-r border-growth-accent/20 transition-all duration-300 z-50 ${
      collapsed ? 'w-16' : 'w-64'
    }`}>
      
      {/* Header */}
      <div className="p-4 border-b border-growth-accent/20">
        <div className="flex items-center justify-between">
          {!collapsed && (
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-forest-gradient rounded-lg flex items-center justify-center">
                <span className="text-crystal-clear font-bold">🌿</span>
              </div>
              <span className="font-semibold text-forest-primary">DeepNexus</span>
            </div>
          )}
          
          <button
            onClick={onToggle}
            className="p-2 rounded-lg hover:bg-growth-accent/10 transition-all hover-bloom"
            title={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            <span className={`transition-transform duration-300 ${collapsed ? 'rotate-180' : ''}`}>
              ◀
            </span>
          </button>
        </div>
      </div>

      {/* System Health Indicator */}
      {!collapsed && (
        <div className="p-4 border-b border-growth-accent/20">
          <div className="bg-mist-gradient rounded-lg p-3">
            <h4 className="text-sm font-semibold text-forest-primary mb-2">System Health</h4>
            <div className="space-y-2">
              {Object.entries(systemHealth).map(([service, status]) => (
                <div key={service} className="flex items-center justify-between">
                  <span className="text-xs text-earth-base capitalize">{service}</span>
                  <div className={`w-2 h-2 rounded-full animate-breathe ${
                    status === 'healthy' ? 'bg-growth-accent' :
                    status === 'error' ? 'bg-crimson-alert' :
                    'bg-bloom-highlight'
                  }`} />
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Navigation Items */}
      <nav className="p-4 flex-1 overflow-y-auto">
        <div className="space-y-2">
          {navigationItems.map(item => renderNavigationItem(item))}
        </div>
      </nav>

      {/* Quick Actions (when collapsed) */}
      {collapsed && (
        <div className="p-2 border-t border-growth-accent/20">
          <div className="space-y-2">
            <button className="w-full p-2 rounded-lg hover:bg-growth-accent/10 transition-all hover-bloom" title="Quick Chat">
              💬
            </button>
            <button className="w-full p-2 rounded-lg hover:bg-growth-accent/10 transition-all hover-bloom" title="Quick Analysis">
              🔍
            </button>
            <button className="w-full p-2 rounded-lg hover:bg-growth-accent/10 transition-all hover-bloom" title="Quick Refactor">
              🛠️
            </button>
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="p-4 border-t border-growth-accent/20">
        {!collapsed ? (
          <div className="text-center">
            <div className="text-xs text-earth-base/60">
              🌿 Digital Forest v1.0
            </div>
            <div className="text-xs text-earth-base/40 mt-1">
              Growing with nature's wisdom
            </div>
          </div>
        ) : (
          <div className="text-center">
            <span className="text-lg animate-breathe">🌿</span>
          </div>
        )}
      </div>
    </aside>
  );
};
