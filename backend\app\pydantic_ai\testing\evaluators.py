"""
Evaluation Framework for Pydantic AI

This module provides comprehensive evaluators for assessing AI agent performance
across different dimensions including code quality, tool usage, and response time.
"""

import time
import logging
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from abc import ABC, abstractmethod

from pydantic_ai.messages import ModelMessage, ModelResponse, ToolCallPart, TextPart

logger = logging.getLogger(__name__)


@dataclass
class EvaluationResult:
    """Result of an evaluation."""
    score: float  # 0.0 to 1.0
    details: Dict[str, Any]
    passed: bool
    feedback: str


class BaseEvaluator(ABC):
    """Base class for all evaluators."""
    
    def __init__(self, name: str, weight: float = 1.0):
        self.name = name
        self.weight = weight
    
    @abstractmethod
    async def evaluate(
        self,
        input_data: Any,
        output_data: Any,
        messages: Optional[List[ModelMessage]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> EvaluationResult:
        """Evaluate the input/output pair."""
        pass


class CodeQualityEvaluator(BaseEvaluator):
    """Evaluates code quality in responses."""
    
    def __init__(self, weight: float = 1.0):
        super().__init__("CodeQuality", weight)
        self.quality_keywords = [
            "function", "class", "import", "def", "return",
            "if", "for", "while", "try", "except"
        ]
        self.best_practices = [
            "docstring", "type hint", "error handling", "validation",
            "clean code", "readable", "maintainable"
        ]
    
    async def evaluate(
        self,
        input_data: Any,
        output_data: Any,
        messages: Optional[List[ModelMessage]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> EvaluationResult:
        """Evaluate code quality in the response."""
        
        if not isinstance(output_data, str):
            output_text = str(output_data)
        else:
            output_text = output_data
        
        output_lower = output_text.lower()
        
        # Check for code elements
        code_score = 0.0
        code_elements_found = []
        
        for keyword in self.quality_keywords:
            if keyword in output_lower:
                code_score += 0.1
                code_elements_found.append(keyword)
        
        # Check for best practices
        practices_score = 0.0
        practices_found = []
        
        for practice in self.best_practices:
            if practice in output_lower:
                practices_score += 0.15
                practices_found.append(practice)
        
        # Check for code blocks (markdown)
        code_blocks = output_text.count("```")
        if code_blocks >= 2:  # At least one complete code block
            code_score += 0.2
        
        # Calculate final score
        total_score = min(1.0, (code_score + practices_score) / 2.0)
        
        # Determine if passed (threshold: 0.3)
        passed = total_score >= 0.3
        
        # Generate feedback
        feedback_parts = []
        if code_elements_found:
            feedback_parts.append(f"Found code elements: {', '.join(code_elements_found)}")
        if practices_found:
            feedback_parts.append(f"Best practices mentioned: {', '.join(practices_found)}")
        if code_blocks >= 2:
            feedback_parts.append("Contains properly formatted code blocks")
        
        feedback = "; ".join(feedback_parts) if feedback_parts else "Limited code-related content detected"
        
        return EvaluationResult(
            score=total_score,
            details={
                "code_elements": code_elements_found,
                "best_practices": practices_found,
                "code_blocks": code_blocks // 2,
                "code_score": code_score,
                "practices_score": practices_score
            },
            passed=passed,
            feedback=feedback
        )


class ToolCallAccuracyEvaluator(BaseEvaluator):
    """Evaluates accuracy of tool calling."""
    
    def __init__(self, expected_tools: Optional[List[str]] = None, weight: float = 1.0):
        super().__init__("ToolCallAccuracy", weight)
        self.expected_tools = expected_tools or []
    
    async def evaluate(
        self,
        input_data: Any,
        output_data: Any,
        messages: Optional[List[ModelMessage]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> EvaluationResult:
        """Evaluate tool calling accuracy."""
        
        if not messages:
            return EvaluationResult(
                score=0.0,
                details={"error": "No messages provided"},
                passed=False,
                feedback="Cannot evaluate tool calls without message history"
            )
        
        # Extract tool calls from messages
        tool_calls = []
        for message in messages:
            if isinstance(message, ModelResponse):
                for part in message.parts:
                    if isinstance(part, ToolCallPart):
                        tool_calls.append(part.tool_name)
        
        # Calculate accuracy
        if not self.expected_tools:
            # If no expected tools specified, just check if tools were called appropriately
            score = 1.0 if tool_calls else 0.5  # Partial credit for no tools if none expected
            passed = True
            feedback = f"Tool calls made: {', '.join(tool_calls)}" if tool_calls else "No tool calls made"
        else:
            # Check against expected tools
            expected_set = set(self.expected_tools)
            actual_set = set(tool_calls)
            
            # Calculate precision and recall
            if actual_set:
                precision = len(expected_set & actual_set) / len(actual_set)
            else:
                precision = 1.0 if not expected_set else 0.0
            
            if expected_set:
                recall = len(expected_set & actual_set) / len(expected_set)
            else:
                recall = 1.0
            
            # F1 score
            if precision + recall > 0:
                score = 2 * (precision * recall) / (precision + recall)
            else:
                score = 0.0
            
            passed = score >= 0.7
            
            missing_tools = expected_set - actual_set
            extra_tools = actual_set - expected_set
            
            feedback_parts = []
            if missing_tools:
                feedback_parts.append(f"Missing tools: {', '.join(missing_tools)}")
            if extra_tools:
                feedback_parts.append(f"Extra tools: {', '.join(extra_tools)}")
            if not feedback_parts:
                feedback_parts.append("Tool calls match expectations")
            
            feedback = "; ".join(feedback_parts)
        
        return EvaluationResult(
            score=score,
            details={
                "expected_tools": self.expected_tools,
                "actual_tools": tool_calls,
                "tool_call_count": len(tool_calls)
            },
            passed=passed,
            feedback=feedback
        )


class ResponseTimeEvaluator(BaseEvaluator):
    """Evaluates response time performance."""
    
    def __init__(self, max_time: float = 10.0, weight: float = 1.0):
        super().__init__("ResponseTime", weight)
        self.max_time = max_time
    
    async def evaluate(
        self,
        input_data: Any,
        output_data: Any,
        messages: Optional[List[ModelMessage]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> EvaluationResult:
        """Evaluate response time."""
        
        # Get response time from metadata
        response_time = metadata.get("response_time", 0.0) if metadata else 0.0
        
        # Calculate score (inverse relationship with time)
        if response_time <= 1.0:
            score = 1.0
        elif response_time <= self.max_time:
            score = 1.0 - (response_time - 1.0) / (self.max_time - 1.0)
        else:
            score = 0.0
        
        passed = response_time <= self.max_time
        
        feedback = f"Response time: {response_time:.2f}s (max: {self.max_time}s)"
        
        return EvaluationResult(
            score=score,
            details={
                "response_time": response_time,
                "max_time": self.max_time,
                "within_limit": passed
            },
            passed=passed,
            feedback=feedback
        )


class ComprehensiveEvaluator(BaseEvaluator):
    """Combines multiple evaluators for comprehensive assessment."""
    
    def __init__(self, evaluators: List[BaseEvaluator], weight: float = 1.0):
        super().__init__("Comprehensive", weight)
        self.evaluators = evaluators
    
    async def evaluate(
        self,
        input_data: Any,
        output_data: Any,
        messages: Optional[List[ModelMessage]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> EvaluationResult:
        """Run all evaluators and combine results."""
        
        results = []
        total_weighted_score = 0.0
        total_weight = 0.0
        all_passed = True
        feedback_parts = []
        
        for evaluator in self.evaluators:
            try:
                result = await evaluator.evaluate(input_data, output_data, messages, metadata)
                results.append((evaluator.name, result))
                
                weighted_score = result.score * evaluator.weight
                total_weighted_score += weighted_score
                total_weight += evaluator.weight
                
                if not result.passed:
                    all_passed = False
                
                feedback_parts.append(f"{evaluator.name}: {result.feedback}")
                
            except Exception as e:
                logger.error(f"Evaluator {evaluator.name} failed: {e}")
                results.append((evaluator.name, None))
                all_passed = False
                feedback_parts.append(f"{evaluator.name}: Error - {str(e)}")
        
        # Calculate overall score
        overall_score = total_weighted_score / total_weight if total_weight > 0 else 0.0
        
        return EvaluationResult(
            score=overall_score,
            details={
                "individual_results": {name: result.details if result else None for name, result in results},
                "evaluator_count": len(self.evaluators),
                "total_weight": total_weight
            },
            passed=all_passed and overall_score >= 0.6,
            feedback=" | ".join(feedback_parts)
        )


# Utility functions for creating common evaluator combinations
def create_coding_evaluators() -> List[BaseEvaluator]:
    """Create evaluators for coding tasks."""
    return [
        CodeQualityEvaluator(weight=1.5),
        ToolCallAccuracyEvaluator(weight=1.0),
        ResponseTimeEvaluator(max_time=15.0, weight=0.5)
    ]


def create_vision_evaluators() -> List[BaseEvaluator]:
    """Create evaluators for vision tasks."""
    return [
        ToolCallAccuracyEvaluator(
            expected_tools=["analyze_screenshot", "accessibility_audit"],
            weight=1.5
        ),
        ResponseTimeEvaluator(max_time=20.0, weight=0.5)
    ]


def create_workflow_evaluators() -> List[BaseEvaluator]:
    """Create evaluators for workflow tasks."""
    return [
        ToolCallAccuracyEvaluator(weight=2.0),  # Tool calling is critical for workflows
        ResponseTimeEvaluator(max_time=30.0, weight=0.5)
    ]


# Export all evaluators
__all__ = [
    'EvaluationResult',
    'BaseEvaluator',
    'CodeQualityEvaluator',
    'ToolCallAccuracyEvaluator', 
    'ResponseTimeEvaluator',
    'ComprehensiveEvaluator',
    'create_coding_evaluators',
    'create_vision_evaluators',
    'create_workflow_evaluators'
]
