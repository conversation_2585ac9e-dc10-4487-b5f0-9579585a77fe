"""
Multi-Agent Workflows for Pydantic AI

This module implements advanced multi-agent coordination and workflows,
allowing different agents to work together on complex tasks.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum

from pydantic import BaseModel
from pydantic_ai import Agent, RunContext

from .agents import coding_agent, vision_agent, simple_coding_agent
from .dependencies import CodingDependencies, VisionDependencies, create_dependencies
from .models import TaskResult, VisionAnalysisResult

logger = logging.getLogger(__name__)


class WorkflowStatus(str, Enum):
    """Status of workflow execution."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AgentType(str, Enum):
    """Types of available agents."""
    CODING = "coding"
    VISION = "vision"
    SIMPLE_CODING = "simple_coding"


@dataclass
class WorkflowStep:
    """Individual step in a multi-agent workflow."""
    
    step_id: str
    agent_type: AgentType
    task_description: str
    depends_on: List[str] = None  # List of step_ids this step depends on
    timeout_seconds: int = 300
    retry_count: int = 2


class WorkflowResult(BaseModel):
    """Result of workflow execution."""
    
    workflow_id: str
    status: WorkflowStatus
    steps_completed: int
    total_steps: int
    results: Dict[str, Any]
    errors: List[str]
    execution_time: float
    
    class Config:
        json_encoders = {
            WorkflowStatus: lambda v: v.value,
            AgentType: lambda v: v.value
        }


class MultiAgentWorkflow:
    """
    Orchestrates multi-agent workflows with dependency management.
    
    This class allows complex tasks to be broken down into steps that
    can be executed by different specialized agents in the correct order.
    """
    
    def __init__(self, workflow_id: str):
        self.workflow_id = workflow_id
        self.steps: List[WorkflowStep] = []
        self.results: Dict[str, Any] = {}
        self.errors: List[str] = []
        self.status = WorkflowStatus.PENDING
        
    def add_step(self, step: WorkflowStep) -> 'MultiAgentWorkflow':
        """Add a step to the workflow."""
        self.steps.append(step)
        return self
    
    def add_coding_step(self, step_id: str, task: str, depends_on: List[str] = None) -> 'MultiAgentWorkflow':
        """Convenience method to add a coding agent step."""
        step = WorkflowStep(
            step_id=step_id,
            agent_type=AgentType.CODING,
            task_description=task,
            depends_on=depends_on or []
        )
        return self.add_step(step)
    
    def add_vision_step(self, step_id: str, task: str, depends_on: List[str] = None) -> 'MultiAgentWorkflow':
        """Convenience method to add a vision agent step."""
        step = WorkflowStep(
            step_id=step_id,
            agent_type=AgentType.VISION,
            task_description=task,
            depends_on=depends_on or []
        )
        return self.add_step(step)
    
    async def execute(self, workspace_root: str = ".") -> WorkflowResult:
        """
        Execute the workflow with proper dependency management.
        
        Args:
            workspace_root: Root directory for workspace operations
            
        Returns:
            WorkflowResult with execution details
        """
        import time
        start_time = time.time()
        
        try:
            self.status = WorkflowStatus.RUNNING
            logger.info(f"Starting workflow {self.workflow_id} with {len(self.steps)} steps")
            
            # Create dependencies
            coding_deps = await create_dependencies(workspace_root)
            vision_deps = None  # Will create when needed
            
            # Track completed steps
            completed_steps = set()
            
            # Execute steps in dependency order
            while len(completed_steps) < len(self.steps):
                # Find steps that can be executed (all dependencies met)
                ready_steps = [
                    step for step in self.steps
                    if step.step_id not in completed_steps
                    and all(dep in completed_steps for dep in (step.depends_on or []))
                ]
                
                if not ready_steps:
                    # Check for circular dependencies or missing dependencies
                    remaining_steps = [s for s in self.steps if s.step_id not in completed_steps]
                    missing_deps = []
                    for step in remaining_steps:
                        for dep in (step.depends_on or []):
                            if dep not in [s.step_id for s in self.steps]:
                                missing_deps.append(f"Step {step.step_id} depends on missing step {dep}")
                    
                    if missing_deps:
                        error_msg = f"Missing dependencies: {'; '.join(missing_deps)}"
                        self.errors.append(error_msg)
                        logger.error(error_msg)
                        self.status = WorkflowStatus.FAILED
                        break
                    else:
                        error_msg = "Circular dependency detected in workflow"
                        self.errors.append(error_msg)
                        logger.error(error_msg)
                        self.status = WorkflowStatus.FAILED
                        break
                
                # Execute ready steps (can be done in parallel)
                step_tasks = []
                for step in ready_steps:
                    task = self._execute_step(step, coding_deps, vision_deps, workspace_root)
                    step_tasks.append((step.step_id, task))
                
                # Wait for all ready steps to complete
                for step_id, task in step_tasks:
                    try:
                        result = await task
                        self.results[step_id] = result
                        completed_steps.add(step_id)
                        logger.info(f"Completed step {step_id}")
                    except Exception as e:
                        error_msg = f"Step {step_id} failed: {str(e)}"
                        self.errors.append(error_msg)
                        logger.error(error_msg)
                        # Continue with other steps unless this is a critical failure
            
            # Determine final status
            if len(completed_steps) == len(self.steps):
                self.status = WorkflowStatus.COMPLETED
                logger.info(f"Workflow {self.workflow_id} completed successfully")
            else:
                self.status = WorkflowStatus.FAILED
                logger.error(f"Workflow {self.workflow_id} failed - {len(completed_steps)}/{len(self.steps)} steps completed")
            
        except Exception as e:
            self.status = WorkflowStatus.FAILED
            error_msg = f"Workflow execution failed: {str(e)}"
            self.errors.append(error_msg)
            logger.error(error_msg)
        
        execution_time = time.time() - start_time
        
        return WorkflowResult(
            workflow_id=self.workflow_id,
            status=self.status,
            steps_completed=len([s for s in self.steps if s.step_id in self.results]),
            total_steps=len(self.steps),
            results=self.results,
            errors=self.errors,
            execution_time=execution_time
        )
    
    async def _execute_step(self, step: WorkflowStep, coding_deps: CodingDependencies, 
                          vision_deps: Optional[VisionDependencies], workspace_root: str) -> Any:
        """Execute a single workflow step."""
        logger.info(f"Executing step {step.step_id} with agent {step.agent_type.value}")
        
        # Build context from previous steps
        context = self._build_step_context(step)
        full_task = f"{step.task_description}\n\nContext from previous steps:\n{context}"
        
        # Execute based on agent type
        if step.agent_type == AgentType.CODING:
            result = await coding_agent.run(full_task, deps=coding_deps)
            return {
                "output": result.output,
                "agent_type": "coding",
                "step_id": step.step_id
            }
        
        elif step.agent_type == AgentType.VISION:
            if vision_deps is None:
                from .dependencies import create_vision_dependencies
                vision_deps = await create_vision_dependencies(workspace_root)
            
            result = await vision_agent.run(full_task, deps=vision_deps)
            return {
                "output": result.output,
                "agent_type": "vision", 
                "step_id": step.step_id
            }
        
        elif step.agent_type == AgentType.SIMPLE_CODING:
            result = await simple_coding_agent.run(full_task)
            return {
                "output": result.output,
                "agent_type": "simple_coding",
                "step_id": step.step_id
            }
        
        else:
            raise ValueError(f"Unknown agent type: {step.agent_type}")
    
    def _build_step_context(self, current_step: WorkflowStep) -> str:
        """Build context string from completed dependency steps."""
        if not current_step.depends_on:
            return "No previous context."
        
        context_parts = []
        for dep_step_id in current_step.depends_on:
            if dep_step_id in self.results:
                result = self.results[dep_step_id]
                output = result.get("output", "No output")
                context_parts.append(f"Step {dep_step_id}: {output[:200]}...")
        
        return "\n".join(context_parts) if context_parts else "Dependencies not yet completed."


# Convenience functions for common workflow patterns

async def create_code_analysis_workflow(file_path: str, workspace_root: str = ".") -> WorkflowResult:
    """
    Create a workflow for comprehensive code analysis.
    
    This workflow:
    1. Reads the file
    2. Analyzes code structure
    3. Runs linting and tests
    4. Provides improvement suggestions
    """
    workflow = MultiAgentWorkflow("code_analysis")
    
    workflow.add_coding_step(
        "read_file",
        f"Read and examine the file {file_path}. Provide a summary of its contents and purpose."
    ).add_coding_step(
        "analyze_structure", 
        f"Analyze the code structure, complexity, and maintainability of {file_path}.",
        depends_on=["read_file"]
    ).add_coding_step(
        "run_quality_checks",
        f"Run linting and tests for {file_path} and report any issues.",
        depends_on=["read_file"]
    ).add_coding_step(
        "suggest_improvements",
        "Based on the analysis and quality checks, suggest specific improvements.",
        depends_on=["analyze_structure", "run_quality_checks"]
    )
    
    return await workflow.execute(workspace_root)


async def create_git_workflow(commit_message: str, workspace_root: str = ".") -> WorkflowResult:
    """
    Create a workflow for git operations.
    
    This workflow:
    1. Checks git status
    2. Reviews changes
    3. Runs tests
    4. Commits if tests pass
    """
    workflow = MultiAgentWorkflow("git_workflow")
    
    workflow.add_coding_step(
        "check_status",
        "Check the current git status and list all modified files."
    ).add_coding_step(
        "review_changes",
        "Review the git diff for all modified files and summarize the changes.",
        depends_on=["check_status"]
    ).add_coding_step(
        "run_tests",
        "Run linting and tests to ensure code quality before committing.",
        depends_on=["check_status"]
    ).add_coding_step(
        "commit_changes",
        f"If tests pass, commit the changes with message: '{commit_message}'",
        depends_on=["review_changes", "run_tests"]
    )
    
    return await workflow.execute(workspace_root)


# Export main classes and functions
__all__ = [
    'MultiAgentWorkflow', 'WorkflowStep', 'WorkflowResult', 'WorkflowStatus', 'AgentType',
    'create_code_analysis_workflow', 'create_git_workflow'
]
