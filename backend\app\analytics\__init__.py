"""
📊 Usage Analytics & Insights System

This module provides comprehensive analytics and insights with:
- Real-time usage tracking and metrics
- Advanced user behavior analysis
- Performance insights and optimization recommendations
- Predictive analytics for resource planning
- Interactive dashboards and visualizations
- AI-powered insights and recommendations
"""

from .usage_tracker import (
    UsageTracker,
    UsageEvent,
    UsageMetrics,
    EventType,
    get_usage_tracker
)

from .analytics_engine import (
    AnalyticsEngine,
    AnalyticsReport,
    InsightType,
    RecommendationType,
    get_analytics_engine
)

from .performance_monitor import (
    PerformanceMonitor,
    PerformanceMetrics,
    PerformanceAlert,
    AlertSeverity,
    MetricType,
    get_performance_monitor
)

# Temporarily disabled - modules not yet implemented
# from .insights_generator import (
#     InsightsGenerator,
#     UserInsight,
#     SystemInsight,
#     PredictiveInsight,
#     get_insights_generator
# )

# from .dashboard_data import (
#     DashboardDataProvider,
#     DashboardWidget,
#     WidgetType,
#     ChartData,
#     get_dashboard_provider
# )

# from .behavioral_analyzer import (
#     BehavioralAnalyzer,
#     UserBehaviorPattern,
#     BehaviorType,
#     PatternStrength,
#     get_behavioral_analyzer
# )

__all__ = [
    # Usage Tracking
    "UsageTracker",
    "UsageEvent",
    "UsageMetrics",
    "EventType",
    "get_usage_tracker",

    # Analytics Engine
    "AnalyticsEngine",
    "AnalyticsReport",
    "InsightType",
    "RecommendationType",
    "get_analytics_engine",

    # Performance Monitoring
    "PerformanceMonitor",
    "PerformanceMetrics",
    "PerformanceAlert",
    "AlertSeverity",
    "MetricType",
    "get_performance_monitor",

    # Temporarily disabled - modules not yet implemented
    # # Insights Generation
    # "InsightsGenerator",
    # "UserInsight",
    # "SystemInsight",
    # "PredictiveInsight",
    # "get_insights_generator",

    # # Dashboard Data
    # "DashboardDataProvider",
    # "DashboardWidget",
    # "WidgetType",
    # "ChartData",
    # "get_dashboard_provider",

    # # Behavioral Analysis
    # "BehavioralAnalyzer",
    # "UserBehaviorPattern",
    # "BehaviorType",
    # "PatternStrength",
    # "get_behavioral_analyzer"
]
