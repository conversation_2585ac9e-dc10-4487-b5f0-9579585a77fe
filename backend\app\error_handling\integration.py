"""
Error Handling Integration Module

Integrates all error handling components and provides a unified interface
for the enhanced error recovery system.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Any, Callable, Dict, List, Optional, Union
from functools import wraps

# Import Pydantic AI exceptions with fallback for testing
try:
    from pydantic_ai import ModelRetry
    from pydantic_ai.exceptions import UnexpectedModelBehavior
except ImportError:
    # Fallback for testing or when pydantic_ai is not available
    class ModelRetry(Exception):
        def __init__(self, message: str):
            self.message = message
            super().__init__(message)

    class UnexpectedModelBehavior(Exception):
        pass

from .retry_manager import EnhancedRetryManager, RetryConfig, RetryResult
from .error_categorizer import ErrorCategorizer, ErrorContext
from .recovery_strategies import RecoveryStrategies, RecoveryResult, RecoveryStatus
from .user_notifications import UserNotificationManager, ErrorNotification

# Import ML Error Intelligence System
try:
    from ..autonomous_continue.error_intelligence import ErrorIntelligenceSystem
    from ..autonomous_continue.models import ErrorPattern as MLErrorPattern, IterationResult, ContextPerspective
    ML_INTELLIGENCE_AVAILABLE = True
except ImportError:
    ErrorIntelligenceSystem = None
    MLErrorPattern = None
    IterationResult = None
    ContextPerspective = None
    ML_INTELLIGENCE_AVAILABLE = False

logger = logging.getLogger(__name__)


class ErrorHandlingIntegration:
    """Unified error handling integration for the DeepNexus system with ML intelligence."""

    def __init__(
        self,
        retry_manager: EnhancedRetryManager,
        error_categorizer: ErrorCategorizer,
        recovery_strategies: RecoveryStrategies,
        notification_manager: UserNotificationManager,
        enable_ml_intelligence: bool = True
    ):
        self.retry_manager = retry_manager
        self.error_categorizer = error_categorizer
        self.recovery_strategies = recovery_strategies
        self.notification_manager = notification_manager

        # Initialize ML Error Intelligence System if available
        self.ml_intelligence = None
        self.ml_enabled = enable_ml_intelligence and ML_INTELLIGENCE_AVAILABLE

        if self.ml_enabled:
            try:
                self.ml_intelligence = ErrorIntelligenceSystem()
                logger.info("🧠 ML Error Intelligence System initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize ML Error Intelligence: {e}")
                self.ml_enabled = False

        # Integration statistics
        self.handled_errors = 0
        self.successful_recoveries = 0
        self.failed_recoveries = 0
        self.ml_patterns_learned = 0
    
    async def handle_error_with_recovery(
        self,
        error: Exception,
        operation: str = "",
        context: Optional[Dict[str, Any]] = None,
        original_function: Optional[Callable] = None,
        function_args: Optional[tuple] = None,
        function_kwargs: Optional[Dict[str, Any]] = None,
        retry_config: Optional[RetryConfig] = None,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None,
        show_technical_details: bool = False
    ) -> Dict[str, Any]:
        """
        Comprehensive error handling with recovery.
        
        This is the main entry point for error handling that:
        1. Categorizes the error
        2. Creates user notification
        3. Attempts recovery
        4. Provides detailed results
        """
        
        self.handled_errors += 1
        context = context or {}
        context.update({
            'operation': operation,
            'session_id': session_id,
            'user_id': user_id
        })
        
        # Step 1: Categorize the error
        error_context = self.error_categorizer.classify_error(error, context)

        # Step 1.5: ML Intelligence Analysis (if enabled)
        ml_pattern = None
        ml_suggestions = []
        if self.ml_enabled and self.ml_intelligence:
            try:
                # Create iteration result for ML analysis
                start_time = datetime.now()
                end_time = datetime.now()

                iteration_result = IterationResult(
                    iteration_id=f"error_{session_id}_{start_time.timestamp()}",
                    iteration_number=1,
                    task_description=operation,
                    success=False,
                    output="",
                    error_message=str(error),
                    start_time=start_time,
                    end_time=end_time,
                    duration=end_time - start_time,  # Required field
                    files_changed=context.get('files_changed', []),
                    perspective_used=ContextPerspective.DEBUGGING  # Required field - use DEBUGGING for errors
                )

                # Analyze error with ML system
                ml_pattern = await self.ml_intelligence.analyze_error(iteration_result)
                if ml_pattern:
                    # Get ML-based resolution suggestions
                    ml_suggestions = await self.ml_intelligence.suggest_resolution(ml_pattern)
                    logger.info(f"🧠 ML analysis found pattern: {ml_pattern.pattern_id} with {len(ml_suggestions)} suggestions")

            except Exception as ml_error:
                logger.warning(f"ML analysis failed: {ml_error}")

        # Step 2: Create initial error notification
        error_notification = self.notification_manager.create_error_notification(
            error_context=error_context,
            operation=operation,
            show_technical_details=show_technical_details
        )
        
        # Step 3: Attempt recovery if recommended
        recovery_result = None
        if error_context.retry_recommended:
            try:
                # Create recovery started notification
                recovery_notification = self.notification_manager.create_recovery_notification(
                    recovery_result=RecoveryResult(
                        action=self.recovery_strategies._select_recovery_action(error_context),
                        status=RecoveryStatus.IN_PROGRESS,
                        success=False,
                        message="Starting recovery process"
                    ),
                    operation=operation,
                    session_id=session_id
                )
                
                # Attempt recovery
                recovery_result = await self.recovery_strategies.apply_recovery(
                    error_context=error_context,
                    original_function=original_function,
                    function_args=function_args,
                    function_kwargs=function_kwargs
                )
                
                # Create recovery completion notification
                final_recovery_notification = self.notification_manager.create_recovery_notification(
                    recovery_result=recovery_result,
                    operation=operation,
                    session_id=session_id
                )
                
                # Update statistics
                if recovery_result.success:
                    self.successful_recoveries += 1

                    # ML Learning: Record successful recovery
                    if self.ml_enabled and ml_pattern:
                        try:
                            await self.ml_intelligence.learn_from_success(
                                ml_pattern.pattern_id,
                                f"{recovery_result.action.value}_recovery"
                            )
                            self.ml_patterns_learned += 1
                            logger.info(f"🧠 ML learned from successful recovery: {ml_pattern.pattern_id}")
                        except Exception as ml_error:
                            logger.warning(f"ML learning from success failed: {ml_error}")
                else:
                    self.failed_recoveries += 1

                    # ML Learning: Record failed recovery
                    if self.ml_enabled and ml_pattern:
                        try:
                            await self.ml_intelligence.learn_from_failure(
                                ml_pattern.pattern_id,
                                f"{recovery_result.action.value}_recovery"
                            )
                            logger.info(f"🧠 ML learned from failed recovery: {ml_pattern.pattern_id}")
                        except Exception as ml_error:
                            logger.warning(f"ML learning from failure failed: {ml_error}")

                # Dismiss the "in progress" notification
                self.notification_manager.dismiss_notification(recovery_notification.notification_id)
                
            except Exception as recovery_error:
                logger.error(f"Recovery process failed: {recovery_error}")
                recovery_result = RecoveryResult(
                    action=self.recovery_strategies._select_recovery_action(error_context),
                    status=RecoveryStatus.FAILED,
                    success=False,
                    message=f"Recovery process failed: {recovery_error}",
                    original_error=error
                )
                self.failed_recoveries += 1
        
        # Step 4: Compile comprehensive result
        result = {
            'error_handled': True,
            'error_category': error_context.category.value,
            'error_severity': error_context.severity.value,
            'recovery_attempted': recovery_result is not None,
            'recovery_successful': recovery_result.success if recovery_result else False,
            'user_notification': error_notification.to_dict(),
            'error_context': {
                'operation': operation,
                'timestamp': error_context.timestamp.isoformat(),
                'confidence': error_context.confidence,
                'suggested_actions': error_context.suggested_actions,
                'retry_recommended': error_context.retry_recommended
            },
            'ml_intelligence': {
                'enabled': self.ml_enabled,
                'pattern_detected': ml_pattern is not None,
                'pattern_id': ml_pattern.pattern_id if ml_pattern else None,
                'ml_suggestions': ml_suggestions,
                'pattern_confidence': ml_pattern.confidence_score if ml_pattern else None,
                'occurrence_count': ml_pattern.occurrence_count if ml_pattern else None
            }
        }
        
        # Add recovery details if available
        if recovery_result:
            result['recovery_details'] = {
                'action': recovery_result.action.value,
                'status': recovery_result.status.value,
                'message': recovery_result.message,
                'duration': recovery_result.duration.total_seconds() if recovery_result.duration else None,
                'recovered_result': recovery_result.recovered_result
            }
        
        # Add technical details if requested
        if show_technical_details:
            result['technical_details'] = {
                'original_error': {
                    'type': type(error).__name__,
                    'message': str(error),
                    'args': error.args if hasattr(error, 'args') else []
                },
                'error_classification': {
                    'matched_pattern': error_context.matched_pattern.name if error_context.matched_pattern else None,
                    'recoverability': error_context.recoverability.value,
                    'metadata': error_context.metadata
                }
            }
        
        logger.info(
            f"Error handling completed for {operation}: "
            f"category={error_context.category.value}, "
            f"recovery_attempted={recovery_result is not None}, "
            f"recovery_successful={recovery_result.success if recovery_result else False}"
        )
        
        return result
    
    async def execute_with_enhanced_error_handling(
        self,
        func: Callable,
        operation: str = "",
        retry_config: Optional[RetryConfig] = None,
        context: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None,
        show_technical_details: bool = False,
        *args,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute a function with comprehensive error handling and recovery.
        
        This method wraps function execution with full error handling capabilities.
        """
        
        try:
            # Execute the function
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            return {
                'success': True,
                'result': result,
                'error_handled': False
            }
            
        except Exception as error:
            # Handle the error with full recovery system
            error_handling_result = await self.handle_error_with_recovery(
                error=error,
                operation=operation,
                context=context,
                original_function=func,
                function_args=args,
                function_kwargs=kwargs,
                retry_config=retry_config,
                session_id=session_id,
                user_id=user_id,
                show_technical_details=show_technical_details
            )
            
            # Return combined result
            return {
                'success': error_handling_result.get('recovery_successful', False),
                'result': error_handling_result.get('recovery_details', {}).get('recovered_result'),
                'error_handled': True,
                'error_handling': error_handling_result
            }
    
    def create_error_handling_decorator(
        self,
        operation: str = "",
        retry_config: Optional[RetryConfig] = None,
        show_technical_details: bool = False
    ):
        """
        Create a decorator for automatic error handling.
        
        Usage:
        @error_integration.create_error_handling_decorator(
            operation="AI Model Execution",
            retry_config=RetryConfig(max_attempts=3)
        )
        async def my_ai_function():
            # Function implementation
            pass
        """
        
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # Extract context from kwargs if available
                context = kwargs.pop('_error_context', {})
                session_id = kwargs.pop('_session_id', None)
                user_id = kwargs.pop('_user_id', None)
                
                return await self.execute_with_enhanced_error_handling(
                    func=func,
                    operation=operation or func.__name__,
                    retry_config=retry_config,
                    context=context,
                    session_id=session_id,
                    user_id=user_id,
                    show_technical_details=show_technical_details,
                    *args,
                    **kwargs
                )
            
            return wrapper
        return decorator
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health from error handling perspective."""
        
        # Calculate error rates
        total_errors = self.handled_errors
        recovery_rate = (self.successful_recoveries / total_errors) if total_errors > 0 else 0
        
        # Get component statistics
        retry_stats = self.retry_manager.retry_stats
        categorizer_stats = self.error_categorizer.get_statistics()
        recovery_stats = self.recovery_strategies.get_recovery_statistics()
        notification_stats = self.notification_manager.get_statistics()
        
        # Determine overall health status
        if recovery_rate >= 0.8:
            health_status = "excellent"
        elif recovery_rate >= 0.6:
            health_status = "good"
        elif recovery_rate >= 0.4:
            health_status = "fair"
        else:
            health_status = "poor"
        
        # Get ML intelligence statistics
        ml_stats = {}
        if self.ml_enabled and self.ml_intelligence:
            try:
                ml_stats = {
                    'enabled': True,
                    'patterns_learned': self.ml_patterns_learned,
                    'total_patterns': len(self.ml_intelligence.error_patterns),
                    'error_history_size': len(self.ml_intelligence.error_history),
                    'top_error_types': self._get_top_ml_error_types()
                }
            except Exception as e:
                ml_stats = {'enabled': True, 'error': str(e)}
        else:
            ml_stats = {'enabled': False, 'reason': 'ML Intelligence not available or disabled'}

        return {
            'overall_health': health_status,
            'error_handling_stats': {
                'total_errors_handled': total_errors,
                'successful_recoveries': self.successful_recoveries,
                'failed_recoveries': self.failed_recoveries,
                'recovery_rate': recovery_rate
            },
            'component_health': {
                'retry_manager': {
                    'active_circuit_breakers': len(self.retry_manager.circuit_breakers),
                    'retry_contexts': len(retry_stats)
                },
                'error_categorizer': categorizer_stats,
                'recovery_strategies': recovery_stats,
                'notification_manager': notification_stats,
                'ml_intelligence': ml_stats
            },
            'recommendations': self._get_health_recommendations(recovery_rate, total_errors)
        }
    
    def _get_health_recommendations(self, recovery_rate: float, total_errors: int) -> List[str]:
        """Get health recommendations based on current metrics."""
        recommendations = []
        
        if recovery_rate < 0.5:
            recommendations.append("Consider reviewing recovery strategies - low success rate detected")
        
        if total_errors > 100:
            recommendations.append("High error volume detected - consider investigating root causes")
        
        # Check for specific patterns in error history
        recent_errors = self.error_categorizer.classification_history[-20:]
        if len(recent_errors) >= 10:
            # Check for repeated error categories
            category_counts = {}
            for error_context in recent_errors:
                category = error_context.category.value
                category_counts[category] = category_counts.get(category, 0) + 1
            
            # If any category represents >50% of recent errors
            for category, count in category_counts.items():
                if count / len(recent_errors) > 0.5:
                    recommendations.append(f"High frequency of {category} errors - consider targeted improvements")
        
        if not recommendations:
            recommendations.append("System error handling is performing well")
        
        return recommendations

    def _get_top_ml_error_types(self) -> List[Dict[str, Any]]:
        """Get top error types from ML intelligence system."""
        if not self.ml_enabled or not self.ml_intelligence:
            return []

        try:
            # Count error types from ML patterns
            error_type_counts = {}
            for pattern in self.ml_intelligence.error_patterns.values():
                error_type = pattern.error_type
                if error_type not in error_type_counts:
                    error_type_counts[error_type] = {
                        'count': 0,
                        'total_occurrences': 0,
                        'avg_confidence': 0.0
                    }

                error_type_counts[error_type]['count'] += 1
                error_type_counts[error_type]['total_occurrences'] += pattern.occurrence_count
                error_type_counts[error_type]['avg_confidence'] += pattern.confidence_score

            # Calculate averages and sort by total occurrences
            for error_type, stats in error_type_counts.items():
                stats['avg_confidence'] = stats['avg_confidence'] / stats['count']

            # Sort by total occurrences and return top 5
            sorted_types = sorted(
                error_type_counts.items(),
                key=lambda x: x[1]['total_occurrences'],
                reverse=True
            )

            return [
                {
                    'error_type': error_type,
                    'pattern_count': stats['count'],
                    'total_occurrences': stats['total_occurrences'],
                    'avg_confidence': round(stats['avg_confidence'], 2)
                }
                for error_type, stats in sorted_types[:5]
            ]

        except Exception as e:
            logger.warning(f"Failed to get ML error types: {e}")
            return []
    
    async def test_error_handling_system(self) -> Dict[str, Any]:
        """Test the error handling system with various error scenarios."""
        
        test_results = {}
        
        # Test 1: ModelRetry handling
        try:
            raise ModelRetry("Test model retry message")
        except Exception as e:
            result = await self.handle_error_with_recovery(
                error=e,
                operation="test_model_retry",
                show_technical_details=True
            )
            test_results['model_retry_test'] = {
                'passed': result['error_category'] == 'model_retry',
                'details': result
            }
        
        # Test 2: Network error handling
        try:
            raise ConnectionError("Test connection error")
        except Exception as e:
            result = await self.handle_error_with_recovery(
                error=e,
                operation="test_connection_error",
                show_technical_details=True
            )
            test_results['connection_error_test'] = {
                'passed': result['error_category'] == 'network_connection',
                'details': result
            }
        
        # Test 3: Unknown error handling
        try:
            raise ValueError("Test unknown error")
        except Exception as e:
            result = await self.handle_error_with_recovery(
                error=e,
                operation="test_unknown_error",
                show_technical_details=True
            )
            test_results['unknown_error_test'] = {
                'passed': result['error_handled'] == True,
                'details': result
            }
        
        # Test 4: ML Intelligence system (if enabled)
        if self.ml_enabled:
            try:
                # Test ML pattern detection
                test_error = ValueError("Test ML pattern detection error")
                result = await self.handle_error_with_recovery(
                    error=test_error,
                    operation="test_ml_intelligence",
                    context={'files_changed': ['test_file.py']},
                    show_technical_details=True
                )
                test_results['ml_intelligence_test'] = {
                    'passed': result['ml_intelligence']['enabled'] and result['ml_intelligence']['pattern_detected'],
                    'details': result['ml_intelligence']
                }
            except Exception as e:
                test_results['ml_intelligence_test'] = {
                    'passed': False,
                    'error': str(e)
                }
        else:
            test_results['ml_intelligence_test'] = {
                'passed': True,  # Pass if ML is intentionally disabled
                'details': 'ML Intelligence disabled or not available'
            }

        # Calculate overall test success
        passed_tests = sum(1 for test in test_results.values() if test['passed'])
        total_tests = len(test_results)

        return {
            'test_summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'success_rate': passed_tests / total_tests if total_tests > 0 else 0,
                'ml_intelligence_enabled': self.ml_enabled
            },
            'test_results': test_results,
            'system_ready': passed_tests == total_tests
        }
