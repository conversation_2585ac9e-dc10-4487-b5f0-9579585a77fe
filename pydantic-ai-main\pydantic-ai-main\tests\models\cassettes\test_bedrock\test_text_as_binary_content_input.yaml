interactions:
- request:
    body: '{"messages": [{"role": "user", "content": [{"text": "What is the main content on this document?"}, {"document":
      {"name": "Document 1", "format": "txt", "source": {"bytes": "VGhpcyBpcyBhIHRlc3QgZG9jdW1lbnQu"}}}]}], "system": [{"text":
      "You are a helpful chatbot."}], "inferenceConfig": {}}'
    headers:
      amz-sdk-invocation-id:
      - !!binary |
        ZDI5MGRkY2ItMWRjZi00YzljLWExNjUtZjhlNmViYjc3YjFi
      amz-sdk-request:
      - !!binary |
        YXR0ZW1wdD0x
      content-length:
      - '288'
      content-type:
      - !!binary |
        YXBwbGljYXRpb24vanNvbg==
    method: POST
    uri: https://bedrock-runtime.us-east-1.amazonaws.com/model/us.amazon.nova-pro-v1%3A0/converse
  response:
    headers:
      connection:
      - keep-alive
      content-length:
      - '1106'
      content-type:
      - application/json
    parsed_body:
      metrics:
        latencyMs: 2174
      output:
        message:
          content:
          - text: |-
              The document you're referring to appears to be a test document, which means its primary purpose is likely to serve as an example or a placeholder rather than containing substantive content. Test documents are commonly used for various purposes such as:

              1. **Software Testing**: To verify that a system can correctly handle, display, or process documents.
              2. **Design Mockups**: To illustrate how a document might look in a particular format or style.
              3. **Training Materials**: To provide examples for instructional purposes.
              4. **Placeholders**: To fill space in a system or application where real content will eventually be placed.

              Since this is a test document, it probably doesn't contain any meaningful or specific information beyond what is necessary to serve its testing purpose. If you have specific questions about the format, structure, or any particular element within the document, feel free to ask!
          role: assistant
      stopReason: end_turn
      usage:
        inputTokens: 21
        outputTokens: 175
        totalTokens: 196
    status:
      code: 200
      message: OK
version: 1
