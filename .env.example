# ═══════════════════════════════════════════════════════════════
# DEEPNEXUS AI CODER AGENT - PYDANTIC AI CONFIGURATION
# ═══════════════════════════════════════════════════════════════

# ───────────────────────────────────────────
# OPENROUTER API CONFIGURATION (REQUIRED)
# ───────────────────────────────────────────
OPENROUTER_API_KEY=your_openrouter_api_key_here
# Get your API key from: https://openrouter.ai/keys
# Required for DeepSeek R1 and Google Gemini models

# ───────────────────────────────────────────
# LOGFIRE MONITORING (OPTIONAL)
# ───────────────────────────────────────────
LOGFIRE_TOKEN=your_logfire_token_here
# Get your token from: https://logfire.pydantic.dev/
# Optional but recommended for monitoring and observability

# ───────────────────────────────────────────
# AI MODEL CONFIGURATION
# ───────────────────────────────────────────
CODING_MODEL=deepseek/deepseek-r1-0528:free
VISION_MODEL=google/gemini-2.0-flash-exp:free
EMBEDDING_MODEL=bge-m3
# Models used by the Pydantic AI agents

# ───────────────────────────────────────────
# OLLAMA CONFIGURATION (BGE-M3 EMBEDDINGS)
# ───────────────────────────────────────────
OLLAMA_HOST=http://localhost:11434
# Ollama host for BGE-M3 embedding model

# ───────────────────────────────────────────
# QDRANT CONFIGURATION (SELF-HOSTED)
# ───────────────────────────────────────────
QDRANT_URL=http://qdrant:6333
# QDRANT_API_KEY=  # Optional for self-hosted Qdrant
# Self-hosted Qdrant vector database for code embeddings

# ───────────────────────────────────────────
# APPLICATION CONFIGURATION
# ───────────────────────────────────────────
WORKSPACE_ROOT=/app/workspace
BACKEND_WORKERS=4
LOG_LEVEL=INFO

# ───────────────────────────────────────────
# FRONTEND CONFIGURATION
# ───────────────────────────────────────────
VITE_BACKEND_URL=http://localhost:8000
