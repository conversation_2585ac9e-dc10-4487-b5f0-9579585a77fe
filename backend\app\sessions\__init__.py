"""
🚀 Session Management & History System

This module provides comprehensive session management with:
- Persistent user sessions across restarts
- Chat history with search and filtering
- Session recovery after disconnection
- Export conversations and results
- Multiple concurrent sessions support
- Full integration with workspace/project management

Key Components:
- SessionManager: Core session management with project integration
- ConversationHistory: Advanced chat history with search capabilities
- SessionRecovery: Seamless session recovery mechanisms
- ExportManager: Export conversations and results in multiple formats
"""

from .session_manager import (
    SessionManager,
    Session,
    SessionStatus,
    SessionConfig,
    get_session_manager
)

from .conversation_history import (
    ConversationHistory,
    Message,
    MessageType,
    MessageRole,
    ConversationFilter,
    SearchResult
)

from .session_recovery import (
    SessionRecovery,
    RecoveryStatus,
    RecoveryResult,
    get_session_recovery
)

from .export_manager import (
    ExportManager,
    ExportFormat,
    ExportResult,
    ExportConfig,
    get_export_manager
)

__all__ = [
    # Session Management
    "SessionManager",
    "Session", 
    "SessionStatus",
    "SessionConfig",
    "get_session_manager",
    
    # Conversation History
    "ConversationHistory",
    "Message",
    "MessageType", 
    "MessageRole",
    "ConversationFilter",
    "SearchResult",
    
    # Session Recovery
    "SessionRecovery",
    "RecoveryStatus",
    "RecoveryResult", 
    "get_session_recovery",
    
    # Export Management
    "ExportManager",
    "ExportFormat",
    "ExportResult",
    "ExportConfig",
    "get_export_manager"
]
