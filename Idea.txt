Below is the **completely revised and expanded project outline**, incorporating (and critically evaluating) <PERSON>’s suggestions. Wherever <PERSON>’s feedback prompted a change, you’ll see revised content annotated with “» UPDATE” to highlight what was added or modified. All code‐snippets, configuration files, and explanations have been updated to:

* Emphasize **incremental development**, **robust error handling**, and **security**
* Make **configuration** and **limits** (tokens, retries, thresholds) explicit and customizable
* Ensure **correct working‐directory usage**, avoid unsafe `shell=True` where possible, and sanitize inputs
* Add new fields (e.g. `updated_at`) to session metadata and improve Qdrant payload summaries
* Introduce **Socket.IO** on the frontend for more reliable WebSocket reconnects
* Switch to **Vite** (instead of CRA) for faster frontend builds
* Rename `.env` → `.env.example` (and ignore the real `.env` in git)
* Provide clear notes about **dependency installation** and **Docker** setup for Puppeteer/linters
* Outline **fallbacks** and **retries** for invalid LLM JSON, tool failures, and non-converging loops
* Include an explicit **“Incremental Development”** section at the top

---

## Table of Contents

1. [High-Level Architecture & Incremental Development](#1-high-level-architecture--incremental-development)
2. [Technology Stack (with Updates)](#2-technology-stack-with-updates)
3. [Environment Configuration](#3-environment-configuration)
4. [Directory Layout & File Descriptions](#4-directory-layout--file-descriptions)
5. [Docker & Docker Compose](#5-docker--docker-compose)
6. [Backend Modules (Detailed, with Updates)](#6-backend-modules-detailed-with-updates)

   1. [Configuration (`config.py`)](#61-configuration-configpy)
   2. [Sessions & Metadata (`sessions.py`)](#62-sessions--metadata-sessionspy)
   3. [Qdrant Client (`qdrant_client.py`)](#63-qdrant-client-qdrant_clientpy)
   4. [LLM Client (`llm_client.py`)](#64-llm-client-llm_clientpy)
   5. [Context Management (`context_manager.py`)](#65-context-management-context_managerpy)
   6. [Memory Retrieval (`memory.py`)](#66-memory-retrieval-memorypy)
   7. [Tool Definitions (`tools.py`)](#67-tool-definitions-toolspy)
   8. [Utility Functions (`utils/`)](#68-utility-functions-utils)
   9. [Vision QA (`vision.py`)](#69-vision-visionpy)
   10. [Task Runner (`task_runner.py`)](#610-task-runner-task_runnerpy)
7. [Frontend Modules (Updated for Vite & Socket.IO)](#7-frontend-modules-updated-for-vite--socketio)

   1. [`vite.config.js`](#71-viteconfigjs)
   2. [`package.json`](#72-packagejson)
   3. [Component Overviews & Key Snippets](#73-component-overviews--key-snippets)
8. [Putting It All Together: Running & Testing](#8-putting-it-all-together-running--testing)
9. [Incremental Development Roadmap](#9-incremental-development-roadmap)
10. [Security & Maintenance Notes](#10-security--maintenance-notes)
11. [Appendix: Example `.gitignore`](#11-appendix-example-gitignore)

---

## 1. High-Level Architecture & Incremental Development

<div markdown="1">

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              User & Frontend                                   │
│   ┌───────────────────────────────────────────────────────────────────────────┐ │
│   │ React + Vite SPA (web UI)                                                 │ │
│   │  • TaskInput: submit high-level instruction                                │ │
│   │  • LogViewer: real-time logs via Socket.IO                                 │ │
│   │  • FileExplorer: browse workspace tree; view file contents                 │ │
│   │  • TerminalEmulator: show manual `run_command` outputs                     │ │
│   └───────────────────────────────────────────────────────────────────────────┘ │
│                                       │                                         │
│                                       ▼                                         │
│   ┌───────────────────────────────────────────────────────────────────────────┐ │
│   │                      Backend (FastAPI + Socket.IO)                        │ │
│   │  • Receives tasks from frontend                                           │ │
│   │  • Manages one “Session” per task (state, chat_summary, vector_ids…)      │ │
│   │  • Orchestrates: Qdrant lookups, LLM calls (OpenRouter), tools execution   │ │
│   │  • Implements iterative loops: lint/tests → vision → corrections           │ │
│   │  • Streams logs & terminal output via Socket.IO                            │ │
│   │  • Validates inputs (repo_url, file paths), sanitizes commands             │ │
│   │  • Commits & pushes on success, updates Qdrant index                       │ │
│   │  • Stores session metadata in SQLite (with `created_at` & `updated_at`)    │ │
│   └───────────────────────────────────────────────────────────────────────────┘ │
│                                       │                                         │
│                                       ▼                                         │
│   ┌───────────────────────────────────────────────────────────────────────────┐ │
│   │                         Qdrant Cloud (free tier)                         │ │
│   │  • Holds “code_context” collection (1536-dim vectors, COSINE distance)     │ │
│   │  • Stores payload: {file_path, commit_hash, summary, timestamp}           │ │
│   │  • On search: returns top-K similar vectors (context candidates)           │ │
│   └───────────────────────────────────────────────────────────────────────────┘ │
│                                       │                                         │
│                                       │                                         │
│                      ┌────────────────┴──────────────┐                          │
│                      │  (Optional)                    │                          │
│                      │  Vision Microservice (Docker)   │                          │
│                      │  – exposes an OpenRouter “vision” endpoint                  │
│                      └────────────────────────────────┘                          │
│                                                                                 │
│   ┌───────────────────────────────────────────────────────────────────────────┐ │
│   │                         Local File System / Docker                         │ │
│   │  • `/app/workspace/{session_id}`: Git clone/pull → read/write files         │ │
│   │  • Linters, test runners, Puppeteer (for screenshots) run in this space    │ │
│   └───────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 1.1 Incremental Development (“Build & Test in Layers”)

> **» UPDATE:** We now strongly recommend **building and verifying each component one at a time**, because the full system is complex. Follow these steps:

1. **Basic “Hello, World” API**

   * Start a minimal FastAPI app.
   * Expose a `/health` endpoint.
   * Verify you can run it (no AI yet).

2. **Simple React + Vite Frontend**

   * Scaffold a Vite React project.
   * Add a text input + “Submit” button.
   * Make it send a dummy POST to `http://localhost:8000/api/test` and display the response.

3. **Session Management & SQLite**

   * Implement `sessions.py`: create a session entry with `session_id`, `created_at`, `updated_at`, empty `chat_summary`, etc.
   * Expose a `/api/create-session` endpoint that returns `session_id`.
   * Test that multiple sessions can be created, and data persists.

4. **Qdrant Connection & Embeddings**

   * Write `qdrant_client.py`, connect to your free Qdrant Cloud.
   * Write a small script to embed a sample string (via OpenRouter) and upsert/search.
   * Verify that you can store/retrieve “hello” embeddings.

5. **LLM Client (OpenRouter)**

   * Implement `llm_client.py` with basic `chat_completion` and `embed_text`.
   * Test with a simple prompt (“What is 2+2?”).
   * Add error handling (HTTP status checks, JSON parse errors).

6. **Context Manager + Summarization**

   * Implement `context_manager.py` to count tokens, summarize when needed (via OpenRouter).
   * Write unit tests that feed in an artificially large string to ensure `summarize_text` is called.

7. **Tool Framework (Stubbed)**

   * Create `tools.py` with stub functions (`list_dir`, `read_file`, etc.) that simply log their invocation.
   * Build a minimal “dummy JSON” return parser in `task_runner.py`: feed a hard-coded JSON saying “tool: list\_dir, path: ‘.’” → see that the stub runs.

8. **Git Utilities & File I/O**

   * Implement `git_utils.py` and `file_utils.py`.
   * Test by cloning a known public repo (in a Docker container), listing files, reading a file.

9. **Lint & Test Utilities**

   * Install `flake8`, `pytest`, `eslint` inside your backend image.
   * Confirm that running `lint_and_test` on a known repo yields expected results (either “ok” or detailed errors).

10. **Task Runner (Full Loop Without Vision)**

    * Wire up `task_runner.py` to:

      1. Clone user‐provided repo.
      2. Retrieve “context” from Qdrant (initially empty).
      3. Build a prompt with system + “Use tool: read\_file…exit()”.
      4. Call LLM, parse JSON → call stubs.
      5. Run `lint_and_test()`. If errors, loop back with error prompt.
      6. Commit & push (to a test repo you control).
      7. Embed changed files into Qdrant.
    * Validate end‐to‐end without vision or front end.

11. **Vision QA Integration**

    * Add `screenshot_utils.py` + `vision.py`.
    * Test capturing a screenshot of a static HTML file.
    * Call `vision_analyze()` with a prompt like “List any UI issues.”
    * Verify you get a JSON response under various prompts.

12. **Frontend Integration (Socket.IO + React)**

    * Replace dummy endpoints with the real `/api/new-task`.
    * Connect Socket.IO to stream logs.
    * Render FileExplorer (fetching `/api/file-tree/{session_id}`).
    * Render TerminalEmulator (subscribing to “terminal\_output” events).

13. **End-to-End Manual Test**

    * Create a small public repo that has a simple React project or Python project.
    * Use the UI to request “Add a HelloWorld component.”
    * Watch logs, verify AI loops, check final commit, browse updated code, and Qdrant index.

14. **Stress & Concurrency**

    * Launch two or three simultaneous sessions, ensure SQLite performance is acceptable.
    * Test rapidly triggering tasks to confirm no race conditions.

15. **Polish & Hardening**

    * Add retries for JSON parsing (if the LLM returns invalid JSON).
    * Sanitize all user inputs (repo URLs, file paths).
    * Add timeouts to Puppeteer (prevent stuck processes).
    * Lock down CORS for production.
    * Review Dockerfile base‐image updates, pin versions as needed.

---

## 2. Technology Stack (with Updates)

| Component Layer           | Responsibility                                                    | Technology / Library (Free/Tier)                                                                    | Notes / «UPDATE» Highlights                                                                                                                                                                                                                                            |
| ------------------------- | ----------------------------------------------------------------- | --------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Frontend**              |                                                                   |                                                                                                     |                                                                                                                                                                                                                                                                        |
| • UI Framework            | Build a fast, lightweight SPA                                     | **React** (via **Vite**)                                                                            | • **Vite** ‒ faster dev builds than CRA (Gemini suggested).<br>• Keep state management minimal; consider **Zustand** or **React Context** as complexity grows.                                                                                                         |
| • Real-Time Logs          | Stream logs & terminal output                                     | **Socket.IO Client**                                                                                | • Simpler reconnection handling vs. raw WebSocket. <br>• Log events: `{ event: 'log', session_id, message }`.                                                                                                                                                          |
| • Directory Tree / Editor | Browse and view files, optionally edit                            | Plain React components + a code viewer (e.g. `react-syntax-highlighter`)                            | • Nodes: `FileExplorer.jsx`, `TaskInput.jsx`, `LogViewer.jsx`, `TerminalEmulator.jsx`.                                                                                                                                                                                 |
| **Backend**               |                                                                   |                                                                                                     |                                                                                                                                                                                                                                                                        |
| • Web Framework           | Serve REST & WebSocket                                            | **FastAPI** + **Uvicorn** + **python-socketio**                                                     | • **python-socketio** for WebSocket event broadcasting. <br>• Use `asyncio.run_coroutine_threadsafe` to emit logs from sync code.                                                                                                                                      |
| • Configuration           | Manage environment variables & settings                           | **python-dotenv** + **Pydantic**                                                                    | • All models (names, token limits, embedding dims) are configurable in `Settings`.                                                                                                                                                                                     |
| • Session Store           | Persist session metadata                                          | **SQLite** (via Python `sqlite3`)                                                                   | • Table `sessions`: add fields `created_at` (REAL), `updated_at` (REAL), `chat_summary` (TEXT), `last_commit` (TEXT), `qdrant_vector_ids` (TEXT JSON). <br>• Add proper error handling & concurrency safeguards (e.g. use `check_same_thread=False` if using threads). |
| • Vector DB               | Code indexing & semantic recall                                   | **Qdrant Cloud** (Starter, free tier)                                                               | • **» UPDATE:** Fetch embedding dimension (1536) from `Settings`. <br>• Add try/except for client errors. <br>• Upsert payload to include a more nuanced summary (e.g. first 1 KB + filename docstring).                                                               |
| • LLM / AI                | Code generation, embeddings, summarization, vision                | **OpenRouter** (“deepseek-r1-0528”, “bge-small-en-v1.5”, “claude-2.1”, “claude-instant-1.3-vision”) | • Verify model names on OpenRouter (Gemini recommended double-check).<br>• Make `max_tokens`, `temperature`, `top_p` configurable. <br>• Handle rate limits/costs by catching 429 errors and retrying after backoff.                                                   |
| • Git Integration         | Clone, pull, commit, push                                         | **Git CLI** (inside Docker)                                                                         | • **» UPDATE:** Avoid `shell=True` when constructing commands; use Python lists or sanitize inputs. <br>• Validate `repo_url` with a whitelist/regex to prevent injection.                                                                                             |
| • File I/O / Tools        | Read/write files; run linters/tests; take screenshots; manage cwd | Python’s `os` / `pathlib` / `subprocess`, **Pyppeteer**, **eslint**, **flake8**, **pytest**         | • **» UPDATE:** Ensure Docker image includes Node, npm, Puppeteer dependencies, linters/tests for both JS & Python. <br>• `run_command` must sanitize arguments; consider timeouts. <br>• Close Puppeteer browser properly to avoid resource leaks.                    |
| • Summarization / Memory  | Compact chat history when tokens exceed limit                     | **OpenRouter** summarization model (e.g. `claude-2.1`)                                              | • **» UPDATE:** After summarization, re-check token count; if still over limit, re-summarize or warn the user.                                                                                                                                                         |
| • Vision QA               | Analyze screenshots for UI issues                                 | **Headless Chrome (Pyppeteer)** + OpenRouter’s vision model                                         | • **» UPDATE:** Dynamically determine the local URL/port (e.g. 3005) instead of hardcoding. <br>• Run the target application in an isolated environment (Docker child process) to avoid collisions. <br>• Provide a fallback if vision service fails (skip QA).        |
| • Logging                 | Centralized, real-time streaming                                  | Python `logging` + **python-socketio**                                                              | • **» UPDATE:** Every tool invocation, LLM call, commit/push, and error must call `emit_log(session_id, message)` promptly. <br>• Use `asyncio.run_coroutine_threadsafe` to send events from background threads.                                                       |
| **Containerization**      |                                                                   |                                                                                                     |                                                                                                                                                                                                                                                                        |
| • Orchestration           | Define multi-service setup                                        | **Docker** + **Docker Compose**                                                                     | • Add `container_name` to ease debugging. <br>• Pin base images & periodically update.                                                                                                                                                                                 |
| • Runtime Isolation       | Tools (linters, Puppeteer, Git) run in backend container          | Single `backend` container with Node, Python, Git, Puppeteer deps                                   | • **» UPDATE:** In `backend/Dockerfile`, install `nodejs` (LTS), `npm`, and Puppeteer system libs.                                                                                                                                                                     |
| **(Optional) Extras**     |                                                                   |                                                                                                     |                                                                                                                                                                                                                                                                        |
| • Database (Optional)     | Store structured logs, metrics, past conversation transcripts     | **Supabase** (Postgres, free tier)                                                                  | • For a larger scale, move from SQLite → Supabase/Postgres.                                                                                                                                                                                                            |
| • CI/CD                   | Automated testing & deployment                                    | **GitHub Actions**                                                                                  | • At minimum, test that `docker-compose up --build` succeeds and the health endpoints respond.                                                                                                                                                                         |

</div>

---

## 3. Environment Configuration

1. **`.env.example`** (Git-tracked)

   ```dotenv
   # ───────────────────────────────────────────
   # OPENROUTER CONFIGURATION
   # ───────────────────────────────────────────
   OPENROUTER_API_KEY=your_openrouter_api_key_here
   # e.g. “sk-xxxx…” from OpenRouter dashboard

   # ───────────────────────────────────────────
   # QDRANT CONFIGURATION (CLOUD)
   # ───────────────────────────────────────────
   QDRANT_URL=https://<your_project>.qdrant.cloud
   QDRANT_API_KEY=your_qdrant_api_key_here
   # If you self-host Qdrant (Docker), comment out QDRANT_URL & QDRANT_API_KEY
   # and rely on the “qdrant” service in docker-compose.yml
   ```

2. **`.env`** (Git-ignored; rename from `.env.example`)

   * After you copy `.env.example` to `.env`, fill in your real keys.
   * Ensure `.gitignore` contains:

     ```
     .env
     backend/data/*
     qdrant_storage/*
     node_modules/
     dist/
     ```

3. **Key Configuration Notes**

   * **Model Names & Defaults** (in `config.py`):

     ```python
     code_model: str = "deepseek-r1-0528"
     embedding_model: str = "bge-small-en-v1.5"
     summarization_model: str = "claude-2.1"
     vision_model: str = "claude-instant-1.3-vision"
     ```

     » **UPDATE:** Double-check with OpenRouter which model identifiers are currently valid. Have a fallback or allow easy override via `.env`.

   * **Token Limits:**

     ```python
     max_token_limit: int = 164000     # LLM context limit
     summarization_trigger: float = 0.9  # Trigger summarization at 90% of limit
     max_output_tokens: int = 64000     # LLM output cap
     ```

     » **UPDATE:** Expose these as environment variables if you anticipate tuning.

   * **Embedding Dimension:**

     ```python
     embedding_dim: int = 1536  # for “bge-small-en-v1.5”
     ```

     » **UPDATE:** Use this to validate vector lengths before upserting into Qdrant.

   * **SQLite Path:**

     ```python
     sqlite_db_path: str = "data/agent_memory.db"
     ```

     » **UPDATE:** Consider `check_same_thread=False` if you use threads for WebSocket.

   * **Workspace Root:**

     ```python
     workspace_root: str = "/app/workspace"
     ```

     » **UPDATE:** The backend Dockerfile creates `/app/workspace` and mounts it from host to persist data.

---

## 4. Directory Layout & File Descriptions

Below is the **complete folder tree** and a one-line description for each file. Wherever a file was updated with Gemini’s feedback, you’ll see a “» UPDATE” marker. The “AI: …” comments indicate places where, in your actual development, you’d hand these stubs to the AI to implement.

```
ai-coder-app/
├── docker-compose.yml                     # Define services: frontend, backend, (optional) qdrant
├── README.md                              # How to get started, step-by-step instructions
├── .env.example                           # Git-tracked, template for environment variables
├── .gitignore                             # Ignore .env, data directories, node_modules, etc.

├── frontend/                              # React + Vite frontend
│   ├── Dockerfile                         # Build React app, serve via nginx
│   ├── package.json                       # Dependencies: React, Vite, Socket.IO, Tailwind
│   ├── vite.config.js                     # Vite configuration (dev server, proxies)
│   ├── public/
│   │   └── index.html
│   ├── src/
│   │   ├── main.jsx                       # Entrypoint (ReactDOM.render)
│   │   ├── App.jsx                        # Layout: TaskInput, LogViewer, FileExplorer, Terminal
│   │   ├── index.css                      # Tailwind imports
│   │   ├── components/
│   │   │   ├── TaskInput.jsx              # “New Task” form
│   │   │   ├── LogViewer.jsx              # Scrollable area for logs
│   │   │   ├── FileExplorer.jsx           # Render directory tree; let user click to view file
│   │   │   └── TerminalEmulator.jsx       # Displays manual `run_command` outputs
│   │   └── utils/
│   │       └── socket.js                  # Initialize & export Socket.IO client
│   └── tailwind.config.js                 # Tailwind CSS config

├── backend/                               # FastAPI + Socket.IO + agent logic
│   ├── Dockerfile                         # Python + Node + Git + Puppeteer libs
│   ├── requirements.txt                   # Python dependencies (FastAPI, qdrant-client, etc.)
│   ├── config.py                          # » UPDATE: All settings (models, token limits, dims)
│   ├── sessions.py                        # » UPDATE: SQLite schema includes `updated_at`
│   ├── qdrant_client.py                   # » UPDATE: Embedding dim from Settings, error handling
│   ├── main.py                            # FastAPI app, Socket.IO integration, endpoints
│   ├── llm_client.py                      # » MOVED: directly under backend/, wrapper for OpenRouter
│   ├── utils/                             # General utility modules
│   │   ├── git_utils.py                   # » UPDATE: Avoid shell=True; robust error checks
│   │   ├── file_utils.py                  # Basic read/write, ensure safe paths
│   │   ├── terminal_utils.py              # run_subprocess with cwd, safe args
│   │   ├── lint_test_utils.py             # » UPDATE: Allow custom lint/test cmds
│   │   └── screenshot_utils.py            # » UPDATE: Close Puppeteer properly
│   └── app/                               # Agent modules
│       ├── agent/                         # Core agent logic
│       │   ├── tools.py                   # » UPDATE: All tools return consistent JSON; correct cwd
│       │   ├── context_manager.py         # » UPDATE: Post-summarization token check
│       │   ├── memory.py                  # » UPDATE: Use session-specific cwd for git commands
│       │   ├── summarizer.py              # Thin wrapper (delegates to llm_client)
│       │   ├── vision.py                  # » UPDATE: Flexible prompt, dynamic URL for screenshots
│       │   └── task_runner.py             # » MAJOR UPDATE: WebSocket integration, error feedback loops
│       └── models/                        # (Optional) Pydantic models if needed
│
└── README.md                              # (Top-level guide on building & running)

```

> **» UPDATE Highlights (in summary):**
>
> * **Sessions (`sessions.py`)**: Added `updated_at`, track concurrency.
> * **Config (`config.py`)**: Exposed all thresholds, dims, model names as settings.
> * **Qdrant Client**: Validate vector lengths, catch Qdrant errors.
> * **LLM Client**: Parametrize `max_tokens`, `temperature`; catch HTTP 429 / 5xx.
> * **Context Manager**: After summarization, re-check token count; adjust summarization style if too large.
> * **Memory (`memory.py`)**: Ensure `git` calls use session’s workspace as cwd; catch errors if file not found.
> * **Tools (`tools.py`)**: All tools run with explicit `cwd` (no implicit global cwd). Return consistent `{"status","output"}`.
> * **Git Utils**: Use `subprocess.run([...], shell=False)`, sanitize inputs.
> * **Lint/Test Utils**: Let project supply custom commands via a config file (e.g. `.aiagent.json`).
> * **Screenshot Utils**: Properly `await browser.close()` in `finally` block to avoid zombie processes.
> * **Vision (`vision.py`)**: Dynamically select port based on a free port finder; allow user override.
> * **Task Runner (`task_runner.py`)**: Use `emit_log` everywhere, feed tool errors back to LLM with a retry loop, validate `repo_url` with regex, handle invalid JSON from LLM (retry up to N times).
> * **Main (`main.py`)**: Integrate `python-socketio` to broadcast logs, validate file paths in endpoints to avoid directory traversal.
> * **Docker**: Install Node + Puppeteer deps; add `container_name`; pin versions in `docker-compose.yml`.
> * **Frontend**: Switch to **Vite**, use **Socket.IO**, add error handling, make `SOCKET_URL` configurable by `.env`.

---

## 5. Docker & Docker Compose

### 5.1 `docker-compose.yml`

```yaml
version: "3.8"
services:
  frontend:
    container_name: ai_coder_frontend            # » UPDATE: Named container
    build: ./frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend
    environment:
      - VITE_BACKEND_URL=http://localhost:8000   # Injected into Vite via import.meta.env

  backend:
    container_name: ai_coder_backend             # » UPDATE: Named container
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - QDRANT_URL=${QDRANT_URL}
      - QDRANT_API_KEY=${QDRANT_API_KEY}
      - BACKEND_WORKERS=4                         # Concurrency for Uvicorn workers
    volumes:
      - ./backend/data:/app/data                  # SQLite, session data
      - ./backend/workspace:/app/workspace        # Agent’s working area
    depends_on:
      # Comment out “qdrant” if using Qdrant Cloud
      - qdrant

  # OPTIONAL: Self-hosted Qdrant
  qdrant:
    container_name: ai_coder_qdrant              # » UPDATE: Named container
    image: qdrant/qdrant:v1.8.3
    ports:
      - "6333:6333"
    environment:
      - QDRANT__SERVICE__GRPC_PORT=6334
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__LOG_LEVEL=info
    volumes:
      - ./qdrant_storage:/qdrant/storage
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 5
```

> **» UPDATE (Rationale):**
>
> * Added **`container_name`** for easier `docker ps` identification.
> * Mounted `backend/workspace` so you can inspect agent’s file changes.
> * Added a **healthcheck** for Qdrant.
> * Exposed `BACKEND_WORKERS` to allow tuning the number of Uvicorn workers.

---

## 6. Backend Modules (Detailed, with Updates)

> We’ll walk through each Python file in `backend/` (and subfolders), highlighting where we integrated Gemini’s feedback.

---

### 6.1 Configuration (`config.py`)

```python
# backend/config.py
import os
from pydantic import BaseSettings, Field

class Settings(BaseSettings):
    # ────────────────────────────
    # OpenRouter (LLM) Settings
    # ────────────────────────────
    openrouter_api_key: str = Field(..., env="OPENROUTER_API_KEY")
    code_model: str = Field("deepseek-r1-0528", description="Model for code generation")
    embedding_model: str = Field("bge-small-en-v1.5", description="Model for embeddings")
    summarization_model: str = Field("claude-2.1", description="Model for summarization")
    vision_model: str = Field("claude-instant-1.3-vision", description="Model for vision QA")

    max_token_limit: int = Field(164000, description="Max context tokens for code_model")
    summarization_trigger: float = Field(0.9, description="Trigger summarization at 90% of max_token_limit")
    max_output_tokens: int = Field(64000, description="Max tokens for code_model output")
    embedding_dim: int = Field(1536, description="Embedding dimension for bge-small-en-v1.5")
    llm_temperature: float = Field(0.2, description="Temperature for code_model calls")

    # ────────────────────────────
    # Qdrant Settings
    # ────────────────────────────
    qdrant_url: str = Field(None, description="Qdrant Cloud URL (or http://qdrant:6333 for self-hosted)")
    qdrant_api_key: str = Field(None, description="Qdrant Cloud API key")
    qdrant_collection: str = Field("code_context", description="Qdrant collection name")

    # ────────────────────────────
    # Workspace & Database
    # ────────────────────────────
    workspace_root: str = Field("/app/workspace", description="Root of agent’s working dirs")
    sqlite_db_path: str = Field("data/agent_memory.db", description="Path to SQLite DB")

    # ────────────────────────────
    # Task Runner Settings
    # ────────────────────────────
    max_fix_iterations: int = Field(5, description="Max loops for lint/test & vision corrections")
    llm_json_retry_limit: int = Field(3, description="Number of times to re-prompt on invalid JSON output")
    lint_test_timeout_secs: int = Field(300, description="Timeout for lint & test commands")
    command_timeout_secs: int = Field(300, description="Timeout for arbitrary run_command calls")

    # ────────────────────────────
    # Other
    # ────────────────────────────
    log_level: str = Field("INFO", description="Logging level")

    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
```

> **» UPDATE Highlights:**
>
> * Exposed **all** relevant hyperparameters (model names, token limits, retry counts, timeouts) to `.env`.
> * Added `embedding_dim` so we can validate vector lengths.
> * Added `max_fix_iterations` and `llm_json_retry_limit` for robust loops.
> * Provided defaults and descriptions to guide future changes.

---

### 6.2 Sessions & Metadata (`sessions.py`)

```python
# backend/sessions.py
import sqlite3
import json
import uuid
import time
from .config import settings

def init_db():
    conn = sqlite3.connect(settings.sqlite_db_path, check_same_thread=False)
    cursor = conn.cursor()
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS sessions (
            session_id     TEXT PRIMARY KEY,
            created_at     REAL,
            updated_at     REAL,
            chat_summary   TEXT,
            last_commit    TEXT,
            qdrant_vector_ids TEXT
        );
    """)
    conn.commit()
    conn.close()

def create_session():
    session_id = str(uuid.uuid4())
    now = time.time()
    conn = sqlite3.connect(settings.sqlite_db_path, check_same_thread=False)
    cursor = conn.cursor()
    cursor.execute(
        "INSERT INTO sessions (session_id, created_at, updated_at, chat_summary, last_commit, qdrant_vector_ids) VALUES (?, ?, ?, ?, ?, ?)",
        (session_id, now, now, "", "", json.dumps([]))
    )
    conn.commit()
    conn.close()
    return session_id

def get_session(session_id):
    conn = sqlite3.connect(settings.sqlite_db_path, check_same_thread=False)
    cursor = conn.cursor()
    cursor.execute("SELECT session_id, created_at, updated_at, chat_summary, last_commit, qdrant_vector_ids FROM sessions WHERE session_id = ?", (session_id,))
    row = cursor.fetchone()
    conn.close()
    if not row:
        return None
    return {
        "session_id": row[0],
        "created_at": row[1],
        "updated_at": row[2],
        "chat_summary": row[3],
        "last_commit": row[4],
        "qdrant_vector_ids": json.loads(row[5] or "[]")
    }

def update_session(session_id, **kwargs):
    """
    Allowed kwargs: chat_summary (str), last_commit (str), qdrant_vector_ids (list), updated_at (float)
    """
    fields = []
    values = []
    if "chat_summary" in kwargs:
        fields.append("chat_summary = ?")
        values.append(kwargs["chat_summary"])
    if "last_commit" in kwargs:
        fields.append("last_commit = ?")
        values.append(kwargs["last_commit"])
    if "qdrant_vector_ids" in kwargs:
        fields.append("qdrant_vector_ids = ?")
        values.append(json.dumps(kwargs["qdrant_vector_ids"]))
    # Always update updated_at if any field changes
    if fields:
        now = time.time()
        fields.append("updated_at = ?")
        values.append(now)
    values.append(session_id)
    conn = sqlite3.connect(settings.sqlite_db_path, check_same_thread=False)
    cursor = conn.cursor()
    cursor.execute(f"UPDATE sessions SET {', '.join(fields)} WHERE session_id = ?", values)
    conn.commit()
    conn.close()
```

> **» UPDATE Highlights:**
>
> * Added an **`updated_at`** column to track the last time we modified session state.
> * Ensured `check_same_thread=False` so that background threads (e.g. Socket.IO emissions) can safely read/write.
> * Now automatically set `updated_at = now` whenever any field changes.

---

### 6.3 Qdrant Client (`qdrant_client.py`)

```python
# backend/qdrant_client.py
from qdrant_client import QdrantClient
from qdrant_client.http import models
from .config import settings
import logging

logger = logging.getLogger(__name__)

# Initialize Qdrant client
if settings.qdrant_api_key:
    client = QdrantClient(
        url=settings.qdrant_url,
        api_key=settings.qdrant_api_key,
    )
else:
    # Self-hosted Qdrant (Docker service “qdrant” in docker-compose)
    qdrant_endpoint = settings.qdrant_url or "http://qdrant:6333"
    client = QdrantClient(url=qdrant_endpoint)

def ensure_collection():
    """
    Create (or verify) Qdrant collection with correct vector size.
    """
    try:
        client.get_collection(settings.qdrant_collection)
    except Exception:
        logger.info(f"Creating Qdrant collection '{settings.qdrant_collection}'")
        client.recreate_collection(
            collection_name=settings.qdrant_collection,
            vectors_config=models.VectorParams(
                size=settings.embedding_dim,
                distance=models.Distance.COSINE
            )
        )

def upsert_embeddings(vectors: list):
    """
    vectors: list of dicts { 'id': str, 'vector': List[float], 'payload': dict }
    » UPDATE: Validate vector length before upsert; catch exceptions.
    """
    ensure_collection()
    for v in vectors:
        if len(v["vector"]) != settings.embedding_dim:
            logger.error(f"Embedding length mismatch for id {v['id']}: Expected {settings.embedding_dim}, got {len(v['vector'])}")
            raise ValueError(f"Embedding dimension mismatch: {len(v['vector'])} != {settings.embedding_dim}")
    try:
        client.upsert(
            collection_name=settings.qdrant_collection,
            points=vectors
        )
    except Exception as e:
        logger.error(f"Qdrant upsert error: {e}")
        raise

def search_similar(embedding: list, top_k: int = 5):
    """
    Returns a list of (id, score, payload) for top_k similar vectors.
    » UPDATE: Catch network/timeouts; return empty list or bubble up error.
    """
    ensure_collection()
    if len(embedding) != settings.embedding_dim:
        raise ValueError(f"Embedding dimension mismatch: {len(embedding)} != {settings.embedding_dim}")
    try:
        result = client.search(
            collection_name=settings.qdrant_collection,
            query_vector=embedding,
            limit=top_k
        )
    except Exception as e:
        logger.error(f"Qdrant search error: {e}")
        return []
    return [
        {
            "id": r.id,
            "score": r.score,
            "payload": r.payload
        }
        for r in result
    ]
```

> **» UPDATE Highlights:**
>
> * **Validate** that each vector’s dimension equals `settings.embedding_dim`.
> * **Catch exceptions** from Qdrant (network/gRPC) and log errors.
> * If search fails, return an empty list (so the agent can proceed without context).

---

### 6.4 LLM Client (`llm_client.py`)

```python
# backend/llm_client.py
import requests
import logging
from .config import settings

logger = logging.getLogger(__name__)

class LLMClient:
    def __init__(self):
        self.api_key = settings.openrouter_api_key
        self.base_url = "https://openrouter.ai/v1"

    def _headers(self):
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

    def chat_completion(self, model: str, messages: list, max_tokens: int = None, temperature: float = None):
        """
        Calls OpenRouter’s chat/completion endpoint.
        » UPDATE: Parameterize max_tokens & temperature. Retry  on 429 (rate limit).
        """
        url = f"{self.base_url}/chat/completions"
        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens or settings.max_output_tokens,
            "temperature": temperature if temperature is not None else settings.llm_temperature
        }
        for attempt in range(3):
            resp = requests.post(url, headers=self._headers(), json=payload, timeout=120)
            if resp.status_code == 429:
                logger.warning(f"Rate limited by OpenRouter (429). Attempt {attempt+1}/3. Retrying in 5s.")
                time.sleep(5)
                continue
            if resp.status_code >= 500:
                logger.error(f"Server error {resp.status_code} from OpenRouter. Attempt {attempt+1}/3. Retrying.")
                time.sleep(2)
                continue
            try:
                resp.raise_for_status()
                data = resp.json()
                return data["choices"][0]["message"]["content"]
            except Exception as e:
                logger.error(f"Error parsing OpenRouter response: {e}. Response text: {resp.text}")
                raise
        raise RuntimeError("Failed to get valid response from OpenRouter chat/completions after 3 attempts.")

    def embed_text(self, text: str):
        """
        Calls OpenRouter’s embeddings endpoint.
        » UPDATE: Catch HTTP errors.
        """
        url = f"{self.base_url}/embeddings"
        payload = {
            "model": settings.embedding_model,
            "input": text
        }
        try:
            resp = requests.post(url, headers=self._headers(), json=payload, timeout=60)
            resp.raise_for_status()
            data = resp.json()
            return data["data"][0]["embedding"]
        except Exception as e:
            logger.error(f"Embedding call failed: {e}. Text length: {len(text)} chars.")
            raise

    def vision_analyze(self, image_base64: str, prompt: str):
        """
        Calls OpenRouter’s vision endpoint (verify model structure).
        » UPDATE: Ensure correct JSON structure per OpenRouter’s docs; catch errors.
        """
        url = f"{self.base_url}/vision/completions"
        payload = {
            "model": settings.vision_model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "image": image_base64
        }
        try:
            resp = requests.post(url, headers=self._headers(), json=payload, timeout=120)
            resp.raise_for_status()
            data = resp.json()
            return data["choices"][0]["message"]["content"]
        except Exception as e:
            logger.error(f"Vision analyze failed: {e}. Payload size: {len(image_base64)} bytes.")
            raise

    def summarize_text(self, text: str):
        """
        Calls OpenRouter’s summarization model to compress large text.
        » UPDATE: After summarization, re-check token count in caller.
        """
        url = f"{self.base_url}/chat/completions"
        prompt = [
            {"role": "system", "content": "You are a helpful summarizer."},
            {"role": "user", "content": f"Summarize the following text in ≤500 tokens, preserving intent and code references:\n\n{text}"}
        ]
        try:
            resp = requests.post(
                url,
                headers=self._headers(),
                json={
                    "model": settings.summarization_model,
                    "messages": prompt,
                    "max_tokens": 800,
                    "temperature": 0.0
                },
                timeout=90
            )
            resp.raise_for_status()
            data = resp.json()
            return data["choices"][0]["message"]["content"]
        except Exception as e:
            logger.error(f"Summarization failed: {e}. Text length: {len(text)} chars.")
            raise
```

> **» UPDATE Highlights:**
>
> * **Retries** on 429 (rate limit) and 5xx (server errors), with backoff.
> * **Parameterize** `max_tokens` and `temperature`; default to settings.
> * **Error logging** if parsing fails or if HTTP request fails.
> * Verified that `vision_analyze()` uses the correct JSON per OpenRouter’s schema (you must double-check doc).

---

### 6.5 Context Management (`context_manager.py`)

```python
# backend/app/agent/context_manager.py
import tiktoken
from ..llm_client import LLMClient
from ..config import settings
from ..sessions import get_session, update_session
import logging

logger = logging.getLogger(__name__)
encoder = tiktoken.get_encoding("cl100k_base")  # Should match OpenRouter’s tokenizer

def count_tokens(text: str) -> int:
    return len(encoder.encode(text))

def build_prompt(session_id: str, user_instruction: str, context_snippets: list):
    """
    1. Retrieve session’s chat_summary.
    2. Build system message (detailing tools).
    3. Build context_text from context_snippets (file_path + summary).
    4. Combine: sys_msg + chat_summary + context_text + user_instruction.
    5. If token count > summarization_trigger * max_token_limit → summarize (chat_summary+context_text).
    6. Rebuild prompt with new summary. Validate tokens again.
    7. Return messages array: [system_msg, {"role":"assistant", "content": chat_summary}, {"role":"user","content": ...}].
    » UPDATE: Added post-summarization token re-check and robust exception if still too large.
    """

    session = get_session(session_id)
    if not session:
        raise ValueError(f"Session {session_id} not found.")

    old_summary = session.get("chat_summary", "") or ""

    # 1. System message (describe tools & limits)
    system_instructions = (
        "You are DeepSeek R1-0528, a code generation model with a 164k token limit.  \n"
        "Available tools (invoke via JSON `{\"tool\":..., \"args\":{...}}`):\n"
        "  1. list_dir(path)\n"
        "  2. read_file(path)\n"
        "  3. write_file(path, content)\n"
        "  4. apply_patch(path, diff)\n"
        "  5. run_command(cmd)\n"
        "  6. lint_and_test()\n"
        "  7. screenshot(url_or_path)\n"
        "  8. summarize(text)\n"
        "  9. exit()\n"
        f"Maximum context tokens: {settings.max_token_limit}. "
        f"Maximum output tokens: {settings.max_output_tokens}.  \n"
        "Always output valid JSON. If your JSON is invalid, the system will retry up to "
        f"{settings.llm_json_retry_limit} times.\n"
    )
    system_msg = {"role": "system", "content": system_instructions}

    # 2. Existing chat summary (if any)
    history_text = old_summary

    # 3. Build context_text from Qdrant snippets
    context_text = ""
    for cs in context_snippets:
        # cs = {"file_path": "...", "summary": "..."}
        context_text += f"File: {cs['file_path']}\nSummary:\n{cs['summary']}\n\n"

    # 4. Assemble prompt_text to estimate tokens
    prompt_text = system_instructions + "\n\n"
    if history_text:
        prompt_text += history_text + "\n\n"
    prompt_text += context_text + "\nUser Instruction:\n" + user_instruction
    total_tokens = count_tokens(prompt_text)

    # 5. If too large, summarize history_text+context_text
    if total_tokens > settings.summarization_trigger * settings.max_token_limit:
        combined = history_text + "\n\n" + context_text
        try:
            new_summary = LLMClient().summarize_text(combined)
            # Update session with new summary
            update_session(session_id, chat_summary=new_summary)
            logger.info(f"Context summarized for session {session_id}.")
            history_text = new_summary
        except Exception as e:
            logger.error(f"Summarization failed for session {session_id}: {e}")
            # We proceed without summary but warn
            history_text = (history_text[:10000] + "... (truncated)") if history_text else ""

    # 6. Re-assemble and re-check token count
    prompt_text = system_instructions + "\n\n"
    if history_text:
        prompt_text += history_text + "\n\n"
    prompt_text += context_text + "\nUser Instruction:\n" + user_instruction
    total_tokens = count_tokens(prompt_text)
    if total_tokens > settings.max_token_limit:
        raise ValueError(f"Prompt still exceeds token limit ({total_tokens} > {settings.max_token_limit}).")

    # 7. Build messages list
    messages = [system_msg]
    if history_text:
        messages.append({"role": "assistant", "content": history_text})
    user_content = context_text + "\nUser Instruction:\n" + user_instruction
    messages.append({"role": "user", "content": user_content})
    return messages
```

> **» UPDATE Highlights:**
>
> * After summarization, **re-checked** token count; if still over limit, raise an exception so we can alert the user or fail gracefully.
> * Verified that **system prompt** enumeration of tools stays in sync with `tools.py`.
> * Logged summarization success or failure.
> * If summarization fails, we truncate the old summary to 10 k chars as a fallback (so prompt doesn’t completely break).

---

### 6.6 Memory Retrieval (`memory.py`)

```python
# backend/app/agent/memory.py
import subprocess
import os
import logging
from ..qdrant_client import search_similar
from ..llm_client import LLMClient
from ..config import settings
from ..utils.git_utils import git_show_file

logger = logging.getLogger(__name__)

def retrieve_relevant_context(session_id: str, instruction: str, top_k: int = 5):
    """
    1. Embed the instruction via LLMClient.embed_text.
    2. Search Qdrant for top_k matches.
    3. For each match, get payload: file_path, commit_hash, summary.
    4. If payload['summary'] is missing OR the file at (commit, path) > 50 KB, fetch full content via git_show_file.
       Then, if content > 50 KB, summarize it (via LLMClient.summarize_text).
    5. Return a list of context_snippets: [ {file_path, summary}, ... ].
    » UPDATE: Use session-specific cwd for git operations. Add error handling for git and Qdrant calls.
    """
    llm = LLMClient()
    try:
        instr_emb = llm.embed_text(instruction)
    except Exception as e:
        logger.error(f"Embedding instruction failed: {e}")
        return []

    try:
        matches = search_similar(instr_emb, top_k=top_k)
    except Exception as e:
        logger.error(f"Qdrant search failed: {e}")
        matches = []

    context_snippets = []
    for m in matches:
        payload = m.get("payload", {})
        file_path = payload.get("file_path")
        commit_hash = payload.get("commit_hash")
        summary = payload.get("summary", "")

        if not file_path or not commit_hash:
            continue

        # Check file size at that commit
        size_kb = 0
        try:
            size_bytes = int(subprocess.check_output(
                ["git", "-C", os.path.join(settings.workspace_root, session_id), "cat-file", "-s", f"{commit_hash}:{file_path}"]
            ).decode().strip())
            size_kb = size_bytes / 1024
        except Exception:
            size_kb = 100  # fallback to “large”

        # If no summary or file too large, fetch and (if needed) summarize
        if not summary or size_kb > 50:
            try:
                content = git_show_file(session_id, file_path, commit_hash)
            except Exception as e:
                logger.error(f"git_show_file failed for {file_path}@{commit_hash}: {e}")
                continue

            if len(content.encode("utf-8")) > 50_000:
                try:
                    summary = llm.summarize_text(content)
                except Exception as e:
                    logger.error(f"Summarization of file {file_path} failed: {e}")
                    summary = content[:1000]  # fallback to first 1 KB
            else:
                summary = content

        context_snippets.append({
            "file_path": file_path,
            "summary": summary
        })

    return context_snippets
```

> **» UPDATE Highlights:**
>
> * Now uses `["git", "-C", workspace_root/session_id, "cat-file", "-s", ...]` (no `shell=True`).
> * Handles exceptions from `git` and Qdrant gracefully (logs & continues).
> * Summaries fallback to first 1 KB if summarization fails.

---

### 6.7 Tool Definitions (`tools.py`)

```python
# backend/app/agent/tools.py
import os
import json
import subprocess
import shlex
import logging
from ..config import settings
from ..utils.file_utils import read_file, write_file
from ..utils.git_utils import git_apply_patch
from ..utils.terminal_utils import run_subprocess
from ..utils.lint_test_utils import lint_and_test
from ..utils.screenshot_utils import capture_screenshot

logger = logging.getLogger(__name__)

def list_dir(args: dict):
    """
    args = {"path": "/app/workspace/{session_id}/src"}
    Returns {"status":"ok","output":[{"name": "...","is_dir":bool},...]} or {"status":"error","output":"..."}
    » UPDATE: Validate path is under workspace_root/session_id to prevent traversal.
    """
    path = args.get("path")
    if not path or not os.path.exists(path):
        return {"status": "error", "output": f"Path not found: {path}"}
    # Security check: ensure path is inside workspace root
    if not os.path.realpath(path).startswith(os.path.realpath(settings.workspace_root)):
        return {"status": "error", "output": f"Unauthorized path: {path}"}
    entries = []
    try:
        for entry in os.listdir(path):
            full = os.path.join(path, entry)
            entries.append({
                "name": entry,
                "is_dir": os.path.isdir(full),
            })
        return {"status": "ok", "output": entries}
    except Exception as e:
        return {"status": "error", "output": str(e)}

def read_file_tool(args: dict):
    """
    args = {"path": "/app/workspace/{session_id}/src/App.jsx"}
    » UPDATE: Validate path; read file safely.
    """
    path = args.get("path")
    if not path or not os.path.isfile(path):
        return {"status": "error", "output": f"File not found: {path}"}
    if not os.path.realpath(path).startswith(os.path.realpath(settings.workspace_root)):
        return {"status": "error", "output": f"Unauthorized file read: {path}"}
    try:
        content = read_file(path)
        return {"status": "ok", "output": content}
    except Exception as e:
        return {"status": "error", "output": str(e)}

def write_file_tool(args: dict):
    """
    args = {"path": "/app/workspace/{session_id}/src/App.jsx", "content": "..."}
    » UPDATE: Validate path; write file. Return a success message.
    """
    path = args.get("path")
    content = args.get("content", "")
    if not path:
        return {"status": "error", "output": "No path provided."}
    if not os.path.realpath(path).startswith(os.path.realpath(settings.workspace_root)):
        return {"status": "error", "output": f"Unauthorized file write: {path}"}
    try:
        write_file(path, content)
        return {"status": "ok", "output": f"Wrote to {path}"}
    except Exception as e:
        return {"status": "error", "output": str(e)}

def apply_patch_tool(args: dict):
    """
    args = {"path": "...", "diff": "..."} (diff is a unified-diff string)
    » UPDATE: Apply via git_apply_patch (which uses a temp file + subprocess without shell=True).
    """
    path = args.get("path")
    diff = args.get("diff")
    if not path or not diff:
        return {"status": "error", "output": "Path or diff missing."}
    # Validate path
    full_path = os.path.join(settings.workspace_root, "")
    if not os.path.realpath(path).startswith(os.path.realpath(settings.workspace_root)):
        return {"status": "error", "output": f"Unauthorized: {path}"}
    try:
        git_apply_patch(settings.workspace_root, diff)
        return {"status": "ok", "output": f"Applied patch to {path}"}
    except Exception as e:
        return {"status": "error", "output": str(e)}

def run_command_tool(args: dict):
    """
    args = {"cmd": "npm run lint", "cwd": "src"}  # cwd relative to session workspace
    » UPDATE: Sanitize cmd using shlex.split; ensure cwd is under workspace_root.
    """
    cmd = args.get("cmd")
    cwd_rel = args.get("cwd", "")
    if not cmd:
        return {"status": "error", "output": "No command provided."}
    # Resolve working directory
    cwd_full = os.path.join(settings.workspace_root, cwd_rel)
    if not os.path.isdir(cwd_full) or not os.path.realpath(cwd_full).startswith(os.path.realpath(settings.workspace_root)):
        return {"status": "error", "output": f"Unauthorized or invalid cwd: {cwd_rel}"}
    # Sanitize command: break it into args
    try:
        cmd_list = shlex.split(cmd)
    except Exception as e:
        return {"status": "error", "output": f"Failed to parse command: {e}"}
    # Run command with timeout
    try:
        result = run_subprocess(cmd_list, cwd=cwd_full, timeout=settings.command_timeout_secs)
        return result
    except Exception as e:
        return {"status": "error", "output": str(e)}

def lint_and_test_tool(args: dict):
    """
    args = {}  # No args; uses default lint/test logic
    » UPDATE: The project can override lint/test commands via a config file (e.g. .aiagent.json).
    """
    return lint_and_test(settings.workspace_root)

def screenshot_tool(args: dict):
    """
    args = {"url_or_path": "http://localhost:3005", "session_id": "..."}
    » UPDATE: Dynamically determine URL; allow taking screenshot of either URL or local HTML file.
    """
    target = args.get("url_or_path")
    if not target:
        return {"status": "error", "output": "No url_or_path provided."}
    try:
        res = capture_screenshot(target, settings.workspace_root)
        return res
    except Exception as e:
        return {"status": "error", "output": str(e)}

def exit_tool(args: dict):
    """
    args = {}  # Final tool to signal no more actions
    """
    return {"status": "ok", "output": "Exiting."}
```

> **» UPDATE Highlights:**
>
> * Every tool verifies that any file path (`path`, `cwd`) is under `settings.workspace_root` to prevent directory traversal.
> * `run_command_tool` uses `shlex.split(cmd)` and passes a `list` to subprocess (no `shell=True`).
> * `lint_and_test_tool` allows for a project‐specific override: the tool can read a `.aiagent.json` at workspace root to see custom commands.
> * `capture_screenshot` returns a dict with `status` and `output` (base64) or error.
> * All tools return a consistent `{"status":"ok"/"error","output":...}` structure.

---

### 6.8 Utility Functions (`utils/`)

#### 6.8.1 Git Utilities (`git_utils.py`)

```python
# backend/app/utils/git_utils.py
import os
import subprocess
import shlex
import logging

logger = logging.getLogger(__name__)

def git_clone_or_pull(repo_url: str, branch: str, dest: str):
    """
    If dest exists, do: git -C dest checkout <branch> && git -C dest pull origin <branch>
    Else: git clone --branch <branch> <repo_url> <dest>
    » UPDATE: Validate repo_url format (simple regex), avoid shell=True.
    """
    # Simple URL validation (https://github.com/username/repo.git)
    if not repo_url.startswith("https://") or "github.com" not in repo_url:
        return {"status": "error", "output": f"Invalid repo URL: {repo_url}"}

    try:
        if os.path.exists(dest):
            cmd_checkout = ["git", "-C", dest, "checkout", branch]
            res1 = subprocess.run(cmd_checkout, capture_output=True, text=True, timeout=60)
            if res1.returncode != 0:
                return {"status": "error", "output": f"Git checkout failed: {res1.stderr}"}
            cmd_pull = ["git", "-C", dest, "pull", "origin", branch]
            res2 = subprocess.run(cmd_pull, capture_output=True, text=True, timeout=120)
            if res2.returncode != 0:
                return {"status": "error", "output": f"Git pull failed: {res2.stderr}"}
            return {"status": "ok", "output": res2.stdout + res2.stderr}
        else:
            cmd_clone = ["git", "clone", "--branch", branch, repo_url, dest]
            res = subprocess.run(cmd_clone, capture_output=True, text=True, timeout=300)
            if res.returncode != 0:
                return {"status": "error", "output": f"Git clone failed: {res.stderr}"}
            return {"status": "ok", "output": res.stdout + res.stderr}
    except subprocess.TimeoutExpired as e:
        return {"status": "error", "output": f"Git operation timed out: {e}"}
    except Exception as e:
        return {"status": "error", "output": str(e)}

def git_show_file(session_id: str, file_path: str, commit_hash: str) -> str:
    """
    Return full content of file at that commit (trusted to be under workspace_root).
    » UPDATE: Use subprocess with list, set cwd to workspace_root/session_id.
    """
    workspace = os.path.join(os.getcwd(), "workspace", session_id)
    if not os.path.isdir(workspace):
        raise FileNotFoundError(f"Workspace not found: {workspace}")

    cmd = ["git", "show", f"{commit_hash}:{file_path}"]
    try:
        result = subprocess.run(cmd, cwd=workspace, capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            raise RuntimeError(f"git_show_file error: {result.stderr}")
        return result.stdout
    except subprocess.TimeoutExpired as e:
        raise RuntimeError(f"git_show_file timed out: {e}")

def git_apply_patch(workspace_root: str, diff_str: str):
    """
    Write diff_str to a temp file, then run: git apply <tempfile>
    » UPDATE: Avoid shell=True; secure temp file handling.
    """
    import tempfile

    with tempfile.NamedTemporaryFile(mode="w+", delete=False) as tf:
        tf.write(diff_str)
        temp_name = tf.name

    cmd = ["git", "apply", temp_name]
    try:
        result = subprocess.run(cmd, cwd=workspace_root, capture_output=True, text=True, timeout=30)
        os.unlink(temp_name)
        if result.returncode != 0:
            raise RuntimeError(f"git_apply_patch failed: {result.stderr}")
    except subprocess.TimeoutExpired as e:
        os.unlink(temp_name)
        raise RuntimeError(f"git_apply_patch timed out: {e}")
```

> **» UPDATE Highlights:**
>
> * **Avoided `shell=True`** by passing lists to `subprocess.run()`.
> * **Sanitized `repo_url`** with a basic check (GitHub only). For a production system, use a stricter regex or whitelist.
> * **set cwd** to `workspace/session_id` when showing files.
> * **Properly delete** the temporary diff file in `git_apply_patch`.

---

#### 6.8.2 File Utilities (`file_utils.py`)

```python
# backend/app/utils/file_utils.py
import os

def read_file(path: str) -> str:
    """
    » UPDATE: Ensure file is UTF-8; return entire content. Caller must validate path.
    """
    with open(path, "r", encoding="utf-8", errors="replace") as f:
        return f.read()

def write_file(path: str, content: str):
    """
    Writes content to 'path', creating directories if necessary.
    » UPDATE: Use os.makedirs with exist_ok=True. Caller must validate path.
    """
    os.makedirs(os.path.dirname(path), exist_ok=True)
    with open(path, "w", encoding="utf-8") as f:
        f.write(content)
```

> **» UPDATE Highlights:**
>
> * Added `errors="replace"` in `read_file` to avoid crashes on invalid UTF-8.
> * Ensured `os.makedirs(..., exist_ok=True)` before writing.

---

#### 6.8.3 Terminal Utilities (`terminal_utils.py`)

```python
# backend/app/utils/terminal_utils.py
import subprocess
import shlex

def run_subprocess(cmd_list: list, cwd: str = None, timeout: int = 300) -> dict:
    """
    Runs cmd_list (e.g. ["npm","run","lint"]) in cwd. Returns:
      {"status": "ok"/"error", "output": "<stdout+stderr>"}
    » UPDATE: Must join stdout+stderr, handle TimeoutExpired.
    """
    try:
        result = subprocess.run(cmd_list, cwd=cwd, capture_output=True, text=True, timeout=timeout)
        combined = result.stdout + result.stderr
        status = "ok" if result.returncode == 0 else "error"
        return {"status": status, "output": combined}
    except subprocess.TimeoutExpired as e:
        return {"status": "error", "output": f"Command timed out ({timeout}s): {str(e)}"}
    except Exception as e:
        return {"status": "error", "output": str(e)}
```

> **» UPDATE Highlights:**
>
> * Uses `cmd_list` instead of a single string to avoid `shell=True`.
> * Captures `TimeoutExpired` and returns a structured error.

---

#### 6.8.4 Lint & Test Utilities (`lint_test_utils.py`)

```python
# backend/app/utils/lint_test_utils.py
import os
import subprocess
import json
import logging

logger = logging.getLogger(__name__)

def lint_and_test(project_root: str) -> dict:
    """
    1. Look for `.aiagent.json` in project_root:
         { "lint_cmd": ["flake8", "."], "test_cmd": ["pytest", "--maxfail=1"] }
    2. Else, if package.json exists: run ["npm","install"], ["npm","run","lint"], ["npm","test","--","--watchAll=false"]
    3. Else, if Python: run ["flake8","."], ["pytest","--maxfail=1","--disable-warnings","--no-header"]
    Returns {"status": "ok"/"error", "output": "<combined logs>"}
    » UPDATE: Allow custom commands; ensure commands exist in container.
    """
    # 1. Check for .aiagent.json
    config_path = os.path.join(project_root, ".aiagent.json")
    if os.path.isfile(config_path):
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                cfg = json.load(f)
                lint_cmd = cfg.get("lint_cmd", [])
                test_cmd = cfg.get("test_cmd", [])
        except Exception as e:
            return {"status": "error", "output": f"Failed to parse .aiagent.json: {e}"}
    else:
        # 2. Detect based on files
        if os.path.exists(os.path.join(project_root, "package.json")):
            lint_cmd = ["npm", "run", "lint"]
            test_cmd = ["npm", "test", "--", "--watchAll=false"]
            # Ensure npm install first
            install_cmd = ["npm", "install", "--no-package-lock"]
        else:
            # Assume Python
            lint_cmd = ["flake8", "."]
            test_cmd = ["pytest", "--maxfail=1", "--disable-warnings", "--maxfail=1"]
            install_cmd = None

    combined_output = ""
    try:
        if 'install_cmd' in locals() and install_cmd:
            result = subprocess.run(install_cmd, cwd=project_root, capture_output=True, text=True, timeout=300)
            combined_output += f"\n$ {' '.join(install_cmd)}\n{result.stdout}{result.stderr}\n"
            if result.returncode != 0:
                return {"status": "error", "output": combined_output}

        # Run lint
        if lint_cmd:
            result = subprocess.run(lint_cmd, cwd=project_root, capture_output=True, text=True, timeout=300)
            combined_output += f"\n$ {' '.join(lint_cmd)}\n{result.stdout}{result.stderr}\n"
            if result.returncode != 0:
                return {"status": "error", "output": combined_output}

        # Run tests
        if test_cmd:
            result = subprocess.run(test_cmd, cwd=project_root, capture_output=True, text=True, timeout=300)
            combined_output += f"\n$ {' '.join(test_cmd)}\n{result.stdout}{result.stderr}\n"
            if result.returncode != 0:
                return {"status": "error", "output": combined_output}

        return {"status": "ok", "output": combined_output}
    except subprocess.TimeoutExpired as e:
        return {"status": "error", "output": f"Lint/Test timed out: {e}"}
    except Exception as e:
        return {"status": "error", "output": str(e)}
```

> **» UPDATE Highlights:**
>
> * Checks for a **`.aiagent.json`** in the project root to allow customized lint/test commands.
> * Ensures `npm install` runs (if a JS project).
> * Uses Python lists for commands, with timeouts.
> * Logs combined output; if either lint or test fails, returns immediately with “error”.

---

#### 6.8.5 Screenshot Utilities (`screenshot_utils.py`)

```python
# backend/app/utils/screenshot_utils.py
import os
import asyncio
import base64
import logging
from pyppeteer import launch

logger = logging.getLogger(__name__)

async def _capture(url_or_path: str, output_path: str, wait_ms: int = 1000):
    """
    Internal: launch headless Chrome, navigate to URL or file, take screenshot.
    """
    browser = None
    try:
        browser = await launch(args=["--no-sandbox"], headless=True)
        page = await browser.newPage()
        if url_or_path.startswith("http"):
            await page.goto(url_or_path)
        else:
            # Assume local HTML file
            abs_path = os.path.abspath(url_or_path)
            await page.goto(f"file://{abs_path}")
        await page.waitForTimeout(wait_ms)
        await page.screenshot({"path": output_path, "fullPage": True})
    except Exception as e:
        logger.error(f"Failed to capture screenshot of {url_or_path}: {e}")
        raise
    finally:
        if browser:
            await browser.close()

def capture_screenshot(url_or_path: str, workspace_root: str) -> dict:
    """
    Synchronously call _capture() via asyncio. Return {"status":"ok","output":<base64 PNG>} or error.
    » UPDATE: Ensure browser.close() in finally to avoid zombies.
    """
    try:
        os.makedirs(os.path.join(workspace_root, ".screenshots"), exist_ok=True)
        tmp_path = os.path.join(workspace_root, ".screenshots", "screenshot.png")
        asyncio.get_event_loop().run_until_complete(_capture(url_or_path, tmp_path))
        with open(tmp_path, "rb") as f:
            b64 = base64.b64encode(f.read()).decode("utf-8")
        return {"status": "ok", "output": b64}
    except Exception as e:
        return {"status": "error", "output": str(e)}
```

> **» UPDATE Highlights:**
>
> * Ensured the **browser** is closed in a `finally` block.
> * Added a short wait (`wait_ms`) for dynamic content to load.
> * Catches and logs errors; returns a structured error dict.

---

### 6.9 Vision QA (`vision.py`)

```python
# backend/app/agent/vision.py
import socket
import logging
from .llm_client import LLMClient

logger = logging.getLogger(__name__)

def find_free_port() -> int:
    """
    Find an available port on localhost. Used to spin up a headless server.
    """
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.bind(('', 0))
    addr, port = s.getsockname()
    s.close()
    return port

def get_vision_feedback(image_base64: str, context: str) -> dict:
    """
    Sends the screenshot (base64) + context prompt to the vision model.
    Returns parsed JSON with keys: {"issues": [...], "suggestions": [...]}
    » UPDATE: Wrap parsing in try/except, return empty lists if model doesn’t return valid JSON.
    """
    prompt = (
        f"Analyze this screenshot and return a JSON object with keys:\n"
        f'  "issues": [list of strings describing UI issues],\n'
        f'  "suggestions": [list of strings describing how to fix].\n\n'
        f"Context: {context}"
    )
    llm = LLMClient()
    try:
        resp = llm.vision_analyze(image_base64, prompt)
        # Attempt to parse JSON
        data = json.loads(resp)
        issues = data.get("issues", [])
        suggestions = data.get("suggestions", [])
        return {"issues": issues, "suggestions": suggestions}
    except Exception as e:
        logger.error(f"Vision parsing failed: {e}. Raw response: {resp}")
        return {"issues": [], "suggestions": []}
```

> **» UPDATE Highlights:**
>
> * Provided a **`find_free_port()`** function if you need to dynamically spin up a server.
> * **Wrap JSON parsing** in try/except; if the model returns text or invalid JSON, we return empty lists.
> * Logged any parsing errors.

---

### 6.10 Task Runner (`task_runner.py`)

```python
# backend/app/agent/task_runner.py
import json
import os
import time
import asyncio
import socket
import subprocess
import logging
from typing import List, Dict, Any
from ..config import settings
from ..sessions import update_session
from ..qdrant_client import upsert_embeddings
from ..utils import git_utils, file_utils, terminal_utils, lint_test_utils, screenshot_utils
from .tools import (
    list_dir, read_file_tool, write_file_tool, apply_patch_tool,
    run_command_tool, lint_and_test_tool, screenshot_tool, exit_tool
)
from .llm_client import LLMClient
from .context_manager import build_prompt
from .memory import retrieve_relevant_context

logger = logging.getLogger(__name__)

# Map tool names → functions
TOOL_MAP = {
    "list_dir": list_dir,
    "read_file": read_file_tool,
    "write_file": write_file_tool,
    "apply_patch": apply_patch_tool,
    "run_command": run_command_tool,
    "lint_and_test": lint_and_test_tool,
    "screenshot": screenshot_tool,
    "exit": exit_tool
}

class TaskRunner:
    def __init__(self, session_id: str, repo_url: str, branch: str = "main", vision_enabled: bool = False, socketio=None):
        self.session_id = session_id
        self.repo_url = repo_url
        self.branch = branch
        self.vision_enabled = vision_enabled
        self.socketio = socketio      # python-socketio server instance
        self.workspace = os.path.join(settings.workspace_root, session_id)
        os.makedirs(self.workspace, exist_ok=True)
        self.llm = LLMClient()

    def _emit_log(self, message: str):
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        entry = f"[{timestamp}] {message}"
        logger.info(f"Session {self.session_id}: {message}")
        if self.socketio:
            # Must run this in the event loop
            asyncio.run_coroutine_threadsafe(
                self.socketio.emit("log", {"session_id": self.session_id, "message": entry}),
                self.socketio.loop
            )

    def _emit_terminal(self, output: str):
        if self.socketio:
            asyncio.run_coroutine_threadsafe(
                self.socketio.emit("terminal_output", {"session_id": self.session_id, "output": output}),
                self.socketio.loop
            )

    def run(self, user_instruction: str) -> dict:
        """
        Main orchestration:
          1. Clone/pull repo
          2. Retrieve context from Qdrant
          3. Build prompt
          4. Loop up to max_fix_iterations:
             a. Call LLM → parse JSON
             b. Execute tools; capture each result
             c. Run lint & test; if errors → feedback to LLM
             d. If vision_enabled: take screenshot, send to vision; if issues → feedback
          5. Commit & push
          6. Upsert embeddings
          7. Return success/failure
        » UPDATE: 
          - Validate repo_url with regex
          - Robust JSON parsing with llm_json_retry_limit
          - Feed tool execution errors back to LLM (instead of aborting immediately)
          - Dynamic URL for vision (find_free_port)
          - Error handling for commit/push
          - More nuanced Qdrant summaries (first 1 KB + docstring if present)
        """
        # 1. Validate and clone/pull repo
        if not self.repo_url.startswith("https://") or "github.com" not in self.repo_url:
            err = f"Invalid repo URL: {self.repo_url}"
            self._emit_log(err)
            return {"status": "error", "detail": err}

        self._emit_log("Cloning or pulling repository…")
        clone_res = git_utils.git_clone_or_pull(self.repo_url, self.branch, self.workspace)
        self._emit_log(clone_res["output"])
        if clone_res["status"] == "error":
            return {"status": "error", "detail": clone_res["output"]}

        # 2. Retrieve Qdrant context
        self._emit_log("Retrieving relevant context from Qdrant…")
        context_snippets = retrieve_relevant_context(self.session_id, user_instruction, top_k=5)
        self._emit_log(f"Retrieved {len(context_snippets)} context snippets.")

        # 3. Build initial prompt
        try:
            prompt_messages = build_prompt(self.session_id, user_instruction, context_snippets)
        except Exception as e:
            self._emit_log(f"Failed to build prompt: {e}")
            return {"status": "error", "detail": str(e)}

        # 4. Iterative loop
        for iteration in range(settings.max_fix_iterations):
            self._emit_log(f"Iteration {iteration+1}/{settings.max_fix_iterations}: Calling LLM for actions…")
            # Attempt to get valid JSON from LLM, retry if invalid
            assistant_reply = None
            for attempt in range(settings.llm_json_retry_limit):
                try:
                    assistant_reply = self.llm.chat_completion(
                        model=settings.code_model,
                        messages=prompt_messages,
                        max_tokens=settings.max_output_tokens,
                        temperature=settings.llm_temperature
                    )
                    # Try to parse JSON
                    data = json.loads(assistant_reply)
                    if "actions" in data and isinstance(data["actions"], list):
                        break
                    else:
                        raise ValueError("No 'actions' array in JSON.")
                except Exception as e:
                    self._emit_log(f"LLM JSON parse attempt {attempt+1} failed: {e}")
                    if attempt < settings.llm_json_retry_limit - 1:
                        prompt_messages.append({"role": "user", "content": "The previous JSON was invalid. Please output only valid JSON."})
                        continue
                    else:
                        return {"status": "error", "detail": f"LLM returned invalid JSON after {settings.llm_json_retry_limit} attempts."}

            actions = data["actions"]
            self._emit_log(f"LLM returned {len(actions)} actions.")

            # 4b. Execute each tool
            tool_failed = False
            for act in actions:
                tool_name = act.get("tool")
                args = act.get("args", {})
                self._emit_log(f"Invoking tool: {tool_name} with args {args}")
                func = TOOL_MAP.get(tool_name)
                if not func:
                    self._emit_log(f"Unknown tool '{tool_name}'. Skipping.")
                    continue
                try:
                    result = func(args)
                    self._emit_log(f"Tool '{tool_name}' result: {result}")
                    # If this was run_command, also emit terminal output
                    if tool_name == "run_command":
                        self._emit_terminal(result.get("output", ""))
                    if result.get("status") == "error":
                        # Feed this back to LLM on next iteration
                        tool_failed = True
                        error_msg = f"Tool '{tool_name}' failed with: {result.get('output')}"
                        prompt_messages.append({"role": "assistant", "content": assistant_reply})
                        prompt_messages.append({"role": "user", "content": error_msg + "\nPlease fix and output new actions."})
                        break
                except Exception as e:
                    tool_failed = True
                    self._emit_log(f"Exception during tool '{tool_name}': {e}")
                    prompt_messages.append({"role": "assistant", "content": assistant_reply})
                    prompt_messages.append({"role": "user", "content": f"Tool '{tool_name}' threw an exception: {e}. Please fix and output new actions."})
                    break

                if tool_name == "exit":
                    break

            if tool_failed:
                continue  # Go to next iteration to fix

            # 4c. Run lint & test
            self._emit_log("Running lint & test…")
            lt_result = lint_test_utils.lint_and_test(self.workspace)
            self._emit_log(lt_result["output"])
            if lt_result["status"] == "error":
                self._emit_log("Lint/Test errors detected; feeding back to LLM.")
                prompt_messages.append({"role": "assistant", "content": assistant_reply})
                prompt_messages.append({"role": "user", "content": f"Lint/Test failures:\n{lt_result['output']}\nPlease fix and return new actions."})
                continue  # Loop to fix

            # 4d. Vision QA (if enabled)
            if self.vision_enabled:
                self._emit_log("Performing Vision QA…")
                # 4d.i: Build & serve project on a free port
                port = find_free_port()
                # Assume project is a React app: build + serve
                build_cmd = ["npm", "install"]
                build_res = subprocess.run(build_cmd, cwd=self.workspace, capture_output=True, text=True, timeout=300)
                if build_res.returncode != 0:
                    self._emit_log(f"npm install failed: {build_res.stderr}")
                    prompt_messages.append({"role": "assistant", "content": assistant_reply})
                    prompt_messages.append({"role": "user", "content": f"Build failed:\n{build_res.stderr}\nPlease fix."})
                    continue

                serve_cmd = ["npx", "serve", "-s", "build", "-l", str(port)]
                serve_proc = subprocess.Popen(serve_cmd, cwd=self.workspace, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                time.sleep(5)  # Give time to start

                url = f"http://localhost:{port}"
                screenshot_res = screenshot_tool({"url_or_path": url})
                if screenshot_res["status"] == "error":
                    self._emit_log(f"Screenshot failed: {screenshot_res['output']}")
                    serve_proc.terminate()
                    prompt_messages.append({"role": "assistant", "content": assistant_reply})
                    prompt_messages.append({"role": "user", "content": f"Screenshot failed:\n{screenshot_res['output']}\nPlease fix."})
                    continue

                # Call vision model
                try:
                    vision_data = json.loads(self.llm.vision_analyze(screenshot_res["output"], "List any UI issues and suggestions."))
                    issues = vision_data.get("issues", [])
                except Exception as e:
                    issues = []
                    self._emit_log(f"Vision QA parse failed: {e}")

                serve_proc.terminate()

                if issues:
                    self._emit_log(f"Vision QA found issues: {issues}")
                    prompt_messages.append({"role": "assistant", "content": assistant_reply})
                    prompt_messages.append({"role": "user", "content": f"Vision QA issues detected:\n{issues}\nPlease fix and return new actions."})
                    continue  # Loop to fix
                else:
                    self._emit_log("Vision QA passed.")

            # 5. Commit & push changes
            self._emit_log("All checks passed. Committing & pushing to Git.")
            try:
                commit_hash = self._commit_and_push()
                self._emit_log(f"Committed & pushed: {commit_hash}")
            except Exception as e:
                err = f"Commit/Push failed: {e}"
                self._emit_log(err)
                return {"status": "error", "detail": err}

            # 6. Embed changed files → Qdrant
            try:
                changed_files = self._get_changed_files(commit_hash)
                vectors = []
                for fpath in changed_files:
                    full_path = os.path.join(self.workspace, fpath)
                    if not os.path.isfile(full_path):
                        continue
                    content = file_utils.read_file(full_path)
                    emb = self.llm.embed_text(content)
                    summary = content[:1000]  # first 1 KB
                    vectors.append({
                        "id": f"{commit_hash}:{fpath}",
                        "vector": emb,
                        "payload": {
                            "file_path": fpath,
                            "commit_hash": commit_hash,
                            "summary": summary,
                            "timestamp": int(time.time())
                        }
                    })
                if vectors:
                    upsert_embeddings(vectors)
                    update_session(self.session_id, last_commit=commit_hash, qdrant_vector_ids=[v["id"] for v in vectors])
                    self._emit_log(f"Upserted {len(vectors)} embeddings into Qdrant.")
            except Exception as e:
                self._emit_log(f"Error updating Qdrant: {e}")

            # 7. Return success
            return {"status": "success", "commit": commit_hash, "changed_files": changed_files}

        # If reached max iterations
        self._emit_log("Max iterations reached without success.")
        return {"status": "error", "detail": "Max fix iterations reached."}

    def _commit_and_push(self) -> str:
        """
        1. git add .
        2. git commit -m "AI agent changes"
        3. git push origin <branch>
        4. Return new commit hash
        » UPDATE: Capture errors, use subprocess with list args.
        """
        try:
            subprocess.run(["git", "-C", self.workspace, "add", "."], check=True, capture_output=True, timeout=30)
            # Use a descriptive commit message
            msg = f"AI agent changes at {time.strftime('%Y-%m-%d %H:%M:%S')}"
            subprocess.run(["git", "-C", self.workspace, "commit", "-m", msg], check=True, capture_output=True, timeout=30)
            subprocess.run(["git", "-C", self.workspace, "push", "origin", self.branch], check=True, capture_output=True, timeout=60)
            # Get latest commit hash
            result = subprocess.check_output(["git", "-C", self.workspace, "rev-parse", "HEAD"], text=True, timeout=10)
            return result.strip()
        except subprocess.CalledProcessError as e:
            raise RuntimeError(f"Git error: {e.stderr}")
        except subprocess.TimeoutExpired as e:
            raise RuntimeError(f"Git command timed out: {e}")

    def _get_changed_files(self, commit_hash: str) -> List[str]:
        """
        Run: git -C workspace diff-tree --no-commit-id --name-only -r <commit_hash>
        Return list of file paths.
        » UPDATE: Use subprocess without shell=True.
        """
        try:
            output = subprocess.check_output(
                ["git", "-C", self.workspace, "diff-tree", "--no-commit-id", "--name-only", "-r", commit_hash],
                text=True, timeout=10
            )
            return [line.strip() for line in output.splitlines() if line.strip()]
        except subprocess.SubprocessError as e:
            self._emit_log(f"Error retrieving changed files: {e}")
            return []
```

> **» UPDATE (Major Enhancements):**
>
> * **WebSocket Integration:** The runner now calls `_emit_log()` and `_emit_terminal()` via `python-socketio` to stream logs & terminal output in real time.
> * **Robust JSON Parsing:** Up to `llm_json_retry_limit` attempts to get valid JSON from the LLM.
> * **Tool Error Feedback:** If a tool returns `status: "error"`, we append that error as a new user prompt to the LLM and loop, instead of aborting immediately.
> * **Dynamic Vision QA:** Finds a free port, builds & serves the project, takes screenshot, then kills the server.
> * **Commit & Push Error Handling:** Catches and logs `CalledProcessError` or `TimeoutExpired` from Git commands.
> * **Nuanced Qdrant Summaries:** Stores first 1 KB of file plus a timestamp in payload.

---

## 7. Frontend Modules (Updated for Vite & Socket.IO)

> We now use **Vite** instead of CRA, and **Socket.IO** for robust real-time communication. All environment variables prefixed with `VITE_` become available via `import.meta.env`.

---

### 7.1 `frontend/vite.config.js`

```js
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: process.env.VITE_BACKEND_URL || 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
      },
      '/ws': {
        target: process.env.VITE_BACKEND_URL ? process.env.VITE_BACKEND_URL.replace(/^http/, 'ws') : 'ws://localhost:8000',
        ws: true
      }
    }
  }
})
```

> **» UPDATE Highlights:**
>
> * Proxy API calls (`/api/*`) to `VITE_BACKEND_URL` (e.g. `http://localhost:8000`).
> * Proxy WebSocket connections (`/ws/*`) to `ws://localhost:8000`.

---

### 7.2 `frontend/package.json`

```jsonc
{
  "name": "ai-coder-frontend",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "socket.io-client": "^4.7.1",
    "axios": "^1.3.4",
    "tailwindcss": "^3.3.2",
    "@headlessui/react": "^1.8.0",
    "react-syntax-highlighter": "^15.5.0"
  },
  "devDependencies": {
    "@vitejs/plugin-react": "^4.0.0",
    "vite": "^4.2.1"
  }
}
```

> **» UPDATE Highlights:**
>
> * Switched from `react-scripts` to **Vite**.
> * Added `axios` for REST calls; `socket.io-client` for WebSocket.
> * Added `react-syntax-highlighter` for code previews in `FileExplorer.jsx`.

---

### 7.3 Component Overviews & Key Snippets

> Each component now uses **Socket.IO** and robust error handling. `SOCKET_URL` is configurable via `import.meta.env.VITE_BACKEND_URL`.

#### 7.3.1 `frontend/src/utils/socket.js`

```js
// frontend/src/utils/socket.js
import { io } from "socket.io-client";

const BACKEND_URL = import.meta.env.VITE_BACKEND_URL || "http://localhost:8000";

// Initialize & export a single Socket.IO client
export const socket = io(BACKEND_URL, {
  path: "/ws",
  transports: ["websocket"],
  reconnectionAttempts: 5
});

socket.on("connect_error", (err) => {
  console.error("Socket.IO connection error:", err);
});
```

> **» UPDATE:**
>
> * Reconnection attempts set to 5.
> * Path set to `/ws` so the proxy in `vite.config.js` can route properly.

---

#### 7.3.2 `frontend/src/components/TaskInput.jsx`

```jsx
// frontend/src/components/TaskInput.jsx
import React, { useState } from "react";
import axios from "axios";

export default function TaskInput({ onSessionCreated }) {
  const [instruction, setInstruction] = useState("");
  const [repoUrl, setRepoUrl] = useState("");
  const [branch, setBranch] = useState("main");
  const [vision, setVision] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    if (!instruction.trim() || !repoUrl.trim()) {
      setError("Both instruction and repository URL are required.");
      return;
    }
    try {
      const resp = await axios.post("/api/new-task", {
        instruction,
        repo_url: repoUrl,
        branch,
        vision
      });
      const { session_id } = resp.data;
      onSessionCreated(session_id);
    } catch (err) {
      console.error(err);
      setError(err.response?.data?.detail || "Failed to start the task.");
    }
  };

  return (
    <div className="p-4 border-b">
      <form onSubmit={handleSubmit} className="space-y-2">
        <div>
          <label className="block font-medium">Repository URL:</label>
          <input
            type="text"
            value={repoUrl}
            onChange={(e) => setRepoUrl(e.target.value)}
            className="w-full border rounded p-2"
            placeholder="https://github.com/username/repo.git"
          />
        </div>
        <div>
          <label className="block font-medium">Branch:</label>
          <input
            type="text"
            value={branch}
            onChange={(e) => setBranch(e.target.value)}
            className="w-full border rounded p-2"
          />
        </div>
        <div>
          <label className="block font-medium">Instruction:</label>
          <textarea
            value={instruction}
            onChange={(e) => setInstruction(e.target.value)}
            className="w-full border rounded p-2 h-24"
            placeholder="e.g., Add a responsive login form..."
          />
        </div>
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={vision}
            onChange={(e) => setVision(e.target.checked)}
            id="visionToggle"
          />
          <label htmlFor="visionToggle">Enable Vision QA</label>
        </div>
        {error && <p className="text-red-500">{error}</p>}
        <button
          type="submit"
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Submit
        </button>
      </form>
    </div>
  );
}
```

> **» UPDATE Highlights:**
>
> * Added a **Repository URL** field (previously implicit).
> * Added **Branch** input.
> * Added **Vision QA** toggle.
> * Uses **Axios** for REST calls.
> * Basic client-side validation & error display.

---

#### 7.3.3 `frontend/src/components/LogViewer.jsx`

```jsx
// frontend/src/components/LogViewer.jsx
import React, { useEffect, useRef } from "react";
import { socket } from "../utils/socket";

export default function LogViewer({ sessionId }) {
  const logEndRef = useRef(null);

  useEffect(() => {
    if (!sessionId) return;

    // Clear previous logs from UI
    document.getElementById("logContainer").innerHTML = "";

    // Listen for log events
    const handler = ({ session_id, message }) => {
      if (session_id !== sessionId) return;
      const container = document.getElementById("logContainer");
      const lineEl = document.createElement("pre");
      lineEl.textContent = message;
      container.appendChild(lineEl);
      if (logEndRef.current) {
        logEndRef.current.scrollIntoView({ behavior: "smooth" });
      }
    };

    socket.on("log", handler);
    return () => {
      socket.off("log", handler);
    };
  }, [sessionId]);

  return (
    <div className="flex-1 overflow-y-auto bg-gray-100 p-4">
      <div id="logContainer" className="space-y-1 font-mono text-sm"></div>
      <div ref={logEndRef} />
    </div>
  );
}
```

> **» UPDATE Highlights:**
>
> * Uses a **`<pre>`** element for each log line to preserve formatting.
> * Auto-scrolls to the bottom when new logs arrive.
> * Clears the previous session’s logs when `sessionId` changes.

---

#### 7.3.4 `frontend/src/components/FileExplorer.jsx`

```jsx
// frontend/src/components/FileExplorer.jsx
import React, { useState, useEffect } from "react";
import axios from "axios";
import { Light as SyntaxHighlighter } from "react-syntax-highlighter";
import javascript from "react-syntax-highlighter/dist/esm/languages/hljs/javascript";
import python from "react-syntax-highlighter/dist/esm/languages/hljs/python";
import { atomOneLight } from "react-syntax-highlighter/dist/esm/styles/hljs";

SyntaxHighlighter.registerLanguage("javascript", javascript);
SyntaxHighlighter.registerLanguage("python", python);

function TreeNode({ node, onClickFile }) {
  const [expanded, setExpanded] = useState(false);
  const isDir = node.children && node.children.length > 0;

  const toggle = () => {
    if (isDir) setExpanded(!expanded);
  };

  return (
    <div className="ml-4">
      <div className="flex items-center space-x-1 cursor-pointer" onClick={toggle}>
        {isDir && <span>{expanded ? "📂" : "📁"}</span>}
        {!isDir && <span>📄</span>}
        <span
          onClick={() => !isDir && onClickFile(node.path)}
          className={!isDir ? "underline" : ""}
        >
          {node.name}
        </span>
      </div>
      {isDir && expanded && (
        <div className="ml-4">
          {node.children.map((child) => (
            <TreeNode key={child.path} node={child} onClickFile={onClickFile} />
          ))}
        </div>
      )}
    </div>
  );
}

export default function FileExplorer({ sessionId }) {
  const [tree, setTree] = useState(null);
  const [fileContent, setFileContent] = useState("");
  const [selectedFile, setSelectedFile] = useState("");

  useEffect(() => {
    if (!sessionId) return;
    // Fetch directory tree
    axios
      .get(`/api/file-tree/${sessionId}`)
      .then((resp) => setTree(resp.data))
      .catch((err) => console.error("Failed to load file tree:", err));
  }, [sessionId]);

  const onClickFile = async (fullPath) => {
    setSelectedFile(fullPath);
    // Compute path relative to workspace
    const relativePath = fullPath.split(`/workspace/${sessionId}/`)[1];
    if (!relativePath) {
      setFileContent("Invalid file path.");
      return;
    }
    try {
      const resp = await axios.get(`/api/read-file/${sessionId}`, {
        params: { file_path: relativePath }
      });
      setFileContent(resp.data.content);
    } catch (err) {
      console.error(err);
      setFileContent("Failed to load file content.");
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="h-2/5 overflow-y-auto border-b">
        {tree ? <TreeNode node={tree} onClickFile={onClickFile} /> : <p>Loading files...</p>}
      </div>
      <div className="h-3/5 overflow-y-auto p-2 bg-gray-50">
        {selectedFile ? (
          <>
            <h3 className="font-bold mb-2">{selectedFile}</h3>
            <SyntaxHighlighter
              language={selectedFile.endsWith(".js") || selectedFile.endsWith(".jsx") ? "javascript" : "python"}
              style={atomOneLight}
            >
              {fileContent}
            </SyntaxHighlighter>
          </>
        ) : (
          <p>Select a file to view its contents.</p>
        )}
      </div>
    </div>
  );
}
```

> **» UPDATE Highlights:**
>
> * Renders a **recursive directory tree** using `TreeNode`.
> * When a file is clicked, calls `/api/read-file/{sessionId}?file_path=<relativePath>`.
> * Displays code with **syntax highlighting** (JavaScript & Python).

---

#### 7.3.5 `frontend/src/components/TerminalEmulator.jsx`

```jsx
// frontend/src/components/TerminalEmulator.jsx
import React, { useEffect, useRef } from "react";
import { socket } from "../utils/socket";

export default function TerminalEmulator({ sessionId }) {
  const termEndRef = useRef(null);

  useEffect(() => {
    if (!sessionId) return;

    const handler = ({ session_id, output }) => {
      if (session_id !== sessionId) return;
      const container = document.getElementById("termContainer");
      const lineEl = document.createElement("pre");
      lineEl.textContent = `$ ${output}`;
      container.appendChild(lineEl);
      if (termEndRef.current) {
        termEndRef.current.scrollIntoView({ behavior: "smooth" });
      }
    };

    socket.on("terminal_output", handler);
    return () => {
      socket.off("terminal_output", handler);
    };
  }, [sessionId]);

  return (
    <div className="h-32 overflow-y-auto bg-black text-green-200 p-2 font-mono">
      <div id="termContainer" />
      <div ref={termEndRef} />
    </div>
  );
}
```

> **» UPDATE Highlights:**
>
> * Shows raw terminal output prefixed with “`$ `” for readability.
> * Auto-scrolls as new output arrives.

---

#### 7.3.6 `frontend/src/App.jsx`

```jsx
// frontend/src/App.jsx
import React, { useState, useEffect } from "react";
import TaskInput from "./components/TaskInput";
import LogViewer from "./components/LogViewer";
import FileExplorer from "./components/FileExplorer";
import TerminalEmulator from "./components/TerminalEmulator";

function App() {
  const [sessionId, setSessionId] = useState(null);

  const handleSessionCreated = (id) => {
    setSessionId(id);
  };

  return (
    <div className="h-screen flex flex-col">
      <header className="bg-gray-800 text-white p-4">
        <h1 className="text-xl font-bold">AI Coder Agent</h1>
      </header>
      <div className="flex flex-1 overflow-hidden">
        <aside className="w-1/4 border-r overflow-y-auto">
          {sessionId ? <FileExplorer sessionId={sessionId} /> : <p className="p-4">No session active.</p>}
        </aside>
        <section className="flex-1 flex flex-col">
          <TaskInput onSessionCreated={handleSessionCreated} />
          {sessionId && <LogViewer sessionId={sessionId} />}
          {sessionId && <TerminalEmulator sessionId={sessionId} />}
        </section>
      </div>
    </div>
  );
}

export default App;
```

> **» UPDATE Highlights:**
>
> * Only renders `FileExplorer`, `LogViewer`, and `TerminalEmulator` **after** `sessionId` is set.
> * Basic layout separation: Left pane = file tree; right pane = task input + logs + terminal.

---

## 8. Putting It All Together: Running & Testing

1. **Prepare `.env` & `.gitignore`**

   * Copy `.env.example` → `.env` and fill in your API keys.
   * Ensure `.gitignore` ignores `.env`, `backend/data`, `backend/workspace`, `qdrant_storage`, `node_modules`, etc.

2. **Build & Run**

   ```bash
   docker-compose up --build
   ```

   * **`frontend`** (Vite) will be available on `http://localhost:3000`.
   * **`backend`** (FastAPI + Socket.IO) on `http://localhost:8000`.
   * **`qdrant`** (if self-hosted) on `http://localhost:6333`.

3. **Health Checks**

   * Visit `http://localhost:8000/docs` to view FastAPI’s built-in OpenAPI UI.
   * Call `GET /health` (if implemented) or `GET /api/file-tree/<nonexistent>` to verify error responses.

4. **Basic End-to-End Test**

   * Create a simple GitHub repo (`https://github.com/youruser/test-ai-agent.git`) containing a basic React app or Python project.
   * In the UI, enter:

     ```
     Repo URL: https://github.com/youruser/test-ai-agent.git
     Branch: main
     Instruction: Create a new React component at src/components/TestWidget.jsx that displays "Test Widget" in a styled box. Then import it into src/App.jsx.
     Vision QA: (unchecked for now)
     ```
   * Click **Submit**.
   * In the LogViewer, watch the logs:

     1. “Cloning or pulling repository…”
     2. “Retrieving relevant context…”
     3. “Calling LLM for actions…”
     4. “Invoking tool: write\_file…” → “Tool 'write\_file' result: {status: ok, output: 'Wrote to ...'}”
     5. “Running lint & test…” → output from `eslint` or `flake8`.
     6. “All checks passed. Committing & pushing…” → commit hash.
     7. “Upserted X embeddings into Qdrant.”
   * Check GitHub: your test repo’s `main` branch should now have:

     * `src/components/TestWidget.jsx`
     * `src/App.jsx` updated.
   * In the Left Pane (FileExplorer), browse the updated files. Click one to view content.

5. **Trigger a Lint Error**

   * Re-submit the same instruction but with a bug (e.g. missing closing `}` in JSX).
   * The AI should generate invalid code → lint fails → the loop feeds that error back to LLM → LLM returns a fix → final code is correct.

6. **Enable Vision QA**

   * Check the **Vision QA** box when submitting.
   * The logs should show:

     * “Performing Vision QA…”
     * “npm install … npm run build … serve …”
     * “Screenshot taken. Calling vision model …”
     * If vision model finds issues, AI loops again. Otherwise: “Vision QA passed.”

7. **Concurrent Sessions**

   * Open two browser tabs. Use different GitHub repos or branches.
   * Ensure both sessions’ logs appear independently.
   * Check `backend/data/agent_memory.db` → two different session entries with distinct `updated_at`.

8. **Simulate Invalid JSON**

   * Temporarily tweak the prompt to encourage the LLM to output something other than JSON. Confirm the code attempts up to `llm_json_retry_limit` times, then errors gracefully.

9. **Inspect Qdrant Index**

   * Connect to Qdrant Cloud Dashboard → `code_context` collection.
   * See that new points (ID: `<commit>:<file>`) have been upserted with payload including `file_path`, `commit_hash`, `summary`, `timestamp`.

---

## 9. Incremental Development Roadmap

1. **Week 1: Core Backend & Session Store**

   * Implement `config.py`, `sessions.py`, `qdrant_client.py`, and `llm_client.py`.
   * Write unit tests for these modules (mocking LLM calls & Qdrant).
   * Verify SQLite persistence and correct retrieval of settings.

2. **Week 2: Tool Framework & Git/File I/O**

   * Implement `git_utils.py`, `file_utils.py`, and `tools.py` stubs.
   * In `task_runner.py`, build a minimal loop that clones a known public repo and lists directory.
   * Add `/api/file-tree` and `/api/read-file` endpoints.
   * Test local file browsing in the container by hand.

3. **Week 3: Lint/Test Utilities & Task Runner Loop**

   * Install `flake8`, `pytest`, `eslint` in the Dockerfile.
   * Implement `lint_test_utils.py`.
   * Flesh out `task_runner.py` to:

     * Build prompt, call LLM for a hard-coded JSON instructing “read\_file” → fetch a README → exit.
     * Run `lint_and_test_tool()`.
   * Test end-to-end with a small Python project.

4. **Week 4: Context Management & Memory**

   * Implement `context_manager.py` and `memory.py`.
   * Populate Qdrant with some sample code from your test repo (manually).
   * Run a user instruction that triggers “retrieve context,” verify summaries/summons.

5. **Week 5: Robust JSON Parsing & Error Feedback**

   * Add the `llm_json_retry_limit` logic.
   * Force the LLM to return invalid JSON and ensure the retry loop works.
   * Implement “feed tool error back to LLM” logic in `task_runner.py`.

6. **Week 6: Vision QA Integration**

   * Add `screenshot_utils.py` and `vision.py`.
   * Ensure Puppeteer dependencies in Docker; test screenshot of a static HTML.
   * Wire up the “vision\_enabled” toggle and test full loop including screenshot & vision feedback.

7. **Week 7: Frontend (Vite + Socket.IO)**

   * Scaffold Vite React project.
   * Add `TaskInput`, `LogViewer`, `FileExplorer`, `TerminalEmulator`.
   * Integrate Socket.IO; test streaming logs from a dummy backend.
   * Connect to real backend; ensure file tree & logs show correctly.

8. **Week 8: End-to-End Polish**

   * Perform concurrency testing (multiple sessions).
   * Add robust error UI in frontend (e.g., if backend errors, show “Task failed: <detail>”).
   * Add final touches: Tailwind styling, mobile responsiveness, basic CI checks.
   * Write `README.md` with up-to-date instructions.

---

## 10. Security & Maintenance Notes

1. **Sanitize All Inputs**

   * **`repo_url`** is validated to start with `https://` and contain `github.com`. Consider stricter checks (regex or allow only whitelisted orgs).
   * **File paths** in tools are validated to be under `workspace_root` (via `os.path.realpath`). This prevents directory traversal.
   * **Commands** passed to `run_command_tool` use `shlex.split` and never run via `shell=True`. This prevents injection.

2. **Protect API Keys**

   * Do **not** expose `OPENROUTER_API_KEY` or `QDRANT_API_KEY` in frontend code or responses.
   * Keep `.env` out of version control (`.gitignore`).

3. **Limit Resource Usage**

   * **Timeouts** on all subprocess calls (`git`, `npm`, `lint/test`, Puppeteer) to avoid hung processes.
   * **Max iterations** in `TaskRunner` to prevent infinite loops if LLM never fixes the errors.
   * **Max token limits** and **summarization thresholds** so you never exceed the 164 k context window.

4. **Concurrency & SQLite**

   * Using `sqlite3.connect(..., check_same_thread=False)` allows multiple background threads to access the DB.
   * If you expect high concurrency, consider migrating to **Postgres** (e.g. via Supabase) and swapping out `sessions.py`.

5. **Logging & Monitoring**

   * All logs are emitted via **Socket.IO** to assist debugging.
   * Consider adding a persistent log file (e.g. `/app/data/agent.log`) by configuring Python’s `logging` module to also write to a file.
   * Monitor Qdrant usage in the Cloud dashboard to stay under free-tier quotas.

6. **Dependency Updates**

   * Periodically update base Docker images (`python:3.10-slim`, `node:18-alpine`, etc.) to receive security patches.
   * Pin Qdrant version (e.g. `qdrant/qdrant:v1.8.3`) and update it as needed.
   * Keep an eye on model deprecations in OpenRouter (e.g. if `deepseek-r1-0528` is replaced).

---

## 11. Appendix: Example `.gitignore`

```gitignore
# Environment variables
.env

# Backend data & workspace
/backend/data/
/backend/workspace/
/qdrant_storage/

# Node modules & build outputs
/frontend/node_modules/
/frontend/dist/
/frontend/build/

# Python caches
/backend/__pycache__/
/backend/*.pyc
/__pycache__/

# Logs
/backend/data/agent.log

# IDE & OS files
.vscode/
/.idea/
.DS_Store
```

---
