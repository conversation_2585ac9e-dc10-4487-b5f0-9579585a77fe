interactions:
- request:
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '154'
      content-type:
      - application/json
      host:
      - generativelanguage.googleapis.com
    method: POST
    parsed_body:
      contents:
      - parts:
        - text: What is the capital of France?
        role: user
      generation_config:
        stop_sequences:
        - Paris
      safety_settings: null
    uri: https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
  response:
    headers:
      alt-svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      content-length:
      - '416'
      content-type:
      - application/json; charset=UTF-8
      server-timing:
      - gfet4t7; dur=314
      transfer-encoding:
      - chunked
      vary:
      - Origin
      - X-Origin
      - Referer
    parsed_body:
      candidates:
      - content:
          parts:
          - text: ''
          role: model
        finishReason: STOP
      modelVersion: gemini-1.5-flash
      usageMetadata:
        promptTokenCount: 7
        promptTokensDetails:
        - modality: TEXT
          tokenCount: 7
        totalTokenCount: 7
    status:
      code: 200
      message: OK
version: 1
