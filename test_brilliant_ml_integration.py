#!/usr/bin/env python3
"""
🧠🚀 BRILLIANT ML ERROR INTELLIGENCE INTEGRATION TEST 🚀🧠

This script demonstrates the AMAZING integration between:
- Error Handling System
- Machine Learning Error Intelligence
- Pattern Recognition & Learning
- Cross-system Integration

FEATURES TESTED:
✅ ML Pattern Detection
✅ Error Classification
✅ Recovery Learning
✅ Intelligence Statistics
✅ Real-time Adaptation
"""

import requests
import json
import time
from datetime import datetime

def print_banner(text, color="🔥"):
    print(f"\n{color} {text} {color}")
    print("=" * (len(text) + 6))

def test_ml_integration():
    """Test the BRILLIANT ML integration!"""
    base_url = "http://localhost:8000"
    
    print_banner("BRILLIANT ML ERROR INTELLIGENCE TEST", "🧠")
    
    # Test 1: Check ML Intelligence Status
    print("\n🔍 Testing ML Intelligence Status...")
    try:
        response = requests.get(f"{base_url}/api/error-handling/health")
        health = response.json()
        ml_stats = health['component_health']['ml_intelligence']
        
        print(f"✅ ML Intelligence Enabled: {ml_stats['enabled']}")
        print(f"🧠 Patterns Learned: {ml_stats['patterns_learned']}")
        print(f"📊 Total Patterns: {ml_stats['total_patterns']}")
        print(f"📈 Error History Size: {ml_stats['error_history_size']}")
        
        if ml_stats['top_error_types']:
            print("🏆 Top Error Types:")
            for error_type in ml_stats['top_error_types'][:3]:
                print(f"   • {error_type['error_type']}: {error_type['total_occurrences']} occurrences "
                      f"({error_type['avg_confidence']:.1%} confidence)")
    except Exception as e:
        print(f"❌ ML Status Test Failed: {e}")
        return False
    
    # Test 2: Trigger ML Learning
    print("\n🔥 Testing ML Pattern Learning...")
    error_types = ["connection", "timeout", "model_retry"]
    
    for i, error_type in enumerate(error_types, 1):
        try:
            print(f"   🧪 Testing {error_type} error (attempt {i})...")
            
            response = requests.post(
                f"{base_url}/api/error-handling/test-error-handling",
                json={
                    "error_type": error_type,
                    "operation": f"brilliant_test_{error_type}_{i}",
                    "session_id": f"brilliant_session_{i}"
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                ml_info = result['handling_result']['ml_intelligence']
                
                print(f"      ✅ Error handled successfully")
                print(f"      🧠 ML Enabled: {ml_info['enabled']}")
                print(f"      🎯 Pattern Detected: {ml_info['pattern_detected']}")
                
                if ml_info['pattern_id']:
                    print(f"      🔍 Pattern ID: {ml_info['pattern_id']}")
                    print(f"      💡 ML Suggestions: {len(ml_info['ml_suggestions'])}")
                
            time.sleep(0.5)  # Brief pause between tests
            
        except Exception as e:
            print(f"      ❌ Test failed: {e}")
    
    # Test 3: Check Updated ML Statistics
    print("\n📊 Checking Updated ML Intelligence...")
    try:
        response = requests.get(f"{base_url}/api/error-handling/health")
        health = response.json()
        ml_stats = health['component_health']['ml_intelligence']
        
        print(f"🧠 Final ML Statistics:")
        print(f"   • Patterns Learned: {ml_stats['patterns_learned']}")
        print(f"   • Total Patterns: {ml_stats['total_patterns']}")
        print(f"   • Error History: {ml_stats['error_history_size']} entries")
        
        if ml_stats['top_error_types']:
            print(f"   • Top Error Types: {len(ml_stats['top_error_types'])}")
            for error_type in ml_stats['top_error_types']:
                print(f"     - {error_type['error_type']}: "
                      f"{error_type['total_occurrences']} occurrences "
                      f"({error_type['avg_confidence']:.1%} confidence)")
        
    except Exception as e:
        print(f"❌ Final stats check failed: {e}")
    
    # Test 4: Comprehensive System Test
    print("\n🎯 Running Comprehensive System Test...")
    try:
        response = requests.post(f"{base_url}/api/error-handling/run-system-test")
        test_results = response.json()
        
        summary = test_results['test_summary']
        print(f"✅ System Test Results:")
        print(f"   • Total Tests: {summary['total_tests']}")
        print(f"   • Passed Tests: {summary['passed_tests']}")
        print(f"   • Success Rate: {summary['success_rate']:.1%}")
        print(f"   • ML Intelligence Enabled: {summary['ml_intelligence_enabled']}")
        print(f"   • System Ready: {test_results['system_ready']}")
        
        # Check ML Intelligence specific test
        ml_test = test_results['test_results'].get('ml_intelligence_test', {})
        print(f"   • ML Intelligence Test: {'✅ PASSED' if ml_test.get('passed') else '❌ FAILED'}")
        
        return test_results['system_ready']
        
    except Exception as e:
        print(f"❌ Comprehensive test failed: {e}")
        return False

def main():
    """Run the BRILLIANT ML integration test!"""
    print_banner("🧠 BRILLIANT ML ERROR INTELLIGENCE INTEGRATION 🧠", "🚀")
    print(f"🕒 Test started at: {datetime.now()}")
    
    success = test_ml_integration()
    
    print_banner("TEST RESULTS", "🎯")
    
    if success:
        print("🎉 BRILLIANT SUCCESS! 🎉")
        print("✅ ML Error Intelligence is FULLY INTEGRATED!")
        print("✅ Pattern Recognition is WORKING!")
        print("✅ Learning from Recovery is ACTIVE!")
        print("✅ Cross-system Integration is PERFECT!")
        print("\n🧠 The AI system now has INTELLIGENT error handling with:")
        print("   • Real-time pattern detection")
        print("   • Learning from successes and failures")
        print("   • Adaptive recovery strategies")
        print("   • Comprehensive error intelligence")
    else:
        print("❌ Some components need attention")
        print("🔧 Check the logs for specific issues")
    
    print(f"\n🕒 Test completed at: {datetime.now()}")
    print_banner("🚀 BRILLIANT ML INTEGRATION COMPLETE! 🚀", "🎉")

if __name__ == "__main__":
    main()
