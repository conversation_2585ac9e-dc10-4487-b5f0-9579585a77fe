interactions:
- request:
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '136'
      content-type:
      - application/json
      host:
      - api.openai.com
    method: POST
    parsed_body:
      input:
      - content: What is the capital of France?
        role: user
      instructions: ''
      model: gpt-4o
      stream: true
      temperature: -1
    uri: https://api.openai.com/v1/responses
  response:
    headers:
      alt-svc:
      - h3=":443"; ma=86400
      connection:
      - keep-alive
      content-length:
      - '236'
      content-type:
      - application/json
      openai-organization:
      - pydantic-28gund
      openai-processing-ms:
      - '15'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
    parsed_body:
      error:
        code: decimal_below_min_value
        message: 'Invalid ''temperature'': decimal below minimum value. Expected a value >= 0, but got -1 instead.'
        param: temperature
        type: invalid_request_error
    status:
      code: 400
      message: Bad Request
version: 1
