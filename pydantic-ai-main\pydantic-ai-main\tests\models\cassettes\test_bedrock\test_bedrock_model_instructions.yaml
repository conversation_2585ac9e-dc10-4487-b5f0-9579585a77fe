interactions:
- request:
    body: '{"messages": [{"role": "user", "content": [{"text": "What is the capital of France?"}]}], "system": [{"text": "You
      are a helpful assistant."}], "inferenceConfig": {}}'
    headers:
      amz-sdk-invocation-id:
      - !!binary |
        YmM2NjFmZjctZDZhYi00NzI5LWIyNTQtNjc5MzA1MGMyOGU3
      amz-sdk-request:
      - !!binary |
        YXR0ZW1wdD0x
      content-length:
      - '166'
      content-type:
      - !!binary |
        YXBwbGljYXRpb24vanNvbg==
    method: POST
    uri: https://bedrock-runtime.us-east-1.amazonaws.com/model/us.amazon.nova-pro-v1%3A0/converse
  response:
    headers:
      connection:
      - keep-alive
      content-length:
      - '521'
      content-type:
      - application/json
    parsed_body:
      metrics:
        latencyMs: 863
      output:
        message:
          content:
          - text: The capital of France is Paris. Paris is not only the political and economic hub of the country but also
              a major center for culture, fashion, art, and tourism. It is renowned for its rich history, iconic landmarks
              such as the Eiffel Tower, Notre-Dame Cathedral, and the Louvre Museum, as well as its influence on global culture
              and cuisine.
          role: assistant
      stopReason: end_turn
      usage:
        inputTokens: 13
        outputTokens: 71
        totalTokens: 84
    status:
      code: 200
      message: OK
version: 1
