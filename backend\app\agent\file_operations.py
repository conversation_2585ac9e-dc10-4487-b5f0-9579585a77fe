#!/usr/bin/env python3
"""
File Operations Module for AI Coder Agent - Phase 3
Provides secure file system operations with Docker compatibility.

Features:
- Safe file read/write with encoding detection
- Atomic operations with backup support
- Path traversal protection
- Workspace boundary enforcement
- File watching and change detection
- Comprehensive error handling
"""

import os
import shutil
import tempfile
import time
from pathlib import Path
from typing import Dict, List, Optional, Union, Any, Tuple
import chardet
import logging
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import asyncio
from concurrent.futures import ThreadPoolExecutor

# Import configuration
try:
    from ...config import settings
    from ...app.core.project_manager import project_manager
except ImportError:
    from config import settings
    try:
        from backend.app.core.project_manager import project_manager
    except ImportError:
        project_manager = None

logger = logging.getLogger(__name__)

class FileOperationError(Exception):
    """Custom exception for file operation errors."""
    pass

class SecurityError(FileOperationError):
    """Exception for security-related file operation errors."""
    pass

class FileChangeHandler(FileSystemEventHandler):
    """Handler for file system change events."""
    
    def __init__(self, callback=None):
        self.callback = callback
        self.debounce_time = getattr(settings, 'watch_debounce_seconds', 0.5)
        self.pending_events = {}
        
    def on_modified(self, event):
        if not event.is_directory:
            self._debounce_event('modified', event.src_path)
    
    def on_created(self, event):
        if not event.is_directory:
            self._debounce_event('created', event.src_path)
    
    def on_deleted(self, event):
        if not event.is_directory:
            self._debounce_event('deleted', event.src_path)
    
    def _debounce_event(self, event_type: str, file_path: str):
        """Debounce file events to prevent excessive processing."""
        current_time = time.time()
        key = f"{event_type}:{file_path}"
        
        # Cancel previous event if within debounce time
        if key in self.pending_events:
            self.pending_events[key].cancel()
        
        # Schedule new event
        timer = asyncio.get_event_loop().call_later(
            self.debounce_time,
            self._process_event,
            event_type,
            file_path
        )
        self.pending_events[key] = timer
    
    def _process_event(self, event_type: str, file_path: str):
        """Process debounced file event."""
        key = f"{event_type}:{file_path}"
        if key in self.pending_events:
            del self.pending_events[key]
        
        if self.callback:
            try:
                self.callback(event_type, file_path)
            except Exception as e:
                logger.error(f"Error processing file event {event_type} for {file_path}: {e}")

class FileOperations:
    """Secure file operations with project-scoped workspace boundary enforcement."""

    def __init__(self, project_slug: Optional[str] = None):
        # Project-aware workspace setup
        if project_manager and project_slug:
            # Use project-specific workspace
            project = None
            for p in project_manager.active_projects.values():
                if p.slug == project_slug:
                    project = p
                    break

            if project:
                self.workspace_root = project.get_workspace_path(str(project_manager.base_workspace))
                self.project = project
                self.max_file_size = project.config.max_file_size
                self.allowed_extensions = project.config.allowed_extensions
            else:
                # Fallback to default workspace
                self.workspace_root = Path(getattr(settings, 'workspace_root', '/app/workspace'))
                self.project = None
                self.max_file_size = getattr(settings, 'max_file_size', 10_000_000)
                self.allowed_extensions = getattr(settings, 'allowed_extensions', [
                    '.py', '.js', '.ts', '.java', '.cpp', '.go', '.rs', '.md', '.txt',
                    '.json', '.yaml', '.yml', '.html', '.css', '.sql', '.sh', '.bat'
                ])
        else:
            # Use current project or default workspace
            if project_manager and project_manager.current_project:
                self.workspace_root = project_manager.get_project_workspace_path()
                self.project = project_manager.current_project
                self.max_file_size = self.project.config.max_file_size
                self.allowed_extensions = self.project.config.allowed_extensions
            else:
                # Fallback to default workspace
                self.workspace_root = Path(getattr(settings, 'workspace_root', '/app/workspace'))
                self.project = None
                self.max_file_size = getattr(settings, 'max_file_size', 10_000_000)
                self.allowed_extensions = getattr(settings, 'allowed_extensions', [
                    '.py', '.js', '.ts', '.java', '.cpp', '.go', '.rs', '.md', '.txt',
                    '.json', '.yaml', '.yml', '.html', '.css', '.sql', '.sh', '.bat'
                ])

        self.backup_enabled = getattr(settings, 'backup_enabled', True)
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.watchers = {}  # file_path -> Observer

        # Ensure workspace root exists
        self.workspace_root.mkdir(parents=True, exist_ok=True)

        project_info = f" (Project: {self.project.slug})" if self.project else " (No project)"
        logger.info(f"FileOperations initialized with workspace: {self.workspace_root}{project_info}")
    
    def _validate_path(self, path: Union[str, Path], allow_create: bool = False) -> Path:
        """
        Validate file path for security and workspace boundaries.
        
        Args:
            path: File path to validate
            allow_create: Whether to allow non-existent paths
            
        Returns:
            Validated Path object
            
        Raises:
            SecurityError: If path is outside workspace or contains traversal
            FileOperationError: If path doesn't exist and allow_create is False
        """
        try:
            # Convert to Path object
            path_obj = Path(path).resolve()
            
            # Check if path is within workspace
            try:
                path_obj.relative_to(self.workspace_root.resolve())
            except ValueError:
                raise SecurityError(f"Path outside workspace: {path}")
            
            # Check for path traversal attempts
            if '..' in str(path) or str(path).startswith('/'):
                if not str(path_obj).startswith(str(self.workspace_root.resolve())):
                    raise SecurityError(f"Path traversal detected: {path}")
            
            # Check if path exists (if required)
            if not allow_create and not path_obj.exists():
                raise FileOperationError(f"Path does not exist: {path}")
            
            return path_obj
            
        except Exception as e:
            if isinstance(e, (SecurityError, FileOperationError)):
                raise
            raise FileOperationError(f"Invalid path: {path} - {e}")
    
    def _validate_file_extension(self, path: Path) -> bool:
        """Check if file extension is allowed."""
        if not self.allowed_extensions:
            return True
        return path.suffix.lower() in [ext.lower() for ext in self.allowed_extensions]
    
    def _detect_encoding(self, file_path: Path) -> str:
        """Detect file encoding using chardet."""
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(min(10000, os.path.getsize(file_path)))  # Read first 10KB
                result = chardet.detect(raw_data)
                encoding = result.get('encoding', 'utf-8')
                confidence = result.get('confidence', 0)
                
                # Fall back to utf-8 if confidence is low
                if confidence < 0.7:
                    encoding = 'utf-8'
                
                logger.debug(f"Detected encoding for {file_path}: {encoding} (confidence: {confidence})")
                return encoding
        except Exception as e:
            logger.warning(f"Encoding detection failed for {file_path}: {e}, using utf-8")
            return 'utf-8'
    
    def _create_backup(self, file_path: Path) -> Optional[Path]:
        """Create backup of existing file."""
        if not self.backup_enabled or not file_path.exists():
            return None
        
        try:
            backup_dir = file_path.parent / '.backups'
            backup_dir.mkdir(exist_ok=True)
            
            timestamp = int(time.time())
            backup_name = f"{file_path.name}.backup.{timestamp}"
            backup_path = backup_dir / backup_name
            
            shutil.copy2(file_path, backup_path)
            logger.debug(f"Created backup: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.warning(f"Failed to create backup for {file_path}: {e}")
            return None
    
    async def read_file(self, path: Union[str, Path], encoding: Optional[str] = None) -> Dict[str, Any]:
        """
        Read file content with encoding detection and error handling.
        
        Args:
            path: File path to read
            encoding: Optional encoding override
            
        Returns:
            Dict with file content and metadata
        """
        try:
            file_path = self._validate_path(path)
            
            # Check file size
            file_size = file_path.stat().st_size
            if file_size > self.max_file_size:
                raise FileOperationError(f"File too large: {file_size} bytes (max: {self.max_file_size})")
            
            # Detect encoding if not provided
            if encoding is None:
                encoding = self._detect_encoding(file_path)
            
            # Read file content
            def _read():
                with open(file_path, 'r', encoding=encoding, errors='replace') as f:
                    return f.read()
            
            content = await asyncio.get_event_loop().run_in_executor(self.executor, _read)
            
            # Get file metadata
            stat = file_path.stat()
            
            return {
                'status': 'success',
                'content': content,
                'metadata': {
                    'path': str(file_path),
                    'size': file_size,
                    'encoding': encoding,
                    'modified': stat.st_mtime,
                    'created': stat.st_ctime,
                    'permissions': oct(stat.st_mode)[-3:],
                    'extension': file_path.suffix
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to read file {path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def write_file(self, path: Union[str, Path], content: str,
                        encoding: str = 'utf-8', create_dirs: bool = True) -> Dict[str, Any]:
        """
        Write file content with atomic operations and backup support.

        Args:
            path: File path to write
            content: Content to write
            encoding: File encoding
            create_dirs: Whether to create parent directories

        Returns:
            Dict with operation result
        """
        try:
            file_path = self._validate_path(path, allow_create=True)

            # Validate file extension
            if not self._validate_file_extension(file_path):
                raise SecurityError(f"File extension not allowed: {file_path.suffix}")

            # Create parent directories if needed
            if create_dirs:
                file_path.parent.mkdir(parents=True, exist_ok=True)

            # Create backup if file exists
            backup_path = self._create_backup(file_path)

            # Write to temporary file first (atomic operation)
            temp_path = None
            try:
                with tempfile.NamedTemporaryFile(
                    mode='w',
                    encoding=encoding,
                    dir=file_path.parent,
                    delete=False,
                    suffix='.tmp'
                ) as temp_file:
                    temp_path = Path(temp_file.name)

                    def _write():
                        temp_file.write(content)
                        temp_file.flush()
                        os.fsync(temp_file.fileno())

                    await asyncio.get_event_loop().run_in_executor(self.executor, _write)

                # Atomic move
                shutil.move(str(temp_path), str(file_path))

                # Get file metadata
                stat = file_path.stat()

                return {
                    'status': 'success',
                    'path': str(file_path),
                    'size': stat.st_size,
                    'backup_created': backup_path is not None,
                    'backup_path': str(backup_path) if backup_path else None
                }

            except Exception as e:
                # Clean up temp file on error
                if temp_path and temp_path.exists():
                    temp_path.unlink()
                raise

        except Exception as e:
            logger.error(f"Failed to write file {path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def create_file(self, path: Union[str, Path], content: str = '',
                         overwrite: bool = False) -> Dict[str, Any]:
        """
        Create new file with conflict resolution.

        Args:
            path: File path to create
            content: Initial content
            overwrite: Whether to overwrite existing file

        Returns:
            Dict with operation result
        """
        try:
            file_path = self._validate_path(path, allow_create=True)

            # Check if file exists
            if file_path.exists() and not overwrite:
                return {
                    'status': 'error',
                    'error': f'File already exists: {path}',
                    'error_type': 'FileExistsError'
                }

            return await self.write_file(path, content)

        except Exception as e:
            logger.error(f"Failed to create file {path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def delete_file(self, path: Union[str, Path], create_backup: bool = True) -> Dict[str, Any]:
        """
        Delete file with safety checks and backup options.

        Args:
            path: File path to delete
            create_backup: Whether to create backup before deletion

        Returns:
            Dict with operation result
        """
        try:
            file_path = self._validate_path(path)

            if not file_path.is_file():
                raise FileOperationError(f"Not a file: {path}")

            backup_path = None
            if create_backup:
                backup_path = self._create_backup(file_path)

            # Delete file
            def _delete():
                file_path.unlink()

            await asyncio.get_event_loop().run_in_executor(self.executor, _delete)

            return {
                'status': 'success',
                'deleted_path': str(file_path),
                'backup_created': backup_path is not None,
                'backup_path': str(backup_path) if backup_path else None
            }

        except Exception as e:
            logger.error(f"Failed to delete file {path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def list_directory(self, path: Union[str, Path], recursive: bool = False,
                           include_hidden: bool = False, filter_extensions: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        List directory contents with filtering options.

        Args:
            path: Directory path to list
            recursive: Whether to list recursively
            include_hidden: Whether to include hidden files
            filter_extensions: Optional list of extensions to filter by

        Returns:
            Dict with directory listing
        """
        try:
            dir_path = self._validate_path(path)

            if not dir_path.is_dir():
                raise FileOperationError(f"Not a directory: {path}")

            def _list_dir():
                entries = []

                if recursive:
                    pattern = '**/*' if include_hidden else '**/[!.]*'
                    for item in dir_path.glob(pattern):
                        if item.is_file() or item.is_dir():
                            entries.append(self._get_file_info(item))
                else:
                    for item in dir_path.iterdir():
                        if not include_hidden and item.name.startswith('.'):
                            continue
                        entries.append(self._get_file_info(item))

                # Filter by extensions if specified
                if filter_extensions:
                    filter_exts = [ext.lower() for ext in filter_extensions]
                    entries = [e for e in entries if e['is_directory'] or e['extension'].lower() in filter_exts]

                return sorted(entries, key=lambda x: (not x['is_directory'], x['name'].lower()))

            entries = await asyncio.get_event_loop().run_in_executor(self.executor, _list_dir)

            return {
                'status': 'success',
                'path': str(dir_path),
                'entries': entries,
                'total_count': len(entries)
            }

        except Exception as e:
            logger.error(f"Failed to list directory {path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    def _get_file_info(self, path: Path) -> Dict[str, Any]:
        """Get file/directory information."""
        try:
            stat = path.stat()
            return {
                'name': path.name,
                'path': str(path),
                'relative_path': str(path.relative_to(self.workspace_root)),
                'is_directory': path.is_dir(),
                'is_file': path.is_file(),
                'size': stat.st_size if path.is_file() else 0,
                'modified': stat.st_mtime,
                'created': stat.st_ctime,
                'permissions': oct(stat.st_mode)[-3:],
                'extension': path.suffix if path.is_file() else '',
                'is_hidden': path.name.startswith('.')
            }
        except Exception as e:
            logger.warning(f"Failed to get info for {path}: {e}")
            return {
                'name': path.name,
                'path': str(path),
                'error': str(e)
            }

    async def get_file_info(self, path: Union[str, Path]) -> Dict[str, Any]:
        """
        Get detailed file/directory metadata.

        Args:
            path: File or directory path

        Returns:
            Dict with file information
        """
        try:
            file_path = self._validate_path(path)

            def _get_info():
                return self._get_file_info(file_path)

            info = await asyncio.get_event_loop().run_in_executor(self.executor, _get_info)

            return {
                'status': 'success',
                **info
            }

        except Exception as e:
            logger.error(f"Failed to get file info for {path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    def watch_file_changes(self, path: Union[str, Path], callback=None) -> Dict[str, Any]:
        """
        Start watching file/directory for changes.

        Args:
            path: Path to watch
            callback: Optional callback function for change events

        Returns:
            Dict with watch operation result
        """
        try:
            watch_path = self._validate_path(path)
            watch_key = str(watch_path)

            # Stop existing watcher if any
            if watch_key in self.watchers:
                self.stop_watching(watch_path)

            # Create new observer
            observer = Observer()
            handler = FileChangeHandler(callback)

            if watch_path.is_file():
                observer.schedule(handler, str(watch_path.parent), recursive=False)
            else:
                observer.schedule(handler, str(watch_path), recursive=True)

            observer.start()
            self.watchers[watch_key] = observer

            logger.info(f"Started watching: {watch_path}")
            return {
                'status': 'success',
                'watching': str(watch_path),
                'watcher_id': watch_key
            }

        except Exception as e:
            logger.error(f"Failed to start watching {path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    def stop_watching(self, path: Union[str, Path]) -> Dict[str, Any]:
        """
        Stop watching file/directory for changes.

        Args:
            path: Path to stop watching

        Returns:
            Dict with operation result
        """
        try:
            watch_key = str(self._validate_path(path))

            if watch_key in self.watchers:
                observer = self.watchers[watch_key]
                observer.stop()
                observer.join(timeout=5)
                del self.watchers[watch_key]

                logger.info(f"Stopped watching: {watch_key}")
                return {
                    'status': 'success',
                    'stopped_watching': watch_key
                }
            else:
                return {
                    'status': 'error',
                    'error': f'No watcher found for: {path}',
                    'error_type': 'WatcherNotFound'
                }

        except Exception as e:
            logger.error(f"Failed to stop watching {path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    def get_active_watchers(self) -> Dict[str, Any]:
        """Get list of active file watchers."""
        return {
            'status': 'success',
            'active_watchers': list(self.watchers.keys()),
            'count': len(self.watchers)
        }

    def cleanup(self):
        """Clean up resources and stop all watchers."""
        try:
            # Stop all watchers
            for watch_key in list(self.watchers.keys()):
                self.stop_watching(watch_key)

            # Shutdown executor
            self.executor.shutdown(wait=True)

            logger.info("FileOperations cleanup completed")

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Global instance
file_operations = FileOperations()
