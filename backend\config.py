# backend/config.py
from pydantic import Field
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # ────────────────────────────
    # OpenRouter (LLM) Settings
    # ────────────────────────────
    openrouter_api_key: str = <PERSON>("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", env="OPENROUTER_API_KEY")
    code_model: str = Field("deepseek/deepseek-r1-0528:free", description="Model for code generation")
    summarization_model: str = Field("deepseek/deepseek-r1-0528:free", description="Model for summarization")
    vision_model: str = Field("google/gemini-2.0-flash-exp:free", description="Model for vision QA")

    max_token_limit: int = Field(164000, description="Max context tokens for code_model")
    summarization_trigger: float = Field(0.9, description="Trigger summarization at 90% of max_token_limit")
    max_output_tokens: int = Field(64000, description="Max tokens for code_model output")
    llm_temperature: float = Field(0.2, description="Temperature for code_model calls")

    # ────────────────────────────
    # Ollama (Embeddings) Settings
    # ────────────────────────────
    ollama_host: str = Field("http://ollama:11434", env="OLLAMA_HOST", description="Ollama server URL")
    embedding_model: str = Field("bge-m3", description="Ollama embedding model")
    embedding_dim: int = Field(1024, description="Embedding dimension for bge-m3")
    ollama_timeout: int = Field(30, description="Timeout for Ollama API calls in seconds")

    # ────────────────────────────
    # Qdrant Settings (Self-hosted)
    # ────────────────────────────
    qdrant_url: str = Field("http://qdrant:6333", env="QDRANT_URL", description="Self-hosted Qdrant URL")
    qdrant_api_key: Optional[str] = Field(None, env="QDRANT_API_KEY", description="Qdrant API key (optional for self-hosted)")
    qdrant_collection: str = Field("code_embeddings_bge_m3", description="Qdrant collection name")

    # ────────────────────────────
    # Workspace & Database
    # ────────────────────────────
    workspace_root: str = Field("/app/workspace", description="Root of agent's working dirs")
    sqlite_db_path: str = Field("data/agent_memory.db", description="Path to SQLite DB")

    # ────────────────────────────
    # Task Runner Settings (Phase 5)
    # ────────────────────────────
    task_timeout_seconds: int = Field(300, description="Maximum task execution time (5 minutes)")
    max_lint_test_cycles: int = Field(3, description="Maximum lint/test/fix iterations")
    auto_commit_on_success: bool = Field(True, description="Automatically commit successful changes")
    task_history_retention_days: int = Field(7, description="Days to retain task execution history")

    # Legacy settings (maintain compatibility)
    max_fix_iterations: int = Field(5, description="Max loops for lint/test & vision corrections")
    llm_json_retry_limit: int = Field(3, description="Number of times to re-prompt on invalid JSON output")
    lint_test_timeout_secs: int = Field(300, description="Timeout for lint & test commands")
    command_timeout_secs: int = Field(300, description="Timeout for arbitrary run_command calls")

    # ────────────────────────────
    # Lint and Test Settings (Phase 5)
    # ────────────────────────────
    python_linter: str = Field("flake8", description="Python linting tool")
    python_test_runner: str = Field("pytest", description="Python test runner")
    javascript_linter: str = Field("eslint", description="JavaScript linting tool")
    javascript_test_runner: str = Field("npm test", description="JavaScript test command")
    lint_timeout_seconds: int = Field(60, description="Timeout for linting operations")
    test_timeout_seconds: int = Field(180, description="Timeout for test operations")

    # ────────────────────────────
    # Context Enhancement Settings (Phase 5)
    # ────────────────────────────
    max_context_files: int = Field(20, description="Maximum files to include in task context")
    context_similarity_threshold: float = Field(0.7, description="Minimum similarity for context inclusion")
    conversation_memory_limit: int = Field(50, description="Maximum conversation turns to remember")

    # ────────────────────────────
    # Server Settings
    # ────────────────────────────
    backend_workers: int = Field(4, env="BACKEND_WORKERS", description="Number of Uvicorn workers")
    log_level: str = Field("INFO", env="LOG_LEVEL", description="Logging level")
    
    # ────────────────────────────
    # CORS Settings
    # ────────────────────────────
    cors_origins: list[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000"],
        description="Allowed CORS origins"
    )

    class Config:
        env_file = ".env"
        case_sensitive = True

    def validate_api_keys(self) -> dict:
        """Validate that required services are configured."""
        validation_results = {
            "openrouter_configured": self.openrouter_api_key != "your_openrouter_api_key_here",
            "qdrant_configured": bool(self.qdrant_url),  # Self-hosted, no API key required
            "ollama_configured": bool(self.ollama_host),
        }

        validation_results["all_configured"] = all([
            validation_results["openrouter_configured"],
            validation_results["qdrant_configured"],
            validation_results["ollama_configured"],
        ])

        return validation_results

# Global settings instance
settings = Settings()
