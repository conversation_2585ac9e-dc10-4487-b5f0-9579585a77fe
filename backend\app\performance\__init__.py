"""
⚡ Performance Optimizations System

This module provides comprehensive performance optimization with:
- Intelligent caching strategies and optimization
- Database query optimization and indexing
- Memory management and garbage collection
- CPU optimization and load balancing
- Network optimization and compression
- Background task optimization
"""

from .cache_optimizer import (
    CacheOptimizer,
    CacheStrategy,
    CacheMetrics,
    get_cache_optimizer
)

from .query_optimizer import (
    QueryOptimizer,
    QueryPlan,
    OptimizationResult,
    get_query_optimizer
)

from .memory_optimizer import (
    MemoryOptimizer,
    MemoryMetrics,
    GarbageCollectionStrategy,
    get_memory_optimizer
)

from .cpu_optimizer import (
    CPUOptimizer,
    LoadBalancer,
    ProcessingStrategy,
    get_cpu_optimizer
)

from .network_optimizer import (
    NetworkOptimizer,
    CompressionStrategy,
    NetworkMetrics,
    get_network_optimizer
)

from .background_optimizer import (
    BackgroundOptimizer,
    TaskPriority,
    SchedulingStrategy,
    get_background_optimizer
)

__all__ = [
    # Cache Optimization
    "CacheOptimizer",
    "CacheStrategy",
    "CacheMetrics",
    "get_cache_optimizer",
    
    # Query Optimization
    "QueryOptimizer",
    "QueryPlan",
    "OptimizationResult",
    "get_query_optimizer",
    
    # Memory Optimization
    "MemoryOptimizer",
    "MemoryMetrics",
    "GarbageCollectionStrategy",
    "get_memory_optimizer",
    
    # CPU Optimization
    "CPUOptimizer",
    "LoadBalancer",
    "ProcessingStrategy",
    "get_cpu_optimizer",
    
    # Network Optimization
    "NetworkOptimizer",
    "CompressionStrategy",
    "NetworkMetrics",
    "get_network_optimizer",
    
    # Background Task Optimization
    "BackgroundOptimizer",
    "TaskPriority",
    "SchedulingStrategy",
    "get_background_optimizer"
]
