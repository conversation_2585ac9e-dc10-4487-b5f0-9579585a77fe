#!/usr/bin/env python3
"""
Test Multi-Agent Workflows for Pydantic AI

This script tests the advanced multi-agent workflow capabilities
to ensure proper coordination and dependency management.
"""

import asyncio
import logging
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.pydantic_ai.workflows import (
    MultiAgentWorkflow, WorkflowStep, AgentType, WorkflowStatus,
    create_code_analysis_workflow, create_git_workflow
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_simple_workflow():
    """Test a simple multi-step workflow."""
    print("\n🧪 Testing Simple Multi-Step Workflow...")
    
    try:
        # Create a simple workflow
        workflow = MultiAgentWorkflow("simple_test")
        
        workflow.add_coding_step(
            "step1",
            "List the files in the current directory."
        ).add_coding_step(
            "step2", 
            "Based on the file listing, identify any Python files and describe their purpose.",
            depends_on=["step1"]
        )
        
        # Execute workflow
        result = await workflow.execute(workspace_root=".")
        
        print(f"✅ Workflow Status: {result.status}")
        print(f"✅ Steps Completed: {result.steps_completed}/{result.total_steps}")
        print(f"✅ Execution Time: {result.execution_time:.2f}s")
        
        if result.errors:
            print(f"⚠️  Errors: {result.errors}")
        
        return result.status == WorkflowStatus.COMPLETED
        
    except Exception as e:
        print(f"❌ Simple workflow error: {e}")
        return False


async def test_dependency_workflow():
    """Test workflow with complex dependencies."""
    print("\n🧪 Testing Complex Dependency Workflow...")
    
    try:
        # Create workflow with multiple dependencies
        workflow = MultiAgentWorkflow("dependency_test")
        
        workflow.add_coding_step(
            "init",
            "Initialize analysis by checking the current directory structure."
        ).add_coding_step(
            "analyze_backend",
            "Analyze the backend directory structure and main components.",
            depends_on=["init"]
        ).add_coding_step(
            "analyze_pydantic",
            "Analyze the pydantic_ai directory and its modules.",
            depends_on=["init"]
        ).add_coding_step(
            "summarize",
            "Provide a comprehensive summary of the project structure and architecture.",
            depends_on=["analyze_backend", "analyze_pydantic"]
        )
        
        # Execute workflow
        result = await workflow.execute(workspace_root=".")
        
        print(f"✅ Workflow Status: {result.status}")
        print(f"✅ Steps Completed: {result.steps_completed}/{result.total_steps}")
        print(f"✅ Execution Time: {result.execution_time:.2f}s")
        
        # Check that dependencies were respected
        if "summarize" in result.results:
            print("✅ Dependencies respected - final step completed")
        
        return result.status == WorkflowStatus.COMPLETED
        
    except Exception as e:
        print(f"❌ Dependency workflow error: {e}")
        return False


async def test_mixed_agent_workflow():
    """Test workflow with different agent types."""
    print("\n🧪 Testing Mixed Agent Workflow...")
    
    try:
        # Create workflow with different agent types
        workflow = MultiAgentWorkflow("mixed_agent_test")
        
        workflow.add_step(WorkflowStep(
            step_id="simple_analysis",
            agent_type=AgentType.SIMPLE_CODING,
            task_description="Explain what Pydantic AI is and its benefits."
        )).add_coding_step(
            "technical_analysis",
            "Analyze the technical implementation of our custom DeepSeek model.",
            depends_on=["simple_analysis"]
        )
        
        # Execute workflow
        result = await workflow.execute(workspace_root=".")
        
        print(f"✅ Workflow Status: {result.status}")
        print(f"✅ Steps Completed: {result.steps_completed}/{result.total_steps}")
        print(f"✅ Agent Types Used: {[r.get('agent_type') for r in result.results.values()]}")
        
        return result.status == WorkflowStatus.COMPLETED
        
    except Exception as e:
        print(f"❌ Mixed agent workflow error: {e}")
        return False


async def test_code_analysis_workflow():
    """Test the pre-built code analysis workflow."""
    print("\n🧪 Testing Code Analysis Workflow...")
    
    try:
        # Test the pre-built code analysis workflow
        result = await create_code_analysis_workflow(
            "backend/app/pydantic_ai/deepseek_model.py",
            workspace_root="."
        )
        
        print(f"✅ Code Analysis Status: {result.status}")
        print(f"✅ Steps Completed: {result.steps_completed}/{result.total_steps}")
        print(f"✅ Execution Time: {result.execution_time:.2f}s")
        
        # Check specific steps
        expected_steps = ["read_file", "analyze_structure", "run_quality_checks", "suggest_improvements"]
        completed_steps = list(result.results.keys())
        
        for step in expected_steps:
            if step in completed_steps:
                print(f"✅ Step '{step}' completed")
            else:
                print(f"❌ Step '{step}' missing")
        
        return result.status == WorkflowStatus.COMPLETED
        
    except Exception as e:
        print(f"❌ Code analysis workflow error: {e}")
        return False


async def test_error_handling():
    """Test workflow error handling."""
    print("\n🧪 Testing Error Handling...")
    
    try:
        # Create workflow with invalid dependency
        workflow = MultiAgentWorkflow("error_test")
        
        workflow.add_coding_step(
            "step1",
            "This step should work fine."
        ).add_coding_step(
            "step2",
            "This step depends on a non-existent step.",
            depends_on=["nonexistent_step"]
        )
        
        # Execute workflow
        result = await workflow.execute(workspace_root=".")
        
        print(f"✅ Error Handling Status: {result.status}")
        print(f"✅ Errors Detected: {len(result.errors)}")
        
        if result.errors:
            print(f"✅ Error Messages: {result.errors}")
        
        # Should fail due to missing dependency
        return result.status == WorkflowStatus.FAILED and len(result.errors) > 0
        
    except Exception as e:
        print(f"❌ Error handling test error: {e}")
        return False


async def test_workflow_context():
    """Test context passing between workflow steps."""
    print("\n🧪 Testing Workflow Context Passing...")
    
    try:
        # Create workflow that builds context
        workflow = MultiAgentWorkflow("context_test")
        
        workflow.add_coding_step(
            "gather_info",
            "Count the number of Python files in the backend/app/pydantic_ai directory."
        ).add_coding_step(
            "analyze_info",
            "Based on the file count from the previous step, provide insights about the project size.",
            depends_on=["gather_info"]
        )
        
        # Execute workflow
        result = await workflow.execute(workspace_root=".")
        
        print(f"✅ Context Test Status: {result.status}")
        
        # Check that context was passed
        if "analyze_info" in result.results:
            analyze_output = result.results["analyze_info"]["output"]
            if "previous step" in analyze_output.lower() or "file count" in analyze_output.lower():
                print("✅ Context successfully passed between steps")
                return True
            else:
                print("⚠️  Context may not have been passed properly")
        
        return result.status == WorkflowStatus.COMPLETED
        
    except Exception as e:
        print(f"❌ Context test error: {e}")
        return False


async def main():
    """Run all workflow tests."""
    print("🚀 Testing Multi-Agent Workflows")
    print("=" * 60)
    
    tests = [
        ("Simple Workflow", test_simple_workflow),
        ("Dependency Workflow", test_dependency_workflow),
        ("Mixed Agent Workflow", test_mixed_agent_workflow),
        ("Code Analysis Workflow", test_code_analysis_workflow),
        ("Error Handling", test_error_handling),
        ("Context Passing", test_workflow_context),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            success = await test_func()
            results.append((test_name, success))
            status = "✅ PASSED" if success else "❌ FAILED"
            print(f"{status}: {test_name}")
        except Exception as e:
            print(f"❌ FAILED: {test_name} - {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Workflow Test Results:")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status}: {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All workflow tests passed! Multi-agent coordination is working!")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
