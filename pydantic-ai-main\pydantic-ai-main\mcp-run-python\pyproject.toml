[project]
name = "mcp-run-python"
version = "0.0.1"
readme = "README.md"
classifiers = ["Private :: do not release"]

[dependency-groups]
dev = [
    "anyio>=4.5.0",
    "dirty-equals>=0.9.0",
    "httpx>=0.28.1",
    "inline-snapshot>=0.19.3",
    "mcp>=1.4.1; python_version >= '3.10'",
    "micropip>=0.9.0; python_version >= '3.12'",
    "pytest>=8.3.3",
    "pytest-pretty>=1.2.0",
]

[tool.uv.sources]
mcp-run-python = { workspace = true }
