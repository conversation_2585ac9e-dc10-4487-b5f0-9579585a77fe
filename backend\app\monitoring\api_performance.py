"""
📈 API Performance Monitor - Response times and error rates

This module provides comprehensive API performance monitoring with:
- Real-time response time tracking
- Error rate monitoring and alerting
- Endpoint performance analytics
- Performance trend analysis
- Automatic performance optimization suggestions
"""

import asyncio
import time
from collections import defaultdict, deque
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)


class AlertLevel(str, Enum):
    """Performance alert levels."""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"


class APIMetrics(BaseModel):
    """API endpoint metrics."""
    
    endpoint: str = Field(..., description="API endpoint path")
    method: str = Field(..., description="HTTP method")
    
    # Response time metrics
    avg_response_time: float = Field(default=0.0, description="Average response time in ms")
    min_response_time: float = Field(default=0.0, description="Minimum response time in ms")
    max_response_time: float = Field(default=0.0, description="Maximum response time in ms")
    p95_response_time: float = Field(default=0.0, description="95th percentile response time in ms")
    
    # Request metrics
    total_requests: int = Field(default=0, description="Total number of requests")
    requests_per_minute: float = Field(default=0.0, description="Requests per minute")
    
    # Error metrics
    error_count: int = Field(default=0, description="Number of errors")
    error_rate: float = Field(default=0.0, description="Error rate percentage")
    
    # Status code breakdown
    status_codes: Dict[str, int] = Field(default_factory=dict, description="Status code counts")
    
    # Timestamps
    first_request: Optional[datetime] = Field(None, description="First request timestamp")
    last_request: Optional[datetime] = Field(None, description="Last request timestamp")


class PerformanceAlert(BaseModel):
    """Performance alert model."""
    
    alert_id: str = Field(..., description="Unique alert identifier")
    level: AlertLevel = Field(..., description="Alert severity level")
    endpoint: str = Field(..., description="Affected endpoint")
    message: str = Field(..., description="Alert message")
    metric_value: float = Field(..., description="Current metric value")
    threshold: float = Field(..., description="Alert threshold")
    timestamp: datetime = Field(default_factory=datetime.now, description="Alert timestamp")
    resolved: bool = Field(default=False, description="Whether alert is resolved")


class APIPerformanceMonitor:
    """
    📈 Advanced API Performance Monitor
    
    Tracks API performance metrics, detects performance issues,
    and provides real-time analytics for optimization.
    """
    
    def __init__(self, max_history_size: int = 1000):
        self.max_history_size = max_history_size
        
        # Performance data storage
        self.endpoint_metrics: Dict[str, APIMetrics] = {}
        self.response_times: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history_size))
        self.request_counts: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history_size))
        self.error_counts: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history_size))
        
        # Alert system
        self.active_alerts: Dict[str, PerformanceAlert] = {}
        self.alert_thresholds = {
            "response_time_warning": 1000.0,  # 1 second
            "response_time_critical": 5000.0,  # 5 seconds
            "error_rate_warning": 5.0,  # 5%
            "error_rate_critical": 10.0,  # 10%
        }
        
        # Start background monitoring
        self._start_monitoring_tasks()
    
    def record_request(
        self,
        endpoint: str,
        method: str,
        response_time_ms: float,
        status_code: int,
        timestamp: Optional[datetime] = None
    ) -> None:
        """Record an API request for performance tracking."""
        if timestamp is None:
            timestamp = datetime.now()
        
        endpoint_key = f"{method} {endpoint}"
        
        # Initialize metrics if not exists
        if endpoint_key not in self.endpoint_metrics:
            self.endpoint_metrics[endpoint_key] = APIMetrics(
                endpoint=endpoint,
                method=method,
                first_request=timestamp
            )
        
        metrics = self.endpoint_metrics[endpoint_key]
        
        # Update response time metrics
        self.response_times[endpoint_key].append((timestamp, response_time_ms))
        
        # Update request counts
        self.request_counts[endpoint_key].append(timestamp)
        
        # Update error counts
        is_error = status_code >= 400
        if is_error:
            self.error_counts[endpoint_key].append(timestamp)
        
        # Update metrics
        self._update_endpoint_metrics(endpoint_key, response_time_ms, status_code, timestamp)
        
        # Check for performance alerts
        self._check_performance_alerts(endpoint_key)
    
    def _update_endpoint_metrics(
        self,
        endpoint_key: str,
        response_time_ms: float,
        status_code: int,
        timestamp: datetime
    ) -> None:
        """Update endpoint metrics with new data."""
        metrics = self.endpoint_metrics[endpoint_key]
        
        # Update basic counters
        metrics.total_requests += 1
        metrics.last_request = timestamp
        
        # Update status code counts
        status_str = str(status_code)
        if status_str not in metrics.status_codes:
            metrics.status_codes[status_str] = 0
        metrics.status_codes[status_str] += 1
        
        # Update error metrics
        if status_code >= 400:
            metrics.error_count += 1
        
        metrics.error_rate = (metrics.error_count / metrics.total_requests) * 100
        
        # Update response time metrics
        response_times = [rt for _, rt in self.response_times[endpoint_key]]
        if response_times:
            metrics.avg_response_time = sum(response_times) / len(response_times)
            metrics.min_response_time = min(response_times)
            metrics.max_response_time = max(response_times)
            
            # Calculate 95th percentile
            sorted_times = sorted(response_times)
            p95_index = int(len(sorted_times) * 0.95)
            metrics.p95_response_time = sorted_times[p95_index] if sorted_times else 0.0
        
        # Calculate requests per minute
        recent_requests = [
            ts for ts in self.request_counts[endpoint_key]
            if ts > datetime.now() - timedelta(minutes=1)
        ]
        metrics.requests_per_minute = len(recent_requests)
    
    def _check_performance_alerts(self, endpoint_key: str) -> None:
        """Check for performance issues and create alerts."""
        metrics = self.endpoint_metrics[endpoint_key]
        
        # Check response time alerts
        if metrics.avg_response_time > self.alert_thresholds["response_time_critical"]:
            self._create_alert(
                endpoint_key,
                AlertLevel.CRITICAL,
                f"Critical response time: {metrics.avg_response_time:.1f}ms",
                metrics.avg_response_time,
                self.alert_thresholds["response_time_critical"]
            )
        elif metrics.avg_response_time > self.alert_thresholds["response_time_warning"]:
            self._create_alert(
                endpoint_key,
                AlertLevel.WARNING,
                f"High response time: {metrics.avg_response_time:.1f}ms",
                metrics.avg_response_time,
                self.alert_thresholds["response_time_warning"]
            )
        
        # Check error rate alerts
        if metrics.error_rate > self.alert_thresholds["error_rate_critical"]:
            self._create_alert(
                endpoint_key,
                AlertLevel.CRITICAL,
                f"Critical error rate: {metrics.error_rate:.1f}%",
                metrics.error_rate,
                self.alert_thresholds["error_rate_critical"]
            )
        elif metrics.error_rate > self.alert_thresholds["error_rate_warning"]:
            self._create_alert(
                endpoint_key,
                AlertLevel.WARNING,
                f"High error rate: {metrics.error_rate:.1f}%",
                metrics.error_rate,
                self.alert_thresholds["error_rate_warning"]
            )
    
    def _create_alert(
        self,
        endpoint_key: str,
        level: AlertLevel,
        message: str,
        metric_value: float,
        threshold: float
    ) -> None:
        """Create a performance alert."""
        alert_id = f"{endpoint_key}_{level.value}_{int(time.time())}"
        
        alert = PerformanceAlert(
            alert_id=alert_id,
            level=level,
            endpoint=endpoint_key,
            message=message,
            metric_value=metric_value,
            threshold=threshold
        )
        
        self.active_alerts[alert_id] = alert
        logger.warning(f"Performance alert: {message} for {endpoint_key}")
    
    def get_endpoint_metrics(self, endpoint: Optional[str] = None) -> Dict[str, APIMetrics]:
        """Get performance metrics for endpoints."""
        if endpoint:
            return {k: v for k, v in self.endpoint_metrics.items() if endpoint in k}
        return self.endpoint_metrics.copy()
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get overall performance summary."""
        if not self.endpoint_metrics:
            return {
                "total_endpoints": 0,
                "total_requests": 0,
                "avg_response_time": 0.0,
                "overall_error_rate": 0.0,
                "active_alerts": 0
            }
        
        total_requests = sum(m.total_requests for m in self.endpoint_metrics.values())
        total_errors = sum(m.error_count for m in self.endpoint_metrics.values())
        avg_response_times = [m.avg_response_time for m in self.endpoint_metrics.values() if m.avg_response_time > 0]
        
        return {
            "total_endpoints": len(self.endpoint_metrics),
            "total_requests": total_requests,
            "avg_response_time": sum(avg_response_times) / len(avg_response_times) if avg_response_times else 0.0,
            "overall_error_rate": (total_errors / total_requests * 100) if total_requests > 0 else 0.0,
            "active_alerts": len([a for a in self.active_alerts.values() if not a.resolved]),
            "slowest_endpoints": self._get_slowest_endpoints(),
            "most_error_prone_endpoints": self._get_most_error_prone_endpoints()
        }
    
    def _get_slowest_endpoints(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get the slowest endpoints."""
        sorted_endpoints = sorted(
            self.endpoint_metrics.items(),
            key=lambda x: x[1].avg_response_time,
            reverse=True
        )
        
        return [
            {
                "endpoint": endpoint,
                "avg_response_time": metrics.avg_response_time,
                "total_requests": metrics.total_requests
            }
            for endpoint, metrics in sorted_endpoints[:limit]
        ]
    
    def _get_most_error_prone_endpoints(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get endpoints with highest error rates."""
        sorted_endpoints = sorted(
            self.endpoint_metrics.items(),
            key=lambda x: x[1].error_rate,
            reverse=True
        )
        
        return [
            {
                "endpoint": endpoint,
                "error_rate": metrics.error_rate,
                "error_count": metrics.error_count,
                "total_requests": metrics.total_requests
            }
            for endpoint, metrics in sorted_endpoints[:limit]
            if metrics.error_rate > 0
        ]
    
    def get_active_alerts(self) -> List[PerformanceAlert]:
        """Get all active performance alerts."""
        return [alert for alert in self.active_alerts.values() if not alert.resolved]
    
    def resolve_alert(self, alert_id: str) -> bool:
        """Resolve a performance alert."""
        if alert_id in self.active_alerts:
            self.active_alerts[alert_id].resolved = True
            return True
        return False
    
    def _start_monitoring_tasks(self) -> None:
        """Start background monitoring tasks."""
        async def cleanup_old_data():
            while True:
                try:
                    # Clean up old alerts (older than 1 hour)
                    cutoff_time = datetime.now() - timedelta(hours=1)
                    old_alerts = [
                        alert_id for alert_id, alert in self.active_alerts.items()
                        if alert.timestamp < cutoff_time and alert.resolved
                    ]
                    
                    for alert_id in old_alerts:
                        del self.active_alerts[alert_id]
                    
                    await asyncio.sleep(300)  # Clean up every 5 minutes
                    
                except Exception as e:
                    logger.error(f"Performance monitor cleanup failed: {e}")
                    await asyncio.sleep(60)
        
        # Start cleanup task
        asyncio.create_task(cleanup_old_data())


# Global API performance monitor instance
_api_performance_monitor: Optional[APIPerformanceMonitor] = None


def get_api_performance_monitor() -> APIPerformanceMonitor:
    """Get the global API performance monitor instance."""
    global _api_performance_monitor
    if _api_performance_monitor is None:
        _api_performance_monitor = APIPerformanceMonitor()
    return _api_performance_monitor
