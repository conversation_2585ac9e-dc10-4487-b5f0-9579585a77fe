"""
⚙️ Preferences Manager - Persistent user preferences system

This module provides comprehensive user preferences management with:
- Persistent storage of user settings
- Category-based preference organization
- Validation and default values
- Real-time preference updates
- Import/export functionality
"""

import json
import sqlite3
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field, validator
import logging

logger = logging.getLogger(__name__)


class PreferenceCategory(str, Enum):
    """User preference categories."""
    GENERAL = "general"
    AI_MODELS = "ai_models"
    TOOLS = "tools"
    INTERFACE = "interface"
    WORKSPACE = "workspace"
    NOTIFICATIONS = "notifications"
    PRIVACY = "privacy"
    ADVANCED = "advanced"


class UserPreferences(BaseModel):
    """User preferences model with validation."""
    
    user_id: str = Field(..., description="User identifier")
    
    # General preferences
    general: Dict[str, Any] = Field(default_factory=lambda: {
        "language": "en",
        "timezone": "UTC",
        "theme": "dark",
        "auto_save": True,
        "auto_save_interval": 30,
        "show_tips": True,
        "enable_animations": True
    })
    
    # AI model preferences
    ai_models: Dict[str, Any] = Field(default_factory=lambda: {
        "default_coding_model": "deepseek/deepseek-r1-0528:free",
        "default_vision_model": "google/gemini-2.0-flash-exp:free",
        "default_embedding_model": "bge-m3",
        "max_tokens": 8000,
        "temperature": 0.7,
        "enable_streaming": True,
        "model_fallback": True,
        "retry_failed_requests": True
    })
    
    # Tool preferences
    tools: Dict[str, Any] = Field(default_factory=lambda: {
        "enabled_tools": [],  # Empty means all tools enabled
        "disabled_tools": [],
        "tool_timeout": 30,
        "show_tool_output": True,
        "confirm_destructive_actions": True,
        "auto_approve_safe_tools": False
    })
    
    # Interface preferences
    interface: Dict[str, Any] = Field(default_factory=lambda: {
        "sidebar_collapsed": False,
        "show_line_numbers": True,
        "word_wrap": True,
        "font_size": 14,
        "font_family": "Monaco, Consolas, monospace",
        "show_minimap": True,
        "highlight_active_line": True,
        "show_whitespace": False
    })
    
    # Workspace preferences
    workspace: Dict[str, Any] = Field(default_factory=lambda: {
        "default_workspace_path": "/workspace",
        "auto_detect_project_type": True,
        "show_hidden_files": False,
        "file_tree_depth": 3,
        "auto_refresh_file_tree": True,
        "remember_open_files": True,
        "workspace_templates": []
    })
    
    # Notification preferences
    notifications: Dict[str, Any] = Field(default_factory=lambda: {
        "enable_notifications": True,
        "show_success_notifications": True,
        "show_error_notifications": True,
        "show_warning_notifications": True,
        "notification_duration": 5,
        "sound_enabled": False,
        "desktop_notifications": False
    })
    
    # Privacy preferences
    privacy: Dict[str, Any] = Field(default_factory=lambda: {
        "analytics_enabled": True,
        "error_reporting": True,
        "usage_statistics": True,
        "share_anonymous_data": False,
        "data_retention_days": 30
    })
    
    # Advanced preferences
    advanced: Dict[str, Any] = Field(default_factory=lambda: {
        "debug_mode": False,
        "verbose_logging": False,
        "experimental_features": False,
        "api_rate_limit": 60,
        "concurrent_requests": 5,
        "cache_enabled": True,
        "cache_size_mb": 100
    })
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.now, description="Preferences creation time")
    updated_at: datetime = Field(default_factory=datetime.now, description="Last update time")
    version: str = Field(default="1.0", description="Preferences schema version")
    
    @validator('general', 'ai_models', 'tools', 'interface', 'workspace', 'notifications', 'privacy', 'advanced')
    def validate_preference_dict(cls, v):
        """Validate preference dictionaries."""
        if not isinstance(v, dict):
            raise ValueError("Preference value must be a dictionary")
        return v
    
    def update_preference(self, category: PreferenceCategory, key: str, value: Any) -> None:
        """Update a specific preference value."""
        category_dict = getattr(self, category.value)
        category_dict[key] = value
        self.updated_at = datetime.now()
    
    def get_preference(self, category: PreferenceCategory, key: str, default: Any = None) -> Any:
        """Get a specific preference value."""
        category_dict = getattr(self, category.value)
        return category_dict.get(key, default)
    
    def reset_category(self, category: PreferenceCategory) -> None:
        """Reset a preference category to defaults."""
        defaults = UserPreferences(user_id=self.user_id)
        setattr(self, category.value, getattr(defaults, category.value))
        self.updated_at = datetime.now()


class PreferencesManager:
    """
    ⚙️ Advanced Preferences Manager
    
    Manages user preferences with persistent storage, validation,
    and real-time updates across the application.
    """
    
    def __init__(self, db_path: str = "data/user_preferences.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # In-memory cache for performance
        self.preferences_cache: Dict[str, UserPreferences] = {}
        
        # Initialize database
        self._init_database()
        
        # Load preferences into cache
        self._load_preferences_cache()
    
    def _init_database(self) -> None:
        """Initialize preferences database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS user_preferences (
                    user_id TEXT PRIMARY KEY,
                    general_json TEXT NOT NULL,
                    ai_models_json TEXT NOT NULL,
                    tools_json TEXT NOT NULL,
                    interface_json TEXT NOT NULL,
                    workspace_json TEXT NOT NULL,
                    notifications_json TEXT NOT NULL,
                    privacy_json TEXT NOT NULL,
                    advanced_json TEXT NOT NULL,
                    created_at TIMESTAMP NOT NULL,
                    updated_at TIMESTAMP NOT NULL,
                    version TEXT NOT NULL
                )
            """)
            
            # Create indexes
            conn.execute("CREATE INDEX IF NOT EXISTS idx_preferences_updated ON user_preferences(updated_at)")
    
    def _load_preferences_cache(self) -> None:
        """Load all preferences into memory cache."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("SELECT * FROM user_preferences")
                
                for row in cursor.fetchall():
                    preferences = self._row_to_preferences(row)
                    self.preferences_cache[preferences.user_id] = preferences
                
                logger.info(f"Loaded {len(self.preferences_cache)} user preferences")
                
        except Exception as e:
            logger.error(f"Failed to load preferences cache: {e}")
    
    def _row_to_preferences(self, row: sqlite3.Row) -> UserPreferences:
        """Convert database row to UserPreferences object."""
        return UserPreferences(
            user_id=row['user_id'],
            general=json.loads(row['general_json']),
            ai_models=json.loads(row['ai_models_json']),
            tools=json.loads(row['tools_json']),
            interface=json.loads(row['interface_json']),
            workspace=json.loads(row['workspace_json']),
            notifications=json.loads(row['notifications_json']),
            privacy=json.loads(row['privacy_json']),
            advanced=json.loads(row['advanced_json']),
            created_at=datetime.fromisoformat(row['created_at']),
            updated_at=datetime.fromisoformat(row['updated_at']),
            version=row['version']
        )
    
    async def get_user_preferences(self, user_id: str) -> UserPreferences:
        """Get user preferences, creating defaults if not exists."""
        # Check cache first
        if user_id in self.preferences_cache:
            return self.preferences_cache[user_id]
        
        # Load from database
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("SELECT * FROM user_preferences WHERE user_id = ?", (user_id,))
                row = cursor.fetchone()
                
                if row:
                    preferences = self._row_to_preferences(row)
                    self.preferences_cache[user_id] = preferences
                    return preferences
                    
        except Exception as e:
            logger.error(f"Failed to load preferences for user {user_id}: {e}")
        
        # Create default preferences
        preferences = UserPreferences(user_id=user_id)
        await self.save_user_preferences(preferences)
        return preferences
    
    async def save_user_preferences(self, preferences: UserPreferences) -> bool:
        """Save user preferences to database."""
        try:
            preferences.updated_at = datetime.now()
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO user_preferences (
                        user_id, general_json, ai_models_json, tools_json,
                        interface_json, workspace_json, notifications_json,
                        privacy_json, advanced_json, created_at, updated_at, version
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    preferences.user_id,
                    json.dumps(preferences.general),
                    json.dumps(preferences.ai_models),
                    json.dumps(preferences.tools),
                    json.dumps(preferences.interface),
                    json.dumps(preferences.workspace),
                    json.dumps(preferences.notifications),
                    json.dumps(preferences.privacy),
                    json.dumps(preferences.advanced),
                    preferences.created_at.isoformat(),
                    preferences.updated_at.isoformat(),
                    preferences.version
                ))
            
            # Update cache
            self.preferences_cache[preferences.user_id] = preferences
            
            logger.debug(f"Saved preferences for user {preferences.user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save preferences: {e}")
            return False
    
    async def update_preference(
        self,
        user_id: str,
        category: PreferenceCategory,
        key: str,
        value: Any
    ) -> bool:
        """Update a specific preference value."""
        try:
            preferences = await self.get_user_preferences(user_id)
            preferences.update_preference(category, key, value)
            return await self.save_user_preferences(preferences)
            
        except Exception as e:
            logger.error(f"Failed to update preference: {e}")
            return False
    
    async def get_preference(
        self,
        user_id: str,
        category: PreferenceCategory,
        key: str,
        default: Any = None
    ) -> Any:
        """Get a specific preference value."""
        try:
            preferences = await self.get_user_preferences(user_id)
            return preferences.get_preference(category, key, default)
            
        except Exception as e:
            logger.error(f"Failed to get preference: {e}")
            return default
    
    async def reset_user_preferences(self, user_id: str) -> bool:
        """Reset user preferences to defaults."""
        try:
            default_preferences = UserPreferences(user_id=user_id)
            return await self.save_user_preferences(default_preferences)
            
        except Exception as e:
            logger.error(f"Failed to reset preferences: {e}")
            return False
    
    async def reset_preference_category(
        self,
        user_id: str,
        category: PreferenceCategory
    ) -> bool:
        """Reset a specific preference category to defaults."""
        try:
            preferences = await self.get_user_preferences(user_id)
            preferences.reset_category(category)
            return await self.save_user_preferences(preferences)
            
        except Exception as e:
            logger.error(f"Failed to reset preference category: {e}")
            return False
    
    async def export_preferences(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Export user preferences as JSON."""
        try:
            preferences = await self.get_user_preferences(user_id)
            return preferences.model_dump()
            
        except Exception as e:
            logger.error(f"Failed to export preferences: {e}")
            return None
    
    async def import_preferences(self, user_id: str, preferences_data: Dict[str, Any]) -> bool:
        """Import user preferences from JSON."""
        try:
            # Validate and create preferences object
            preferences_data['user_id'] = user_id  # Ensure correct user_id
            preferences = UserPreferences(**preferences_data)
            return await self.save_user_preferences(preferences)
            
        except Exception as e:
            logger.error(f"Failed to import preferences: {e}")
            return False
    
    def get_all_users(self) -> List[str]:
        """Get list of all users with preferences."""
        return list(self.preferences_cache.keys())
    
    def get_preferences_stats(self) -> Dict[str, Any]:
        """Get preferences system statistics."""
        return {
            "total_users": len(self.preferences_cache),
            "database_path": str(self.db_path),
            "cache_size": len(self.preferences_cache),
            "last_updated": max(
                (prefs.updated_at for prefs in self.preferences_cache.values()),
                default=datetime.now()
            ).isoformat() if self.preferences_cache else None
        }


# Global preferences manager instance
_preferences_manager: Optional[PreferencesManager] = None


def get_preferences_manager() -> PreferencesManager:
    """Get the global preferences manager instance."""
    global _preferences_manager
    if _preferences_manager is None:
        _preferences_manager = PreferencesManager()
    return _preferences_manager
