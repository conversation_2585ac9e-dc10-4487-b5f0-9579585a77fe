import React, { useState, useEffect, useRef } from 'react';

interface PlanTask {
  id: string;
  title: string;
  description: string;
  type: 'feature' | 'bugfix' | 'refactor' | 'testing' | 'documentation';
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'in_progress' | 'completed' | 'blocked';
  estimated_hours: number;
  dependencies: string[];
  ai_suggestions: string[];
  complexity: number;
  position: { x: number; y: number };
}

interface DevelopmentPlan {
  id: string;
  name: string;
  description: string;
  status: 'draft' | 'active' | 'completed' | 'archived';
  tasks: PlanTask[];
  milestones: Array<{
    id: string;
    name: string;
    date: Date;
    tasks: string[];
  }>;
  ai_insights: {
    estimated_completion: string;
    risk_factors: string[];
    optimization_suggestions: string[];
  };
}

interface AIPlanningCanvasProps {
  projectId?: string;
}

export const AIPlanningCanvas: React.FC<AIPlanningCanvasProps> = ({ projectId }) => {
  const [plans, setPlans] = useState<DevelopmentPlan[]>([]);
  const [currentPlan, setCurrentPlan] = useState<DevelopmentPlan | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedTask, setSelectedTask] = useState<PlanTask | null>(null);
  const [draggedTask, setDraggedTask] = useState<PlanTask | null>(null);
  const [showCreatePlan, setShowCreatePlan] = useState(false);
  const canvasRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    loadPlans();
  }, [projectId]);

  const loadPlans = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/v1/planning/sessions');
      if (response.ok) {
        const data = await response.json();
        setPlans(data.sessions || []);
        if (data.sessions && data.sessions.length > 0) {
          loadPlan(data.sessions[0].id);
        }
      }
    } catch (error) {
      console.error('Failed to load plans:', error);
      // Mock data for development
      const mockPlan: DevelopmentPlan = {
        id: '1',
        name: 'DeepNexus Phase 3 Development',
        description: 'AI Intelligence Interface implementation',
        status: 'active',
        tasks: [
          {
            id: 'task-1',
            title: 'AI Planning Canvas',
            description: 'Create visual task orchestration interface',
            type: 'feature',
            priority: 'high',
            status: 'in_progress',
            estimated_hours: 8,
            dependencies: [],
            ai_suggestions: ['Add drag-and-drop functionality', 'Implement real-time collaboration'],
            complexity: 7,
            position: { x: 100, y: 100 }
          },
          {
            id: 'task-2',
            title: 'Session Management',
            description: 'Multi-session support with history',
            type: 'feature',
            priority: 'medium',
            status: 'pending',
            estimated_hours: 6,
            dependencies: ['task-1'],
            ai_suggestions: ['Add session search', 'Implement session templates'],
            complexity: 5,
            position: { x: 300, y: 150 }
          },
          {
            id: 'task-3',
            title: 'Context Optimization',
            description: 'Smart context compression and relevance scoring',
            type: 'feature',
            priority: 'high',
            status: 'pending',
            estimated_hours: 12,
            dependencies: ['task-2'],
            ai_suggestions: ['Use ML for relevance scoring', 'Implement caching strategy'],
            complexity: 9,
            position: { x: 500, y: 200 }
          }
        ],
        milestones: [
          {
            id: 'milestone-1',
            name: 'AI Planning Complete',
            date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            tasks: ['task-1', 'task-2']
          },
          {
            id: 'milestone-2',
            name: 'Context System Ready',
            date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
            tasks: ['task-3']
          }
        ],
        ai_insights: {
          estimated_completion: '2 weeks',
          risk_factors: ['Complex context optimization', 'Integration challenges'],
          optimization_suggestions: ['Parallel development of independent features', 'Early testing of context algorithms']
        }
      };
      setPlans([mockPlan]);
      setCurrentPlan(mockPlan);
    } finally {
      setIsLoading(false);
    }
  };

  const loadPlan = async (planId: string) => {
    try {
      const response = await fetch(`/api/v1/planning/plans/${planId}`);
      if (response.ok) {
        const data = await response.json();
        setCurrentPlan(data.plan);
      }
    } catch (error) {
      console.error('Failed to load plan:', error);
    }
  };

  const generateAIPlan = async (type: 'feature' | 'bugfix' | 'refactor') => {
    setIsGenerating(true);
    try {
      const response = await fetch(`/api/v1/planning/templates/${type}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          project_id: projectId,
          context: 'Phase 3 AI Intelligence Interface development'
        })
      });

      if (response.ok) {
        const data = await response.json();
        const newPlan: DevelopmentPlan = {
          id: Date.now().toString(),
          name: data.plan.name || `AI Generated ${type} Plan`,
          description: data.plan.description || `Automatically generated ${type} plan`,
          status: 'draft',
          tasks: data.plan.tasks.map((task: any, index: number) => ({
            ...task,
            id: `task-${Date.now()}-${index}`,
            position: { x: 100 + index * 200, y: 100 + Math.random() * 200 }
          })),
          milestones: data.plan.milestones || [],
          ai_insights: data.plan.ai_insights || {
            estimated_completion: 'TBD',
            risk_factors: [],
            optimization_suggestions: []
          }
        };
        setPlans(prev => [newPlan, ...prev]);
        setCurrentPlan(newPlan);
      }
    } catch (error) {
      console.error('Failed to generate AI plan:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const updateTaskPosition = (taskId: string, position: { x: number; y: number }) => {
    if (!currentPlan) return;

    const updatedPlan = {
      ...currentPlan,
      tasks: currentPlan.tasks.map(task =>
        task.id === taskId ? { ...task, position } : task
      )
    };
    setCurrentPlan(updatedPlan);
  };

  const handleTaskDragStart = (task: PlanTask) => {
    setDraggedTask(task);
  };

  const handleTaskDragEnd = (e: React.DragEvent) => {
    if (draggedTask && canvasRef.current) {
      const rect = canvasRef.current.getBoundingClientRect();
      const newPosition = {
        x: e.clientX - rect.left - 128, // Center the task
        y: e.clientY - rect.top - 60
      };
      updateTaskPosition(draggedTask.id, newPosition);
    }
    setDraggedTask(null);
  };

  const handleCanvasClick = (e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      setSelectedTask(null);
    }
  };

  const getTaskColor = (task: PlanTask) => {
    switch (task.priority) {
      case 'critical': return 'border-crimson-alert bg-crimson-alert/10';
      case 'high': return 'border-amber-data bg-amber-data/10';
      case 'medium': return 'border-bloom-highlight bg-bloom-highlight/10';
      case 'low': return 'border-growth-accent bg-growth-accent/10';
      default: return 'border-earth-base/20 bg-mist-gradient';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return '✅';
      case 'in_progress': return '🔄';
      case 'blocked': return '🚫';
      default: return '⏳';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'feature': return '✨';
      case 'bugfix': return '🐛';
      case 'refactor': return '🔧';
      case 'testing': return '🧪';
      case 'documentation': return '📝';
      default: return '📋';
    }
  };

  return (
    <div className="h-full flex flex-col bg-mist-gradient">
      {/* Header */}
      <div className="p-6 border-b border-growth-accent/20 bg-crystal-clear">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-h2 text-forest-primary">🧠 AI Planning Canvas</h1>
            <p className="text-earth-base/70">Visual task orchestration with AI intelligence</p>
          </div>
          
          <div className="flex items-center space-x-3">
            <select
              value={currentPlan?.id || ''}
              onChange={(e) => e.target.value && loadPlan(e.target.value)}
              className="px-3 py-2 border border-growth-accent/20 rounded-lg bg-mist-gradient focus:outline-none focus:ring-2 focus:ring-growth-accent/50"
            >
              <option value="">Select Plan</option>
              {plans.map(plan => (
                <option key={plan.id} value={plan.id}>{plan.name}</option>
              ))}
            </select>
            
            <div className="flex space-x-2">
              <button
                onClick={() => setShowCreatePlan(true)}
                className="px-4 py-2 border border-growth-accent/20 rounded-lg text-earth-base hover:bg-growth-accent/5 transition-all hover-bloom"
              >
                ➕ Create Plan
              </button>
              <button
                onClick={() => generateAIPlan('feature')}
                disabled={isGenerating}
                className="px-4 py-2 bg-growth-gradient text-crystal-clear rounded-lg hover-bloom transition-all disabled:opacity-50"
              >
                {isGenerating ? '🤖' : '✨'} Feature Plan
              </button>
              <button
                onClick={() => generateAIPlan('bugfix')}
                disabled={isGenerating}
                className="px-4 py-2 bg-amber-data text-crystal-clear rounded-lg hover-bloom transition-all disabled:opacity-50"
              >
                🐛 Bugfix Plan
              </button>
              <button
                onClick={() => generateAIPlan('refactor')}
                disabled={isGenerating}
                className="px-4 py-2 bg-forest-primary text-crystal-clear rounded-lg hover-bloom transition-all disabled:opacity-50"
              >
                🔧 Refactor Plan
              </button>
            </div>
          </div>
        </div>

        {/* Plan Info */}
        {currentPlan && (
          <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-mist-gradient rounded-lg p-3">
              <div className="text-sm text-earth-base/70">Total Tasks</div>
              <div className="text-xl font-bold text-forest-primary">{currentPlan.tasks.length}</div>
            </div>
            <div className="bg-mist-gradient rounded-lg p-3">
              <div className="text-sm text-earth-base/70">Estimated Completion</div>
              <div className="text-xl font-bold text-growth-accent">{currentPlan.ai_insights.estimated_completion}</div>
            </div>
            <div className="bg-mist-gradient rounded-lg p-3">
              <div className="text-sm text-earth-base/70">Status</div>
              <div className={`text-xl font-bold capitalize ${
                currentPlan.status === 'active' ? 'text-growth-accent' :
                currentPlan.status === 'completed' ? 'text-golden-success' :
                'text-bloom-highlight'
              }`}>
                {currentPlan.status}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Canvas */}
      <div className="flex-1 flex">
        {/* Main Canvas */}
        <div 
          ref={canvasRef}
          className="flex-1 relative overflow-auto bg-gradient-to-br from-frost-light to-crystal-clear"
          onClick={handleCanvasClick}
          style={{ minHeight: '600px' }}
        >
          {isLoading ? (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="flex items-center space-x-3">
                <div className="w-4 h-4 bg-growth-accent rounded-full animate-bounce"></div>
                <div className="w-4 h-4 bg-growth-accent rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                <div className="w-4 h-4 bg-growth-accent rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                <span className="text-forest-primary font-medium">Loading AI plans...</span>
              </div>
            </div>
          ) : currentPlan ? (
            <>
              {/* Task Nodes */}
              {currentPlan.tasks.map((task) => (
                <div
                  key={task.id}
                  className={`absolute w-64 p-4 rounded-lg border-2 cursor-move hover-bloom transition-all ${getTaskColor(task)} ${
                    selectedTask?.id === task.id ? 'ring-2 ring-growth-accent shadow-lg' : ''
                  }`}
                  style={{
                    left: task.position.x,
                    top: task.position.y,
                    transform: draggedTask?.id === task.id ? 'scale(1.05)' : 'scale(1)'
                  }}
                  draggable
                  onDragStart={() => handleTaskDragStart(task)}
                  onDragEnd={handleTaskDragEnd}
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedTask(task);
                  }}
                >
                  {/* Task Header */}
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{getTypeIcon(task.type)}</span>
                      <span className="text-lg">{getStatusIcon(task.status)}</span>
                    </div>
                    <div className={`px-2 py-1 rounded text-xs font-medium ${
                      task.priority === 'critical' ? 'bg-crimson-alert text-crystal-clear' :
                      task.priority === 'high' ? 'bg-amber-data text-crystal-clear' :
                      task.priority === 'medium' ? 'bg-bloom-highlight text-earth-base' :
                      'bg-growth-accent text-crystal-clear'
                    }`}>
                      {task.priority}
                    </div>
                  </div>

                  {/* Task Content */}
                  <h3 className="font-semibold text-forest-primary mb-1 line-clamp-2">{task.title}</h3>
                  <p className="text-sm text-earth-base/70 mb-3 line-clamp-2">{task.description}</p>

                  {/* Task Metrics */}
                  <div className="flex items-center justify-between text-xs text-earth-base/60">
                    <span>⏱️ {task.estimated_hours}h</span>
                    <span>🧩 {task.complexity}/10</span>
                    <span>🔗 {task.dependencies.length}</span>
                  </div>

                  {/* AI Suggestions Preview */}
                  {task.ai_suggestions.length > 0 && (
                    <div className="mt-2 text-xs text-growth-accent">
                      💡 {task.ai_suggestions.length} AI suggestions
                    </div>
                  )}
                </div>
              ))}

              {/* Dependency Lines */}
              <svg className="absolute inset-0 pointer-events-none" style={{ zIndex: 1 }}>
                {currentPlan.tasks.map(task =>
                  task.dependencies.map(depId => {
                    const depTask = currentPlan.tasks.find(t => t.id === depId);
                    if (!depTask) return null;
                    
                    return (
                      <line
                        key={`${task.id}-${depId}`}
                        x1={depTask.position.x + 128}
                        y1={depTask.position.y + 60}
                        x2={task.position.x + 128}
                        y2={task.position.y + 60}
                        stroke="var(--growth-accent)"
                        strokeWidth="2"
                        strokeDasharray="5,5"
                        opacity="0.6"
                        markerEnd="url(#arrowhead)"
                      />
                    );
                  })
                )}
                <defs>
                  <marker
                    id="arrowhead"
                    markerWidth="10"
                    markerHeight="7"
                    refX="9"
                    refY="3.5"
                    orient="auto"
                  >
                    <polygon
                      points="0 0, 10 3.5, 0 7"
                      fill="var(--growth-accent)"
                      opacity="0.6"
                    />
                  </marker>
                </defs>
              </svg>

              {/* Milestones */}
              {currentPlan.milestones.map((milestone, index) => (
                <div
                  key={milestone.id}
                  className="absolute bg-golden-success/20 border-2 border-golden-success rounded-lg p-3 min-w-48"
                  style={{
                    left: 50,
                    top: 50 + index * 100,
                    zIndex: 10
                  }}
                >
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="text-lg">🏁</span>
                    <span className="font-semibold text-golden-success">{milestone.name}</span>
                  </div>
                  <div className="text-sm text-earth-base/70">
                    📅 {milestone.date.toLocaleDateString()}
                  </div>
                  <div className="text-xs text-earth-base/60 mt-1">
                    {milestone.tasks.length} tasks
                  </div>
                </div>
              ))}
            </>
          ) : (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="text-6xl mb-4">🧠</div>
                <h3 className="text-xl font-semibold text-forest-primary mb-2">No Plan Selected</h3>
                <p className="text-earth-base/70 mb-4">Create or select a development plan to get started</p>
                <button
                  onClick={() => generateAIPlan('feature')}
                  className="px-6 py-3 bg-growth-gradient text-crystal-clear rounded-lg hover-bloom transition-all"
                >
                  🤖 Generate AI Plan
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Task Details Panel */}
        {selectedTask && (
          <div className="w-80 border-l border-growth-accent/20 bg-crystal-clear p-6 overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-forest-primary">Task Details</h3>
              <button
                onClick={() => setSelectedTask(null)}
                className="p-1 hover:bg-growth-accent/10 rounded transition-all"
              >
                ✕
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-xl">{getTypeIcon(selectedTask.type)}</span>
                  <span className="font-medium capitalize">{selectedTask.type}</span>
                </div>
                <h4 className="font-semibold text-forest-primary">{selectedTask.title}</h4>
                <p className="text-sm text-earth-base/70 mt-1">{selectedTask.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <div className="text-xs text-earth-base/60">Status</div>
                  <div className="flex items-center space-x-1">
                    <span>{getStatusIcon(selectedTask.status)}</span>
                    <span className="capitalize">{selectedTask.status}</span>
                  </div>
                </div>
                <div>
                  <div className="text-xs text-earth-base/60">Priority</div>
                  <div className={`capitalize ${
                    selectedTask.priority === 'critical' ? 'text-crimson-alert' :
                    selectedTask.priority === 'high' ? 'text-amber-data' :
                    selectedTask.priority === 'medium' ? 'text-bloom-highlight' :
                    'text-growth-accent'
                  }`}>
                    {selectedTask.priority}
                  </div>
                </div>
                <div>
                  <div className="text-xs text-earth-base/60">Estimated Hours</div>
                  <div>{selectedTask.estimated_hours}h</div>
                </div>
                <div>
                  <div className="text-xs text-earth-base/60">Complexity</div>
                  <div>{selectedTask.complexity}/10</div>
                </div>
              </div>

              {selectedTask.dependencies.length > 0 && (
                <div>
                  <div className="text-sm font-medium text-earth-base mb-2">Dependencies</div>
                  <div className="space-y-1">
                    {selectedTask.dependencies.map(depId => {
                      const depTask = currentPlan?.tasks.find(t => t.id === depId);
                      return depTask ? (
                        <div key={depId} className="text-sm text-earth-base/70 bg-mist-gradient rounded px-2 py-1">
                          {depTask.title}
                        </div>
                      ) : null;
                    })}
                  </div>
                </div>
              )}

              {selectedTask.ai_suggestions.length > 0 && (
                <div>
                  <div className="text-sm font-medium text-earth-base mb-2">💡 AI Suggestions</div>
                  <div className="space-y-2">
                    {selectedTask.ai_suggestions.map((suggestion, index) => (
                      <div key={index} className="text-sm text-earth-base/80 bg-growth-accent/10 rounded px-3 py-2">
                        {suggestion}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="pt-4 border-t border-growth-accent/20">
                <button className="w-full px-4 py-2 bg-growth-gradient text-crystal-clear rounded-lg hover-bloom transition-all mb-2">
                  🚀 Start Task
                </button>
                <button className="w-full px-4 py-2 border border-growth-accent/20 rounded-lg text-earth-base hover:bg-growth-accent/5 transition-all">
                  ✏️ Edit Task
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Create Plan Modal */}
      {showCreatePlan && (
        <div className="fixed inset-0 bg-shadow-deep/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-crystal-clear rounded-xl max-w-md w-full p-6 animate-bloom">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-forest-primary">Create New Plan</h3>
              <button
                onClick={() => setShowCreatePlan(false)}
                className="p-1 hover:bg-growth-accent/10 rounded transition-all"
              >
                ✕
              </button>
            </div>
            <div className="space-y-4">
              <input
                type="text"
                placeholder="Plan name..."
                className="w-full px-3 py-2 border border-growth-accent/20 rounded-lg bg-mist-gradient focus:outline-none focus:ring-2 focus:ring-growth-accent/50"
              />
              <textarea
                placeholder="Plan description..."
                rows={3}
                className="w-full px-3 py-2 border border-growth-accent/20 rounded-lg bg-mist-gradient focus:outline-none focus:ring-2 focus:ring-growth-accent/50"
              />
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowCreatePlan(false)}
                  className="flex-1 px-4 py-2 border border-growth-accent/20 rounded-lg text-earth-base hover:bg-growth-accent/5 transition-all"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    // Create plan logic here
                    setShowCreatePlan(false);
                  }}
                  className="flex-1 px-4 py-2 bg-growth-gradient text-crystal-clear rounded-lg hover-bloom transition-all"
                >
                  Create Plan
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
