"""
AI Planning Engine Core - Phase 8D

Central orchestration system for AI-powered planning.
Coordinates primary planning agent and validation agent.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
import json

from .models import (
    DevelopmentPlan, PlanningRequest, PlanningResponse, 
    PlanningSession, PlanValidation
)
from .primary_planner import PrimaryPlanningAgent
from .validation_agent import PlanValidationAgent

logger = logging.getLogger(__name__)


class PlanningEngine:
    """Central AI planning engine coordinating multiple agents."""
    
    def __init__(self):
        self.primary_planner = PrimaryPlanningAgent()
        self.validation_agent = PlanValidationAgent()
        self.active_sessions: Dict[str, PlanningSession] = {}
        self.planning_enabled = True  # Can be toggled by frontend
        
        logger.info("🎯 AI Planning Engine initialized")
    
    async def create_plan(self, request: PlanningRequest, session_id: Optional[str] = None) -> PlanningResponse:
        """Create a comprehensive development plan using multi-AI approach."""
        
        if not self.planning_enabled:
            raise ValueError("AI Planning is currently disabled")
        
        logger.info(f"🎯 Starting AI-powered planning: {request.title}")
        start_time = datetime.now()
        
        try:
            # Get or create session
            session = self._get_or_create_session(session_id)
            session.add_request(request)
            
            # Step 1: Primary planning
            logger.info("🤖 Primary planning agent creating plan...")
            plan = await self.primary_planner.create_plan(request)
            
            # Step 2: Plan validation (if requested)
            validation = None
            if request.validation_required:
                logger.info("🔍 Validation agent analyzing plan...")
                validation = await self.validation_agent.validate_plan(plan)
                plan.validations.append(validation)
                plan.validation_status = "validated"
                
                # Apply improvements from validation
                await self._apply_validation_improvements(plan, validation)
            
            # Step 3: Create response
            duration = (datetime.now() - start_time).total_seconds()
            response = PlanningResponse(
                request_id=request.request_id,
                plan=plan,
                planning_agent=self.primary_planner.agent_name,
                validation_agent=self.validation_agent.agent_name if validation else None,
                planning_duration_seconds=duration,
                confidence_score=self._calculate_confidence_score(plan, validation),
                completeness_score=validation.completeness_score / 10.0 if validation else 0.8,
                feasibility_score=validation.feasibility_score / 10.0 if validation else 0.8,
                alternative_approaches=self._generate_alternative_approaches(plan),
                assumptions_made=self._extract_assumptions(plan),
                recommendations=self._generate_recommendations(plan, validation)
            )
            
            # Add to session
            session.add_response(response)
            
            logger.info(f"✅ AI planning completed in {duration:.2f}s: {plan.plan_id}")
            return response
            
        except Exception as e:
            logger.error(f"❌ AI planning failed: {e}")
            raise
    
    def _get_or_create_session(self, session_id: Optional[str] = None) -> PlanningSession:
        """Get existing session or create new one."""
        if session_id and session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            session.last_activity = datetime.now()
            return session
        
        # Create new session
        session = PlanningSession()
        self.active_sessions[session.session_id] = session
        return session
    
    async def _apply_validation_improvements(self, plan: DevelopmentPlan, validation: PlanValidation) -> None:
        """Apply improvements suggested by validation agent."""
        
        # Add identified risks
        plan.risks.extend(validation.identified_risks)
        
        # Update plan status based on validation
        if validation.overall_score >= 8.0:
            plan.validation_status = "approved"
        elif validation.overall_score >= 6.0:
            plan.validation_status = "validated"
        else:
            plan.validation_status = "needs_revision"
        
        # Add missing elements as tasks if critical
        critical_missing = [
            elem for elem in validation.missing_elements 
            if any(keyword in elem.lower() for keyword in ['test', 'security', 'deploy'])
        ]
        
        if critical_missing:
            logger.info(f"🔧 Adding {len(critical_missing)} critical missing tasks")
            # Note: In a full implementation, we would create actual tasks here
            # For now, we add them as recommendations
            plan.objectives.extend([f"Address: {elem}" for elem in critical_missing])
    
    def _calculate_confidence_score(self, plan: DevelopmentPlan, validation: Optional[PlanValidation]) -> float:
        """Calculate overall confidence score for the plan."""
        base_score = 0.7  # Base confidence
        
        # Adjust based on plan completeness
        if len(plan.tasks) >= 5:
            base_score += 0.1
        if len(plan.risks) >= 2:
            base_score += 0.05
        if len(plan.resources) >= 1:
            base_score += 0.05
        
        # Adjust based on validation
        if validation:
            validation_factor = validation.overall_score / 10.0
            base_score = (base_score + validation_factor) / 2
        
        return min(1.0, base_score)
    
    def _generate_alternative_approaches(self, plan: DevelopmentPlan) -> List[str]:
        """Generate alternative approaches based on plan analysis."""
        alternatives = []
        
        # Suggest phased approach for large plans
        if len(plan.tasks) > 10:
            alternatives.append("Phased implementation: Break into smaller releases")
        
        # Suggest parallel development for independent tasks
        independent_tasks = [t for t in plan.tasks if not t.dependencies]
        if len(independent_tasks) > 3:
            alternatives.append("Parallel development: Execute independent tasks simultaneously")
        
        # Suggest MVP approach for complex plans
        complex_tasks = [t for t in plan.tasks if t.complexity.value in ['complex', 'very_complex']]
        if len(complex_tasks) > 2:
            alternatives.append("MVP approach: Start with core features, iterate on complexity")
        
        return alternatives
    
    def _extract_assumptions(self, plan: DevelopmentPlan) -> List[str]:
        """Extract assumptions made during planning."""
        assumptions = [
            "Development team has required skills and experience",
            "No major external dependencies or blockers",
            "Standard development environment and tools available"
        ]
        
        # Add specific assumptions based on plan content
        if plan.estimated_total_hours > 80:
            assumptions.append("Team can dedicate sufficient time to complete within timeline")
        
        if any(r.level.value in ['high', 'critical'] for r in plan.risks):
            assumptions.append("High-risk items can be mitigated effectively")
        
        return assumptions
    
    def _generate_recommendations(self, plan: DevelopmentPlan, validation: Optional[PlanValidation]) -> List[str]:
        """Generate high-level recommendations."""
        recommendations = [
            "Conduct regular progress reviews and plan adjustments",
            "Maintain clear communication channels with all stakeholders"
        ]
        
        # Add validation-based recommendations
        if validation:
            if validation.overall_score < 7.0:
                recommendations.append("Consider plan revision before execution")
            
            if validation.timeline_concerns:
                recommendations.append("Review timeline estimates and add buffer time")
            
            if validation.resource_concerns:
                recommendations.append("Validate resource availability and skill alignment")
        
        # Add plan-specific recommendations
        if len(plan.risks) > 5:
            recommendations.append("Implement comprehensive risk monitoring and mitigation")
        
        if plan.estimated_total_hours > 160:
            recommendations.append("Consider breaking into multiple phases or releases")
        
        return recommendations
    
    async def get_session(self, session_id: str) -> Optional[PlanningSession]:
        """Get planning session by ID."""
        return self.active_sessions.get(session_id)
    
    async def list_sessions(self, user_id: Optional[str] = None) -> List[PlanningSession]:
        """List planning sessions, optionally filtered by user."""
        sessions = list(self.active_sessions.values())
        
        if user_id:
            sessions = [s for s in sessions if s.user_id == user_id]
        
        return sessions
    
    async def update_plan(self, plan_id: str, updates: Dict[str, Any]) -> DevelopmentPlan:
        """Update an existing plan."""
        # Find the plan in active sessions
        for session in self.active_sessions.values():
            for plan in session.plans:
                if plan.plan_id == plan_id:
                    # Apply updates
                    for key, value in updates.items():
                        if hasattr(plan, key):
                            setattr(plan, key, value)
                    
                    plan.updated_at = datetime.now()
                    return plan
        
        raise ValueError(f"Plan not found: {plan_id}")
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete a planning session."""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
            return True
        return False
    
    def toggle_planning(self, enabled: bool) -> None:
        """Toggle AI planning on/off (for frontend control)."""
        self.planning_enabled = enabled
        logger.info(f"🎯 AI Planning {'enabled' if enabled else 'disabled'}")
    
    def is_planning_enabled(self) -> bool:
        """Check if AI planning is currently enabled."""
        return self.planning_enabled
    
    async def get_planning_stats(self) -> Dict[str, Any]:
        """Get planning system statistics."""
        total_sessions = len(self.active_sessions)
        total_plans = sum(len(session.plans) for session in self.active_sessions.values())
        total_requests = sum(len(session.requests) for session in self.active_sessions.values())
        
        # Calculate average scores
        all_validations = []
        for session in self.active_sessions.values():
            for plan in session.plans:
                all_validations.extend(plan.validations)
        
        avg_overall_score = 0.0
        avg_feasibility_score = 0.0
        avg_completeness_score = 0.0
        
        if all_validations:
            avg_overall_score = sum(v.overall_score for v in all_validations) / len(all_validations)
            avg_feasibility_score = sum(v.feasibility_score for v in all_validations) / len(all_validations)
            avg_completeness_score = sum(v.completeness_score for v in all_validations) / len(all_validations)
        
        return {
            'planning_enabled': self.planning_enabled,
            'total_sessions': total_sessions,
            'total_plans': total_plans,
            'total_requests': total_requests,
            'total_validations': len(all_validations),
            'average_scores': {
                'overall': round(avg_overall_score, 2),
                'feasibility': round(avg_feasibility_score, 2),
                'completeness': round(avg_completeness_score, 2)
            },
            'primary_planner': self.primary_planner.agent_name,
            'validation_agent': self.validation_agent.agent_name
        }


# Global planning engine instance
planning_engine = PlanningEngine()
