"""
Simple Vision Model for Pydantic AI

This module implements a simple vision model that works with OpenRouter
for basic vision analysis tasks without complex tool calling.
"""

import logging
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, AsyncIterator

from pydantic_ai.models import Model
from pydantic_ai.messages import ModelResponse, TextPart
from pydantic_ai.settings import ModelSettings
from pydantic_ai.usage import Usage

from llm_client import openrouter_client

logger = logging.getLogger(__name__)


class SimpleVisionModel(Model):
    """
    Simple vision model for basic vision analysis.
    
    This model uses OpenRouter's vision-capable models for text-based responses.
    It focuses on simple text responses without complex tool calling.
    """
    
    def __init__(self, model_name: str = "google/gemini-2.0-flash-exp:free"):
        self._model_name = model_name
        self._system = "google"
        logger.info(f"🖼️  Initialized SimpleVisionModel with {model_name}")
    
    @property
    def model_name(self) -> str:
        """Return the model name."""
        return self._model_name
    
    @property
    def system(self) -> str:
        """Return the system name."""
        return self._system
    
    async def request(
        self,
        messages: List[Any],  # Pydantic AI messages
        model_settings: Optional[ModelSettings] = None,
        model_request_parameters: Optional[Dict[str, Any]] = None
    ) -> ModelResponse:
        """
        Process a request with proper Pydantic AI message handling.

        Args:
            messages: List of Pydantic AI message objects
            model_settings: Optional model settings
            model_request_parameters: Optional request parameters

        Returns:
            ModelResponse with text content
        """
        try:
            # Convert Pydantic AI messages to OpenRouter format
            openrouter_messages = []

            for message in messages:
                # Handle ModelRequest objects
                if hasattr(message, 'parts'):
                    for part in message.parts:
                        if hasattr(part, 'content'):
                            # Extract text content
                            content = part.content
                            if isinstance(content, str):
                                openrouter_messages.append({
                                    "role": "user",
                                    "content": content
                                })
                            elif isinstance(content, list):
                                # Handle multimodal content
                                text_parts = []
                                for item in content:
                                    if isinstance(item, str):
                                        text_parts.append(item)
                                    elif hasattr(item, 'content'):
                                        text_parts.append(str(item.content))
                                    else:
                                        text_parts.append(str(item))

                                if text_parts:
                                    openrouter_messages.append({
                                        "role": "user",
                                        "content": " ".join(text_parts)
                                    })

                # Handle ModelResponse objects
                elif hasattr(message, 'parts'):
                    for part in message.parts:
                        if hasattr(part, 'content'):
                            openrouter_messages.append({
                                "role": "assistant",
                                "content": str(part.content)
                            })

                # Handle simple string messages
                elif isinstance(message, str):
                    openrouter_messages.append({
                        "role": "user",
                        "content": message
                    })

                # Handle dict messages
                elif isinstance(message, dict):
                    role = message.get('role', 'user')
                    content = message.get('content', '')
                    openrouter_messages.append({
                        "role": role,
                        "content": str(content)
                    })

            # Ensure we have at least one message
            if not openrouter_messages:
                openrouter_messages = [{"role": "user", "content": "Hello"}]

            # Make request to OpenRouter
            logger.info(f"🖼️  Making vision request to {self._model_name}")

            response_text = await openrouter_client.chat_completion(
                messages=openrouter_messages,
                model=self._model_name
            )

            # Return simple text response
            return ModelResponse(
                parts=[TextPart(content=response_text)],
                model_name=self._model_name,
                timestamp=datetime.now(timezone.utc),
                usage=Usage(requests=1)
            )

        except Exception as e:
            logger.error(f"🖼️  Vision model request failed: {e}")
            # Return error as text response
            return ModelResponse(
                parts=[TextPart(content=f"Error: {str(e)}")],
                model_name=self._model_name,
                timestamp=datetime.now(timezone.utc),
                usage=Usage(requests=1)
            )
    
    async def request_stream(
        self,
        messages: List[Dict[str, Any]],
        model_settings: Optional[ModelSettings] = None,
        model_request_parameters: Optional[Dict[str, Any]] = None
    ) -> AsyncIterator[ModelResponse]:
        """
        Stream response (not implemented for vision model).
        
        Vision analysis typically requires the full response, so streaming
        is not implemented. Falls back to regular request.
        """
        response = await self.request(messages, model_settings, model_request_parameters)
        yield response


# Export the simple vision model
__all__ = ['SimpleVisionModel']
