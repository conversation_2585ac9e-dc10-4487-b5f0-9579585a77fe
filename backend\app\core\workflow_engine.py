"""
Advanced Workflow Orchestration Engine

Provides sophisticated workflow management with:
- Dynamic workflow generation
- Conditional branching and loops
- Parallel execution with dependencies
- Error recovery and rollback
- Workflow templates and reusability
- Real-time monitoring and control
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, Any, Optional, List, Callable, Union, Set
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import json
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


class WorkflowStatus(Enum):
    """Workflow execution status."""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class StepStatus(Enum):
    """Step execution status."""
    PENDING = "pending"
    READY = "ready"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    RETRYING = "retrying"


@dataclass
class WorkflowContext:
    """Workflow execution context."""
    workflow_id: str
    variables: Dict[str, Any] = field(default_factory=dict)
    shared_state: Dict[str, Any] = field(default_factory=dict)
    step_results: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class StepDefinition:
    """Definition of a workflow step."""
    step_id: str
    name: str
    handler: Callable
    dependencies: List[str] = field(default_factory=list)
    conditions: List[Callable] = field(default_factory=list)
    retry_config: Optional[Dict[str, Any]] = None
    timeout: Optional[float] = None
    parallel_group: Optional[str] = None
    rollback_handler: Optional[Callable] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class StepExecution:
    """Runtime step execution state."""
    step_id: str
    status: StepStatus = StepStatus.PENDING
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    result: Any = None
    error: Optional[str] = None
    retry_count: int = 0
    execution_time: float = 0.0


class ConditionalBranch:
    """Conditional workflow branching."""
    
    def __init__(self, condition: Callable[[WorkflowContext], bool], steps: List[StepDefinition]):
        self.condition = condition
        self.steps = steps


class WorkflowLoop:
    """Workflow loop construct."""
    
    def __init__(
        self,
        condition: Callable[[WorkflowContext], bool],
        steps: List[StepDefinition],
        max_iterations: int = 100
    ):
        self.condition = condition
        self.steps = steps
        self.max_iterations = max_iterations


class AdvancedWorkflowEngine:
    """Advanced workflow orchestration engine."""
    
    def __init__(self):
        self.active_workflows: Dict[str, 'WorkflowExecution'] = {}
        self.workflow_templates: Dict[str, 'WorkflowTemplate'] = {}
        self.execution_history: deque = deque(maxlen=1000)
        self.event_handlers: Dict[str, List[Callable]] = defaultdict(list)
    
    def register_template(self, template: 'WorkflowTemplate') -> None:
        """Register a workflow template."""
        self.workflow_templates[template.template_id] = template
        logger.info(f"Registered workflow template: {template.template_id}")
    
    def register_event_handler(self, event_type: str, handler: Callable) -> None:
        """Register an event handler."""
        self.event_handlers[event_type].append(handler)
    
    async def create_workflow_from_template(
        self,
        template_id: str,
        workflow_id: Optional[str] = None,
        variables: Optional[Dict[str, Any]] = None
    ) -> 'WorkflowExecution':
        """Create workflow from template."""
        if template_id not in self.workflow_templates:
            raise ValueError(f"Template {template_id} not found")
        
        template = self.workflow_templates[template_id]
        workflow_id = workflow_id or str(uuid.uuid4())
        
        context = WorkflowContext(
            workflow_id=workflow_id,
            variables=variables or {},
            metadata={'template_id': template_id}
        )
        
        workflow = WorkflowExecution(
            workflow_id=workflow_id,
            template=template,
            context=context,
            engine=self
        )
        
        self.active_workflows[workflow_id] = workflow
        await self._emit_event('workflow_created', workflow)
        
        return workflow
    
    async def execute_workflow(
        self,
        workflow_id: str,
        timeout: Optional[float] = None
    ) -> Dict[str, Any]:
        """Execute a workflow."""
        if workflow_id not in self.active_workflows:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        workflow = self.active_workflows[workflow_id]
        
        try:
            result = await workflow.execute(timeout=timeout)
            self.execution_history.append({
                'workflow_id': workflow_id,
                'status': workflow.status.value,
                'start_time': workflow.start_time,
                'end_time': workflow.end_time,
                'execution_time': workflow.execution_time,
                'result': result
            })
            return result
        finally:
            # Clean up completed workflows
            if workflow.status in [WorkflowStatus.COMPLETED, WorkflowStatus.FAILED, WorkflowStatus.CANCELLED]:
                del self.active_workflows[workflow_id]
    
    async def pause_workflow(self, workflow_id: str) -> bool:
        """Pause a running workflow."""
        if workflow_id in self.active_workflows:
            workflow = self.active_workflows[workflow_id]
            await workflow.pause()
            await self._emit_event('workflow_paused', workflow)
            return True
        return False
    
    async def resume_workflow(self, workflow_id: str) -> bool:
        """Resume a paused workflow."""
        if workflow_id in self.active_workflows:
            workflow = self.active_workflows[workflow_id]
            await workflow.resume()
            await self._emit_event('workflow_resumed', workflow)
            return True
        return False
    
    async def cancel_workflow(self, workflow_id: str) -> bool:
        """Cancel a workflow."""
        if workflow_id in self.active_workflows:
            workflow = self.active_workflows[workflow_id]
            await workflow.cancel()
            await self._emit_event('workflow_cancelled', workflow)
            return True
        return False
    
    async def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get workflow status."""
        if workflow_id in self.active_workflows:
            workflow = self.active_workflows[workflow_id]
            return workflow.get_status()
        return None
    
    async def _emit_event(self, event_type: str, workflow: 'WorkflowExecution') -> None:
        """Emit workflow event."""
        for handler in self.event_handlers[event_type]:
            try:
                await handler(workflow)
            except Exception as e:
                logger.error(f"Event handler failed for {event_type}: {e}")


class WorkflowTemplate:
    """Workflow template definition."""
    
    def __init__(self, template_id: str, name: str, description: str = ""):
        self.template_id = template_id
        self.name = name
        self.description = description
        self.steps: List[StepDefinition] = []
        self.branches: List[ConditionalBranch] = []
        self.loops: List[WorkflowLoop] = []
        self.variables: Dict[str, Any] = {}
        self.metadata: Dict[str, Any] = {}
    
    def add_step(self, step: StepDefinition) -> 'WorkflowTemplate':
        """Add a step to the template."""
        self.steps.append(step)
        return self
    
    def add_branch(self, branch: ConditionalBranch) -> 'WorkflowTemplate':
        """Add a conditional branch."""
        self.branches.append(branch)
        return self
    
    def add_loop(self, loop: WorkflowLoop) -> 'WorkflowTemplate':
        """Add a loop construct."""
        self.loops.append(loop)
        return self


class WorkflowExecution:
    """Runtime workflow execution."""
    
    def __init__(
        self,
        workflow_id: str,
        template: WorkflowTemplate,
        context: WorkflowContext,
        engine: AdvancedWorkflowEngine
    ):
        self.workflow_id = workflow_id
        self.template = template
        self.context = context
        self.engine = engine
        
        self.status = WorkflowStatus.PENDING
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.execution_time: float = 0.0
        
        self.step_executions: Dict[str, StepExecution] = {}
        self.completed_steps: Set[str] = set()
        self.failed_steps: Set[str] = set()
        
        self._pause_event = asyncio.Event()
        self._pause_event.set()  # Start unpaused
        self._cancel_event = asyncio.Event()
    
    async def execute(self, timeout: Optional[float] = None) -> Dict[str, Any]:
        """Execute the workflow."""
        self.status = WorkflowStatus.RUNNING
        self.start_time = datetime.now()
        
        try:
            # Execute main steps
            await self._execute_steps(self.template.steps, timeout)
            
            # Execute conditional branches
            for branch in self.template.branches:
                if branch.condition(self.context):
                    await self._execute_steps(branch.steps, timeout)
            
            # Execute loops
            for loop in self.template.loops:
                await self._execute_loop(loop, timeout)
            
            self.status = WorkflowStatus.COMPLETED
            
        except asyncio.CancelledError:
            self.status = WorkflowStatus.CANCELLED
            raise
        except Exception as e:
            self.status = WorkflowStatus.FAILED
            logger.error(f"Workflow {self.workflow_id} failed: {e}")
            raise
        finally:
            self.end_time = datetime.now()
            if self.start_time:
                self.execution_time = (self.end_time - self.start_time).total_seconds()
        
        return {
            'workflow_id': self.workflow_id,
            'status': self.status.value,
            'execution_time': self.execution_time,
            'step_results': self.context.step_results,
            'shared_state': self.context.shared_state
        }
    
    async def _execute_steps(self, steps: List[StepDefinition], timeout: Optional[float]) -> None:
        """Execute a list of steps with dependency management."""
        remaining_steps = {step.step_id: step for step in steps}
        
        while remaining_steps:
            # Check for cancellation
            if self._cancel_event.is_set():
                raise asyncio.CancelledError()
            
            # Wait if paused
            await self._pause_event.wait()
            
            # Find ready steps
            ready_steps = []
            for step_id, step in remaining_steps.items():
                if all(dep in self.completed_steps for dep in step.dependencies):
                    if all(condition(self.context) for condition in step.conditions):
                        ready_steps.append(step)
            
            if not ready_steps:
                # Check for deadlock
                if remaining_steps:
                    failed_deps = []
                    for step in remaining_steps.values():
                        missing_deps = [dep for dep in step.dependencies if dep not in self.completed_steps]
                        if missing_deps:
                            failed_deps.extend(missing_deps)
                    
                    raise RuntimeError(f"Workflow deadlock: missing dependencies {failed_deps}")
                break
            
            # Group steps by parallel group
            parallel_groups = defaultdict(list)
            for step in ready_steps:
                group = step.parallel_group or step.step_id
                parallel_groups[group].append(step)
            
            # Execute parallel groups
            for group_steps in parallel_groups.values():
                if len(group_steps) == 1:
                    # Single step execution
                    step = group_steps[0]
                    await self._execute_step(step, timeout)
                    remaining_steps.pop(step.step_id)
                else:
                    # Parallel execution
                    tasks = [self._execute_step(step, timeout) for step in group_steps]
                    await asyncio.gather(*tasks)
                    for step in group_steps:
                        remaining_steps.pop(step.step_id)
    
    async def _execute_step(self, step: StepDefinition, timeout: Optional[float]) -> None:
        """Execute a single step."""
        execution = StepExecution(step_id=step.step_id)
        self.step_executions[step.step_id] = execution
        
        execution.status = StepStatus.RUNNING
        execution.start_time = datetime.now()
        
        try:
            # Apply timeout
            step_timeout = step.timeout or timeout
            if step_timeout:
                result = await asyncio.wait_for(
                    step.handler(self.context),
                    timeout=step_timeout
                )
            else:
                result = await step.handler(self.context)
            
            execution.result = result
            execution.status = StepStatus.COMPLETED
            self.context.step_results[step.step_id] = result
            self.completed_steps.add(step.step_id)
            
        except Exception as e:
            execution.error = str(e)
            execution.status = StepStatus.FAILED
            self.failed_steps.add(step.step_id)
            
            # Handle retry
            retry_config = step.retry_config or {}
            max_retries = retry_config.get('max_retries', 0)
            
            if execution.retry_count < max_retries:
                execution.retry_count += 1
                execution.status = StepStatus.RETRYING
                
                # Wait before retry
                retry_delay = retry_config.get('delay', 1.0)
                await asyncio.sleep(retry_delay)
                
                # Retry the step
                await self._execute_step(step, timeout)
                return
            
            # Handle rollback
            if step.rollback_handler:
                try:
                    await step.rollback_handler(self.context)
                except Exception as rollback_error:
                    logger.error(f"Rollback failed for step {step.step_id}: {rollback_error}")
            
            raise
        finally:
            execution.end_time = datetime.now()
            if execution.start_time:
                execution.execution_time = (execution.end_time - execution.start_time).total_seconds()
    
    async def _execute_loop(self, loop: WorkflowLoop, timeout: Optional[float]) -> None:
        """Execute a loop construct."""
        iteration = 0
        
        while iteration < loop.max_iterations and loop.condition(self.context):
            await self._execute_steps(loop.steps, timeout)
            iteration += 1
            
            # Check for cancellation
            if self._cancel_event.is_set():
                break
    
    async def pause(self) -> None:
        """Pause workflow execution."""
        self.status = WorkflowStatus.PAUSED
        self._pause_event.clear()
    
    async def resume(self) -> None:
        """Resume workflow execution."""
        self.status = WorkflowStatus.RUNNING
        self._pause_event.set()
    
    async def cancel(self) -> None:
        """Cancel workflow execution."""
        self.status = WorkflowStatus.CANCELLED
        self._cancel_event.set()
    
    def get_status(self) -> Dict[str, Any]:
        """Get current workflow status."""
        return {
            'workflow_id': self.workflow_id,
            'status': self.status.value,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'execution_time': self.execution_time,
            'completed_steps': len(self.completed_steps),
            'failed_steps': len(self.failed_steps),
            'total_steps': len(self.template.steps),
            'step_executions': {
                step_id: {
                    'status': execution.status.value,
                    'execution_time': execution.execution_time,
                    'retry_count': execution.retry_count,
                    'error': execution.error
                }
                for step_id, execution in self.step_executions.items()
            }
        }


# Global workflow engine
workflow_engine = AdvancedWorkflowEngine()
