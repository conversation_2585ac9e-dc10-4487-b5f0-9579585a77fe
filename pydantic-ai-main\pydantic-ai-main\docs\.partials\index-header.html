<div class="text-center">
  <img class="index-header off-glb" src="./img/pydantic-ai-dark.svg#only-dark" alt="PydanticAI">
</div>
<div class="text-center">
  <img class="index-header off-glb" src="./img/pydantic-ai-light.svg#only-light" alt="PydanticAI">
</div>
<p class="text-center">
  <em>Agent Framework / shim to use Pydantic with LLMs</em>
</p>
<p class="text-center">
  <a href="https://github.com/pydantic/pydantic-ai/actions/workflows/ci.yml?query=branch%3Amain">
    <img src="https://github.com/pydantic/pydantic-ai/actions/workflows/ci.yml/badge.svg?event=push" alt="CI">
  </a>
  <a href="https://coverage-badge.samuelcolvin.workers.dev/redirect/pydantic/pydantic-ai">
    <img src="https://coverage-badge.samuelcolvin.workers.dev/pydantic/pydantic-ai.svg" alt="Coverage">
  </a>
  <a href="https://pypi.python.org/pypi/pydantic-ai">
    <img src="https://img.shields.io/pypi/v/pydantic-ai.svg" alt="PyPI">
  </a>
  <a href="https://github.com/pydantic/pydantic-ai">
    <img src="https://img.shields.io/pypi/pyversions/pydantic-ai.svg" alt="versions">
  </a>
  <a href="https://github.com/pydantic/pydantic-ai/blob/main/LICENSE">
    <img src="https://img.shields.io/github/license/pydantic/pydantic-ai.svg" alt="license">
  </a>
  <a href="https://logfire.pydantic.dev/docs/join-slack/">
    <img src="https://img.shields.io/badge/Slack-Join%20Slack-4A154B?logo=slack" alt="Join Slack" />
  </a>
</p>

<p class="text-emphasis">
  PydanticAI is a Python agent framework designed to make it less painful to
  build production grade applications with Generative AI.
</p>
