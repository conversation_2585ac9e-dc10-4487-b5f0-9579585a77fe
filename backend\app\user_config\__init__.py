"""
🔧 User Preferences & Configuration System

This module provides comprehensive user configuration management with:
- Persistent user preferences system
- Runtime model selection interface
- Tool customization and filtering
- Workspace templates and presets
- Configuration validation and defaults
"""

from .preferences_manager import (
    PreferencesManager,
    UserPreferences,
    PreferenceCategory,
    get_preferences_manager
)

from .model_selector import (
    ModelSelector,
    ModelConfig,
    ModelProvider,
    get_model_selector
)

from .tool_customizer import (
    ToolCustomizer,
    ToolConfig,
    ToolPermission,
    get_tool_customizer
)

from .workspace_manager import (
    WorkspaceManager,
    WorkspaceTemplate,
    WorkspaceConfig,
    get_workspace_manager
)

__all__ = [
    # Preferences Management
    "PreferencesManager",
    "UserPreferences",
    "PreferenceCategory",
    "get_preferences_manager",
    
    # Model Selection
    "ModelSelector",
    "ModelConfig",
    "ModelProvider",
    "get_model_selector",
    
    # Tool Customization
    "ToolCustomizer",
    "ToolConfig",
    "ToolPermission",
    "get_tool_customizer",
    
    # Workspace Management
    "WorkspaceManager",
    "WorkspaceTemplate",
    "WorkspaceConfig",
    "get_workspace_manager"
]
