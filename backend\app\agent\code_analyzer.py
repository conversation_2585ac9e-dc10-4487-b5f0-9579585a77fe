#!/usr/bin/env python3
"""
Code Analysis & Indexing Module for AI Coder Agent - Phase 3
Provides code parsing, analysis, and semantic indexing with BGE-M3 embeddings.

Features:
- Multi-language code parsing with tree-sitter
- Code structure extraction (functions, classes, imports)
- Dependency analysis and relationship mapping
- Code complexity metrics and pattern detection
- Semantic embedding generation with BGE-M3
- Integration with Qdrant vector database
"""

import os
import ast
import re
import hashlib
from pathlib import Path
from typing import Dict, List, Optional, Union, Any, Tuple
import logging
import asyncio
from concurrent.futures import ThreadPoolExecutor
import json

# Import configuration and existing clients
try:
    from ...config import settings
    from ...ollama_client import ollama_client
    from ...vector_db import qdrant_client
except ImportError:
    from config import settings
    from ollama_client import ollama_client
    from vector_db import qdrant_client

logger = logging.getLogger(__name__)

class CodeAnalysisError(Exception):
    """Custom exception for code analysis errors."""
    pass

class CodeAnalyzer:
    """Code analysis and indexing with semantic embeddings."""
    
    def __init__(self):
        self.workspace_root = Path(getattr(settings, 'workspace_root', '/app/workspace'))
        self.supported_languages = getattr(settings, 'supported_languages', [
            'python', 'javascript', 'typescript', 'java', 'cpp', 'go', 'rust'
        ])
        self.max_analysis_file_size = getattr(settings, 'max_analysis_file_size', 1_000_000)  # 1MB
        self.indexing_batch_size = getattr(settings, 'indexing_batch_size', 100)
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Language file extensions mapping
        self.language_extensions = {
            'python': ['.py', '.pyw'],
            'javascript': ['.js', '.jsx', '.mjs'],
            'typescript': ['.ts', '.tsx'],
            'java': ['.java'],
            'cpp': ['.cpp', '.cxx', '.cc', '.c', '.h', '.hpp'],
            'go': ['.go'],
            'rust': ['.rs']
        }
        
        # Reverse mapping for quick lookup
        self.extension_to_language = {}
        for lang, exts in self.language_extensions.items():
            for ext in exts:
                self.extension_to_language[ext] = lang
        
        logger.info(f"CodeAnalyzer initialized for languages: {self.supported_languages}")
    
    def _detect_language(self, file_path: Path) -> Optional[str]:
        """Detect programming language from file extension."""
        return self.extension_to_language.get(file_path.suffix.lower())
    
    def _validate_file_for_analysis(self, file_path: Path) -> bool:
        """Check if file is suitable for analysis."""
        if not file_path.exists() or not file_path.is_file():
            return False
        
        # Check file size
        if file_path.stat().st_size > self.max_analysis_file_size:
            logger.warning(f"File too large for analysis: {file_path}")
            return False
        
        # Check if language is supported
        language = self._detect_language(file_path)
        if not language or language not in self.supported_languages:
            return False
        
        return True
    
    async def parse_code_structure(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Parse code structure for functions, classes, imports, etc.
        
        Args:
            file_path: Path to code file
            
        Returns:
            Dict with parsed code structure
        """
        try:
            file_path = Path(file_path)
            
            if not self._validate_file_for_analysis(file_path):
                return {
                    'status': 'error',
                    'error': f'File not suitable for analysis: {file_path}',
                    'error_type': 'ValidationError'
                }
            
            language = self._detect_language(file_path)
            
            def _parse():
                with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                    content = f.read()
                
                if language == 'python':
                    return self._parse_python_structure(content, file_path)
                elif language in ['javascript', 'typescript']:
                    return self._parse_js_ts_structure(content, file_path)
                else:
                    # For other languages, use basic regex parsing
                    return self._parse_generic_structure(content, file_path, language)
            
            structure = await asyncio.get_event_loop().run_in_executor(self.executor, _parse)
            
            return {
                'status': 'success',
                'file_path': str(file_path),
                'language': language,
                'structure': structure
            }
            
        except Exception as e:
            logger.error(f"Failed to parse code structure for {file_path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }
    
    def _parse_python_structure(self, content: str, file_path: Path) -> Dict[str, Any]:
        """Parse Python code structure using AST."""
        try:
            tree = ast.parse(content)
            
            structure = {
                'imports': [],
                'classes': [],
                'functions': [],
                'variables': [],
                'docstring': None
            }
            
            # Get module docstring
            if (tree.body and isinstance(tree.body[0], ast.Expr) and 
                isinstance(tree.body[0].value, ast.Constant) and 
                isinstance(tree.body[0].value.value, str)):
                structure['docstring'] = tree.body[0].value.value
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        structure['imports'].append({
                            'type': 'import',
                            'module': alias.name,
                            'alias': alias.asname,
                            'line': node.lineno
                        })
                
                elif isinstance(node, ast.ImportFrom):
                    for alias in node.names:
                        structure['imports'].append({
                            'type': 'from_import',
                            'module': node.module,
                            'name': alias.name,
                            'alias': alias.asname,
                            'line': node.lineno
                        })
                
                elif isinstance(node, ast.ClassDef):
                    class_info = {
                        'name': node.name,
                        'line': node.lineno,
                        'bases': [self._get_node_name(base) for base in node.bases],
                        'methods': [],
                        'docstring': ast.get_docstring(node)
                    }
                    
                    # Get methods
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            class_info['methods'].append({
                                'name': item.name,
                                'line': item.lineno,
                                'args': [arg.arg for arg in item.args.args],
                                'docstring': ast.get_docstring(item)
                            })
                    
                    structure['classes'].append(class_info)
                
                elif isinstance(node, ast.FunctionDef) and not any(
                    isinstance(parent, ast.ClassDef) for parent in ast.walk(tree) 
                    if hasattr(parent, 'body') and node in getattr(parent, 'body', [])
                ):
                    # Only top-level functions
                    structure['functions'].append({
                        'name': node.name,
                        'line': node.lineno,
                        'args': [arg.arg for arg in node.args.args],
                        'docstring': ast.get_docstring(node),
                        'is_async': isinstance(node, ast.AsyncFunctionDef)
                    })
                
                elif isinstance(node, ast.Assign):
                    # Global variables
                    for target in node.targets:
                        if isinstance(target, ast.Name):
                            structure['variables'].append({
                                'name': target.id,
                                'line': node.lineno,
                                'type': 'assignment'
                            })
            
            return structure
            
        except SyntaxError as e:
            logger.warning(f"Syntax error in Python file {file_path}: {e}")
            return {'error': f'Syntax error: {e}'}
        except Exception as e:
            logger.error(f"Error parsing Python structure: {e}")
            return {'error': str(e)}
    
    def _get_node_name(self, node) -> str:
        """Get name from AST node."""
        if isinstance(node, ast.Name):
            return node.id
        elif isinstance(node, ast.Attribute):
            return f"{self._get_node_name(node.value)}.{node.attr}"
        else:
            return str(node)
    
    def _parse_js_ts_structure(self, content: str, file_path: Path) -> Dict[str, Any]:
        """Parse JavaScript/TypeScript structure using regex patterns."""
        structure = {
            'imports': [],
            'exports': [],
            'functions': [],
            'classes': [],
            'variables': []
        }
        
        lines = content.split('\n')
        
        # Import patterns
        import_patterns = [
            r'import\s+(.+?)\s+from\s+[\'"](.+?)[\'"]',
            r'import\s+[\'"](.+?)[\'"]',
            r'const\s+(.+?)\s*=\s*require\([\'"](.+?)[\'"]\)'
        ]
        
        # Function patterns
        function_patterns = [
            r'function\s+(\w+)\s*\(',
            r'const\s+(\w+)\s*=\s*\([^)]*\)\s*=>',
            r'(\w+)\s*:\s*function\s*\(',
            r'async\s+function\s+(\w+)\s*\('
        ]
        
        # Class patterns
        class_pattern = r'class\s+(\w+)(?:\s+extends\s+(\w+))?'
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            # Check imports
            for pattern in import_patterns:
                match = re.search(pattern, line)
                if match:
                    structure['imports'].append({
                        'line': i,
                        'match': match.groups(),
                        'raw': line
                    })
            
            # Check functions
            for pattern in function_patterns:
                match = re.search(pattern, line)
                if match:
                    structure['functions'].append({
                        'name': match.group(1),
                        'line': i,
                        'raw': line
                    })
            
            # Check classes
            match = re.search(class_pattern, line)
            if match:
                structure['classes'].append({
                    'name': match.group(1),
                    'extends': match.group(2) if match.group(2) else None,
                    'line': i,
                    'raw': line
                })
            
            # Check exports
            if 'export' in line:
                structure['exports'].append({
                    'line': i,
                    'raw': line
                })
        
        return structure
    
    def _parse_generic_structure(self, content: str, file_path: Path, language: str) -> Dict[str, Any]:
        """Generic structure parsing for other languages using regex."""
        structure = {
            'functions': [],
            'classes': [],
            'imports': [],
            'language': language
        }
        
        lines = content.split('\n')
        
        # Language-specific patterns
        patterns = {
            'java': {
                'class': r'(?:public\s+)?class\s+(\w+)',
                'method': r'(?:public\s+|private\s+|protected\s+)?(?:static\s+)?[\w<>\[\]]+\s+(\w+)\s*\(',
                'import': r'import\s+(.+?);'
            },
            'cpp': {
                'class': r'class\s+(\w+)',
                'function': r'[\w:]+\s+(\w+)\s*\([^)]*\)\s*{',
                'include': r'#include\s*[<"](.+?)[>"]'
            },
            'go': {
                'function': r'func\s+(?:\(\w+\s+\*?\w+\)\s+)?(\w+)\s*\(',
                'struct': r'type\s+(\w+)\s+struct',
                'import': r'import\s+["](.+?)["]'
            },
            'rust': {
                'function': r'fn\s+(\w+)\s*\(',
                'struct': r'struct\s+(\w+)',
                'use': r'use\s+(.+?);'
            }
        }
        
        lang_patterns = patterns.get(language, {})
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            for pattern_type, pattern in lang_patterns.items():
                match = re.search(pattern, line)
                if match:
                    if pattern_type in ['class', 'struct']:
                        structure['classes'].append({
                            'name': match.group(1),
                            'line': i,
                            'type': pattern_type
                        })
                    elif pattern_type in ['function', 'method']:
                        structure['functions'].append({
                            'name': match.group(1),
                            'line': i,
                            'type': pattern_type
                        })
                    elif pattern_type in ['import', 'include', 'use']:
                        structure['imports'].append({
                            'name': match.group(1),
                            'line': i,
                            'type': pattern_type
                        })
        
        return structure

    async def analyze_code_complexity(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Analyze code complexity metrics.

        Args:
            file_path: Path to code file

        Returns:
            Dict with complexity metrics
        """
        try:
            file_path = Path(file_path)

            if not self._validate_file_for_analysis(file_path):
                return {
                    'status': 'error',
                    'error': f'File not suitable for analysis: {file_path}',
                    'error_type': 'ValidationError'
                }

            def _analyze():
                with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                    content = f.read()

                lines = content.split('\n')

                metrics = {
                    'total_lines': len(lines),
                    'code_lines': 0,
                    'comment_lines': 0,
                    'blank_lines': 0,
                    'cyclomatic_complexity': 0,
                    'function_count': 0,
                    'class_count': 0,
                    'max_line_length': 0,
                    'avg_line_length': 0
                }

                language = self._detect_language(file_path)
                comment_patterns = self._get_comment_patterns(language)

                total_length = 0

                for line in lines:
                    stripped = line.strip()

                    if not stripped:
                        metrics['blank_lines'] += 1
                    elif any(stripped.startswith(pattern) for pattern in comment_patterns):
                        metrics['comment_lines'] += 1
                    else:
                        metrics['code_lines'] += 1

                        # Cyclomatic complexity (basic)
                        complexity_keywords = ['if', 'elif', 'else', 'for', 'while', 'try', 'except', 'case', 'switch']
                        for keyword in complexity_keywords:
                            if f' {keyword} ' in f' {stripped} ' or stripped.startswith(f'{keyword} '):
                                metrics['cyclomatic_complexity'] += 1

                    # Line length metrics
                    line_length = len(line)
                    total_length += line_length
                    if line_length > metrics['max_line_length']:
                        metrics['max_line_length'] = line_length

                if metrics['total_lines'] > 0:
                    metrics['avg_line_length'] = total_length / metrics['total_lines']

                # Count functions and classes (basic regex)
                if language == 'python':
                    metrics['function_count'] = len(re.findall(r'^\s*def\s+\w+', content, re.MULTILINE))
                    metrics['class_count'] = len(re.findall(r'^\s*class\s+\w+', content, re.MULTILINE))
                elif language in ['javascript', 'typescript']:
                    metrics['function_count'] = len(re.findall(r'function\s+\w+|=>\s*{|\w+\s*:\s*function', content))
                    metrics['class_count'] = len(re.findall(r'class\s+\w+', content))

                return metrics

            metrics = await asyncio.get_event_loop().run_in_executor(self.executor, _analyze)

            return {
                'status': 'success',
                'file_path': str(file_path),
                'language': self._detect_language(file_path),
                'metrics': metrics
            }

        except Exception as e:
            logger.error(f"Failed to analyze code complexity for {file_path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    def _get_comment_patterns(self, language: str) -> List[str]:
        """Get comment patterns for different languages."""
        patterns = {
            'python': ['#'],
            'javascript': ['//', '/*'],
            'typescript': ['//', '/*'],
            'java': ['//', '/*'],
            'cpp': ['//', '/*'],
            'go': ['//', '/*'],
            'rust': ['//', '/*']
        }
        return patterns.get(language, ['#'])

    async def generate_code_embeddings(self, file_path: Union[str, Path],
                                     chunk_size: int = 1000) -> Dict[str, Any]:
        """
        Generate semantic embeddings for code using BGE-M3.

        Args:
            file_path: Path to code file
            chunk_size: Size of code chunks for embedding

        Returns:
            Dict with embedding results
        """
        try:
            file_path = Path(file_path)

            if not self._validate_file_for_analysis(file_path):
                return {
                    'status': 'error',
                    'error': f'File not suitable for analysis: {file_path}',
                    'error_type': 'ValidationError'
                }

            def _generate():
                with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                    content = f.read()

                # Split content into chunks
                chunks = []
                lines = content.split('\n')
                current_chunk = []
                current_size = 0

                for line in lines:
                    line_size = len(line) + 1  # +1 for newline

                    if current_size + line_size > chunk_size and current_chunk:
                        chunks.append('\n'.join(current_chunk))
                        current_chunk = [line]
                        current_size = line_size
                    else:
                        current_chunk.append(line)
                        current_size += line_size

                if current_chunk:
                    chunks.append('\n'.join(current_chunk))

                return chunks

            chunks = await asyncio.get_event_loop().run_in_executor(self.executor, _generate)

            # Generate embeddings for each chunk
            embeddings = []
            for i, chunk in enumerate(chunks):
                try:
                    # Use BGE-M3 model for embeddings
                    embedding = await ollama_client.embed_text(chunk)

                    embeddings.append({
                        'chunk_index': i,
                        'content': chunk,
                        'embedding': embedding,
                        'content_hash': hashlib.md5(chunk.encode()).hexdigest()
                    })

                except Exception as e:
                    logger.error(f"Error generating embedding for chunk {i}: {e}")

            return {
                'status': 'success',
                'file_path': str(file_path),
                'language': self._detect_language(file_path),
                'total_chunks': len(chunks),
                'successful_embeddings': len(embeddings),
                'embeddings': embeddings
            }

        except Exception as e:
            logger.error(f"Failed to generate embeddings for {file_path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def index_code_file(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Index a code file with structure analysis and embeddings.

        Args:
            file_path: Path to code file

        Returns:
            Dict with indexing results
        """
        try:
            file_path = Path(file_path)

            # Get code structure
            structure_result = await self.parse_code_structure(file_path)
            if structure_result['status'] != 'success':
                return structure_result

            # Get complexity metrics
            complexity_result = await self.analyze_code_complexity(file_path)
            if complexity_result['status'] != 'success':
                return complexity_result

            # Generate embeddings
            embeddings_result = await self.generate_code_embeddings(file_path)
            if embeddings_result['status'] != 'success':
                return embeddings_result

            # Store in Qdrant
            collection_name = getattr(settings, 'code_collection_name', 'code_embeddings_bge_m3')

            points = []
            for embedding_data in embeddings_result['embeddings']:
                point_id = f"{file_path.name}_{embedding_data['chunk_index']}_{embedding_data['content_hash'][:8]}"

                payload = {
                    'file_path': str(file_path),
                    'relative_path': str(file_path.relative_to(self.workspace_root)),
                    'language': structure_result['language'],
                    'chunk_index': embedding_data['chunk_index'],
                    'content': embedding_data['content'],
                    'content_hash': embedding_data['content_hash'],
                    'structure': structure_result['structure'],
                    'complexity': complexity_result['metrics'],
                    'indexed_at': asyncio.get_event_loop().time()
                }

                points.append({
                    'id': point_id,
                    'vector': embedding_data['embedding'],
                    'payload': payload
                })

            # Upsert to Qdrant
            if points:
                try:
                    embeddings = [point['vector'] for point in points]
                    metadata = [point['payload'] for point in points]
                    ids = [point['id'] for point in points]

                    upsert_result = await qdrant_client.upsert_embeddings(
                        embeddings=embeddings,
                        metadata=metadata,
                        ids=ids
                    )
                    logger.info(f"Successfully upserted {len(points)} points to Qdrant")
                except Exception as e:
                    logger.error(f"Failed to upsert points to Qdrant: {e}")
                    return {
                        'status': 'error',
                        'error': f"Failed to store embeddings: {e}",
                        'error_type': 'QdrantError'
                    }

            return {
                'status': 'success',
                'file_path': str(file_path),
                'language': structure_result['language'],
                'chunks_indexed': len(points),
                'structure_elements': {
                    'functions': len(structure_result['structure'].get('functions', [])),
                    'classes': len(structure_result['structure'].get('classes', [])),
                    'imports': len(structure_result['structure'].get('imports', []))
                },
                'complexity_score': complexity_result['metrics'].get('cyclomatic_complexity', 0)
            }

        except Exception as e:
            logger.error(f"Failed to index code file {file_path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def search_code_semantically(self, query: str, limit: int = 10,
                                     language_filter: Optional[str] = None) -> Dict[str, Any]:
        """
        Search code semantically using embeddings.

        Args:
            query: Search query
            limit: Maximum number of results
            language_filter: Optional language filter

        Returns:
            Dict with search results
        """
        try:
            # Generate embedding for query
            query_embedding = await ollama_client.embed_text(query)

            # Search in Qdrant
            collection_name = getattr(settings, 'code_collection_name', 'code_embeddings_bge_m3')

            search_filter = {}
            if language_filter:
                search_filter['language'] = language_filter

            search_result = await qdrant_client.search_similar(
                query_embedding=query_embedding,
                top_k=limit,
                filter_conditions=search_filter
            )

            if search_result.get('status') != 'success':
                return {
                    'status': 'error',
                    'error': f"Search failed: {search_result.get('error')}",
                    'error_type': 'SearchError'
                }

            # Format results
            results = []
            for hit in search_result.get('results', []):
                payload = hit.get('payload', {})
                results.append({
                    'score': hit.get('score', 0),
                    'file_path': payload.get('file_path'),
                    'relative_path': payload.get('relative_path'),
                    'language': payload.get('language'),
                    'chunk_index': payload.get('chunk_index'),
                    'content': payload.get('content', '')[:500] + '...' if len(payload.get('content', '')) > 500 else payload.get('content', ''),
                    'structure_summary': {
                        'functions': len(payload.get('structure', {}).get('functions', [])),
                        'classes': len(payload.get('structure', {}).get('classes', [])),
                        'imports': len(payload.get('structure', {}).get('imports', []))
                    },
                    'complexity': payload.get('complexity', {}).get('cyclomatic_complexity', 0)
                })

            return {
                'status': 'success',
                'query': query,
                'language_filter': language_filter,
                'total_results': len(results),
                'results': results
            }

        except Exception as e:
            logger.error(f"Failed to search code semantically: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def analyze_project_dependencies(self, project_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Analyze project-wide dependencies and relationships.

        Args:
            project_path: Path to project directory

        Returns:
            Dict with dependency analysis
        """
        try:
            project_path = Path(project_path)

            if not project_path.exists() or not project_path.is_dir():
                return {
                    'status': 'error',
                    'error': f'Invalid project path: {project_path}',
                    'error_type': 'ValidationError'
                }

            dependencies = {
                'internal_imports': {},  # file -> [imported_files]
                'external_imports': {},  # file -> [external_modules]
                'function_calls': {},    # file -> [function_names]
                'class_inheritance': {}, # file -> [base_classes]
                'dependency_graph': {}   # file -> [dependent_files]
            }

            # Find all code files
            code_files = []
            for ext_list in self.language_extensions.values():
                for ext in ext_list:
                    code_files.extend(project_path.rglob(f'*{ext}'))

            # Analyze each file
            for file_path in code_files:
                if not self._validate_file_for_analysis(file_path):
                    continue

                try:
                    structure_result = await self.parse_code_structure(file_path)
                    if structure_result['status'] != 'success':
                        continue

                    relative_path = str(file_path.relative_to(project_path))
                    structure = structure_result['structure']

                    # Process imports
                    internal_imports = []
                    external_imports = []

                    for import_info in structure.get('imports', []):
                        if isinstance(import_info, dict):
                            module_name = import_info.get('module') or import_info.get('name', '')
                        else:
                            module_name = str(import_info)

                        # Check if it's an internal import (relative or project file)
                        if module_name.startswith('.') or any(
                            (project_path / f"{module_name.replace('.', '/')}{ext}").exists()
                            for ext in ['.py', '.js', '.ts']
                        ):
                            internal_imports.append(module_name)
                        else:
                            external_imports.append(module_name)

                    dependencies['internal_imports'][relative_path] = internal_imports
                    dependencies['external_imports'][relative_path] = external_imports

                    # Process functions and classes
                    functions = [f.get('name', '') for f in structure.get('functions', [])]
                    classes = [c.get('name', '') for c in structure.get('classes', [])]

                    dependencies['function_calls'][relative_path] = functions

                    # Process class inheritance
                    inheritance = []
                    for class_info in structure.get('classes', []):
                        if isinstance(class_info, dict) and class_info.get('bases'):
                            inheritance.extend(class_info['bases'])

                    dependencies['class_inheritance'][relative_path] = inheritance

                except Exception as e:
                    logger.warning(f"Failed to analyze dependencies for {file_path}: {e}")

            # Build dependency graph
            for file_path, imports in dependencies['internal_imports'].items():
                dependencies['dependency_graph'][file_path] = []
                for import_name in imports:
                    # Find matching files
                    for other_file in dependencies['internal_imports'].keys():
                        if import_name in other_file or other_file.replace('/', '.').replace('.py', '') == import_name:
                            dependencies['dependency_graph'][file_path].append(other_file)

            return {
                'status': 'success',
                'project_path': str(project_path),
                'total_files_analyzed': len(dependencies['internal_imports']),
                'dependencies': dependencies,
                'summary': {
                    'total_internal_imports': sum(len(imports) for imports in dependencies['internal_imports'].values()),
                    'total_external_imports': sum(len(imports) for imports in dependencies['external_imports'].values()),
                    'total_functions': sum(len(funcs) for funcs in dependencies['function_calls'].values()),
                    'files_with_inheritance': len([f for f, inh in dependencies['class_inheritance'].items() if inh])
                }
            }

        except Exception as e:
            logger.error(f"Failed to analyze project dependencies: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def search_similar_code(self, query: str, project_path: Union[str, Path] = None,
                                limit: int = 10) -> Dict[str, Any]:
        """
        Search for similar code using semantic embeddings.

        Args:
            query: Search query
            project_path: Path to project directory (optional)
            limit: Maximum number of results

        Returns:
            Dict with search results
        """
        try:
            # Use the existing semantic search method
            search_result = await self.search_code_semantically(query, limit)

            if search_result['status'] != 'success':
                return search_result

            # Filter by project path if specified
            if project_path:
                project_path = Path(project_path)
                filtered_results = []

                for result in search_result.get('results', []):
                    file_path = Path(result.get('file_path', ''))
                    try:
                        # Check if file is within project
                        file_path.relative_to(project_path)
                        filtered_results.append(result)
                    except ValueError:
                        # File is outside project, skip
                        continue

                search_result['results'] = filtered_results
                search_result['total_results'] = len(filtered_results)

            return search_result

        except Exception as e:
            logger.error(f"Failed to search similar code: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    def cleanup(self):
        """Clean up resources."""
        try:
            self.executor.shutdown(wait=True)
            logger.info("CodeAnalyzer cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Global instance
code_analyzer = CodeAnalyzer()
