#!/usr/bin/env python3
"""
Lint and Test Utilities for AI Coder Agent - Phase 5
Provides secure linting and testing automation for Python and JavaScript projects.

Features:
- Python linting with flake8
- Python testing with pytest
- JavaScript linting with eslint
- JavaScript testing with npm test
- Configurable commands via .aiagent.json
- Timeout handling for long-running operations
- Structured output parsing for LLM consumption
"""

import asyncio
import json
import logging
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import jsonschema
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

class LintTestUtils:
    """Utilities for running linting and testing operations."""
    
    def __init__(self, workspace_root: Union[str, Path] = "/app/workspace"):
        self.workspace_root = Path(workspace_root)
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Default configurations
        self.default_config = {
            "linting": {
                "python": {
                    "enabled": True,
                    "command": "flake8",
                    "args": ["--max-line-length=88", "--ignore=E203,W503"],
                    "timeout": 60
                },
                "javascript": {
                    "enabled": True,
                    "command": "eslint",
                    "args": ["--ext", ".js,.jsx,.ts,.tsx"],
                    "timeout": 60
                }
            },
            "testing": {
                "python": {
                    "enabled": True,
                    "command": "pytest",
                    "args": ["-v", "--tb=short"],
                    "timeout": 180
                },
                "javascript": {
                    "enabled": True,
                    "command": "npm test",
                    "args": ["--", "--watchAll=false"],
                    "timeout": 180
                }
            }
        }
        
        # JSON schema for .aiagent.json validation
        self.config_schema = {
            "type": "object",
            "properties": {
                "version": {"type": "string"},
                "project": {
                    "type": "object",
                    "properties": {
                        "name": {"type": "string"},
                        "type": {"type": "string"},
                        "description": {"type": "string"}
                    }
                },
                "linting": {
                    "type": "object",
                    "properties": {
                        "python": {
                            "type": "object",
                            "properties": {
                                "enabled": {"type": "boolean"},
                                "command": {"type": "string"},
                                "args": {"type": "array", "items": {"type": "string"}},
                                "timeout": {"type": "number"}
                            }
                        },
                        "javascript": {
                            "type": "object",
                            "properties": {
                                "enabled": {"type": "boolean"},
                                "command": {"type": "string"},
                                "args": {"type": "array", "items": {"type": "string"}},
                                "timeout": {"type": "number"}
                            }
                        }
                    }
                },
                "testing": {
                    "type": "object",
                    "properties": {
                        "python": {
                            "type": "object",
                            "properties": {
                                "enabled": {"type": "boolean"},
                                "command": {"type": "string"},
                                "args": {"type": "array", "items": {"type": "string"}},
                                "timeout": {"type": "number"}
                            }
                        },
                        "javascript": {
                            "type": "object",
                            "properties": {
                                "enabled": {"type": "boolean"},
                                "command": {"type": "string"},
                                "args": {"type": "array", "items": {"type": "string"}},
                                "timeout": {"type": "number"}
                            }
                        }
                    }
                }
            }
        }
    
    def _validate_path(self, path: Union[str, Path]) -> Path:
        """Validate and resolve path within workspace."""
        path = Path(path).resolve()

        # For testing purposes, allow current directory if it's /app
        if str(path) == '/app' or str(path).startswith('/app/'):
            return path

        # Ensure path is within workspace
        try:
            path.relative_to(self.workspace_root.resolve())
        except ValueError:
            # Allow relative paths that resolve to workspace
            if not path.is_absolute():
                workspace_path = self.workspace_root / path
                if workspace_path.exists():
                    return workspace_path
            raise ValueError(f"Path {path} is outside workspace {self.workspace_root}")

        return path
    
    async def load_project_config(self, project_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Load project configuration from .aiagent.json.
        
        Args:
            project_path: Path to project directory
            
        Returns:
            Dict with project configuration
        """
        try:
            project_path = self._validate_path(project_path)
            config_file = project_path / ".aiagent.json"
            
            if not config_file.exists():
                logger.info(f"No .aiagent.json found in {project_path}, using defaults")
                return self.default_config
            
            def _load_config():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # Validate against schema
                try:
                    jsonschema.validate(config, self.config_schema)
                except jsonschema.ValidationError as e:
                    logger.warning(f"Invalid .aiagent.json: {e}. Using defaults.")
                    return self.default_config
                
                # Merge with defaults
                merged_config = self.default_config.copy()
                if 'linting' in config:
                    merged_config['linting'].update(config['linting'])
                if 'testing' in config:
                    merged_config['testing'].update(config['testing'])
                
                return merged_config
            
            config = await asyncio.get_event_loop().run_in_executor(self.executor, _load_config)
            
            return {
                'status': 'success',
                'config': config,
                'config_file': str(config_file)
            }
            
        except Exception as e:
            logger.error(f"Failed to load project config from {project_path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__,
                'config': self.default_config
            }
    
    async def run_python_linting(self, project_path: Union[str, Path], 
                                files: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Run Python linting using flake8.
        
        Args:
            project_path: Path to project directory
            files: Optional list of specific files to lint
            
        Returns:
            Dict with linting results
        """
        try:
            project_path = self._validate_path(project_path)
            
            # Load project configuration
            config_result = await self.load_project_config(project_path)
            config = config_result.get('config', self.default_config)
            
            python_config = config.get('linting', {}).get('python', {})
            if not python_config.get('enabled', True):
                return {
                    'status': 'skipped',
                    'reason': 'Python linting disabled in configuration'
                }
            
            command = python_config.get('command', 'flake8')
            args = python_config.get('args', ['--max-line-length=88', '--ignore=E203,W503'])
            timeout = python_config.get('timeout', 60)
            
            # Build command
            cmd = [command] + args
            if files:
                # Validate and add specific files
                validated_files = []
                for file in files:
                    file_path = self._validate_path(project_path / file)
                    if file_path.exists() and file_path.suffix == '.py':
                        validated_files.append(str(file_path))
                cmd.extend(validated_files)
            else:
                # Lint all Python files in project
                cmd.append(str(project_path))
            
            def _run_lint():
                start_time = time.time()
                try:
                    result = subprocess.run(
                        cmd,
                        cwd=str(project_path),
                        capture_output=True,
                        text=True,
                        timeout=timeout,
                        shell=False  # Security: never use shell=True
                    )
                    
                    execution_time = time.time() - start_time
                    
                    return {
                        'returncode': result.returncode,
                        'stdout': result.stdout,
                        'stderr': result.stderr,
                        'execution_time': execution_time,
                        'command': ' '.join(cmd)
                    }
                    
                except subprocess.TimeoutExpired:
                    return {
                        'returncode': -1,
                        'stdout': '',
                        'stderr': f'Linting timed out after {timeout} seconds',
                        'execution_time': timeout,
                        'command': ' '.join(cmd),
                        'timeout': True
                    }
                except FileNotFoundError:
                    return {
                        'returncode': -1,
                        'stdout': '',
                        'stderr': f'Linting command not found: {command}',
                        'execution_time': 0,
                        'command': ' '.join(cmd),
                        'command_not_found': True
                    }
            
            result = await asyncio.get_event_loop().run_in_executor(self.executor, _run_lint)
            
            # Parse linting output
            issues = self._parse_flake8_output(result.get('stdout', ''))
            
            return {
                'status': 'success',
                'project_path': str(project_path),
                'linter': command,
                'passed': result['returncode'] == 0,
                'issues_count': len(issues),
                'issues': issues,
                'execution_time': result['execution_time'],
                'raw_output': result['stdout'],
                'raw_error': result['stderr'],
                'command': result['command']
            }
            
        except Exception as e:
            logger.error(f"Failed to run Python linting for {project_path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    def _parse_flake8_output(self, output: str) -> List[Dict[str, Any]]:
        """Parse flake8 output into structured format."""
        issues = []
        for line in output.strip().split('\n'):
            if not line.strip():
                continue

            # flake8 format: filename:line:column: error_code error_message
            parts = line.split(':', 3)
            if len(parts) >= 4:
                try:
                    issues.append({
                        'file': parts[0],
                        'line': int(parts[1]),
                        'column': int(parts[2]),
                        'message': parts[3].strip(),
                        'severity': 'error' if parts[3].strip().startswith('E') else 'warning'
                    })
                except (ValueError, IndexError):
                    # If parsing fails, include raw line
                    issues.append({
                        'file': 'unknown',
                        'line': 0,
                        'column': 0,
                        'message': line,
                        'severity': 'error'
                    })

        return issues

    async def run_python_tests(self, project_path: Union[str, Path],
                              test_files: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Run Python tests using pytest.

        Args:
            project_path: Path to project directory
            test_files: Optional list of specific test files to run

        Returns:
            Dict with test results
        """
        try:
            project_path = self._validate_path(project_path)

            # Load project configuration
            config_result = await self.load_project_config(project_path)
            config = config_result.get('config', self.default_config)

            python_config = config.get('testing', {}).get('python', {})
            if not python_config.get('enabled', True):
                return {
                    'status': 'skipped',
                    'reason': 'Python testing disabled in configuration'
                }

            command = python_config.get('command', 'pytest')
            args = python_config.get('args', ['-v', '--tb=short'])
            timeout = python_config.get('timeout', 180)

            # Build command
            cmd = [command] + args
            if test_files:
                # Validate and add specific test files
                validated_files = []
                for file in test_files:
                    file_path = self._validate_path(project_path / file)
                    if file_path.exists():
                        validated_files.append(str(file_path))
                cmd.extend(validated_files)

            def _run_tests():
                start_time = time.time()
                try:
                    result = subprocess.run(
                        cmd,
                        cwd=str(project_path),
                        capture_output=True,
                        text=True,
                        timeout=timeout,
                        shell=False  # Security: never use shell=True
                    )

                    execution_time = time.time() - start_time

                    return {
                        'returncode': result.returncode,
                        'stdout': result.stdout,
                        'stderr': result.stderr,
                        'execution_time': execution_time,
                        'command': ' '.join(cmd)
                    }

                except subprocess.TimeoutExpired:
                    return {
                        'returncode': -1,
                        'stdout': '',
                        'stderr': f'Tests timed out after {timeout} seconds',
                        'execution_time': timeout,
                        'command': ' '.join(cmd),
                        'timeout': True
                    }
                except FileNotFoundError:
                    return {
                        'returncode': -1,
                        'stdout': '',
                        'stderr': f'Test command not found: {command}',
                        'execution_time': 0,
                        'command': ' '.join(cmd),
                        'command_not_found': True
                    }

            result = await asyncio.get_event_loop().run_in_executor(self.executor, _run_tests)

            # Parse test output
            test_results = self._parse_pytest_output(result.get('stdout', ''))

            return {
                'status': 'success',
                'project_path': str(project_path),
                'test_runner': command,
                'passed': result['returncode'] == 0,
                'tests_run': test_results.get('tests_run', 0),
                'tests_passed': test_results.get('tests_passed', 0),
                'tests_failed': test_results.get('tests_failed', 0),
                'tests_skipped': test_results.get('tests_skipped', 0),
                'execution_time': result['execution_time'],
                'test_details': test_results.get('test_details', []),
                'raw_output': result['stdout'],
                'raw_error': result['stderr'],
                'command': result['command']
            }

        except Exception as e:
            logger.error(f"Failed to run Python tests for {project_path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    def _parse_pytest_output(self, output: str) -> Dict[str, Any]:
        """Parse pytest output into structured format."""
        results = {
            'tests_run': 0,
            'tests_passed': 0,
            'tests_failed': 0,
            'tests_skipped': 0,
            'test_details': []
        }

        lines = output.split('\n')
        for line in lines:
            line = line.strip()

            # Look for test result summary
            if 'passed' in line and ('failed' in line or 'error' in line or 'skipped' in line):
                # Parse summary line like "5 passed, 2 failed, 1 skipped in 2.34s"
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == 'passed' and i > 0:
                        try:
                            results['tests_passed'] = int(parts[i-1])
                        except ValueError:
                            pass
                    elif part == 'failed' and i > 0:
                        try:
                            results['tests_failed'] = int(parts[i-1])
                        except ValueError:
                            pass
                    elif part == 'skipped' and i > 0:
                        try:
                            results['tests_skipped'] = int(parts[i-1])
                        except ValueError:
                            pass

            # Look for individual test results
            if '::' in line and ('PASSED' in line or 'FAILED' in line or 'SKIPPED' in line):
                test_name = line.split()[0] if line.split() else 'unknown'
                status = 'passed'
                if 'FAILED' in line:
                    status = 'failed'
                elif 'SKIPPED' in line:
                    status = 'skipped'

                results['test_details'].append({
                    'name': test_name,
                    'status': status
                })

        results['tests_run'] = results['tests_passed'] + results['tests_failed'] + results['tests_skipped']
        return results

    async def run_javascript_linting(self, project_path: Union[str, Path],
                                   files: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Run JavaScript linting using eslint.

        Args:
            project_path: Path to project directory
            files: Optional list of specific files to lint

        Returns:
            Dict with linting results
        """
        try:
            project_path = self._validate_path(project_path)

            # Load project configuration
            config_result = await self.load_project_config(project_path)
            config = config_result.get('config', self.default_config)

            js_config = config.get('linting', {}).get('javascript', {})
            if not js_config.get('enabled', True):
                return {
                    'status': 'skipped',
                    'reason': 'JavaScript linting disabled in configuration'
                }

            command = js_config.get('command', 'eslint')
            args = js_config.get('args', ['--ext', '.js,.jsx,.ts,.tsx'])
            timeout = js_config.get('timeout', 60)

            # Build command
            cmd = [command] + args
            if files:
                # Validate and add specific files
                validated_files = []
                for file in files:
                    file_path = self._validate_path(project_path / file)
                    if file_path.exists() and file_path.suffix in ['.js', '.jsx', '.ts', '.tsx']:
                        validated_files.append(str(file_path))
                cmd.extend(validated_files)
            else:
                # Lint all JS/TS files in project
                cmd.append(str(project_path))

            def _run_lint():
                start_time = time.time()
                try:
                    result = subprocess.run(
                        cmd,
                        cwd=str(project_path),
                        capture_output=True,
                        text=True,
                        timeout=timeout,
                        shell=False  # Security: never use shell=True
                    )

                    execution_time = time.time() - start_time

                    return {
                        'returncode': result.returncode,
                        'stdout': result.stdout,
                        'stderr': result.stderr,
                        'execution_time': execution_time,
                        'command': ' '.join(cmd)
                    }

                except subprocess.TimeoutExpired:
                    return {
                        'returncode': -1,
                        'stdout': '',
                        'stderr': f'Linting timed out after {timeout} seconds',
                        'execution_time': timeout,
                        'command': ' '.join(cmd),
                        'timeout': True
                    }
                except FileNotFoundError:
                    return {
                        'returncode': -1,
                        'stdout': '',
                        'stderr': f'Linting command not found: {command}',
                        'execution_time': 0,
                        'command': ' '.join(cmd),
                        'command_not_found': True
                    }

            result = await asyncio.get_event_loop().run_in_executor(self.executor, _run_lint)

            # Parse linting output
            issues = self._parse_eslint_output(result.get('stdout', ''))

            return {
                'status': 'success',
                'project_path': str(project_path),
                'linter': command,
                'passed': result['returncode'] == 0,
                'issues_count': len(issues),
                'issues': issues,
                'execution_time': result['execution_time'],
                'raw_output': result['stdout'],
                'raw_error': result['stderr'],
                'command': result['command']
            }

        except Exception as e:
            logger.error(f"Failed to run JavaScript linting for {project_path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    def _parse_eslint_output(self, output: str) -> List[Dict[str, Any]]:
        """Parse eslint output into structured format."""
        issues = []
        for line in output.strip().split('\n'):
            if not line.strip():
                continue

            # eslint format: filename:line:column: error/warning message
            parts = line.split(':', 3)
            if len(parts) >= 4:
                try:
                    issues.append({
                        'file': parts[0],
                        'line': int(parts[1]),
                        'column': int(parts[2]),
                        'message': parts[3].strip(),
                        'severity': 'error' if 'error' in parts[3].lower() else 'warning'
                    })
                except (ValueError, IndexError):
                    # If parsing fails, include raw line
                    issues.append({
                        'file': 'unknown',
                        'line': 0,
                        'column': 0,
                        'message': line,
                        'severity': 'error'
                    })

        return issues

    async def run_javascript_tests(self, project_path: Union[str, Path],
                                 test_files: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Run JavaScript tests using npm test.

        Args:
            project_path: Path to project directory
            test_files: Optional list of specific test files to run

        Returns:
            Dict with test results
        """
        try:
            project_path = self._validate_path(project_path)

            # Load project configuration
            config_result = await self.load_project_config(project_path)
            config = config_result.get('config', self.default_config)

            js_config = config.get('testing', {}).get('javascript', {})
            if not js_config.get('enabled', True):
                return {
                    'status': 'skipped',
                    'reason': 'JavaScript testing disabled in configuration'
                }

            command = js_config.get('command', 'npm test')
            args = js_config.get('args', ['--', '--watchAll=false'])
            timeout = js_config.get('timeout', 180)

            # Build command
            cmd = command.split() + args
            if test_files:
                # Add specific test files
                cmd.extend(test_files)

            def _run_tests():
                start_time = time.time()
                try:
                    result = subprocess.run(
                        cmd,
                        cwd=str(project_path),
                        capture_output=True,
                        text=True,
                        timeout=timeout,
                        shell=False  # Security: never use shell=True
                    )

                    execution_time = time.time() - start_time

                    return {
                        'returncode': result.returncode,
                        'stdout': result.stdout,
                        'stderr': result.stderr,
                        'execution_time': execution_time,
                        'command': ' '.join(cmd)
                    }

                except subprocess.TimeoutExpired:
                    return {
                        'returncode': -1,
                        'stdout': '',
                        'stderr': f'Tests timed out after {timeout} seconds',
                        'execution_time': timeout,
                        'command': ' '.join(cmd),
                        'timeout': True
                    }
                except FileNotFoundError:
                    return {
                        'returncode': -1,
                        'stdout': '',
                        'stderr': f'Test command not found: {command}',
                        'execution_time': 0,
                        'command': ' '.join(cmd),
                        'command_not_found': True
                    }

            result = await asyncio.get_event_loop().run_in_executor(self.executor, _run_tests)

            # Parse test output
            test_results = self._parse_npm_test_output(result.get('stdout', ''))

            return {
                'status': 'success',
                'project_path': str(project_path),
                'test_runner': command,
                'passed': result['returncode'] == 0,
                'tests_run': test_results.get('tests_run', 0),
                'tests_passed': test_results.get('tests_passed', 0),
                'tests_failed': test_results.get('tests_failed', 0),
                'tests_skipped': test_results.get('tests_skipped', 0),
                'execution_time': result['execution_time'],
                'test_details': test_results.get('test_details', []),
                'raw_output': result['stdout'],
                'raw_error': result['stderr'],
                'command': result['command']
            }

        except Exception as e:
            logger.error(f"Failed to run JavaScript tests for {project_path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    def _parse_npm_test_output(self, output: str) -> Dict[str, Any]:
        """Parse npm test output into structured format."""
        results = {
            'tests_run': 0,
            'tests_passed': 0,
            'tests_failed': 0,
            'tests_skipped': 0,
            'test_details': []
        }

        lines = output.split('\n')
        for line in lines:
            line = line.strip()

            # Look for Jest test results
            if 'Tests:' in line:
                # Parse line like "Tests: 5 passed, 2 failed, 1 skipped, 8 total"
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == 'passed,' and i > 0:
                        try:
                            results['tests_passed'] = int(parts[i-1])
                        except ValueError:
                            pass
                    elif part == 'failed,' and i > 0:
                        try:
                            results['tests_failed'] = int(parts[i-1])
                        except ValueError:
                            pass
                    elif part == 'skipped,' and i > 0:
                        try:
                            results['tests_skipped'] = int(parts[i-1])
                        except ValueError:
                            pass
                    elif part == 'total' and i > 0:
                        try:
                            results['tests_run'] = int(parts[i-1])
                        except ValueError:
                            pass

            # Look for individual test results (Jest format)
            if ('✓' in line or '✗' in line or 'PASS' in line or 'FAIL' in line) and '.test.' in line:
                test_name = line.split()[1] if len(line.split()) > 1 else 'unknown'
                status = 'passed' if ('✓' in line or 'PASS' in line) else 'failed'

                results['test_details'].append({
                    'name': test_name,
                    'status': status
                })

        # If we couldn't parse the total, calculate it
        if results['tests_run'] == 0:
            results['tests_run'] = results['tests_passed'] + results['tests_failed'] + results['tests_skipped']

        return results

    async def parse_test_results(self, test_output: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse and structure test results for LLM consumption.

        Args:
            test_output: Raw test output from run_python_tests or run_javascript_tests

        Returns:
            Dict with structured test results
        """
        try:
            if test_output.get('status') != 'success':
                return test_output

            # Extract key information for LLM
            summary = {
                'status': 'success',
                'test_runner': test_output.get('test_runner', 'unknown'),
                'overall_passed': test_output.get('passed', False),
                'execution_time': test_output.get('execution_time', 0),
                'summary': {
                    'total_tests': test_output.get('tests_run', 0),
                    'passed': test_output.get('tests_passed', 0),
                    'failed': test_output.get('tests_failed', 0),
                    'skipped': test_output.get('tests_skipped', 0)
                },
                'failed_tests': [],
                'recommendations': []
            }

            # Extract failed test details
            for test in test_output.get('test_details', []):
                if test.get('status') == 'failed':
                    summary['failed_tests'].append(test['name'])

            # Generate recommendations based on results
            if not summary['overall_passed']:
                if summary['summary']['failed'] > 0:
                    summary['recommendations'].append(
                        f"Fix {summary['summary']['failed']} failing test(s)"
                    )
                if 'timeout' in test_output:
                    summary['recommendations'].append(
                        "Consider increasing test timeout or optimizing slow tests"
                    )
                if 'command_not_found' in test_output:
                    summary['recommendations'].append(
                        f"Install missing test runner: {test_output.get('test_runner', 'unknown')}"
                    )

            return summary

        except Exception as e:
            logger.error(f"Failed to parse test results: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    async def run_full_check(self, project_path: Union[str, Path],
                           language: str = 'auto') -> Dict[str, Any]:
        """
        Run full linting and testing check for a project.

        Args:
            project_path: Path to project directory
            language: Language to check ('python', 'javascript', 'auto')

        Returns:
            Dict with complete check results
        """
        try:
            project_path = self._validate_path(project_path)

            # Auto-detect language if needed
            if language == 'auto':
                language = self._detect_project_language(project_path)

            results = {
                'status': 'success',
                'project_path': str(project_path),
                'language': language,
                'timestamp': time.time(),
                'linting': {},
                'testing': {},
                'overall_passed': False
            }

            # Run linting
            if language == 'python':
                results['linting'] = await self.run_python_linting(project_path)
            elif language == 'javascript':
                results['linting'] = await self.run_javascript_linting(project_path)

            # Run testing
            if language == 'python':
                results['testing'] = await self.run_python_tests(project_path)
            elif language == 'javascript':
                results['testing'] = await self.run_javascript_tests(project_path)

            # Determine overall status
            lint_passed = results['linting'].get('passed', False)
            test_passed = results['testing'].get('passed', False)
            results['overall_passed'] = lint_passed and test_passed

            return results

        except Exception as e:
            logger.error(f"Failed to run full check for {project_path}: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__
            }

    def _detect_project_language(self, project_path: Path) -> str:
        """Detect the primary language of a project."""
        # Check for Python indicators
        if (project_path / 'requirements.txt').exists() or \
           (project_path / 'setup.py').exists() or \
           (project_path / 'pyproject.toml').exists():
            return 'python'

        # Check for JavaScript/Node.js indicators
        if (project_path / 'package.json').exists() or \
           (project_path / 'node_modules').exists():
            return 'javascript'

        # Count file types
        python_files = len(list(project_path.glob('**/*.py')))
        js_files = len(list(project_path.glob('**/*.js'))) + \
                  len(list(project_path.glob('**/*.jsx'))) + \
                  len(list(project_path.glob('**/*.ts'))) + \
                  len(list(project_path.glob('**/*.tsx')))

        if python_files > js_files:
            return 'python'
        elif js_files > 0:
            return 'javascript'

        return 'python'  # Default to Python


# Global instance
lint_test_utils = LintTestUtils()
