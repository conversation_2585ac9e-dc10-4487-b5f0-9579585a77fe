"""
Performance Benchmarking for Pydantic AI

This module provides comprehensive performance testing and benchmarking
capabilities for AI agents including response time, throughput, and resource usage.
"""

import asyncio
import time
import logging
import statistics
from typing import Any, Dict, List, Optional, Callable, Awaitable
from dataclasses import dataclass, field
from datetime import datetime, timezone

from pydantic_ai import Agent
from .test_cases import TestDataset, BaseTestCase
from .evaluators import BaseEvaluator, EvaluationResult

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics for a single test run."""
    response_time: float
    success: bool
    error_message: Optional[str] = None
    memory_usage: Optional[float] = None
    token_usage: Optional[int] = None
    tool_calls: int = 0


@dataclass
class BenchmarkResult:
    """Result of a performance benchmark."""
    test_name: str
    total_runs: int
    successful_runs: int
    failed_runs: int
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    median_response_time: float
    p95_response_time: float
    success_rate: float
    throughput: float  # requests per second
    total_duration: float
    metrics: List[PerformanceMetrics] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "test_name": self.test_name,
            "total_runs": self.total_runs,
            "successful_runs": self.successful_runs,
            "failed_runs": self.failed_runs,
            "avg_response_time": self.avg_response_time,
            "min_response_time": self.min_response_time,
            "max_response_time": self.max_response_time,
            "median_response_time": self.median_response_time,
            "p95_response_time": self.p95_response_time,
            "success_rate": self.success_rate,
            "throughput": self.throughput,
            "total_duration": self.total_duration,
            "error_summary": self._get_error_summary()
        }
    
    def _get_error_summary(self) -> Dict[str, int]:
        """Get summary of errors."""
        error_counts = {}
        for metric in self.metrics:
            if metric.error_message:
                error_counts[metric.error_message] = error_counts.get(metric.error_message, 0) + 1
        return error_counts


class PerformanceBenchmark:
    """Performance benchmark runner."""
    
    def __init__(
        self,
        agent: Agent,
        concurrency: int = 1,
        warmup_runs: int = 2,
        timeout: float = 30.0
    ):
        self.agent = agent
        self.concurrency = concurrency
        self.warmup_runs = warmup_runs
        self.timeout = timeout
    
    async def run_single_test(
        self,
        test_case: BaseTestCase,
        deps: Any = None
    ) -> PerformanceMetrics:
        """Run a single test and measure performance."""
        start_time = time.time()
        
        try:
            # Run the agent
            result = await asyncio.wait_for(
                self.agent.run(test_case.input_data, deps=deps),
                timeout=self.timeout
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            # Count tool calls (if available in result)
            tool_calls = 0
            if hasattr(result, 'messages'):
                for message in result.messages:
                    if hasattr(message, 'parts'):
                        for part in message.parts:
                            if hasattr(part, 'tool_name'):
                                tool_calls += 1
            
            return PerformanceMetrics(
                response_time=response_time,
                success=True,
                tool_calls=tool_calls
            )
            
        except asyncio.TimeoutError:
            return PerformanceMetrics(
                response_time=self.timeout,
                success=False,
                error_message="Timeout"
            )
        except Exception as e:
            end_time = time.time()
            return PerformanceMetrics(
                response_time=end_time - start_time,
                success=False,
                error_message=str(e)
            )
    
    async def run_benchmark(
        self,
        test_case: BaseTestCase,
        num_runs: int = 10,
        deps: Any = None
    ) -> BenchmarkResult:
        """Run benchmark for a single test case."""
        logger.info(f"🏃 Running benchmark for '{test_case.name}' with {num_runs} runs")
        
        # Warmup runs
        if self.warmup_runs > 0:
            logger.info(f"🔥 Running {self.warmup_runs} warmup runs...")
            for _ in range(self.warmup_runs):
                await self.run_single_test(test_case, deps)
        
        # Actual benchmark runs
        start_time = time.time()
        
        if self.concurrency == 1:
            # Sequential execution
            metrics = []
            for i in range(num_runs):
                metric = await self.run_single_test(test_case, deps)
                metrics.append(metric)
                logger.debug(f"Run {i+1}/{num_runs}: {metric.response_time:.2f}s, Success: {metric.success}")
        else:
            # Concurrent execution
            semaphore = asyncio.Semaphore(self.concurrency)
            
            async def run_with_semaphore():
                async with semaphore:
                    return await self.run_single_test(test_case, deps)
            
            tasks = [run_with_semaphore() for _ in range(num_runs)]
            metrics = await asyncio.gather(*tasks)
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # Calculate statistics
        successful_metrics = [m for m in metrics if m.success]
        response_times = [m.response_time for m in successful_metrics]
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            median_response_time = statistics.median(response_times)
            p95_response_time = self._calculate_percentile(response_times, 95)
        else:
            avg_response_time = min_response_time = max_response_time = 0.0
            median_response_time = p95_response_time = 0.0
        
        successful_runs = len(successful_metrics)
        failed_runs = num_runs - successful_runs
        success_rate = successful_runs / num_runs if num_runs > 0 else 0.0
        throughput = successful_runs / total_duration if total_duration > 0 else 0.0
        
        result = BenchmarkResult(
            test_name=test_case.name,
            total_runs=num_runs,
            successful_runs=successful_runs,
            failed_runs=failed_runs,
            avg_response_time=avg_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            median_response_time=median_response_time,
            p95_response_time=p95_response_time,
            success_rate=success_rate,
            throughput=throughput,
            total_duration=total_duration,
            metrics=metrics
        )
        
        logger.info(f"✅ Benchmark completed: {successful_runs}/{num_runs} successful, "
                   f"avg: {avg_response_time:.2f}s, throughput: {throughput:.2f} req/s")
        
        return result
    
    def _calculate_percentile(self, values: List[float], percentile: float) -> float:
        """Calculate percentile value."""
        if not values:
            return 0.0
        
        sorted_values = sorted(values)
        index = (percentile / 100.0) * (len(sorted_values) - 1)
        
        if index.is_integer():
            return sorted_values[int(index)]
        else:
            lower_index = int(index)
            upper_index = lower_index + 1
            weight = index - lower_index
            return sorted_values[lower_index] * (1 - weight) + sorted_values[upper_index] * weight


async def run_performance_tests(
    agent: Agent,
    test_dataset: TestDataset,
    deps: Any = None,
    num_runs: int = 10,
    concurrency: int = 1,
    warmup_runs: int = 2
) -> List[BenchmarkResult]:
    """Run performance tests on a dataset."""
    
    benchmark = PerformanceBenchmark(
        agent=agent,
        concurrency=concurrency,
        warmup_runs=warmup_runs
    )
    
    results = []
    
    logger.info(f"🚀 Starting performance tests on {len(test_dataset)} test cases")
    
    for i, test_case in enumerate(test_dataset):
        logger.info(f"📊 Running test {i+1}/{len(test_dataset)}: {test_case.name}")
        
        try:
            result = await benchmark.run_benchmark(test_case, num_runs, deps)
            results.append(result)
        except Exception as e:
            logger.error(f"❌ Test '{test_case.name}' failed: {e}")
            # Create a failed result
            failed_result = BenchmarkResult(
                test_name=test_case.name,
                total_runs=num_runs,
                successful_runs=0,
                failed_runs=num_runs,
                avg_response_time=0.0,
                min_response_time=0.0,
                max_response_time=0.0,
                median_response_time=0.0,
                p95_response_time=0.0,
                success_rate=0.0,
                throughput=0.0,
                total_duration=0.0
            )
            results.append(failed_result)
    
    logger.info(f"✅ Performance tests completed: {len(results)} results")
    return results


def generate_performance_report(results: List[BenchmarkResult]) -> Dict[str, Any]:
    """Generate a comprehensive performance report."""
    
    if not results:
        return {"error": "No results to report"}
    
    # Overall statistics
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r.success_rate > 0)
    failed_tests = total_tests - successful_tests
    
    # Response time statistics (only successful tests)
    successful_results = [r for r in results if r.success_rate > 0]
    if successful_results:
        avg_response_times = [r.avg_response_time for r in successful_results]
        overall_avg_response_time = statistics.mean(avg_response_times)
        overall_min_response_time = min(r.min_response_time for r in successful_results)
        overall_max_response_time = max(r.max_response_time for r in successful_results)
        overall_median_response_time = statistics.median(avg_response_times)
        
        # Throughput statistics
        throughputs = [r.throughput for r in successful_results]
        overall_avg_throughput = statistics.mean(throughputs)
        overall_max_throughput = max(throughputs)
    else:
        overall_avg_response_time = 0.0
        overall_min_response_time = 0.0
        overall_max_response_time = 0.0
        overall_median_response_time = 0.0
        overall_avg_throughput = 0.0
        overall_max_throughput = 0.0
    
    # Success rate statistics
    success_rates = [r.success_rate for r in results]
    overall_success_rate = statistics.mean(success_rates)
    
    report = {
        "summary": {
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "failed_tests": failed_tests,
            "overall_success_rate": overall_success_rate,
            "overall_avg_response_time": overall_avg_response_time,
            "overall_min_response_time": overall_min_response_time,
            "overall_max_response_time": overall_max_response_time,
            "overall_median_response_time": overall_median_response_time,
            "overall_avg_throughput": overall_avg_throughput,
            "overall_max_throughput": overall_max_throughput
        },
        "test_results": [result.to_dict() for result in results],
        "recommendations": _generate_recommendations(results),
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    
    return report


def _generate_recommendations(results: List[BenchmarkResult]) -> List[str]:
    """Generate performance recommendations based on results."""
    recommendations = []
    
    # Check success rates
    low_success_tests = [r for r in results if r.success_rate < 0.8]
    if low_success_tests:
        recommendations.append(
            f"⚠️  {len(low_success_tests)} tests have low success rates (<80%). "
            "Consider investigating error patterns and improving error handling."
        )
    
    # Check response times
    slow_tests = [r for r in results if r.avg_response_time > 10.0]
    if slow_tests:
        recommendations.append(
            f"🐌 {len(slow_tests)} tests have slow response times (>10s). "
            "Consider optimizing model calls or implementing caching."
        )
    
    # Check throughput
    successful_results = [r for r in results if r.success_rate > 0]
    if successful_results:
        avg_throughput = statistics.mean([r.throughput for r in successful_results])
        if avg_throughput < 0.1:  # Less than 0.1 requests per second
            recommendations.append(
                "📈 Low overall throughput detected. Consider implementing concurrent processing "
                "or optimizing agent response times."
            )
    
    # Check consistency
    response_time_variations = []
    for result in successful_results:
        if result.successful_runs > 1:
            variation = (result.max_response_time - result.min_response_time) / result.avg_response_time
            response_time_variations.append(variation)
    
    if response_time_variations:
        avg_variation = statistics.mean(response_time_variations)
        if avg_variation > 1.0:  # High variation
            recommendations.append(
                "📊 High response time variation detected. Consider investigating "
                "inconsistent performance patterns."
            )
    
    if not recommendations:
        recommendations.append("✅ Performance looks good! No major issues detected.")
    
    return recommendations


# Export all classes and functions
__all__ = [
    'PerformanceMetrics',
    'BenchmarkResult',
    'PerformanceBenchmark',
    'run_performance_tests',
    'generate_performance_report'
]
