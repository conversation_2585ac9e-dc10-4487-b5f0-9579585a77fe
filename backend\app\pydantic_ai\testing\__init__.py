"""
Testing Infrastructure for Pydantic AI

This module provides comprehensive testing utilities including:
- TestModel and FunctionModel integration
- Evaluation framework with metrics
- Test case generation and management
- Performance testing and benchmarking
"""

from .test_models import (
    create_test_model,
    create_function_model,
    create_coding_test_model,
    create_vision_test_model,
    create_workflow_test_model,
    TestModelConfig,
    FunctionModelConfig
)

from .evaluators import (
    CodeQualityEvaluator,
    ToolCallAccuracyEvaluator,
    ResponseTimeEvaluator,
    ComprehensiveEvaluator,
    create_coding_evaluators,
    create_vision_evaluators,
    create_workflow_evaluators
)

from .test_cases import (
    CodingTestCase,
    VisionTestCase,
    WorkflowTestCase,
    TestDataset,
    create_test_dataset,
    load_test_cases
)

from .benchmarks import (
    PerformanceBenchmark,
    run_performance_tests,
    generate_performance_report
)

__all__ = [
    # Test Models
    'create_test_model',
    'create_function_model',
    'create_coding_test_model',
    'create_vision_test_model',
    'create_workflow_test_model',
    'TestModelConfig',
    'FunctionModelConfig',
    
    # Evaluators
    'CodeQualityEvaluator',
    'ToolCallAccuracyEvaluator',
    'ResponseTimeEvaluator',
    'ComprehensiveEvaluator',
    'create_coding_evaluators',
    'create_vision_evaluators',
    'create_workflow_evaluators',
    
    # Test Cases
    'CodingTestCase',
    'VisionTestCase',
    'WorkflowTestCase',
    'TestDataset',
    'create_test_dataset',
    'load_test_cases',
    
    # Benchmarks
    'PerformanceBenchmark',
    'run_performance_tests',
    'generate_performance_report'
]
