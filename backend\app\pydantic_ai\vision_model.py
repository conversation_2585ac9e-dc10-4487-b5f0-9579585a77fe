"""
Custom Vision Model for Pydantic AI

This module implements a custom vision model that works with OpenRouter
and supports tool calling for vision analysis tasks.
"""

import json
import logging
import re
from typing import Any, Dict, List, Optional, Union, AsyncIterator

from pydantic_ai.models import Model
from pydantic_ai.messages import ModelResponse, TextPart, ToolCallPart
from pydantic_ai.tools import ToolDefinition
from pydantic_ai.settings import ModelSettings

from llm_client import openrouter_client

logger = logging.getLogger(__name__)


class VisionModel(Model):
    """
    Custom vision model that supports both text and image analysis with tool calling.
    
    This model uses OpenRouter's vision-capable models and implements tool calling
    through prompt engineering, similar to our DeepSeek R1 approach.
    """
    
    def __init__(self, model_name: str = "google/gemini-2.0-flash-exp:free"):
        self.model_name = model_name
        self.supports_tool_calling = True
        logger.info(f"🖼️  Initialized VisionModel with {model_name}")
    
    def name(self) -> str:
        """Return the model name."""
        return self.model_name
    
    async def request(
        self,
        messages: List[Dict[str, Any]],
        model_settings: Optional[ModelSettings] = None,
        model_request_parameters: Optional[Dict[str, Any]] = None
    ) -> ModelResponse:
        """
        Process a request with vision and tool calling support.
        
        Args:
            messages: List of message dictionaries
            model_settings: Optional model settings
            model_request_parameters: Optional request parameters
            
        Returns:
            ModelResponse with text and/or tool calls
        """
        try:
            # Extract tools from model_request_parameters
            tools = model_request_parameters.get('tools', []) if model_request_parameters else []
            
            # Convert messages to OpenRouter format
            openrouter_messages = self._convert_messages(messages)
            
            # Add tool information to system message if tools are available
            if tools:
                tool_prompt = self._build_tool_prompt(tools)
                
                # Add tool instructions to the last user message
                if openrouter_messages and openrouter_messages[-1].get('role') == 'user':
                    content = openrouter_messages[-1]['content']
                    if isinstance(content, str):
                        openrouter_messages[-1]['content'] = f"{content}\n\n{tool_prompt}"
                    elif isinstance(content, list):
                        # Add tool prompt as text content
                        content.append({
                            "type": "text",
                            "text": tool_prompt
                        })
            
            # Make request to OpenRouter
            logger.info(f"🖼️  Making vision request to {self.model_name}")
            
            # Use vision_analyze for image content, chat_completion for text-only
            has_images = self._has_image_content(openrouter_messages)
            
            if has_images and len(openrouter_messages) == 1:
                # Single message with image - use vision_analyze
                user_message = openrouter_messages[0]
                image_data, text_prompt = self._extract_image_and_text(user_message)
                
                if image_data:
                    response_text = await openrouter_client.vision_analyze(
                        image_base64=image_data,
                        prompt=text_prompt,
                        model=self.model_name
                    )
                else:
                    # Fallback to chat completion
                    response_text = await openrouter_client.chat_completion(
                        messages=openrouter_messages,
                        model=self.model_name
                    )
            else:
                # Multi-message or text-only - use chat completion
                response_text = await openrouter_client.chat_completion(
                    messages=openrouter_messages,
                    model=self.model_name
                )
            
            # Parse response for tool calls if tools are available
            if tools:
                return self._parse_tool_response(response_text, tools)
            else:
                # Return simple text response
                return ModelResponse(
                    parts=[TextPart(content=response_text)],
                    timestamp=None
                )
                
        except Exception as e:
            logger.error(f"🖼️  Vision model request failed: {e}")
            raise
    
    def _convert_messages(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Convert Pydantic AI messages to OpenRouter format."""
        converted = []
        
        for message in messages:
            role = message.get('role', 'user')
            content = message.get('content', '')
            
            # Handle different content types
            if isinstance(content, list):
                # Multi-part content (text + images)
                converted_content = []
                for part in content:
                    if hasattr(part, 'content'):
                        if hasattr(part, 'image_url'):
                            # Image content
                            converted_content.append({
                                "type": "image_url",
                                "image_url": {"url": part.image_url}
                            })
                        else:
                            # Text content
                            converted_content.append({
                                "type": "text", 
                                "text": str(part.content)
                            })
                    else:
                        # Direct content
                        converted_content.append({
                            "type": "text",
                            "text": str(part)
                        })
                
                converted.append({
                    "role": role,
                    "content": converted_content
                })
            else:
                # Simple text content
                converted.append({
                    "role": role,
                    "content": str(content)
                })
        
        return converted
    
    def _has_image_content(self, messages: List[Dict[str, Any]]) -> bool:
        """Check if messages contain image content."""
        for message in messages:
            content = message.get('content', [])
            if isinstance(content, list):
                for part in content:
                    if isinstance(part, dict) and part.get('type') == 'image_url':
                        return True
        return False
    
    def _extract_image_and_text(self, message: Dict[str, Any]) -> tuple[Optional[str], str]:
        """Extract image data and text from a message."""
        content = message.get('content', [])
        image_data = None
        text_parts = []
        
        if isinstance(content, list):
            for part in content:
                if isinstance(part, dict):
                    if part.get('type') == 'image_url':
                        image_url = part.get('image_url', {}).get('url', '')
                        if image_url.startswith('data:image/'):
                            # Extract base64 data
                            image_data = image_url.split(',', 1)[1] if ',' in image_url else image_url
                    elif part.get('type') == 'text':
                        text_parts.append(part.get('text', ''))
                else:
                    text_parts.append(str(part))
        else:
            text_parts.append(str(content))
        
        return image_data, ' '.join(text_parts)
    
    def _build_tool_prompt(self, tools: List[ToolDefinition]) -> str:
        """Build tool calling prompt for vision model."""
        tool_descriptions = []
        
        for tool in tools:
            tool_info = {
                "name": tool.name,
                "description": tool.description,
                "parameters": tool.parameters_json_schema
            }
            tool_descriptions.append(json.dumps(tool_info, indent=2))
        
        return f"""
You have access to the following tools for vision analysis:

{chr(10).join(tool_descriptions)}

If you need to use any tools, respond with a JSON code block containing an array of tool calls:

```json
[
  {{
    "tool": "tool_name",
    "args": {{
      "param1": "value1",
      "param2": "value2"
    }},
    "reasoning": "Why you're calling this tool"
  }}
]
```

You can call multiple tools in a single response. Always provide reasoning for each tool call.
"""
    
    def _parse_tool_response(self, response_text: str, tools: List[ToolDefinition]) -> ModelResponse:
        """Parse response text for tool calls."""
        # Look for JSON code blocks
        json_pattern = r'```(?:json)?\s*(\[.*?\])\s*```'
        matches = re.findall(json_pattern, response_text, re.DOTALL)
        
        parts = []
        
        if matches:
            # Found tool calls
            try:
                tool_calls_data = json.loads(matches[0])
                
                if isinstance(tool_calls_data, list):
                    for tool_call in tool_calls_data:
                        tool_name = tool_call.get('tool')
                        tool_args = tool_call.get('args', {})
                        
                        # Validate tool exists
                        tool_names = [t.name for t in tools]
                        if tool_name in tool_names:
                            parts.append(ToolCallPart(
                                tool_name=tool_name,
                                args=tool_args,
                                tool_call_id=f"call_{len(parts)}"
                            ))
                        else:
                            logger.warning(f"🖼️  Unknown tool in response: {tool_name}")
                
                # Add any remaining text (remove the JSON block)
                remaining_text = re.sub(json_pattern, '', response_text, flags=re.DOTALL).strip()
                if remaining_text:
                    parts.append(TextPart(content=remaining_text))
                    
            except json.JSONDecodeError as e:
                logger.warning(f"🖼️  Failed to parse tool calls JSON: {e}")
                # Treat as regular text response
                parts.append(TextPart(content=response_text))
        else:
            # No tool calls found, treat as text response
            parts.append(TextPart(content=response_text))
        
        return ModelResponse(parts=parts, timestamp=None)
    
    async def request_stream(
        self,
        messages: List[Dict[str, Any]],
        model_settings: Optional[ModelSettings] = None,
        model_request_parameters: Optional[Dict[str, Any]] = None
    ) -> AsyncIterator[ModelResponse]:
        """
        Stream response (not implemented for vision model).
        
        Vision analysis typically requires the full response, so streaming
        is not implemented. Falls back to regular request.
        """
        response = await self.request(messages, model_settings, model_request_parameters)
        yield response


# Export the vision model
__all__ = ['VisionModel']
