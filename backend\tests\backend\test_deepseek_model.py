#!/usr/bin/env python3
"""
Test script for the custom DeepSeek R1 model implementation.

This script tests both basic text responses and tool calling functionality
to ensure our custom model works correctly with Pydantic AI.
"""

import asyncio
import logging
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.pydantic_ai.deepseek_model import DeepSeekR1Model
from pydantic_ai import Agent
from pydantic_ai.tools import ToolDefinition

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_basic_response():
    """Test basic text response without tools."""
    print("\n=== Testing Basic Response ===")
    
    model = DeepSeekR1Model()
    agent = Agent(model=model)
    
    try:
        result = await agent.run("Hello! Can you tell me what 2+2 equals?")
        print(f"Response: {result.output}")
        print(f"Model: DeepSeek R1")
        return True
    except Exception as e:
        print(f"Error in basic response test: {e}")
        return False


async def test_tool_calling():
    """Test tool calling functionality."""
    print("\n=== Testing Tool Calling ===")
    
    model = DeepSeekR1Model()
    agent = Agent(model=model)
    
    @agent.tool_plain
    def add_numbers(a: int, b: int) -> int:
        """Add two numbers together."""
        return a + b
    
    @agent.tool_plain
    def get_weather(city: str) -> str:
        """Get weather information for a city."""
        return f"The weather in {city} is sunny and 72°F"
    
    try:
        # Test simple tool call
        result = await agent.run("Please add 15 and 27 using the add_numbers tool.")
        print(f"Tool call result: {result.output}")
        
        # Test another tool
        result2 = await agent.run("What's the weather like in San Francisco?")
        print(f"Weather result: {result2.output}")
        
        return True
    except Exception as e:
        print(f"Error in tool calling test: {e}")
        return False


async def test_complex_scenario():
    """Test a more complex scenario with multiple tool calls."""
    print("\n=== Testing Complex Scenario ===")
    
    model = DeepSeekR1Model()
    agent = Agent(model=model)
    
    @agent.tool_plain
    def calculate(expression: str) -> str:
        """Safely evaluate a mathematical expression."""
        try:
            # Simple evaluation for basic math
            allowed_chars = set('0123456789+-*/().')
            if all(c in allowed_chars or c.isspace() for c in expression):
                result = eval(expression)
                return str(result)
            else:
                return "Invalid expression"
        except:
            return "Error in calculation"
    
    @agent.tool_plain
    def save_result(result: str, description: str) -> str:
        """Save a calculation result with description."""
        return f"Saved: {description} = {result}"
    
    try:
        result = await agent.run(
            "Please calculate 25 * 4 + 10, then save the result with description 'Quarter calculation'"
        )
        print(f"Complex scenario result: {result.output}")
        return True
    except Exception as e:
        print(f"Error in complex scenario test: {e}")
        return False


async def test_error_handling():
    """Test error handling when tools fail."""
    print("\n=== Testing Error Handling ===")
    
    model = DeepSeekR1Model()
    agent = Agent(model=model)
    
    @agent.tool_plain
    def failing_tool(input_text: str) -> str:
        """A tool that always fails."""
        raise Exception("This tool always fails")
    
    try:
        result = await agent.run("Please use the failing_tool with input 'test'")
        print(f"Error handling result: {result.output}")
        return True
    except Exception as e:
        print(f"Error in error handling test: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Testing Custom DeepSeek R1 Model")
    print("=" * 50)
    
    tests = [
        ("Basic Response", test_basic_response),
        ("Tool Calling", test_tool_calling),
        ("Complex Scenario", test_complex_scenario),
        ("Error Handling", test_error_handling),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            success = await test_func()
            results.append((test_name, success))
            status = "✅ PASSED" if success else "❌ FAILED"
            print(f"{status}: {test_name}")
        except Exception as e:
            print(f"❌ FAILED: {test_name} - {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print("=" * 50)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status}: {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! DeepSeek R1 model is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
