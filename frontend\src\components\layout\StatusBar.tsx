import React, { useState, useEffect } from 'react';

interface StatusBarProps {
  systemHealth: {
    api: string;
    database: string;
    services: string;
  };
  currentTheme: string;
  onThemeChange: (theme: 'spring' | 'summer' | 'autumn' | 'winter') => void;
}

interface SystemMetrics {
  cpu: number;
  memory: number;
  activeConnections: number;
  requestsPerMinute: number;
  errorRate: number;
  uptime: string;
}

interface ActiveProcess {
  id: string;
  name: string;
  status: 'running' | 'completed' | 'error';
  progress?: number;
  startTime: Date;
}

export const StatusBar: React.FC<StatusBarProps> = ({
  systemHealth,
  currentTheme,
  // onThemeChange // Not used yet
}) => {
  const [metrics, setMetrics] = useState<SystemMetrics>({
    cpu: 0,
    memory: 0,
    activeConnections: 0,
    requestsPerMinute: 0,
    errorRate: 0,
    uptime: '0m'
  });
  
  const [activeProcesses, setActiveProcesses] = useState<ActiveProcess[]>([]);
  const [showDetails, setShowDetails] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Fetch system metrics every 5 seconds
  useEffect(() => {
    fetchSystemMetrics();
    const interval = setInterval(fetchSystemMetrics, 5000);
    return () => clearInterval(interval);
  }, []);

  // Fetch monitoring data every 10 seconds
  useEffect(() => {
    fetchMonitoringData();
    const interval = setInterval(fetchMonitoringData, 10000);
    return () => clearInterval(interval);
  }, []);

  const fetchSystemMetrics = async () => {
    try {
      const response = await fetch('/api/monitoring/system/metrics');
      if (response.ok) {
        const data = await response.json();
        setMetrics({
          cpu: data.cpu_usage || Math.random() * 100, // Fallback to mock data
          memory: data.memory_usage || Math.random() * 100,
          activeConnections: data.active_connections || Math.floor(Math.random() * 50),
          requestsPerMinute: data.requests_per_minute || Math.floor(Math.random() * 200),
          errorRate: data.error_rate || Math.random() * 5,
          uptime: data.uptime || '1h 23m'
        });
      }
    } catch (error) {
      console.error('Failed to fetch system metrics:', error);
      // Use mock data on error
      setMetrics(prev => ({
        ...prev,
        cpu: Math.random() * 100,
        memory: Math.random() * 100
      }));
    }
  };

  const fetchMonitoringData = async () => {
    try {
      // Fetch agent activity
      const agentResponse = await fetch('/api/monitoring/agents/activity');
      if (agentResponse.ok) {
        const agentData = await agentResponse.json();
        
        // Convert agent activities to active processes
        const processes: ActiveProcess[] = (agentData.active_agents || []).map((agent: any) => ({
          id: agent.agent_id || Math.random().toString(),
          name: agent.current_task || 'AI Processing',
          status: agent.status === 'busy' ? 'running' : 'completed',
          progress: agent.progress || Math.random() * 100,
          startTime: new Date(agent.start_time || Date.now() - Math.random() * 300000)
        }));

        setActiveProcesses(processes);
      }
    } catch (error) {
      console.error('Failed to fetch monitoring data:', error);
      // Mock some active processes
      setActiveProcesses([
        {
          id: '1',
          name: 'Code Analysis',
          status: 'running',
          progress: 65,
          startTime: new Date(Date.now() - 120000)
        }
      ]);
    }
  };

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-growth-accent';
      case 'error': return 'text-crimson-alert';
      default: return 'text-bloom-highlight';
    }
  };

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'healthy': return '🟢';
      case 'error': return '🔴';
      default: return '🟡';
    }
  };

  const formatUptime = (startTime: Date) => {
    const diff = Date.now() - startTime.getTime();
    const minutes = Math.floor(diff / 60000);
    const seconds = Math.floor((diff % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  return (
    <footer className="fixed bottom-0 left-0 right-0 bg-crystal-clear border-t border-growth-accent/20 px-4 py-2 z-40">
      <div className="flex items-center justify-between">
        
        {/* Left Section - System Health */}
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="flex items-center space-x-2 hover-bloom transition-all"
          >
            <span className="text-sm font-medium text-forest-primary">🌿 System</span>
            <div className="flex space-x-1">
              {Object.entries(systemHealth).map(([service, status]) => (
                <span key={service} className="text-xs" title={`${service}: ${status}`}>
                  {getHealthIcon(status)}
                </span>
              ))}
            </div>
          </button>

          {/* System Metrics */}
          <div className="flex items-center space-x-3 text-xs text-earth-base/70">
            <span title="CPU Usage">💻 {metrics.cpu.toFixed(1)}%</span>
            <span title="Memory Usage">🧠 {metrics.memory.toFixed(1)}%</span>
            <span title="Active Connections">🔗 {metrics.activeConnections}</span>
            <span title="Requests per minute">⚡ {metrics.requestsPerMinute}/min</span>
            {metrics.errorRate > 0 && (
              <span title="Error Rate" className="text-crimson-alert">
                ❌ {metrics.errorRate.toFixed(2)}%
              </span>
            )}
          </div>
        </div>

        {/* Center Section - Active Processes */}
        <div className="flex items-center space-x-3">
          {activeProcesses.slice(0, 3).map((process) => (
            <div
              key={process.id}
              className="flex items-center space-x-2 bg-mist-gradient rounded-lg px-3 py-1"
              title={`${process.name} - ${formatUptime(process.startTime)}`}
            >
              <span className={`w-2 h-2 rounded-full animate-breathe ${
                process.status === 'running' ? 'bg-growth-accent' :
                process.status === 'error' ? 'bg-crimson-alert' :
                'bg-golden-success'
              }`} />
              <span className="text-xs text-earth-base">{process.name}</span>
              {process.progress !== undefined && (
                <div className="w-12 h-1 bg-frost-light rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-growth-gradient transition-all duration-300"
                    style={{ width: `${process.progress}%` }}
                  />
                </div>
              )}
            </div>
          ))}
          
          {activeProcesses.length > 3 && (
            <span className="text-xs text-earth-base/60">
              +{activeProcesses.length - 3} more
            </span>
          )}
        </div>

        {/* Right Section - Time & Theme */}
        <div className="flex items-center space-x-4">
          <div className="text-xs text-earth-base/70">
            <span title="Current Time">{currentTime.toLocaleTimeString()}</span>
            <span className="mx-2">•</span>
            <span title="System Uptime">⏱️ {metrics.uptime}</span>
          </div>

          {/* Theme Indicator */}
          <div className="flex items-center space-x-1">
            <span className="text-xs text-earth-base/60">Theme:</span>
            <span className="text-xs font-medium capitalize text-forest-primary">
              {currentTheme} {
                currentTheme === 'spring' ? '🌱' :
                currentTheme === 'summer' ? '🌞' :
                currentTheme === 'autumn' ? '🍂' :
                '❄️'
              }
            </span>
          </div>
        </div>
      </div>

      {/* Detailed System Info (Expandable) */}
      {showDetails && (
        <div className="mt-3 p-3 bg-mist-gradient rounded-lg animate-bloom">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs">
            
            {/* System Health Details */}
            <div>
              <h4 className="font-semibold text-forest-primary mb-2">🏥 Health Status</h4>
              <div className="space-y-1">
                {Object.entries(systemHealth).map(([service, status]) => (
                  <div key={service} className="flex items-center justify-between">
                    <span className="capitalize">{service}:</span>
                    <span className={getHealthColor(status)}>
                      {getHealthIcon(status)} {status}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Performance Metrics */}
            <div>
              <h4 className="font-semibold text-forest-primary mb-2">📊 Performance</h4>
              <div className="space-y-1">
                <div className="flex items-center justify-between">
                  <span>CPU Usage:</span>
                  <span>{metrics.cpu.toFixed(1)}%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Memory:</span>
                  <span>{metrics.memory.toFixed(1)}%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Requests/min:</span>
                  <span>{metrics.requestsPerMinute}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Error Rate:</span>
                  <span className={metrics.errorRate > 1 ? 'text-crimson-alert' : 'text-growth-accent'}>
                    {metrics.errorRate.toFixed(2)}%
                  </span>
                </div>
              </div>
            </div>

            {/* Active Processes */}
            <div>
              <h4 className="font-semibold text-forest-primary mb-2">⚡ Active Processes</h4>
              <div className="space-y-1 max-h-20 overflow-y-auto">
                {activeProcesses.length > 0 ? (
                  activeProcesses.map((process) => (
                    <div key={process.id} className="flex items-center justify-between">
                      <span className="truncate">{process.name}</span>
                      <span className={`${
                        process.status === 'running' ? 'text-growth-accent' :
                        process.status === 'error' ? 'text-crimson-alert' :
                        'text-golden-success'
                      }`}>
                        {process.status === 'running' ? '🔄' :
                         process.status === 'error' ? '❌' : '✅'}
                      </span>
                    </div>
                  ))
                ) : (
                  <span className="text-earth-base/60">No active processes</span>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </footer>
  );
};
