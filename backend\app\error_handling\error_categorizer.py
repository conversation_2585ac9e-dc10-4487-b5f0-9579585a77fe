"""
Error Categorizer for Smart Error Classification and Routing

Provides intelligent error classification to determine appropriate handling strategies.
"""

import re
import logging
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Pattern, Set, Type, Union

from pydantic import BaseModel

# Import Pydantic AI exceptions with fallback for testing
try:
    from pydantic_ai import ModelRetry
    from pydantic_ai.exceptions import (
        UnexpectedModelBehavior,
        UsageLimitExceeded,
        ModelHTTPError,
        UserError,
        AgentRunError
    )
except ImportError:
    # Fallback for testing or when pydantic_ai is not available
    class ModelRetry(Exception):
        def __init__(self, message: str):
            self.message = message
            super().__init__(message)

    class UnexpectedModelBehavior(Exception):
        pass

    class UsageLimitExceeded(Exception):
        pass

    class ModelHTTPError(Exception):
        pass

    class UserError(Exception):
        pass

    class AgentRunError(Exception):
        pass

logger = logging.getLogger(__name__)


class ErrorCategory(Enum):
    """Categories of errors for different handling strategies."""
    
    # User-related errors
    USER_INPUT = "user_input"           # Invalid user input, validation errors
    USER_PERMISSION = "user_permission" # Permission denied, authentication
    USER_QUOTA = "user_quota"          # User quota exceeded
    
    # System errors
    SYSTEM_INTERNAL = "system_internal" # Internal system errors
    SYSTEM_CONFIG = "system_config"     # Configuration errors
    SYSTEM_RESOURCE = "system_resource" # Resource exhaustion
    
    # Network/API errors
    NETWORK_TIMEOUT = "network_timeout" # Network timeouts
    NETWORK_CONNECTION = "network_connection" # Connection failures
    API_RATE_LIMIT = "api_rate_limit"   # Rate limiting
    API_QUOTA = "api_quota"             # API quota exceeded
    API_UNAVAILABLE = "api_unavailable" # Service unavailable
    
    # Model/AI errors
    MODEL_ERROR = "model_error"         # Model processing errors
    MODEL_RETRY = "model_retry"         # Retryable model errors
    MODEL_VALIDATION = "model_validation" # Output validation errors
    MODEL_CONTEXT = "model_context"     # Context length exceeded
    
    # Tool/Function errors
    TOOL_ERROR = "tool_error"           # Tool execution errors
    TOOL_TIMEOUT = "tool_timeout"       # Tool timeout
    TOOL_VALIDATION = "tool_validation" # Tool input validation
    
    # Unknown/Other
    UNKNOWN = "unknown"                 # Unclassified errors


class ErrorSeverity(Enum):
    """Severity levels for errors."""
    LOW = "low"           # Minor issues, can continue
    MEDIUM = "medium"     # Moderate issues, may need retry
    HIGH = "high"         # Serious issues, need intervention
    CRITICAL = "critical" # Critical failures, stop execution


class ErrorRecoverability(Enum):
    """Whether an error can be recovered from."""
    RECOVERABLE = "recoverable"         # Can be retried/recovered
    PARTIALLY_RECOVERABLE = "partially" # May be recoverable with changes
    NON_RECOVERABLE = "non_recoverable" # Cannot be recovered


@dataclass
class ErrorPattern:
    """Pattern for matching and classifying errors."""
    name: str
    category: ErrorCategory
    severity: ErrorSeverity
    recoverability: ErrorRecoverability
    
    # Matching criteria
    exception_types: Set[Type[Exception]] = field(default_factory=set)
    message_patterns: List[Pattern] = field(default_factory=list)
    status_codes: Set[int] = field(default_factory=set)
    
    # Additional metadata
    description: str = ""
    suggested_actions: List[str] = field(default_factory=list)
    user_message: str = ""
    retry_recommended: bool = True
    max_retries: int = 3


@dataclass
class ErrorContext:
    """Context information about an error occurrence."""
    error: Exception
    category: ErrorCategory
    severity: ErrorSeverity
    recoverability: ErrorRecoverability
    
    # Classification details
    matched_pattern: Optional[ErrorPattern] = None
    confidence: float = 1.0
    
    # Context information
    timestamp: datetime = field(default_factory=datetime.now)
    operation: str = ""
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    
    # Additional metadata
    metadata: Dict[str, Any] = field(default_factory=dict)
    suggested_actions: List[str] = field(default_factory=list)
    user_message: str = ""
    
    # Recovery information
    retry_recommended: bool = True
    max_retries: int = 3
    recovery_strategies: List[str] = field(default_factory=list)


class ErrorCategorizer:
    """Intelligent error categorizer with pattern matching."""
    
    def __init__(self):
        self.patterns: List[ErrorPattern] = []
        self.classification_history: List[ErrorContext] = []
        self._initialize_patterns()
    
    def _initialize_patterns(self):
        """Initialize built-in error patterns."""
        
        # Pydantic AI specific patterns
        self.patterns.extend([
            ErrorPattern(
                name="model_retry",
                category=ErrorCategory.MODEL_RETRY,
                severity=ErrorSeverity.MEDIUM,
                recoverability=ErrorRecoverability.RECOVERABLE,
                exception_types={ModelRetry},
                description="Model requested retry with specific guidance",
                suggested_actions=["Follow model's retry guidance", "Adjust input parameters"],
                user_message="The AI model is refining its response. Please wait...",
                retry_recommended=True,
                max_retries=3
            ),
            
            ErrorPattern(
                name="unexpected_model_behavior",
                category=ErrorCategory.MODEL_ERROR,
                severity=ErrorSeverity.HIGH,
                recoverability=ErrorRecoverability.PARTIALLY_RECOVERABLE,
                exception_types={UnexpectedModelBehavior},
                description="Model behavior was unexpected",
                suggested_actions=["Check model configuration", "Review input format", "Try alternative model"],
                user_message="The AI model encountered an unexpected issue. Trying alternative approach...",
                retry_recommended=True,
                max_retries=2
            ),
            
            ErrorPattern(
                name="usage_limit_exceeded",
                category=ErrorCategory.API_QUOTA,
                severity=ErrorSeverity.HIGH,
                recoverability=ErrorRecoverability.NON_RECOVERABLE,
                exception_types={UsageLimitExceeded},
                description="API usage limits exceeded",
                suggested_actions=["Wait for quota reset", "Upgrade plan", "Use alternative model"],
                user_message="API usage limit reached. Please try again later or contact support.",
                retry_recommended=False,
                max_retries=0
            ),
            
            ErrorPattern(
                name="model_http_error",
                category=ErrorCategory.API_UNAVAILABLE,
                severity=ErrorSeverity.HIGH,
                recoverability=ErrorRecoverability.RECOVERABLE,
                exception_types={ModelHTTPError},
                description="HTTP error from model API",
                suggested_actions=["Retry with backoff", "Check API status", "Use fallback model"],
                user_message="Temporary API issue. Retrying...",
                retry_recommended=True,
                max_retries=3
            ),
        ])
        
        # Network and connection patterns
        self.patterns.extend([
            ErrorPattern(
                name="connection_error",
                category=ErrorCategory.NETWORK_CONNECTION,
                severity=ErrorSeverity.MEDIUM,
                recoverability=ErrorRecoverability.RECOVERABLE,
                exception_types={ConnectionError, OSError},
                message_patterns=[re.compile(r"connection.*failed", re.IGNORECASE)],
                description="Network connection failed",
                suggested_actions=["Check internet connection", "Retry with backoff", "Use alternative endpoint"],
                user_message="Connection issue detected. Retrying...",
                retry_recommended=True,
                max_retries=3
            ),
            
            ErrorPattern(
                name="timeout_error",
                category=ErrorCategory.NETWORK_TIMEOUT,
                severity=ErrorSeverity.MEDIUM,
                recoverability=ErrorRecoverability.RECOVERABLE,
                exception_types={TimeoutError},
                message_patterns=[re.compile(r"timeout", re.IGNORECASE)],
                description="Operation timed out",
                suggested_actions=["Increase timeout", "Retry with backoff", "Break into smaller operations"],
                user_message="Operation is taking longer than expected. Retrying...",
                retry_recommended=True,
                max_retries=2
            ),
        ])
        
        # Rate limiting patterns
        self.patterns.extend([
            ErrorPattern(
                name="rate_limit_429",
                category=ErrorCategory.API_RATE_LIMIT,
                severity=ErrorSeverity.MEDIUM,
                recoverability=ErrorRecoverability.RECOVERABLE,
                status_codes={429},
                message_patterns=[re.compile(r"rate.?limit", re.IGNORECASE)],
                description="API rate limit exceeded",
                suggested_actions=["Wait and retry", "Implement exponential backoff", "Reduce request frequency"],
                user_message="API rate limit reached. Waiting before retry...",
                retry_recommended=True,
                max_retries=5
            ),
        ])
        
        # Validation and user input patterns
        self.patterns.extend([
            ErrorPattern(
                name="validation_error",
                category=ErrorCategory.USER_INPUT,
                severity=ErrorSeverity.LOW,
                recoverability=ErrorRecoverability.PARTIALLY_RECOVERABLE,
                exception_types={ValueError, TypeError},
                message_patterns=[re.compile(r"validation.*failed", re.IGNORECASE)],
                description="Input validation failed",
                suggested_actions=["Check input format", "Provide valid data", "Review requirements"],
                user_message="Please check your input and try again.",
                retry_recommended=False,
                max_retries=0
            ),
        ])
    
    def add_pattern(self, pattern: ErrorPattern):
        """Add a custom error pattern."""
        self.patterns.append(pattern)
    
    def classify_error(
        self, 
        error: Exception, 
        context: Optional[Dict[str, Any]] = None
    ) -> ErrorContext:
        """Classify an error and return detailed context."""
        
        context = context or {}
        best_match = None
        best_confidence = 0.0
        
        # Try to match against patterns
        for pattern in self.patterns:
            confidence = self._calculate_match_confidence(error, pattern)
            if confidence > best_confidence:
                best_confidence = confidence
                best_match = pattern
        
        # Create error context
        if best_match:
            error_context = ErrorContext(
                error=error,
                category=best_match.category,
                severity=best_match.severity,
                recoverability=best_match.recoverability,
                matched_pattern=best_match,
                confidence=best_confidence,
                operation=context.get('operation', ''),
                user_id=context.get('user_id'),
                session_id=context.get('session_id'),
                metadata=context,
                suggested_actions=best_match.suggested_actions.copy(),
                user_message=best_match.user_message,
                retry_recommended=best_match.retry_recommended,
                max_retries=best_match.max_retries,
                recovery_strategies=self._get_recovery_strategies(best_match)
            )
        else:
            # Unknown error - use defaults
            error_context = ErrorContext(
                error=error,
                category=ErrorCategory.UNKNOWN,
                severity=ErrorSeverity.MEDIUM,
                recoverability=ErrorRecoverability.PARTIALLY_RECOVERABLE,
                confidence=0.0,
                operation=context.get('operation', ''),
                user_id=context.get('user_id'),
                session_id=context.get('session_id'),
                metadata=context,
                suggested_actions=["Review error details", "Contact support if issue persists"],
                user_message="An unexpected error occurred. Please try again.",
                retry_recommended=True,
                max_retries=1
            )
        
        # Store for analytics
        self.classification_history.append(error_context)
        
        logger.info(
            f"Classified error as {error_context.category.value} "
            f"(severity: {error_context.severity.value}, "
            f"confidence: {error_context.confidence:.2f})"
        )
        
        return error_context
    
    def _calculate_match_confidence(self, error: Exception, pattern: ErrorPattern) -> float:
        """Calculate confidence score for pattern match."""
        confidence = 0.0
        
        # Check exception type match
        if pattern.exception_types and type(error) in pattern.exception_types:
            confidence += 0.6
        
        # Check message patterns
        error_message = str(error).lower()
        for message_pattern in pattern.message_patterns:
            if message_pattern.search(error_message):
                confidence += 0.3
                break
        
        # Check status codes (for HTTP errors)
        if hasattr(error, 'status_code') and pattern.status_codes:
            if error.status_code in pattern.status_codes:
                confidence += 0.4
        
        return min(confidence, 1.0)
    
    def _get_recovery_strategies(self, pattern: ErrorPattern) -> List[str]:
        """Get recovery strategies for a pattern."""
        strategies = []
        
        if pattern.retry_recommended:
            strategies.append("retry_with_backoff")
        
        if pattern.category in [ErrorCategory.API_RATE_LIMIT, ErrorCategory.API_QUOTA]:
            strategies.append("wait_and_retry")
        
        if pattern.category == ErrorCategory.NETWORK_CONNECTION:
            strategies.append("check_connectivity")
        
        if pattern.category == ErrorCategory.MODEL_ERROR:
            strategies.append("try_alternative_model")
        
        return strategies
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get classification statistics."""
        if not self.classification_history:
            return {}
        
        category_counts = {}
        severity_counts = {}
        
        for context in self.classification_history:
            category_counts[context.category.value] = category_counts.get(context.category.value, 0) + 1
            severity_counts[context.severity.value] = severity_counts.get(context.severity.value, 0) + 1
        
        return {
            'total_classifications': len(self.classification_history),
            'category_distribution': category_counts,
            'severity_distribution': severity_counts,
            'average_confidence': sum(c.confidence for c in self.classification_history) / len(self.classification_history)
        }
