import React, { useState, useEffect } from 'react';

interface ContextNode {
  id: string;
  content: string;
  type: 'code' | 'decision' | 'file' | 'reference' | 'conversation';
  relevance_score: number;
  token_count: number;
  connections: string[];
  timestamp: Date;
  metadata: {
    file_path?: string;
    language?: string;
    importance?: number;
    category?: string;
  };
}

interface OptimizationResult {
  original_size: number;
  optimized_size: number;
  compression_ratio: number;
  tokens_saved: number;
  quality_score: number;
  preserved_elements: string[];
  removed_elements: string[];
  suggestions: string[];
}

interface ContextStats {
  total_nodes: number;
  total_tokens: number;
  avg_relevance: number;
  cache_hit_rate: number;
  optimization_potential: number;
  memory_usage: number;
}

export const ContextOptimizer: React.FC = () => {
  const [contextNodes, setContextNodes] = useState<ContextNode[]>([]);
  const [stats, setStats] = useState<ContextStats>({
    total_nodes: 0,
    total_tokens: 0,
    avg_relevance: 0,
    cache_hit_rate: 0,
    optimization_potential: 0,
    memory_usage: 0
  });
  const [optimizationResult, setOptimizationResult] = useState<OptimizationResult | null>(null);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [isCompacting, setIsCompacting] = useState(false);
  const [selectedNodes, setSelectedNodes] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'list' | 'graph' | 'timeline'>('list');
  const [filterType, setFilterType] = useState<string>('all');
  const [minRelevance, setMinRelevance] = useState(0);

  useEffect(() => {
    loadContextData();
    loadStats();
  }, []);

  const loadContextData = async () => {
    try {
      // Mock context data for development
      const mockNodes: ContextNode[] = [
        {
          id: 'node-1',
          content: 'React component for project dashboard with nature-inspired design',
          type: 'code',
          relevance_score: 9.2,
          token_count: 156,
          connections: ['node-2', 'node-3'],
          timestamp: new Date(Date.now() - 60000),
          metadata: {
            file_path: 'src/components/ProjectDashboard.tsx',
            language: 'typescript',
            importance: 9,
            category: 'ui-component'
          }
        },
        {
          id: 'node-2',
          content: 'Design system with seasonal color themes and organic animations',
          type: 'decision',
          relevance_score: 8.7,
          token_count: 89,
          connections: ['node-1', 'node-4'],
          timestamp: new Date(Date.now() - 120000),
          metadata: {
            importance: 8,
            category: 'design-system'
          }
        },
        {
          id: 'node-3',
          content: 'API integration for project management endpoints',
          type: 'reference',
          relevance_score: 7.5,
          token_count: 234,
          connections: ['node-1'],
          timestamp: new Date(Date.now() - 180000),
          metadata: {
            importance: 7,
            category: 'api-integration'
          }
        },
        {
          id: 'node-4',
          content: 'User conversation about implementing drag-and-drop functionality',
          type: 'conversation',
          relevance_score: 6.8,
          token_count: 312,
          connections: ['node-2'],
          timestamp: new Date(Date.now() - 300000),
          metadata: {
            importance: 6,
            category: 'user-feedback'
          }
        },
        {
          id: 'node-5',
          content: 'Old implementation of file explorer component',
          type: 'code',
          relevance_score: 3.2,
          token_count: 445,
          connections: [],
          timestamp: new Date(Date.now() - 3600000),
          metadata: {
            file_path: 'src/components/OldFileExplorer.tsx',
            language: 'typescript',
            importance: 3,
            category: 'deprecated'
          }
        }
      ];
      setContextNodes(mockNodes);
    } catch (error) {
      console.error('Failed to load context data:', error);
    }
  };

  const loadStats = async () => {
    try {
      const response = await fetch('/api/context/optimize/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      } else {
        // Mock stats
        setStats({
          total_nodes: 127,
          total_tokens: 15420,
          avg_relevance: 7.3,
          cache_hit_rate: 78.5,
          optimization_potential: 35.2,
          memory_usage: 2.4
        });
      }
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  };

  const optimizeContext = async () => {
    setIsOptimizing(true);
    try {
      const response = await fetch('/api/context/optimize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          strategy: 'smart_compression',
          target_reduction: 40,
          preserve_code_blocks: true,
          preserve_decisions: true,
          min_relevance_score: minRelevance
        })
      });

      if (response.ok) {
        const data = await response.json();
        setOptimizationResult(data.result);
      } else {
        // Mock optimization result
        setOptimizationResult({
          original_size: 15420,
          optimized_size: 9250,
          compression_ratio: 40.0,
          tokens_saved: 6170,
          quality_score: 8.7,
          preserved_elements: [
            'All code blocks',
            'Key decisions',
            'Recent conversations',
            'High-relevance references'
          ],
          removed_elements: [
            'Duplicate information',
            'Low-relevance conversations',
            'Outdated code snippets',
            'Redundant explanations'
          ],
          suggestions: [
            'Consider increasing relevance threshold for better compression',
            'Archive old conversations to separate storage',
            'Implement automatic cleanup for deprecated code'
          ]
        });
      }
    } catch (error) {
      console.error('Failed to optimize context:', error);
    } finally {
      setIsOptimizing(false);
    }
  };

  const compactContext = async () => {
    setIsCompacting(true);
    try {
      const response = await fetch('/api/context/compact', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          aggressive: false,
          preserve_recent: true,
          max_age_hours: 24
        })
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Compaction completed:', data);
        await loadContextData();
        await loadStats();
      }
    } catch (error) {
      console.error('Failed to compact context:', error);
    } finally {
      setIsCompacting(false);
    }
  };

  const scoreRelevance = async (nodeIds: string[]) => {
    try {
      const response = await fetch('/api/context/relevance/score-multiple', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          node_ids: nodeIds,
          context: 'current_development_session'
        })
      });

      if (response.ok) {
        const data = await response.json();
        // Update nodes with new relevance scores
        setContextNodes(prev => prev.map(node => {
          const scored = data.scores.find((s: any) => s.node_id === node.id);
          return scored ? { ...node, relevance_score: scored.score } : node;
        }));
      }
    } catch (error) {
      console.error('Failed to score relevance:', error);
    }
  };

  const getNodeColor = (node: ContextNode) => {
    if (node.relevance_score >= 8) return 'border-growth-accent bg-growth-accent/10';
    if (node.relevance_score >= 6) return 'border-bloom-highlight bg-bloom-highlight/10';
    if (node.relevance_score >= 4) return 'border-amber-data bg-amber-data/10';
    return 'border-earth-base/30 bg-earth-base/5';
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'code': return '💻';
      case 'decision': return '🎯';
      case 'file': return '📄';
      case 'reference': return '🔗';
      case 'conversation': return '💬';
      default: return '📋';
    }
  };

  const filteredNodes = contextNodes.filter(node => {
    if (filterType !== 'all' && node.type !== filterType) return false;
    if (node.relevance_score < minRelevance) return false;
    return true;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-h2 text-forest-primary">🎯 Context Optimizer</h1>
          <p className="text-earth-base/70">Intelligent context compression and relevance optimization</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={compactContext}
            disabled={isCompacting}
            className="px-4 py-2 border border-growth-accent/20 rounded-lg text-earth-base hover:bg-growth-accent/5 transition-all hover-bloom disabled:opacity-50"
          >
            {isCompacting ? '🔄' : '🗜️'} Compact
          </button>
          <button
            onClick={optimizeContext}
            disabled={isOptimizing}
            className="px-6 py-3 bg-growth-gradient text-crystal-clear rounded-lg font-medium hover-bloom transition-all disabled:opacity-50"
          >
            {isOptimizing ? '🧠 Optimizing...' : '⚡ Optimize'}
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        {[
          { label: 'Total Nodes', value: stats.total_nodes, icon: '🔗', color: 'text-forest-primary' },
          { label: 'Total Tokens', value: stats.total_tokens.toLocaleString(), icon: '🔤', color: 'text-bloom-highlight' },
          { label: 'Avg Relevance', value: `${stats.avg_relevance.toFixed(1)}/10`, icon: '🎯', color: 'text-growth-accent' },
          { label: 'Cache Hit Rate', value: `${stats.cache_hit_rate.toFixed(1)}%`, icon: '💾', color: 'text-golden-success' },
          { label: 'Optimization', value: `${stats.optimization_potential.toFixed(1)}%`, icon: '⚡', color: 'text-amber-data' },
          { label: 'Memory', value: `${stats.memory_usage.toFixed(1)}MB`, icon: '🧠', color: 'text-crimson-alert' }
        ].map((stat) => (
          <div key={stat.label} className="bg-crystal-clear rounded-lg p-4 border border-growth-accent/20 hover-bloom">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-earth-base/60">{stat.label}</p>
                <p className={`text-lg font-bold ${stat.color}`}>{stat.value}</p>
              </div>
              <span className="text-2xl">{stat.icon}</span>
            </div>
          </div>
        ))}
      </div>

      {/* Optimization Result */}
      {optimizationResult && (
        <div className="bg-crystal-clear rounded-lg p-6 border border-growth-accent/20 animate-bloom">
          <h2 className="text-lg font-semibold text-forest-primary mb-4">🎉 Optimization Complete!</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-crimson-alert">{optimizationResult.original_size.toLocaleString()}</div>
              <div className="text-sm text-earth-base/60">Original Tokens</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-growth-accent">{optimizationResult.optimized_size.toLocaleString()}</div>
              <div className="text-sm text-earth-base/60">Optimized Tokens</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-golden-success">{optimizationResult.compression_ratio.toFixed(1)}%</div>
              <div className="text-sm text-earth-base/60">Compression</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-bloom-highlight">{optimizationResult.quality_score.toFixed(1)}/10</div>
              <div className="text-sm text-earth-base/60">Quality Score</div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-forest-primary mb-3">✅ Preserved Elements</h3>
              <div className="space-y-2">
                {optimizationResult.preserved_elements.map((element, index) => (
                  <div key={index} className="bg-growth-accent/10 rounded px-3 py-2 text-sm text-growth-accent">
                    {element}
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-forest-primary mb-3">🗑️ Removed Elements</h3>
              <div className="space-y-2">
                {optimizationResult.removed_elements.map((element, index) => (
                  <div key={index} className="bg-earth-base/10 rounded px-3 py-2 text-sm text-earth-base/70">
                    {element}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {optimizationResult.suggestions.length > 0 && (
            <div className="mt-6">
              <h3 className="font-semibold text-forest-primary mb-3">💡 AI Suggestions</h3>
              <div className="space-y-2">
                {optimizationResult.suggestions.map((suggestion, index) => (
                  <div key={index} className="bg-bloom-highlight/10 rounded px-3 py-2 text-sm text-bloom-highlight">
                    {suggestion}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Controls */}
      <div className="flex items-center justify-between bg-crystal-clear rounded-lg p-4 border border-growth-accent/20">
        <div className="flex items-center space-x-4">
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="px-3 py-2 border border-growth-accent/20 rounded-lg bg-mist-gradient focus:outline-none focus:ring-2 focus:ring-growth-accent/50"
          >
            <option value="all">All Types</option>
            <option value="code">Code</option>
            <option value="decision">Decisions</option>
            <option value="file">Files</option>
            <option value="reference">References</option>
            <option value="conversation">Conversations</option>
          </select>

          <div className="flex items-center space-x-2">
            <label className="text-sm text-earth-base/70">Min Relevance:</label>
            <input
              type="range"
              min="0"
              max="10"
              step="0.1"
              value={minRelevance}
              onChange={(e) => setMinRelevance(parseFloat(e.target.value))}
              className="w-24"
            />
            <span className="text-sm font-medium text-earth-base">{minRelevance.toFixed(1)}</span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {(['list', 'graph', 'timeline'] as const).map((mode) => (
            <button
              key={mode}
              onClick={() => setViewMode(mode)}
              className={`p-2 rounded-lg transition-all hover-bloom ${
                viewMode === mode 
                  ? 'bg-growth-accent text-crystal-clear' 
                  : 'bg-mist-gradient text-earth-base hover:bg-growth-accent/10'
              }`}
              title={`${mode} view`}
            >
              {mode === 'list' ? '☰' : mode === 'graph' ? '🕸️' : '📈'}
            </button>
          ))}
        </div>
      </div>

      {/* Context Nodes */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-forest-primary">
            Context Nodes ({filteredNodes.length})
          </h2>
          {selectedNodes.length > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-earth-base/70">{selectedNodes.length} selected</span>
              <button
                onClick={() => scoreRelevance(selectedNodes)}
                className="px-3 py-1 bg-growth-gradient text-crystal-clear rounded text-sm hover-bloom transition-all"
              >
                🎯 Score Relevance
              </button>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {filteredNodes.map((node) => (
            <div
              key={node.id}
              className={`p-4 rounded-lg border-2 transition-all hover-bloom cursor-pointer ${getNodeColor(node)} ${
                selectedNodes.includes(node.id) ? 'ring-2 ring-growth-accent' : ''
              }`}
              onClick={() => {
                setSelectedNodes(prev => 
                  prev.includes(node.id) 
                    ? prev.filter(id => id !== node.id)
                    : [...prev, node.id]
                );
              }}
            >
              {/* Node Header */}
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <span className="text-lg">{getTypeIcon(node.type)}</span>
                  <span className="text-sm font-medium capitalize text-earth-base">{node.type}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`text-sm font-bold ${
                    node.relevance_score >= 8 ? 'text-growth-accent' :
                    node.relevance_score >= 6 ? 'text-bloom-highlight' :
                    node.relevance_score >= 4 ? 'text-amber-data' :
                    'text-earth-base/60'
                  }`}>
                    {node.relevance_score.toFixed(1)}
                  </span>
                  <span className="text-xs text-earth-base/60">{node.token_count} tokens</span>
                </div>
              </div>

              {/* Node Content */}
              <p className="text-sm text-earth-base/80 mb-3 line-clamp-3">{node.content}</p>

              {/* Node Metadata */}
              <div className="flex items-center justify-between text-xs text-earth-base/60">
                <div className="flex items-center space-x-2">
                  {node.metadata.file_path && (
                    <span className="bg-mist-gradient px-2 py-1 rounded">
                      📄 {node.metadata.file_path.split('/').pop()}
                    </span>
                  )}
                  {node.metadata.category && (
                    <span className="bg-mist-gradient px-2 py-1 rounded">
                      🏷️ {node.metadata.category}
                    </span>
                  )}
                </div>
                <div className="flex items-center space-x-1">
                  <span>🔗 {node.connections.length}</span>
                  <span>⏰ {node.timestamp.toLocaleTimeString()}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {filteredNodes.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🎯</div>
          <h3 className="text-xl font-semibold text-forest-primary mb-2">No Context Nodes Found</h3>
          <p className="text-earth-base/60">Try adjusting your filters or relevance threshold</p>
        </div>
      )}
    </div>
  );
};
