site_name: PydanticAI
site_description: Agent Framework / shim to use Pydantic with LLMs
strict: true
site_url: https://ai.pydantic.dev

repo_name: pydantic/pydantic-ai
repo_url: https://github.com/pydantic/pydantic-ai
edit_uri: edit/main/docs/

copyright: © Pydantic Services Inc. 2024 to present

nav:
  - Introduction: index.md
  - install.md
  - help.md
  - contributing.md
  - troubleshooting.md
  - changelog.md
  - Documentation:
      - agents.md
      - Models:
          - models/index.md
          - models/openai.md
          - models/anthropic.md
          - models/gemini.md
          - models/google.md
          - models/bedrock.md
          - models/cohere.md
          - models/groq.md
          - models/mistral.md
      - dependencies.md
      - tools.md
      - common-tools.md
      - output.md
      - message-history.md
      - testing.md
      - logfire.md
      - multi-agent-applications.md
      - graph.md
      - evals.md
      - input.md
      - direct.md
      - MCP:
          - mcp/index.md
          - mcp/client.md
          - mcp/server.md
          - mcp/run-python.md
      - A2A: a2a.md
      - cli.md
  - Examples:
      - examples/index.md
      - examples/pydantic-model.md
      - examples/weather-agent.md
      - examples/bank-support.md
      - examples/sql-gen.md
      - examples/flight-booking.md
      - examples/rag.md
      - examples/stream-markdown.md
      - examples/stream-whales.md
      - examples/chat-app.md
      - examples/question-graph.md
  - API Reference:
      - api/agent.md
      - api/tools.md
      - api/common_tools.md
      - api/result.md
      - api/messages.md
      - api/exceptions.md
      - api/settings.md
      - api/usage.md
      - api/mcp.md
      - api/format_as_xml.md
      - api/direct.md
      - api/models/base.md
      - api/models/openai.md
      - api/models/anthropic.md
      - api/models/bedrock.md
      - api/models/cohere.md
      - api/models/gemini.md
      - api/models/google.md
      - api/models/groq.md
      - api/models/instrumented.md
      - api/models/mistral.md
      - api/models/test.md
      - api/models/function.md
      - api/models/fallback.md
      - api/models/wrapper.md
      - api/profiles.md
      - api/providers.md
      - api/pydantic_graph/graph.md
      - api/pydantic_graph/nodes.md
      - api/pydantic_graph/persistence.md
      - api/pydantic_graph/mermaid.md
      - api/pydantic_graph/exceptions.md
      - api/pydantic_evals/dataset.md
      - api/pydantic_evals/evaluators.md
      - api/pydantic_evals/reporting.md
      - api/pydantic_evals/otel.md
      - api/pydantic_evals/generation.md
      - api/fasta2a.md

extra:
  # hide the "Made with Material for MkDocs" message
  generator: false

theme:
  name: "material"
  custom_dir: docs/.overrides
  palette:
    - media: "(prefers-color-scheme)"
      primary: pink
      accent: pink
      toggle:
        icon: material/brightness-auto
        name: "Switch to light mode"
    - media: "(prefers-color-scheme: light)"
      scheme: default
      primary: pink
      accent: pink
      toggle:
        icon: material/brightness-7
        name: "Switch to dark mode"
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      primary: pink
      accent: pink
      toggle:
        icon: material/brightness-4
        name: "Switch to system preference"
  features:
    - search.suggest
    - search.highlight
    - content.tabs.link
    - content.code.annotate
    - content.code.copy
    - content.code.select
    - navigation.path
    - navigation.indexes
    - navigation.sections
    - navigation.tracking
    - toc.follow
  logo: "img/logo-white.svg"
  favicon: "favicon.ico"

# https://www.mkdocs.org/user-guide/configuration/#validation
validation:
  omitted_files: warn
  absolute_links: warn
  unrecognized_links: warn
  anchors: warn

extra_css:
  - "extra/tweaks.css"
# used for analytics
extra_javascript:
  - "/flarelytics/client.js"
  - "https://cdn.jsdelivr.net/npm/algoliasearch@5.20.0/dist/lite/builds/browser.umd.js"
  - "https://cdn.jsdelivr.net/npm/instantsearch.js@4.77.3/dist/instantsearch.production.min.js"
  - "/javascripts/algolia-search.js"

markdown_extensions:
  - tables
  - admonition
  - attr_list
  - md_in_html
  - pymdownx.details
  - pymdownx.caret
  - pymdownx.critic
  - pymdownx.mark
  - pymdownx.superfences
  - pymdownx.snippets
  - pymdownx.tilde
  - pymdownx.inlinehilite
  - pymdownx.highlight:
      pygments_lang_class: true
  - pymdownx.extra:
      pymdownx.superfences:
        custom_fences:
          - name: mermaid
            class: mermaid
            format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
      options:
        custom_icons:
          - docs/.overrides/.icons
  - pymdownx.tabbed:
      alternate_style: true
  - pymdownx.tasklist:
      custom_checkbox: true
  - sane_lists # this means you can start a list from any number

watch:
  - pydantic_ai_slim
  - pydantic_graph
  - examples

plugins:
  - search
  - social
  - glightbox
  - mkdocstrings:
      handlers:
        python:
          paths: [pydantic_ai_slim/pydantic_ai]
          options:
            relative_crossrefs: true
            members_order: source
            separate_signature: true
            show_signature_annotations: true
            signature_crossrefs: true
            group_by_category: false
            # 3 because docs are in pages with an H2 just above them
            heading_level: 3
          import:
            - url: https://logfire.pydantic.dev/docs/objects.inv
            - url: https://docs.python.org/3/objects.inv
            - url: https://docs.pydantic.dev/latest/objects.inv
            - url: https://dirty-equals.helpmanual.io/latest/objects.inv
            - url: https://fastapi.tiangolo.com/objects.inv
            - url: https://typing-extensions.readthedocs.io/en/latest/objects.inv
            - url: https://rich.readthedocs.io/en/stable/objects.inv
            # waiting for https://github.com/encode/httpx/discussions/3091#discussioncomment-11205594
  - llmstxt:
      enabled: !ENV [CI, false]
      full_output: llms-full.txt
      markdown_description: |-
        PydanticAI is a Python agent framework designed to make it less painful to build production grade
        applications with Generative AI.
      sections:
        Concepts documentation:
          - agents.md
          - dependencies.md
          - tools.md
          - common-tools.md
          - results.md
          - message-history.md
          - multi-agent-applications.md
        Models:
          - models/*.md
        Graphs:
          - graph.md
        Evals:
          - evals.md
        MCP:
          - mcp/*.md
        Optional:
          - testing.md
          - cli.md
          - logfire.md
          - examples/*.md

hooks:
  - "docs/.hooks/main.py"
  - "docs/.hooks/algolia.py"
