#!/usr/bin/env python3
"""
Comprehensive Phase 2 Completion Test for AI Coder Agent
Tests all Phase 2 requirements from IMPLEMENTATION_PLAN.md
"""

import sys
import time
import requests
from pathlib import Path

# Color codes for output
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_header(title):
    """Print a formatted header."""
    print(f"\n{Colors.BLUE}{'='*60}{Colors.END}")
    print(f"{Colors.BLUE} {title}{Colors.END}")
    print(f"{Colors.BLUE}{'='*60}{Colors.END}")

def print_status(message, success, details=""):
    """Print a status message with color coding."""
    status_icon = f"{Colors.GREEN}✅ PASS{Colors.END}" if success else f"{Colors.RED}❌ FAIL{Colors.END}"
    print(f"{status_icon} {message}")
    if details:
        print(f"    {details}")

def test_openrouter_client():
    """Test OpenRouter LLM client functionality."""
    print_header("Testing OpenRouter LLM Client")
    
    try:
        response = requests.post("http://localhost:8000/api/test/llm", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get("status") == "success":
                print_status("OpenRouter client test", True, "Chat completion and health check successful")
                
                # Check specific components
                health = data.get("health_check", {})
                if health.get("status") == "healthy":
                    print_status("OpenRouter API connectivity", True, "API key valid and models accessible")
                else:
                    print_status("OpenRouter API connectivity", False, f"Health check failed: {health.get('error', 'Unknown error')}")
                
                # Check test response
                test_response = data.get("test_response", "")
                if "Hello from OpenRouter!" in test_response:
                    print_status("Chat completion functionality", True, "Model responded correctly")
                else:
                    print_status("Chat completion functionality", False, f"Unexpected response: {test_response}")
                
                return True
            else:
                print_status("OpenRouter client test", False, f"Test failed: {data.get('error', 'Unknown error')}")
                return False
        else:
            print_status("OpenRouter client test", False, f"HTTP {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print_status("OpenRouter client test", False, f"Exception: {e}")
        return False

def test_ollama_client():
    """Test Ollama embedding client functionality."""
    print_header("Testing Ollama Embedding Client")
    
    try:
        response = requests.post("http://localhost:8000/api/test/embeddings", timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get("status") == "success":
                print_status("Ollama client test", True, "Embedding generation successful")
                
                # Check single embedding
                single_emb = data.get("single_embedding", {})
                if single_emb.get("dimensions_match"):
                    print_status("Single text embedding", True, f"Generated {single_emb.get('embedding_length')} dimensions")
                else:
                    print_status("Single text embedding", False, f"Dimension mismatch: got {single_emb.get('embedding_length')}, expected {single_emb.get('expected_dimensions')}")
                
                # Check batch embedding
                batch_emb = data.get("batch_embedding", {})
                if batch_emb.get("all_same_dimensions"):
                    print_status("Batch embedding", True, f"Generated {batch_emb.get('embeddings_count')} embeddings")
                else:
                    print_status("Batch embedding", False, "Inconsistent embedding dimensions")
                
                # Check health
                health = data.get("health_check", {})
                if health.get("status") == "healthy":
                    print_status("Ollama service health", True, "Model available and functional")
                else:
                    print_status("Ollama service health", False, f"Health issue: {health.get('error', 'Unknown')}")
                
                return True
            else:
                print_status("Ollama client test", False, f"Test failed: {data.get('error', 'Unknown error')}")
                return False
        else:
            print_status("Ollama client test", False, f"HTTP {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print_status("Ollama client test", False, f"Exception: {e}")
        return False

def test_qdrant_client():
    """Test Qdrant vector database client functionality."""
    print_header("Testing Qdrant Vector Database Client")
    
    try:
        response = requests.post("http://localhost:8000/api/test/vector-search", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get("status") == "success":
                print_status("Qdrant client test", True, "Vector operations successful")
                
                # Check collection creation
                if data.get("collection_created"):
                    print_status("Collection management", True, "Collection created/verified")
                else:
                    print_status("Collection management", False, "Failed to create collection")
                
                # Check upsert
                upsert = data.get("upsert_test", {})
                if upsert.get("upsert_successful"):
                    print_status("Vector upsert", True, f"Upserted {upsert.get('embeddings_count')} vectors")
                else:
                    print_status("Vector upsert", False, "Failed to upsert vectors")
                
                # Check search
                search = data.get("search_test", {})
                if search.get("results_count", 0) > 0:
                    print_status("Similarity search", True, f"Found {search.get('results_count')} results")
                else:
                    print_status("Similarity search", False, "No search results returned")
                
                # Check health
                health = data.get("health_check", {})
                if health.get("status") == "healthy":
                    print_status("Qdrant service health", True, "Database accessible and functional")
                else:
                    print_status("Qdrant service health", False, f"Health issue: {health.get('error', 'Unknown')}")
                
                return True
            else:
                print_status("Qdrant client test", False, f"Test failed: {data.get('error', 'Unknown error')}")
                return False
        else:
            print_status("Qdrant client test", False, f"HTTP {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print_status("Qdrant client test", False, f"Exception: {e}")
        return False

def test_context_manager():
    """Test context management system functionality."""
    print_header("Testing Context Management System")
    
    try:
        response = requests.post("http://localhost:8000/api/test/context-manager", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get("status") == "success":
                print_status("Context manager test", True, "All context operations successful")
                
                # Check token counting
                token_counting = data.get("token_counting", {})
                if token_counting.get("token_count", 0) > 0:
                    print_status("Token counting", True, f"Counted {token_counting.get('token_count')} tokens")
                else:
                    print_status("Token counting", False, "Token counting failed")
                
                # Check message token counting
                msg_counting = data.get("message_token_counting", {})
                if msg_counting.get("total_tokens", 0) > 0:
                    print_status("Message token counting", True, f"Counted {msg_counting.get('total_tokens')} tokens for {msg_counting.get('messages_count')} messages")
                else:
                    print_status("Message token counting", False, "Message token counting failed")
                
                # Check system prompt
                sys_prompt = data.get("system_prompt", {})
                if sys_prompt.get("contains_task_context"):
                    print_status("System prompt building", True, "Task context properly integrated")
                else:
                    print_status("System prompt building", False, "Task context not found in prompt")
                
                # Check context preparation
                context_prep = data.get("context_preparation", {})
                if context_prep.get("has_system_message"):
                    print_status("Context preparation", True, f"Prepared {context_prep.get('prepared_messages')} messages")
                else:
                    print_status("Context preparation", False, "Context preparation failed")
                
                return True
            else:
                print_status("Context manager test", False, f"Test failed: {data.get('error', 'Unknown error')}")
                return False
        else:
            print_status("Context manager test", False, f"HTTP {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print_status("Context manager test", False, f"Exception: {e}")
        return False

def test_integration():
    """Test integration between all components."""
    print_header("Testing Component Integration")

    try:
        # Test full workflow: text → embedding → storage → retrieval
        print("Testing full workflow: text → embedding → storage → retrieval...")

        # Step 1: Generate embeddings for test texts
        test_texts = [
            "def hello_world(): print('Hello, World!')",
            "function greetUser(name) { console.log('Hello, ' + name); }",
            "print('Python is awesome')"
        ]

        embeddings_response = requests.post("http://localhost:8000/api/test/embeddings", timeout=60)
        if embeddings_response.status_code != 200:
            print_status("Integration test - embeddings", False, "Failed to generate embeddings")
            return False

        embeddings_data = embeddings_response.json()
        if embeddings_data.get("status") != "success":
            print_status("Integration test - embeddings", False, "Embedding generation failed")
            return False

        print_status("Step 1: Embedding generation", True, "Generated embeddings successfully")

        # Step 2: Test vector storage and search
        vector_response = requests.post("http://localhost:8000/api/test/vector-search", timeout=30)
        if vector_response.status_code != 200:
            print_status("Integration test - vector ops", False, "Failed vector operations")
            return False

        vector_data = vector_response.json()
        if vector_data.get("status") != "success":
            print_status("Integration test - vector ops", False, "Vector operations failed")
            return False

        print_status("Step 2: Vector storage/search", True, "Vector operations successful")

        # Step 3: Test LLM with context
        llm_response = requests.post("http://localhost:8000/api/test/llm", timeout=30)
        if llm_response.status_code != 200:
            print_status("Integration test - LLM", False, "Failed LLM operations")
            return False

        llm_data = llm_response.json()
        if llm_data.get("status") != "success":
            print_status("Integration test - LLM", False, "LLM operations failed")
            return False

        print_status("Step 3: LLM operations", True, "LLM operations successful")

        # Step 4: Test context management
        context_response = requests.post("http://localhost:8000/api/test/context-manager", timeout=30)
        if context_response.status_code != 200:
            print_status("Integration test - context", False, "Failed context operations")
            return False

        context_data = context_response.json()
        if context_data.get("status") != "success":
            print_status("Integration test - context", False, "Context operations failed")
            return False

        print_status("Step 4: Context management", True, "Context operations successful")

        print_status("Full integration workflow", True, "All components working together")
        return True

    except Exception as e:
        print_status("Integration test", False, f"Exception: {e}")
        return False

def test_file_structure():
    """Test that all Phase 2 files exist."""
    print_header("Testing Phase 2 File Structure")

    required_files = [
        "backend/llm_client.py",
        "backend/ollama_client.py",
        "backend/vector_db.py",
        "backend/app/agent/context_manager.py"
    ]

    all_exist = True
    for file_path in required_files:
        exists = Path(file_path).exists()
        print_status(f"File exists: {file_path}", exists)
        if not exists:
            all_exist = False

    return all_exist

def test_imports():
    """Test that all Phase 2 modules can be imported."""
    print_header("Testing Phase 2 Module Imports")

    # Add backend to path
    backend_path = Path("backend").absolute()
    sys.path.insert(0, str(backend_path))

    tests = []

    # Test llm_client.py
    try:
        from llm_client import OpenRouterClient, openrouter_client
        tests.append(("llm_client.py import", True, "OpenRouter client available"))
    except Exception as e:
        tests.append(("llm_client.py import", False, f"Error: {e}"))

    # Test ollama_client.py
    try:
        from ollama_client import OllamaEmbeddingClient, ollama_client
        tests.append(("ollama_client.py import", True, "Ollama client available"))
    except Exception as e:
        tests.append(("ollama_client.py import", False, f"Error: {e}"))

    # Test vector_db.py
    try:
        from vector_db import QdrantVectorClient, qdrant_client
        tests.append(("vector_db.py import", True, "Qdrant client available"))
    except Exception as e:
        tests.append(("vector_db.py import", False, f"Error: {e}"))

    # Test context_manager.py
    try:
        from app.agent.context_manager import ContextManager, context_manager
        tests.append(("context_manager.py import", True, "Context manager available"))
    except Exception as e:
        tests.append(("context_manager.py import", False, f"Error: {e}"))

    all_passed = True
    for test_name, success, details in tests:
        print_status(test_name, success, details)
        if not success:
            all_passed = False

    return all_passed

def main():
    """Run all Phase 2 completion tests."""
    print_header("AI Coder Agent - Phase 2 Completion Test")
    print("Testing all Phase 2 requirements from IMPLEMENTATION_PLAN.md...")

    tests = [
        ("File Structure", test_file_structure),
        ("Module Imports", test_imports),
        ("OpenRouter Client", test_openrouter_client),
        ("Ollama Client", test_ollama_client),
        ("Qdrant Client", test_qdrant_client),
        ("Context Manager", test_context_manager),
        ("Component Integration", test_integration),
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print_status(f"{test_name} (EXCEPTION)", False, f"Unexpected error: {e}")
            results.append((test_name, False))

    # Print summary
    print_header("Phase 2 Completion Test Results")

    passed_tests = []
    failed_tests = []

    for test_name, result in results:
        if result:
            print_status(f"{test_name}", True)
            passed_tests.append(test_name)
        else:
            print_status(f"{test_name}", False)
            failed_tests.append(test_name)

    print(f"\nOverall: {len(passed_tests)}/{len(results)} tests passed")

    if len(passed_tests) == len(results):
        print(f"\n{Colors.GREEN}{Colors.BOLD}🎉 Phase 2 is COMPLETE! All requirements satisfied.{Colors.END}")
        print(f"{Colors.GREEN}✅ Ready to proceed to Phase 3: File Operations & Git Integration{Colors.END}")
        return True
    else:
        print(f"\n{Colors.RED}❌ Phase 2 is INCOMPLETE. {len(failed_tests)} issues need to be resolved.{Colors.END}")
        print(f"{Colors.YELLOW}🔧 Please fix the failing tests before proceeding to Phase 3.{Colors.END}")

        if failed_tests:
            print(f"\nFailed tests:")
            for test in failed_tests:
                print(f"  - {test}")

        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
