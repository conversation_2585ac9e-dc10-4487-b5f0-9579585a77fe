import React, { useState, useEffect } from 'react';

interface HealthStatus {
  status: 'healthy' | 'warning' | 'error' | 'unknown';
  message: string;
  lastChecked: Date;
  responseTime?: number;
}

interface ComponentHealth {
  [key: string]: HealthStatus;
}

interface SystemMetrics {
  cpu: number;
  memory: number;
  disk: number;
  network: number;
  uptime: string;
  activeConnections: number;
  requestsPerSecond: number;
  errorRate: number;
}

interface Alert {
  id: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  component: string;
  resolved: boolean;
}

export const SystemHealth: React.FC = () => {
  const [componentHealth, setComponentHealth] = useState<ComponentHealth>({});
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics>({
    cpu: 0,
    memory: 0,
    disk: 0,
    network: 0,
    uptime: '0m',
    activeConnections: 0,
    requestsPerSecond: 0,
    errorRate: 0
  });
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  useEffect(() => {
    fetchAllHealthData();
    const interval = setInterval(fetchAllHealthData, 10000); // Update every 10 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchAllHealthData = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        fetchComponentHealth(),
        fetchSystemMetrics(),
        fetchSystemAlerts()
      ]);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Failed to fetch health data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchComponentHealth = async () => {
    try {
      // Fetch overall system health
      const healthResponse = await fetch('/api/monitoring/health');
      const healthData = healthResponse.ok ? await healthResponse.json() : null;

      // Fetch component-specific health
      const componentsResponse = await fetch('/api/monitoring/health/components');
      const componentsData = componentsResponse.ok ? await componentsResponse.json() : null;

      // Fetch API health
      const apiResponse = await fetch('/api/health');
      const apiData = apiResponse.ok ? await apiResponse.json() : null;

      // Fetch database health
      const dbResponse = await fetch('/api/database/health');
      const dbData = dbResponse.ok ? await dbResponse.json() : null;

      // Fetch services health
      const servicesResponse = await fetch('/api/services/health');
      const servicesData = servicesResponse.ok ? await servicesResponse.json() : null;

      // Fetch security health
      const securityResponse = await fetch('/api/security/health');
      const securityData = securityResponse.ok ? await securityResponse.json() : null;

      // Fetch analytics health
      const analyticsResponse = await fetch('/api/analytics/health');
      const analyticsData = analyticsResponse.ok ? await analyticsResponse.json() : null;

      // Fetch context health
      const contextResponse = await fetch('/api/context/health');
      const contextData = contextResponse.ok ? await contextResponse.json() : null;

      // Fetch session health
      const sessionResponse = await fetch('/api/sessions/health');
      const sessionData = sessionResponse.ok ? await sessionResponse.json() : null;

      // Fetch planning health
      const planningResponse = await fetch('/api/v1/planning/health');
      const planningData = planningResponse.ok ? await planningResponse.json() : null;

      // Fetch enhanced projects health
      const projectsResponse = await fetch('/api/projects/health');
      const projectsData = projectsResponse.ok ? await projectsResponse.json() : null;

      const health: ComponentHealth = {
        'API Gateway': {
          status: apiResponse.ok ? 'healthy' : 'error',
          message: apiData?.message || (apiResponse.ok ? 'API is responding' : 'API is down'),
          lastChecked: new Date(),
          responseTime: apiData?.response_time
        },
        'Database': {
          status: dbResponse.ok ? 'healthy' : 'error',
          message: dbData?.message || (dbResponse.ok ? 'Database connected' : 'Database connection failed'),
          lastChecked: new Date(),
          responseTime: dbData?.response_time
        },
        'External Services': {
          status: servicesResponse.ok ? 'healthy' : 'error',
          message: servicesData?.message || (servicesResponse.ok ? 'All services operational' : 'Service issues detected'),
          lastChecked: new Date()
        },
        'Security System': {
          status: securityResponse.ok ? 'healthy' : 'error',
          message: securityData?.message || (securityResponse.ok ? 'Security systems active' : 'Security system issues'),
          lastChecked: new Date()
        },
        'Analytics Engine': {
          status: analyticsResponse.ok ? 'healthy' : 'error',
          message: analyticsData?.message || (analyticsResponse.ok ? 'Analytics operational' : 'Analytics system down'),
          lastChecked: new Date()
        },
        'Context Management': {
          status: contextResponse.ok ? 'healthy' : 'error',
          message: contextData?.message || (contextResponse.ok ? 'Context system healthy' : 'Context system issues'),
          lastChecked: new Date()
        },
        'Session Management': {
          status: sessionResponse.ok ? 'healthy' : 'error',
          message: sessionData?.message || (sessionResponse.ok ? 'Session system active' : 'Session system down'),
          lastChecked: new Date()
        },
        'AI Planning': {
          status: planningResponse.ok ? 'healthy' : 'error',
          message: planningData?.message || (planningResponse.ok ? 'Planning system ready' : 'Planning system offline'),
          lastChecked: new Date()
        },
        'Project Management': {
          status: projectsResponse.ok ? 'healthy' : 'error',
          message: projectsData?.message || (projectsResponse.ok ? 'Project system operational' : 'Project system issues'),
          lastChecked: new Date()
        }
      };

      setComponentHealth(health);
    } catch (error) {
      console.error('Failed to fetch component health:', error);
    }
  };

  const fetchSystemMetrics = async () => {
    try {
      const response = await fetch('/api/monitoring/system/metrics');
      if (response.ok) {
        const data = await response.json();
        setSystemMetrics({
          cpu: data.cpu_usage || Math.random() * 100,
          memory: data.memory_usage || Math.random() * 100,
          disk: data.disk_usage || Math.random() * 100,
          network: data.network_usage || Math.random() * 100,
          uptime: data.uptime || '1h 23m',
          activeConnections: data.active_connections || Math.floor(Math.random() * 50),
          requestsPerSecond: data.requests_per_second || Math.floor(Math.random() * 20),
          errorRate: data.error_rate || Math.random() * 5
        });
      }
    } catch (error) {
      console.error('Failed to fetch system metrics:', error);
      // Use mock data on error
      setSystemMetrics(prev => ({
        ...prev,
        cpu: Math.random() * 100,
        memory: Math.random() * 100,
        disk: Math.random() * 100,
        network: Math.random() * 100
      }));
    }
  };

  const fetchSystemAlerts = async () => {
    try {
      const response = await fetch('/api/monitoring/system/alerts');
      if (response.ok) {
        const data = await response.json();
        const alertsData = (data.alerts || []).map((alert: any) => ({
          id: alert.id || Math.random().toString(),
          severity: alert.severity || 'medium',
          message: alert.message || 'System alert',
          timestamp: new Date(alert.timestamp || Date.now()),
          component: alert.component || 'System',
          resolved: alert.resolved || false
        }));
        setAlerts(alertsData);
      }
    } catch (error) {
      console.error('Failed to fetch system alerts:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-growth-accent';
      case 'warning': return 'text-bloom-highlight';
      case 'error': return 'text-crimson-alert';
      default: return 'text-earth-base/60';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return '🟢';
      case 'warning': return '🟡';
      case 'error': return '🔴';
      default: return '⚪';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-crimson-alert text-crystal-clear';
      case 'high': return 'bg-amber-data text-crystal-clear';
      case 'medium': return 'bg-bloom-highlight text-earth-base';
      case 'low': return 'bg-growth-accent/20 text-growth-accent';
      default: return 'bg-earth-base/20 text-earth-base';
    }
  };

  const getMetricColor = (value: number, threshold: number = 80) => {
    if (value > threshold) return 'text-crimson-alert';
    if (value > threshold * 0.7) return 'text-bloom-highlight';
    return 'text-growth-accent';
  };

  const overallHealth = Object.values(componentHealth).every(c => c.status === 'healthy') ? 'healthy' :
                       Object.values(componentHealth).some(c => c.status === 'error') ? 'error' : 'warning';

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <h1 className="text-h2 text-forest-primary">🏥 System Health Dashboard</h1>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            overallHealth === 'healthy' ? 'bg-growth-accent/20 text-growth-accent' :
            overallHealth === 'error' ? 'bg-crimson-alert/20 text-crimson-alert' :
            'bg-bloom-highlight/20 text-bloom-highlight'
          }`}>
            {getStatusIcon(overallHealth)} {overallHealth.toUpperCase()}
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <span className="text-sm text-earth-base/60">
            Last updated: {lastUpdate.toLocaleTimeString()}
          </span>
          <button
            onClick={fetchAllHealthData}
            disabled={isLoading}
            className="px-4 py-2 bg-growth-gradient text-crystal-clear rounded-lg hover-bloom transition-all disabled:opacity-50"
          >
            {isLoading ? '🔄' : '🔄'} Refresh
          </button>
        </div>
      </div>

      {/* System Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[
          { label: 'CPU Usage', value: systemMetrics.cpu, unit: '%', icon: '💻' },
          { label: 'Memory Usage', value: systemMetrics.memory, unit: '%', icon: '🧠' },
          { label: 'Disk Usage', value: systemMetrics.disk, unit: '%', icon: '💾' },
          { label: 'Network Usage', value: systemMetrics.network, unit: '%', icon: '🌐' }
        ].map((metric) => (
          <div key={metric.label} className="bg-crystal-clear rounded-lg p-4 border border-growth-accent/20 hover-bloom">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-earth-base/70">{metric.icon} {metric.label}</span>
              <span className={`text-lg font-bold ${getMetricColor(metric.value)}`}>
                {metric.value.toFixed(1)}{metric.unit}
              </span>
            </div>
            <div className="w-full bg-frost-light rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${
                  metric.value > 80 ? 'bg-crimson-alert' :
                  metric.value > 60 ? 'bg-bloom-highlight' :
                  'bg-growth-gradient'
                }`}
                style={{ width: `${Math.min(metric.value, 100)}%` }}
              />
            </div>
          </div>
        ))}
      </div>

      {/* Component Health Grid */}
      <div className="bg-crystal-clear rounded-lg p-6 border border-growth-accent/20">
        <h2 className="text-xl font-semibold text-forest-primary mb-4">🔧 Component Health</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.entries(componentHealth).map(([component, health]) => (
            <div key={component} className="bg-mist-gradient rounded-lg p-4 hover-bloom transition-all">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-earth-base">{component}</h3>
                <span className={getStatusColor(health.status)}>
                  {getStatusIcon(health.status)}
                </span>
              </div>
              <p className="text-sm text-earth-base/70 mb-2">{health.message}</p>
              <div className="flex items-center justify-between text-xs text-earth-base/60">
                <span>Last checked: {health.lastChecked.toLocaleTimeString()}</span>
                {health.responseTime && (
                  <span>{health.responseTime}ms</span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* System Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-crystal-clear rounded-lg p-4 border border-growth-accent/20">
          <h3 className="font-semibold text-forest-primary mb-3">📊 Performance</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Uptime:</span>
              <span className="font-mono">{systemMetrics.uptime}</span>
            </div>
            <div className="flex justify-between">
              <span>Active Connections:</span>
              <span className="font-mono">{systemMetrics.activeConnections}</span>
            </div>
            <div className="flex justify-between">
              <span>Requests/sec:</span>
              <span className="font-mono">{systemMetrics.requestsPerSecond}</span>
            </div>
            <div className="flex justify-between">
              <span>Error Rate:</span>
              <span className={`font-mono ${getMetricColor(systemMetrics.errorRate, 5)}`}>
                {systemMetrics.errorRate.toFixed(2)}%
              </span>
            </div>
          </div>
        </div>

        <div className="bg-crystal-clear rounded-lg p-4 border border-growth-accent/20">
          <h3 className="font-semibold text-forest-primary mb-3">🚨 Active Alerts</h3>
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {alerts.filter(a => !a.resolved).length > 0 ? (
              alerts.filter(a => !a.resolved).map((alert) => (
                <div key={alert.id} className={`text-xs px-2 py-1 rounded ${getSeverityColor(alert.severity)}`}>
                  <div className="font-medium">{alert.component}</div>
                  <div className="truncate">{alert.message}</div>
                </div>
              ))
            ) : (
              <div className="text-sm text-earth-base/60 text-center py-4">
                🌿 No active alerts
              </div>
            )}
          </div>
        </div>

        <div className="bg-crystal-clear rounded-lg p-4 border border-growth-accent/20">
          <h3 className="font-semibold text-forest-primary mb-3">🎯 Quick Actions</h3>
          <div className="space-y-2">
            <button className="w-full text-left px-3 py-2 bg-mist-gradient rounded hover-bloom transition-all text-sm">
              🔄 Restart Services
            </button>
            <button className="w-full text-left px-3 py-2 bg-mist-gradient rounded hover-bloom transition-all text-sm">
              🧹 Clear Cache
            </button>
            <button className="w-full text-left px-3 py-2 bg-mist-gradient rounded hover-bloom transition-all text-sm">
              📊 Generate Report
            </button>
            <button className="w-full text-left px-3 py-2 bg-mist-gradient rounded hover-bloom transition-all text-sm">
              ⚙️ System Settings
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
