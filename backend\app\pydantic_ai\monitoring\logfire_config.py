"""
Logfire Configuration for DeepNexus AI Coder Agent

This module centralizes Logfire monitoring and observability configuration
for the Phase 7 Pydantic AI migration.
"""

import os
import logging
from typing import Optional

import logfire

logger = logging.getLogger(__name__)

# Your Logfire token - can be overridden by environment variable
DEFAULT_LOGFIRE_TOKEN = 'pylf_v1_eu_mFwk5ZXMXFzWgqY8p7W0xJfgLsjwD7vXPsnTzh3wwMCr'

def configure_logfire(
    token: Optional[str] = None,
    project_name: str = 'deepnexus',
    environment: str = 'development',
    service_name: str = 'deepnexus-ai-coder-agent',
    console_min_level: str = 'info'
) -> None:
    """
    Configure Logfire monitoring for the DeepNexus AI Coder Agent.
    
    Args:
        token: Logfire write token (defaults to env var or hardcoded token)
        project_name: Project name in Logfire
        environment: Environment name (development, production, etc.)
        service_name: Service name for tracing
        console_min_level: Minimum log level for console output
    """
    try:
        # Get token from parameter, environment variable, or default
        logfire_token = (
            token or 
            os.getenv('LOGFIRE_TOKEN') or 
            DEFAULT_LOGFIRE_TOKEN
        )
        
        logger.info(f"Configuring Logfire for project: {project_name}, environment: {environment}")
        
        # Configure Logfire with comprehensive settings
        logfire.configure(
            token=logfire_token,
            project_name=project_name,
            environment=environment,
            service_name=service_name,
            console=logfire.ConsoleOptions(
                min_log_level=console_min_level,
                colors='auto'
            ),
            # Enable source code linking for better debugging
            code_source=logfire.CodeSource(
                repository='https://github.com/R2-ally/DeepNexus',
                revision='main',  # You can update this with actual commit hash
                root_path='backend'
            ),
            # Configure sampling for performance (simplified for compatibility)
            sampling=logfire.SamplingOptions(
                head=1.0  # Keep all traces during development
            )
        )
        
        logger.info("✅ Logfire configured successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to configure Logfire: {e}")
        # Fallback to basic configuration
        logfire.configure(send_to_logfire=False)
        logger.warning("⚠️ Falling back to local-only logging")


def instrument_all_components() -> None:
    """
    Instrument all components for comprehensive monitoring.
    """
    try:
        logger.info("🔧 Instrumenting components for monitoring...")
        
        # Core PydanticAI instrumentation
        logfire.instrument_pydantic_ai()
        logger.info("✅ PydanticAI instrumentation enabled")
        
        # HTTP client instrumentation for OpenRouter API calls
        logfire.instrument_httpx()
        logger.info("✅ HTTPX instrumentation enabled")
        
        # System metrics for performance monitoring
        logfire.instrument_system_metrics()
        logger.info("✅ System metrics instrumentation enabled")
        
        # Standard library logging integration
        import logging
        logging.basicConfig(handlers=[logfire.LogfireLoggingHandler()])
        logger.info("✅ Standard logging integration enabled")
        
        logger.info("🎉 All component instrumentation completed successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to instrument components: {e}")


def create_custom_spans():
    """
    Create custom span decorators for our specific use cases.
    """
    def tool_execution_span(tool_name: str):
        """Decorator for tool execution spans."""
        def decorator(func):
            async def wrapper(*args, **kwargs):
                with logfire.span(f'tool_execution.{tool_name}', tool_name=tool_name):
                    return await func(*args, **kwargs)
            return wrapper
        return decorator
    
    def agent_run_span(agent_type: str):
        """Decorator for agent run spans."""
        def decorator(func):
            async def wrapper(*args, **kwargs):
                with logfire.span(f'agent_run.{agent_type}', agent_type=agent_type):
                    return await func(*args, **kwargs)
            return wrapper
        return decorator
    
    return tool_execution_span, agent_run_span


def log_phase7_milestone(phase: str, task: str, status: str, details: str = ""):
    """
    Log Phase 7 implementation milestones to Logfire.
    
    Args:
        phase: Phase identifier (e.g., "7A", "7B")
        task: Task identifier (e.g., "environment_setup", "tool_migration")
        status: Status (e.g., "started", "completed", "failed")
        details: Additional details
    """
    logfire.info(
        "Phase 7 Milestone: {phase}.{task} - {status}",
        phase=phase,
        task=task,
        status=status,
        details=details,
        milestone=True
    )


def setup_monitoring() -> None:
    """
    Complete monitoring setup for Phase 7A.
    """
    logger.info("🚀 Setting up comprehensive monitoring for Phase 7A...")
    
    # Configure Logfire
    configure_logfire()
    
    # Instrument all components
    instrument_all_components()
    
    # Log the setup completion
    log_phase7_milestone("7A", "monitoring_setup", "completed", "Full Logfire monitoring active")
    
    logger.info("✅ Monitoring setup completed successfully!")


# Initialize monitoring when module is imported
if __name__ != "__main__":
    setup_monitoring()
