"""
🤖 Agent Activity Monitor - Active agents and tool usage statistics

This module provides comprehensive agent activity monitoring with:
- Real-time agent activity tracking
- Tool usage statistics and analytics
- Agent performance metrics
- Resource utilization monitoring
- Activity trend analysis
"""

import asyncio
from collections import defaultdict, deque
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)


class AgentStatus(str, Enum):
    """Agent status enumeration."""
    ACTIVE = "active"
    IDLE = "idle"
    BUSY = "busy"
    ERROR = "error"
    OFFLINE = "offline"


class ToolUsageStats(BaseModel):
    """Tool usage statistics."""
    
    tool_name: str = Field(..., description="Tool name")
    total_calls: int = Field(default=0, description="Total number of calls")
    successful_calls: int = Field(default=0, description="Successful calls")
    failed_calls: int = Field(default=0, description="Failed calls")
    avg_execution_time: float = Field(default=0.0, description="Average execution time in ms")
    total_execution_time: float = Field(default=0.0, description="Total execution time in ms")
    success_rate: float = Field(default=0.0, description="Success rate percentage")
    calls_per_hour: float = Field(default=0.0, description="Calls per hour")
    last_used: Optional[datetime] = Field(None, description="Last usage timestamp")


class AgentMetrics(BaseModel):
    """Agent performance metrics."""
    
    agent_id: str = Field(..., description="Agent identifier")
    agent_type: str = Field(..., description="Agent type/name")
    status: AgentStatus = Field(default=AgentStatus.IDLE, description="Current agent status")
    
    # Activity metrics
    total_tasks: int = Field(default=0, description="Total tasks completed")
    successful_tasks: int = Field(default=0, description="Successful tasks")
    failed_tasks: int = Field(default=0, description="Failed tasks")
    success_rate: float = Field(default=0.0, description="Task success rate percentage")
    
    # Performance metrics
    avg_task_duration: float = Field(default=0.0, description="Average task duration in seconds")
    total_execution_time: float = Field(default=0.0, description="Total execution time in seconds")
    tasks_per_hour: float = Field(default=0.0, description="Tasks completed per hour")
    
    # Resource usage
    cpu_usage: float = Field(default=0.0, description="CPU usage percentage")
    memory_usage: float = Field(default=0.0, description="Memory usage in MB")
    
    # Tool usage
    tools_used: Set[str] = Field(default_factory=set, description="Set of tools used")
    most_used_tool: Optional[str] = Field(None, description="Most frequently used tool")
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.now, description="Agent creation time")
    last_activity: datetime = Field(default_factory=datetime.now, description="Last activity time")
    uptime_seconds: float = Field(default=0.0, description="Agent uptime in seconds")


class AgentActivityMonitor:
    """
    🤖 Advanced Agent Activity Monitor
    
    Tracks agent activities, tool usage, and performance metrics
    for comprehensive agent management and optimization.
    """
    
    def __init__(self, max_history_size: int = 1000):
        self.max_history_size = max_history_size
        
        # Agent tracking
        self.active_agents: Dict[str, AgentMetrics] = {}
        self.agent_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history_size))
        
        # Tool usage tracking
        self.tool_stats: Dict[str, ToolUsageStats] = {}
        self.tool_usage_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history_size))
        
        # Activity tracking
        self.task_history: deque = deque(maxlen=max_history_size)
        self.tool_call_history: deque = deque(maxlen=max_history_size)
        
        # Start background monitoring
        self._start_monitoring_tasks()
    
    def register_agent(
        self,
        agent_id: str,
        agent_type: str,
        status: AgentStatus = AgentStatus.ACTIVE
    ) -> None:
        """Register a new agent for monitoring."""
        if agent_id not in self.active_agents:
            self.active_agents[agent_id] = AgentMetrics(
                agent_id=agent_id,
                agent_type=agent_type,
                status=status
            )
            logger.info(f"Registered agent {agent_id} of type {agent_type}")
    
    def update_agent_status(self, agent_id: str, status: AgentStatus) -> None:
        """Update agent status."""
        if agent_id in self.active_agents:
            self.active_agents[agent_id].status = status
            self.active_agents[agent_id].last_activity = datetime.now()
    
    def record_task_completion(
        self,
        agent_id: str,
        task_duration: float,
        success: bool,
        tools_used: Optional[List[str]] = None
    ) -> None:
        """Record a completed task for an agent."""
        if agent_id not in self.active_agents:
            logger.warning(f"Task recorded for unregistered agent: {agent_id}")
            return
        
        agent = self.active_agents[agent_id]
        
        # Update task metrics
        agent.total_tasks += 1
        if success:
            agent.successful_tasks += 1
        else:
            agent.failed_tasks += 1
        
        agent.success_rate = (agent.successful_tasks / agent.total_tasks) * 100
        agent.total_execution_time += task_duration
        agent.avg_task_duration = agent.total_execution_time / agent.total_tasks
        agent.last_activity = datetime.now()
        
        # Update tools used
        if tools_used:
            agent.tools_used.update(tools_used)
        
        # Record in history
        self.task_history.append({
            "agent_id": agent_id,
            "timestamp": datetime.now(),
            "duration": task_duration,
            "success": success,
            "tools_used": tools_used or []
        })
        
        # Update hourly task rate
        self._update_agent_hourly_rates(agent_id)
    
    def record_tool_usage(
        self,
        tool_name: str,
        execution_time: float,
        success: bool,
        agent_id: Optional[str] = None
    ) -> None:
        """Record tool usage statistics."""
        # Initialize tool stats if not exists
        if tool_name not in self.tool_stats:
            self.tool_stats[tool_name] = ToolUsageStats(tool_name=tool_name)
        
        tool_stats = self.tool_stats[tool_name]
        
        # Update tool metrics
        tool_stats.total_calls += 1
        if success:
            tool_stats.successful_calls += 1
        else:
            tool_stats.failed_calls += 1
        
        tool_stats.success_rate = (tool_stats.successful_calls / tool_stats.total_calls) * 100
        tool_stats.total_execution_time += execution_time
        tool_stats.avg_execution_time = tool_stats.total_execution_time / tool_stats.total_calls
        tool_stats.last_used = datetime.now()
        
        # Record in history
        self.tool_call_history.append({
            "tool_name": tool_name,
            "agent_id": agent_id,
            "timestamp": datetime.now(),
            "execution_time": execution_time,
            "success": success
        })
        
        # Update hourly call rate
        self._update_tool_hourly_rates(tool_name)
        
        # Update agent's most used tool
        if agent_id and agent_id in self.active_agents:
            self._update_agent_most_used_tool(agent_id)
    
    def update_agent_resources(
        self,
        agent_id: str,
        cpu_usage: float,
        memory_usage: float
    ) -> None:
        """Update agent resource usage."""
        if agent_id in self.active_agents:
            agent = self.active_agents[agent_id]
            agent.cpu_usage = cpu_usage
            agent.memory_usage = memory_usage
            agent.last_activity = datetime.now()
    
    def deregister_agent(self, agent_id: str) -> None:
        """Deregister an agent."""
        if agent_id in self.active_agents:
            self.active_agents[agent_id].status = AgentStatus.OFFLINE
            # Keep in history but remove from active
            del self.active_agents[agent_id]
            logger.info(f"Deregistered agent {agent_id}")
    
    def get_agent_metrics(self, agent_id: Optional[str] = None) -> Dict[str, AgentMetrics]:
        """Get agent metrics."""
        if agent_id:
            return {agent_id: self.active_agents[agent_id]} if agent_id in self.active_agents else {}
        return self.active_agents.copy()
    
    def get_tool_usage_stats(self, tool_name: Optional[str] = None) -> Dict[str, ToolUsageStats]:
        """Get tool usage statistics."""
        if tool_name:
            return {tool_name: self.tool_stats[tool_name]} if tool_name in self.tool_stats else {}
        return self.tool_stats.copy()
    
    def get_activity_summary(self) -> Dict[str, Any]:
        """Get overall activity summary."""
        active_agents = [a for a in self.active_agents.values() if a.status == AgentStatus.ACTIVE]
        
        # Calculate recent activity (last hour)
        recent_cutoff = datetime.now() - timedelta(hours=1)
        recent_tasks = [t for t in self.task_history if t["timestamp"] > recent_cutoff]
        recent_tool_calls = [t for t in self.tool_call_history if t["timestamp"] > recent_cutoff]
        
        return {
            "total_agents": len(self.active_agents),
            "active_agents": len(active_agents),
            "idle_agents": len([a for a in self.active_agents.values() if a.status == AgentStatus.IDLE]),
            "busy_agents": len([a for a in self.active_agents.values() if a.status == AgentStatus.BUSY]),
            "error_agents": len([a for a in self.active_agents.values() if a.status == AgentStatus.ERROR]),
            
            "total_tasks_completed": sum(a.total_tasks for a in self.active_agents.values()),
            "total_successful_tasks": sum(a.successful_tasks for a in self.active_agents.values()),
            "overall_success_rate": self._calculate_overall_success_rate(),
            
            "total_tools_available": len(self.tool_stats),
            "total_tool_calls": sum(t.total_calls for t in self.tool_stats.values()),
            "most_used_tools": self._get_most_used_tools(),
            
            "recent_activity": {
                "tasks_last_hour": len(recent_tasks),
                "tool_calls_last_hour": len(recent_tool_calls),
                "avg_task_duration": sum(t["duration"] for t in recent_tasks) / len(recent_tasks) if recent_tasks else 0
            }
        }
    
    def _update_agent_hourly_rates(self, agent_id: str) -> None:
        """Update agent's hourly task completion rate."""
        if agent_id not in self.active_agents:
            return
        
        agent = self.active_agents[agent_id]
        uptime_hours = (datetime.now() - agent.created_at).total_seconds() / 3600
        agent.uptime_seconds = (datetime.now() - agent.created_at).total_seconds()
        
        if uptime_hours > 0:
            agent.tasks_per_hour = agent.total_tasks / uptime_hours
    
    def _update_tool_hourly_rates(self, tool_name: str) -> None:
        """Update tool's hourly call rate."""
        if tool_name not in self.tool_stats:
            return
        
        tool_stats = self.tool_stats[tool_name]
        if tool_stats.last_used:
            # Calculate calls in the last hour
            recent_cutoff = datetime.now() - timedelta(hours=1)
            recent_calls = [
                call for call in self.tool_call_history
                if call["tool_name"] == tool_name and call["timestamp"] > recent_cutoff
            ]
            tool_stats.calls_per_hour = len(recent_calls)
    
    def _update_agent_most_used_tool(self, agent_id: str) -> None:
        """Update agent's most used tool."""
        if agent_id not in self.active_agents:
            return
        
        # Count tool usage for this agent
        tool_counts = defaultdict(int)
        for call in self.tool_call_history:
            if call["agent_id"] == agent_id:
                tool_counts[call["tool_name"]] += 1
        
        if tool_counts:
            most_used = max(tool_counts.items(), key=lambda x: x[1])
            self.active_agents[agent_id].most_used_tool = most_used[0]
    
    def _calculate_overall_success_rate(self) -> float:
        """Calculate overall success rate across all agents."""
        total_tasks = sum(a.total_tasks for a in self.active_agents.values())
        total_successful = sum(a.successful_tasks for a in self.active_agents.values())
        
        return (total_successful / total_tasks * 100) if total_tasks > 0 else 0.0
    
    def _get_most_used_tools(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get the most frequently used tools."""
        sorted_tools = sorted(
            self.tool_stats.items(),
            key=lambda x: x[1].total_calls,
            reverse=True
        )
        
        return [
            {
                "tool_name": tool_name,
                "total_calls": stats.total_calls,
                "success_rate": stats.success_rate,
                "avg_execution_time": stats.avg_execution_time
            }
            for tool_name, stats in sorted_tools[:limit]
        ]
    
    def _start_monitoring_tasks(self) -> None:
        """Start background monitoring tasks."""
        async def update_agent_uptime():
            while True:
                try:
                    # Update agent uptime and hourly rates
                    for agent_id in self.active_agents:
                        self._update_agent_hourly_rates(agent_id)
                    
                    # Update tool hourly rates
                    for tool_name in self.tool_stats:
                        self._update_tool_hourly_rates(tool_name)
                    
                    await asyncio.sleep(60)  # Update every minute
                    
                except Exception as e:
                    logger.error(f"Agent activity monitor update failed: {e}")
                    await asyncio.sleep(60)
        
        # Start monitoring task
        asyncio.create_task(update_agent_uptime())


# Global agent activity monitor instance
_agent_activity_monitor: Optional[AgentActivityMonitor] = None


def get_agent_activity_monitor() -> AgentActivityMonitor:
    """Get the global agent activity monitor instance."""
    global _agent_activity_monitor
    if _agent_activity_monitor is None:
        _agent_activity_monitor = AgentActivityMonitor()
    return _agent_activity_monitor
