"""
🎯 Smart Context Management with Auto-Compact System

This module provides intelligent context management with automatic compaction:
- Intelligent context optimization to reduce token usage
- Auto-compaction system with quality preservation
- Relevance scoring for dynamic context prioritization
- Context caching for frequently used contexts
- Smart summarization with semantic preservation
"""

from .context_optimizer import (
    ContextOptimizer,
    OptimizationStrategy,
    OptimizationR<PERSON>ult,
    get_context_optimizer
)

from .auto_compactor import (
    AutoCompactor,
    CompactionResult,
    CompressionStrategy,
    QualityMetrics,
    Message,
    get_auto_compactor
)

from .relevance_scorer import (
    RelevanceScorer,
    RelevanceScore,
    ScoringCriteria,
    get_relevance_scorer
)

from .context_cache import (
    ContextCache,
    CacheEntry,
    CacheStrategy,
    get_context_cache
)

from .context_summarizer import (
    ContextSummarizer,
    SummarizationConfig,
    SummarizationStrategy,
    SummaryQuality,
    get_context_summarizer
)

__all__ = [
    # Context Optimization
    "ContextOptimizer",
    "OptimizationStrategy", 
    "OptimizationResult",
    "get_context_optimizer",
    
    # Auto-Compaction
    "AutoCompactor",
    "CompactionResult",
    "CompressionStrategy",
    "QualityMetrics",
    "Message",
    "get_auto_compactor",
    
    # Relevance Scoring
    "RelevanceScorer",
    "RelevanceScore",
    "ScoringCriteria",
    "get_relevance_scorer",
    
    # Context Caching
    "ContextCache",
    "CacheEntry",
    "CacheStrategy",
    "get_context_cache",
    
    # Context Summarization
    "ContextSummarizer",
    "SummarizationConfig",
    "SummarizationStrategy",
    "SummaryQuality",
    "get_context_summarizer"
]
