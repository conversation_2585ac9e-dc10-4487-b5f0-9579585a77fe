"""
Test Cases and Dataset Management for Pydantic AI

This module provides test case definitions and dataset management
for comprehensive evaluation of AI agents.
"""

import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)


class TestCaseType(Enum):
    """Types of test cases."""
    CODING = "coding"
    VISION = "vision"
    WORKFLOW = "workflow"
    INTEGRATION = "integration"


@dataclass
class BaseTestCase:
    """Base test case structure."""
    name: str
    description: str
    input_data: Any
    expected_output: Optional[Any] = None
    expected_tools: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None
    test_type: TestCaseType = TestCaseType.CODING
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        data = asdict(self)
        # Convert enum to string for serialization
        if 'test_type' in data and hasattr(data['test_type'], 'value'):
            data['test_type'] = data['test_type'].value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaseTestCase':
        """Create from dictionary."""
        if 'test_type' in data and isinstance(data['test_type'], str):
            data['test_type'] = TestCaseType(data['test_type'])
        return cls(**data)


@dataclass
class CodingTestCase(BaseTestCase):
    """Test case for coding tasks."""
    
    def __init__(
        self,
        name: str,
        description: str,
        input_data: str,
        expected_output: Optional[str] = None,
        expected_tools: Optional[List[str]] = None,
        code_quality_threshold: float = 0.5,
        **kwargs
    ):
        super().__init__(
            name=name,
            description=description,
            input_data=input_data,
            expected_output=expected_output,
            expected_tools=expected_tools,
            test_type=TestCaseType.CODING,
            metadata={
                "code_quality_threshold": code_quality_threshold,
                **kwargs.get("metadata", {})
            }
        )


@dataclass
class VisionTestCase(BaseTestCase):
    """Test case for vision tasks."""
    
    def __init__(
        self,
        name: str,
        description: str,
        input_data: str,  # Usually image path or description
        expected_output: Optional[str] = None,
        expected_tools: Optional[List[str]] = None,
        image_path: Optional[str] = None,
        analysis_type: str = "comprehensive",
        **kwargs
    ):
        super().__init__(
            name=name,
            description=description,
            input_data=input_data,
            expected_output=expected_output,
            expected_tools=expected_tools,
            test_type=TestCaseType.VISION,
            metadata={
                "image_path": image_path,
                "analysis_type": analysis_type,
                **kwargs.get("metadata", {})
            }
        )


@dataclass
class WorkflowTestCase(BaseTestCase):
    """Test case for workflow tasks."""
    
    def __init__(
        self,
        name: str,
        description: str,
        input_data: str,
        expected_output: Optional[str] = None,
        expected_tools: Optional[List[str]] = None,
        workflow_steps: Optional[List[str]] = None,
        max_execution_time: float = 30.0,
        **kwargs
    ):
        super().__init__(
            name=name,
            description=description,
            input_data=input_data,
            expected_output=expected_output,
            expected_tools=expected_tools,
            test_type=TestCaseType.WORKFLOW,
            metadata={
                "workflow_steps": workflow_steps or [],
                "max_execution_time": max_execution_time,
                **kwargs.get("metadata", {})
            }
        )


class TestDataset:
    """Manages collections of test cases."""
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.test_cases: List[BaseTestCase] = []
    
    def add_test_case(self, test_case: BaseTestCase):
        """Add a test case to the dataset."""
        self.test_cases.append(test_case)
        logger.info(f"Added test case '{test_case.name}' to dataset '{self.name}'")
    
    def add_test_cases(self, test_cases: List[BaseTestCase]):
        """Add multiple test cases."""
        for test_case in test_cases:
            self.add_test_case(test_case)
    
    def get_by_type(self, test_type: TestCaseType) -> List[BaseTestCase]:
        """Get test cases by type."""
        return [tc for tc in self.test_cases if tc.test_type == test_type]
    
    def get_by_name(self, name: str) -> Optional[BaseTestCase]:
        """Get test case by name."""
        for test_case in self.test_cases:
            if test_case.name == name:
                return test_case
        return None
    
    def save_to_file(self, file_path: Union[str, Path]):
        """Save dataset to JSON file."""
        file_path = Path(file_path)
        
        data = {
            "name": self.name,
            "description": self.description,
            "test_cases": [tc.to_dict() for tc in self.test_cases]
        }
        
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2, default=str)
        
        logger.info(f"Saved dataset '{self.name}' with {len(self.test_cases)} test cases to {file_path}")
    
    @classmethod
    def load_from_file(cls, file_path: Union[str, Path]) -> 'TestDataset':
        """Load dataset from JSON file."""
        file_path = Path(file_path)
        
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        dataset = cls(data["name"], data.get("description", ""))
        
        for tc_data in data["test_cases"]:
            test_case = BaseTestCase.from_dict(tc_data)
            dataset.add_test_case(test_case)
        
        logger.info(f"Loaded dataset '{dataset.name}' with {len(dataset.test_cases)} test cases from {file_path}")
        return dataset
    
    def __len__(self) -> int:
        return len(self.test_cases)
    
    def __iter__(self):
        return iter(self.test_cases)


def create_test_dataset(dataset_type: str = "comprehensive") -> TestDataset:
    """Create a predefined test dataset."""
    
    if dataset_type == "coding":
        return create_coding_dataset()
    elif dataset_type == "vision":
        return create_vision_dataset()
    elif dataset_type == "workflow":
        return create_workflow_dataset()
    elif dataset_type == "comprehensive":
        return create_comprehensive_dataset()
    else:
        raise ValueError(f"Unknown dataset type: {dataset_type}")


def create_coding_dataset() -> TestDataset:
    """Create a dataset for coding tasks."""
    dataset = TestDataset("coding_tests", "Test cases for coding functionality")
    
    test_cases = [
        CodingTestCase(
            name="simple_hello_world",
            description="Test basic response capability",
            input_data="Write a simple hello world function in Python",
            expected_tools=["write_file"],
            code_quality_threshold=0.3
        ),
        CodingTestCase(
            name="file_reading",
            description="Test file reading capability",
            input_data="Read the contents of README.md",
            expected_tools=["read_file"],
            code_quality_threshold=0.2
        ),
        CodingTestCase(
            name="code_analysis",
            description="Test code analysis capability",
            input_data="Analyze the structure of main.py and provide insights",
            expected_tools=["read_file", "analyze_code"],
            code_quality_threshold=0.5
        ),
        CodingTestCase(
            name="git_operations",
            description="Test git functionality",
            input_data="Check the git status and show recent commits",
            expected_tools=["git_status", "git_log"],
            code_quality_threshold=0.2
        ),
        CodingTestCase(
            name="complex_workflow",
            description="Test complex multi-step workflow",
            input_data="Read main.py, analyze its structure, and suggest improvements",
            expected_tools=["read_file", "analyze_code"],
            code_quality_threshold=0.6
        )
    ]
    
    dataset.add_test_cases(test_cases)
    return dataset


def create_vision_dataset() -> TestDataset:
    """Create a dataset for vision tasks."""
    dataset = TestDataset("vision_tests", "Test cases for vision functionality")
    
    test_cases = [
        VisionTestCase(
            name="screenshot_analysis",
            description="Test screenshot analysis",
            input_data="Analyze this screenshot for UI issues",
            expected_tools=["analyze_screenshot"],
            analysis_type="comprehensive"
        ),
        VisionTestCase(
            name="accessibility_audit",
            description="Test accessibility auditing",
            input_data="Perform accessibility audit on this interface",
            expected_tools=["accessibility_audit"],
            analysis_type="accessibility"
        ),
        VisionTestCase(
            name="ui_element_detection",
            description="Test UI element detection",
            input_data="Identify all UI elements in this interface",
            expected_tools=["detect_ui_elements"],
            analysis_type="elements"
        ),
        VisionTestCase(
            name="visual_regression",
            description="Test visual regression analysis",
            input_data="Compare these two interface versions",
            expected_tools=["visual_regression_analysis"],
            analysis_type="regression"
        )
    ]
    
    dataset.add_test_cases(test_cases)
    return dataset


def create_workflow_dataset() -> TestDataset:
    """Create a dataset for workflow tasks."""
    dataset = TestDataset("workflow_tests", "Test cases for workflow functionality")
    
    test_cases = [
        WorkflowTestCase(
            name="code_review_workflow",
            description="Test code review workflow",
            input_data="Perform a complete code review of the project",
            expected_tools=["read_file", "analyze_code", "git_status"],
            workflow_steps=["read_files", "analyze_structure", "check_git_status", "generate_report"],
            max_execution_time=45.0
        ),
        WorkflowTestCase(
            name="deployment_check_workflow",
            description="Test deployment readiness workflow",
            input_data="Check if the project is ready for deployment",
            expected_tools=["git_status", "analyze_code", "run_tests"],
            workflow_steps=["check_git_clean", "run_tests", "analyze_dependencies"],
            max_execution_time=60.0
        ),
        WorkflowTestCase(
            name="documentation_workflow",
            description="Test documentation generation workflow",
            input_data="Generate comprehensive documentation for the project",
            expected_tools=["read_file", "analyze_code", "write_file"],
            workflow_steps=["scan_files", "analyze_structure", "generate_docs", "write_output"],
            max_execution_time=30.0
        )
    ]
    
    dataset.add_test_cases(test_cases)
    return dataset


def create_comprehensive_dataset() -> TestDataset:
    """Create a comprehensive dataset with all types."""
    dataset = TestDataset("comprehensive_tests", "Comprehensive test suite for all functionality")
    
    # Add all test types
    coding_dataset = create_coding_dataset()
    vision_dataset = create_vision_dataset()
    workflow_dataset = create_workflow_dataset()
    
    dataset.add_test_cases(coding_dataset.test_cases)
    dataset.add_test_cases(vision_dataset.test_cases)
    dataset.add_test_cases(workflow_dataset.test_cases)
    
    return dataset


def load_test_cases(file_path: Union[str, Path]) -> TestDataset:
    """Load test cases from file."""
    return TestDataset.load_from_file(file_path)


# Export all classes and functions
__all__ = [
    'TestCaseType',
    'BaseTestCase',
    'CodingTestCase',
    'VisionTestCase',
    'WorkflowTestCase',
    'TestDataset',
    'create_test_dataset',
    'create_coding_dataset',
    'create_vision_dataset',
    'create_workflow_dataset',
    'create_comprehensive_dataset',
    'load_test_cases'
]
