"""
🚀 Session Management API Endpoints

This module provides comprehensive API endpoints for session management:
- Session creation, retrieval, and management
- Conversation history with search capabilities
- Session recovery and export functionality
- Integration with project/workspace management
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field
import logging

from ..sessions import (
    get_session_manager,
    get_session_recovery,
    get_export_manager,
    SessionConfig,
    Session,
    SessionStatus,
    ConversationHistory,
    ConversationFilter,
    MessageRole,
    MessageType,
    ExportConfig,
    ExportFormat,
    RecoveryResult
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/sessions", tags=["sessions"])

# Request/Response Models
class CreateSessionRequest(BaseModel):
    """Request model for creating a new session."""
    project_id: str = Field(..., description="Project ID for the session")
    workspace_path: str = Field(..., description="Workspace directory path")
    name: str = Field(..., description="Session display name")
    description: str = Field(default="", description="Session description")
    config: Optional[SessionConfig] = Field(None, description="Optional session configuration")


class SessionResponse(BaseModel):
    """Response model for session data."""
    session: Session = Field(..., description="Session data")
    message_count: int = Field(default=0, description="Number of messages in conversation")
    last_activity: datetime = Field(..., description="Last activity timestamp")


class SearchMessagesRequest(BaseModel):
    """Request model for searching messages."""
    session_id: str = Field(..., description="Session ID to search")
    search_query: Optional[str] = Field(None, description="Text search query")
    roles: Optional[List[MessageRole]] = Field(None, description="Filter by message roles")
    message_types: Optional[List[MessageType]] = Field(None, description="Filter by message types")
    start_date: Optional[datetime] = Field(None, description="Start date filter")
    end_date: Optional[datetime] = Field(None, description="End date filter")
    limit: int = Field(default=50, description="Maximum number of results")
    offset: int = Field(default=0, description="Result offset for pagination")


class ExportSessionRequest(BaseModel):
    """Request model for exporting sessions."""
    session_ids: List[str] = Field(..., description="Session IDs to export")
    format: ExportFormat = Field(default=ExportFormat.MARKDOWN, description="Export format")
    include_metadata: bool = Field(default=True, description="Include message metadata")
    include_timestamps: bool = Field(default=True, description="Include timestamps")
    include_statistics: bool = Field(default=False, description="Include conversation statistics")


# Session Management Endpoints

# System endpoints must come before /{session_id} to avoid conflicts
@router.get("/health")
async def session_system_health():
    """Check session management system health."""
    try:
        session_manager = get_session_manager()

        # Basic health checks
        stats = session_manager.get_session_stats()

        health_status = {
            "healthy": True,
            "session_manager": "operational",
            "active_sessions": stats.get("total_active_sessions", 0),
            "timestamp": datetime.now()
        }

        return health_status

    except Exception as e:
        logger.error(f"Session system health check failed: {e}")
        return {
            "healthy": False,
            "error": str(e),
            "timestamp": datetime.now()
        }


@router.get("/system/stats")
async def get_system_stats():
    """Get session management system statistics."""
    try:
        session_manager = get_session_manager()
        session_recovery = get_session_recovery()

        session_stats = session_manager.get_session_stats()
        recovery_stats = session_recovery.get_recovery_stats()

        return {
            "session_management": session_stats,
            "recovery_system": recovery_stats,
            "timestamp": datetime.now()
        }

    except Exception as e:
        logger.error(f"Failed to get system stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get system stats: {str(e)}")


@router.get("/export/project/{project_id}")
async def export_project_sessions(
    project_id: str,
    format: ExportFormat = Query(default=ExportFormat.MARKDOWN),
    include_metadata: bool = Query(default=True),
    include_timestamps: bool = Query(default=True),
    include_statistics: bool = Query(default=False)
):
    """Export all sessions for a project."""
    try:
        export_manager = get_export_manager()

        config = ExportConfig(
            format=format,
            include_metadata=include_metadata,
            include_timestamps=include_timestamps,
            include_statistics=include_statistics
        )

        result = await export_manager.export_project_sessions(project_id, config)

        return {
            "project_id": project_id,
            "export_result": result,
            "download_url": f"/api/sessions/download/{result.export_id}" if result.success else None
        }

    except Exception as e:
        logger.error(f"Failed to export project sessions: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to export project sessions: {str(e)}")


@router.get("/project/{project_id}")
async def get_project_sessions(project_id: str):
    """Get all sessions for a project."""
    try:
        session_manager = get_session_manager()
        conversation_history = ConversationHistory()

        sessions = await session_manager.get_project_sessions(project_id)

        # Enhance with conversation stats
        enhanced_sessions = []
        for session in sessions:
            stats = await conversation_history.get_conversation_stats(session.session_id)
            enhanced_sessions.append({
                "session": session,
                "message_count": stats.get("total_messages", 0),
                "last_activity": session.last_accessed,
                "conversation_duration": stats.get("conversation_duration", 0)
            })

        return {
            "project_id": project_id,
            "sessions": enhanced_sessions,
            "total_sessions": len(sessions)
        }

    except Exception as e:
        logger.error(f"Failed to get project sessions for {project_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get project sessions: {str(e)}")


@router.post("/export")
async def export_sessions(request: ExportSessionRequest):
    """Export sessions to specified format."""
    try:
        export_manager = get_export_manager()

        # Create export config
        config = ExportConfig(
            format=request.format,
            include_metadata=request.include_metadata,
            include_timestamps=request.include_timestamps,
            include_statistics=request.include_statistics
        )

        if len(request.session_ids) == 1:
            # Single session export
            result = await export_manager.export_session(
                request.session_ids[0], config
            )
        else:
            # Multiple session export
            result = await export_manager.export_multiple_sessions(
                request.session_ids, config
            )

        return {
            "export_result": result,
            "download_url": f"/api/sessions/download/{result.export_id}" if result.success else None
        }

    except Exception as e:
        logger.error(f"Failed to export sessions: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to export sessions: {str(e)}")


@router.post("/create", response_model=SessionResponse)
async def create_session(request: CreateSessionRequest):
    """Create a new session for a project."""
    try:
        session_manager = get_session_manager()
        
        session = await session_manager.create_session(
            project_id=request.project_id,
            workspace_path=request.workspace_path,
            name=request.name,
            description=request.description,
            config=request.config
        )
        
        return SessionResponse(
            session=session,
            message_count=0,
            last_activity=session.last_accessed
        )
        
    except Exception as e:
        logger.error(f"Failed to create session: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create session: {str(e)}")


@router.get("/{session_id}", response_model=SessionResponse)
async def get_session(session_id: str):
    """Get session by ID."""
    try:
        session_manager = get_session_manager()
        conversation_history = ConversationHistory()
        
        session = await session_manager.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Get message count
        messages = await conversation_history.get_session_messages(session_id, limit=1)
        stats = await conversation_history.get_conversation_stats(session_id)
        message_count = stats.get("total_messages", 0)
        
        return SessionResponse(
            session=session,
            message_count=message_count,
            last_activity=session.last_accessed
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get session {session_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get session: {str(e)}")





@router.put("/{session_id}/status")
async def update_session_status(session_id: str, status: SessionStatus):
    """Update session status."""
    try:
        session_manager = get_session_manager()
        
        if status == SessionStatus.PAUSED:
            success = await session_manager.pause_session(session_id)
        elif status == SessionStatus.ACTIVE:
            success = await session_manager.resume_session(session_id)
        elif status == SessionStatus.COMPLETED:
            success = await session_manager.complete_session(session_id)
        else:
            raise HTTPException(status_code=400, detail=f"Invalid status: {status}")
        
        if not success:
            raise HTTPException(status_code=404, detail="Session not found or update failed")
        
        return {"session_id": session_id, "status": status, "updated": True}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update session status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update session status: {str(e)}")


@router.delete("/{session_id}")
async def delete_session(session_id: str):
    """Delete a session."""
    try:
        session_manager = get_session_manager()
        
        success = await session_manager.delete_session(session_id)
        if not success:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return {"session_id": session_id, "deleted": True}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete session: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete session: {str(e)}")


# Conversation History Endpoints
@router.get("/{session_id}/messages")
async def get_session_messages(
    session_id: str,
    limit: int = Query(default=50, description="Maximum number of messages"),
    offset: int = Query(default=0, description="Message offset for pagination")
):
    """Get messages for a session."""
    try:
        conversation_history = ConversationHistory()
        
        messages = await conversation_history.get_session_messages(
            session_id, limit=limit, offset=offset
        )
        
        return {
            "session_id": session_id,
            "messages": messages,
            "count": len(messages),
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error(f"Failed to get session messages: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get session messages: {str(e)}")


@router.post("/{session_id}/search")
async def search_messages(request: SearchMessagesRequest):
    """Search messages in a session."""
    try:
        conversation_history = ConversationHistory()
        
        # Create filter from request
        filter_criteria = ConversationFilter(
            search_query=request.search_query,
            roles=request.roles,
            message_types=request.message_types,
            start_date=request.start_date,
            end_date=request.end_date,
            limit=request.limit,
            offset=request.offset
        )
        
        results = await conversation_history.search_messages(
            request.session_id, filter_criteria
        )
        
        return {
            "session_id": request.session_id,
            "search_results": results,
            "count": len(results),
            "query": request.search_query
        }
        
    except Exception as e:
        logger.error(f"Failed to search messages: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to search messages: {str(e)}")


@router.get("/{session_id}/stats")
async def get_conversation_stats(session_id: str):
    """Get conversation statistics for a session."""
    try:
        conversation_history = ConversationHistory()
        
        stats = await conversation_history.get_conversation_stats(session_id)
        
        return {
            "session_id": session_id,
            "statistics": stats
        }
        
    except Exception as e:
        logger.error(f"Failed to get conversation stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get conversation stats: {str(e)}")


# Session Recovery Endpoints
@router.post("/{session_id}/recover")
async def recover_session(
    session_id: str,
    recovery_point: Optional[datetime] = None
):
    """Recover a session from the last known good state."""
    try:
        session_recovery = get_session_recovery()
        
        result = await session_recovery.recover_session(session_id, recovery_point)
        
        return {
            "session_id": session_id,
            "recovery_result": result
        }
        
    except Exception as e:
        logger.error(f"Failed to recover session: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to recover session: {str(e)}")


@router.post("/{session_id}/snapshot")
async def create_session_snapshot(session_id: str):
    """Create a manual snapshot of session state."""
    try:
        session_recovery = get_session_recovery()
        
        success = await session_recovery.create_session_snapshot(session_id, is_automatic=False)
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to create snapshot")
        
        return {
            "session_id": session_id,
            "snapshot_created": True,
            "timestamp": datetime.now()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create session snapshot: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create session snapshot: {str(e)}")


# Export Endpoints
@router.post("/export")
async def export_sessions(request: ExportSessionRequest):
    """Export sessions to specified format."""
    try:
        export_manager = get_export_manager()
        
        # Create export config
        config = ExportConfig(
            format=request.format,
            include_metadata=request.include_metadata,
            include_timestamps=request.include_timestamps,
            include_statistics=request.include_statistics
        )
        
        if len(request.session_ids) == 1:
            # Single session export
            result = await export_manager.export_session(
                request.session_ids[0], config
            )
        else:
            # Multiple session export
            result = await export_manager.export_multiple_sessions(
                request.session_ids, config
            )
        
        return {
            "export_result": result,
            "download_url": f"/api/sessions/download/{result.export_id}" if result.success else None
        }
        
    except Exception as e:
        logger.error(f"Failed to export sessions: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to export sessions: {str(e)}")






