"""
Dependency Injection System for Pydantic AI Migration

This module implements the dependency injection pattern for the Pydantic AI agents,
providing clean separation of concerns and easy testing.
"""

from dataclasses import dataclass
from pathlib import Path
from typing import Union, Optional
import logging

# Import existing Phase 3-5 modules
from app.agent.file_operations import FileOperations
from app.agent.git_operations import GitOperations
from app.agent.code_analyzer import Code<PERSON>nalyzer
from app.agent.change_tracker import ChangeTracker
from app.agent.repo_context import <PERSON>ositoryContext
from vector_db import QdrantVectorClient as QdrantClientBase
from ollama_client import OllamaEmbeddingClient as OllamaClientBase
from llm_client import OpenRouterClient
from utils.lint_test_utils import LintTestUtils

logger = logging.getLogger(__name__)


@dataclass
class CodingDependencies:
    """Dependencies for the main coding agent."""
    
    file_operations: FileOperations
    git_operations: GitOperations
    code_analyzer: CodeAnalyzer
    change_tracker: ChangeTracker
    repo_context: RepositoryContext
    qdrant_client: QdrantClientBase
    ollama_client: OllamaClientBase
    openrouter_client: OpenRouterClient
    lint_test_utils: LintTestUtils
    workspace_root: Path


@dataclass
class VisionDependencies:
    """Dependencies for the vision analysis agent."""
    
    openrouter_client: OpenRouterClient
    file_operations: FileOperations
    workspace_root: Path


async def create_dependencies(
    workspace_root: Union[str, Path] = "/app/workspace",
    project_slug: Optional[str] = None
) -> CodingDependencies:
    """
    Factory function for creating project-aware coding dependencies.

    Args:
        workspace_root: Root directory for workspace operations (fallback)
        project_slug: Optional project slug for project-specific operations

    Returns:
        CodingDependencies instance with all required services
    """
    try:
        # Import project manager here to avoid circular imports
        try:
            from app.core.project_manager import project_manager
        except ImportError:
            from backend.app.core.project_manager import project_manager

        # Determine workspace path based on project context
        if project_slug:
            project = await project_manager.get_project(project_slug)
            if project:
                workspace_path = project.get_workspace_path(str(project_manager.base_workspace))
                logger.info(f"Creating dependencies for project: {project_slug} at {workspace_path}")
            else:
                workspace_path = Path(workspace_root)
                logger.warning(f"Project {project_slug} not found, using default workspace: {workspace_path}")
        elif project_manager.current_project:
            workspace_path = project_manager.get_project_workspace_path()
            logger.info(f"Creating dependencies for current project: {project_manager.current_project.slug} at {workspace_path}")
        else:
            workspace_path = Path(workspace_root)
            logger.info(f"No project context, using default workspace: {workspace_path}")

        workspace_path.mkdir(parents=True, exist_ok=True)

        # Initialize core services with project-aware workspace
        file_operations = FileOperations(project_slug=project_slug)

        git_operations = GitOperations()
        git_operations.workspace_root = workspace_path  # Override workspace root

        code_analyzer = CodeAnalyzer()
        change_tracker = ChangeTracker()
        repo_context = RepositoryContext()
        lint_test_utils = LintTestUtils()

        # Initialize external clients
        qdrant_client = QdrantClientBase()
        ollama_client = OllamaClientBase()
        openrouter_client = OpenRouterClient()

        return CodingDependencies(
            file_operations=file_operations,
            git_operations=git_operations,
            code_analyzer=code_analyzer,
            change_tracker=change_tracker,
            repo_context=repo_context,
            qdrant_client=qdrant_client,
            ollama_client=ollama_client,
            openrouter_client=openrouter_client,
            lint_test_utils=lint_test_utils,
            workspace_root=workspace_path
        )

    except Exception as e:
        logger.error(f"Failed to create dependencies: {e}")
        raise


async def create_vision_dependencies(
    workspace_root: Union[str, Path] = "/app/workspace",
    project_slug: Optional[str] = None
) -> VisionDependencies:
    """
    Factory function for creating project-aware vision dependencies.

    Args:
        workspace_root: Root directory for workspace operations (fallback)
        project_slug: Optional project slug for project-specific operations

    Returns:
        VisionDependencies instance with required services
    """
    try:
        # Import project manager here to avoid circular imports
        try:
            from app.core.project_manager import project_manager
        except ImportError:
            from backend.app.core.project_manager import project_manager

        # Determine workspace path based on project context
        if project_slug:
            project = await project_manager.get_project(project_slug)
            if project:
                workspace_path = project.get_workspace_path(str(project_manager.base_workspace))
                logger.info(f"Creating vision dependencies for project: {project_slug} at {workspace_path}")
            else:
                workspace_path = Path(workspace_root)
                logger.warning(f"Project {project_slug} not found, using default workspace: {workspace_path}")
        elif project_manager.current_project:
            workspace_path = project_manager.get_project_workspace_path()
            logger.info(f"Creating vision dependencies for current project: {project_manager.current_project.slug} at {workspace_path}")
        else:
            workspace_path = Path(workspace_root)
            logger.info(f"No project context, using default workspace: {workspace_path}")

        workspace_path.mkdir(parents=True, exist_ok=True)

        # Initialize required services with project-aware workspace
        openrouter_client = OpenRouterClient()
        file_operations = FileOperations(project_slug=project_slug)

        return VisionDependencies(
            openrouter_client=openrouter_client,
            file_operations=file_operations,
            workspace_root=workspace_path
        )

    except Exception as e:
        logger.error(f"Failed to create vision dependencies: {e}")
        raise


def create_test_dependencies(workspace_root: Optional[Union[str, Path]] = None) -> CodingDependencies:
    """
    Create mock dependencies for testing.
    
    Args:
        workspace_root: Optional workspace root for testing
        
    Returns:
        CodingDependencies with mock implementations
    """
    from unittest.mock import Mock
    
    if workspace_root is None:
        workspace_root = Path("/tmp/test_workspace")
    else:
        workspace_root = Path(workspace_root)
    
    # Create mock implementations for testing
    return CodingDependencies(
        file_operations=Mock(spec=FileOperations),
        git_operations=Mock(spec=GitOperations),
        code_analyzer=Mock(spec=CodeAnalyzer),
        change_tracker=Mock(spec=ChangeTracker),
        repo_context=Mock(spec=RepositoryContext),
        qdrant_client=Mock(spec=QdrantClientBase),
        ollama_client=Mock(spec=OllamaClientBase),
        openrouter_client=Mock(spec=OpenRouterClient),
        lint_test_utils=Mock(spec=LintTestUtils),
        workspace_root=workspace_root
    )
