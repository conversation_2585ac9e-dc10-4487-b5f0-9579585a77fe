"""
Code Intelligence Tools for Pydantic AI - Phase 8B

Tools that provide the coding agent with access to the Code Intelligence Hub
for semantic search, code analysis, and relationship mapping.
"""

import logging
from typing import List, Dict, Optional, Any, Union
from pydantic import BaseModel, Field
import logfire

# Import RunContext directly to avoid circular imports
try:
    from pydantic_ai import RunContext
    from ..dependencies import CodingDependencies
    TYPING_AVAILABLE = True
except ImportError:
    # Fallback type hint for testing
    RunContext = Any
    CodingDependencies = Any
    TYPING_AVAILABLE = False

# Type alias for proper annotation
if TYPING_AVAILABLE:
    ContextType = RunContext[CodingDependencies]
else:
    ContextType = Any

# Import Code Intelligence Hub
try:
    from ...code_intelligence import CodeIntelligenceHub, code_intelligence_hub
    from ...code_intelligence.models import SearchQuery, CodeLanguage
except ImportError:
    # Fallback for testing
    CodeIntelligenceHub = None
    code_intelligence_hub = None
    SearchQuery = None
    CodeLanguage = None

logger = logging.getLogger(__name__)


class CodeSearchRequest(BaseModel):
    """Request model for code search."""
    query: str = Field(..., description="Natural language search query")
    limit: int = Field(default=10, description="Maximum number of results")
    threshold: float = Field(default=0.7, description="Similarity threshold")
    language: Optional[str] = Field(None, description="Programming language filter")
    file_path: Optional[str] = Field(None, description="Specific file to search in")
    element_type: Optional[str] = Field(None, description="Type of code element (function, class, etc.)")


class IndexingRequest(BaseModel):
    """Request model for code indexing."""
    force_reindex: bool = Field(default=False, description="Force reindexing of all files")
    include_patterns: Optional[List[str]] = Field(None, description="File patterns to include")
    exclude_patterns: Optional[List[str]] = Field(None, description="File patterns to exclude")


class RelationshipRequest(BaseModel):
    """Request model for relationship analysis."""
    file_path: str = Field(..., description="Path to the file to analyze")
    analysis_type: str = Field(default="dependencies", description="Type of analysis (dependencies, dependents, impact)")


async def search_codebase(
    ctx: "RunContext[CodingDependencies]",
    query: str,
    limit: int = 10,
    threshold: float = 0.7,
    language: Optional[str] = None,
    file_path: Optional[str] = None,
    element_type: Optional[str] = None
) -> Dict[str, Any]:
    """
    Search the codebase using natural language queries.
    
    This tool enables semantic search across the entire indexed codebase.
    You can search for functions, classes, patterns, or any code-related concepts
    using natural language.
    
    Examples:
    - "Find authentication functions"
    - "Show me database connection code"
    - "Find error handling patterns"
    - "Search for API endpoints"
    
    Args:
        ctx: Run context with dependencies
        query: Natural language search query
        limit: Maximum number of results to return
        threshold: Similarity threshold (0.0 to 1.0)
        language: Filter by programming language (python, javascript, etc.)
        file_path: Search within a specific file
        element_type: Filter by element type (function, class, chunk)
        
    Returns:
        Dictionary with search results and metadata
    """
    with logfire.span("search_codebase", query=query, limit=limit):
        try:
            if not code_intelligence_hub:
                return {
                    'status': 'error',
                    'error': 'Code Intelligence Hub not available'
                }
            
            logger.info(f"🔍 Searching codebase: {query}")
            
            # Create search query
            search_query = SearchQuery(
                query=query,
                limit=limit,
                threshold=threshold
            )
            
            # Apply filters
            filters = {}
            if language:
                filters['language'] = language
            if file_path:
                filters['file_path'] = file_path
            if element_type:
                filters['element_type'] = element_type
            
            if filters:
                search_query.filters = filters
            
            # Perform search
            results = await code_intelligence_hub.search(search_query)
            
            # Format results for agent
            formatted_results = []
            for result in results:
                formatted_result = {
                    'file_path': result.file_path,
                    'content': result.content,
                    'score': result.score,
                    'element_type': result.element_type.value,
                    'start_line': result.start_line,
                    'end_line': result.end_line,
                    'language': result.language.value,
                    'function_name': result.function_name,
                    'class_name': result.class_name,
                    'context_before': result.context_before,
                    'context_after': result.context_after,
                    'tags': result.tags
                }
                formatted_results.append(formatted_result)
            
            logfire.info("Codebase search completed", 
                        query=query, 
                        results_count=len(results))
            
            return {
                'status': 'success',
                'query': query,
                'results': formatted_results,
                'total_results': len(results),
                'filters_applied': filters
            }
            
        except Exception as e:
            logger.error(f"❌ Codebase search failed: {e}")
            logfire.error("Codebase search failed", error=str(e))
            return {
                'status': 'error',
                'error': str(e),
                'query': query
            }


async def index_workspace(
    ctx: Any,  # RunContext[CodingDependencies]
    force_reindex: bool = False,
    include_patterns: Optional[List[str]] = None,
    exclude_patterns: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Index the workspace for code intelligence.
    
    This tool indexes all code files in the workspace to enable semantic search
    and relationship analysis. It analyzes file structure, extracts functions
    and classes, and generates embeddings for semantic search.
    
    Args:
        ctx: Run context with dependencies
        force_reindex: Whether to reindex all files regardless of changes
        include_patterns: File patterns to include (e.g., ['*.py', '*.js'])
        exclude_patterns: Additional patterns to exclude
        
    Returns:
        Dictionary with indexing results and statistics
    """
    with logfire.span("index_workspace", force_reindex=force_reindex):
        try:
            if not code_intelligence_hub:
                return {
                    'status': 'error',
                    'error': 'Code Intelligence Hub not available'
                }
            
            logger.info(f"🔍 Starting workspace indexing (force_reindex={force_reindex})")
            
            # Initialize if needed
            init_result = await code_intelligence_hub.initialize()
            if init_result['status'] != 'success':
                return init_result
            
            # Index workspace
            result = await code_intelligence_hub.index_workspace(
                force_reindex=force_reindex,
                include_patterns=include_patterns,
                exclude_patterns=exclude_patterns
            )
            
            logfire.info("Workspace indexing completed", 
                        status=result['status'],
                        indexed_files=result.get('stats', {}).get('indexed_files', 0))
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Workspace indexing failed: {e}")
            logfire.error("Workspace indexing failed", error=str(e))
            return {
                'status': 'error',
                'error': str(e)
            }


async def index_file(
    ctx: Any,  # RunContext[CodingDependencies]
    file_path: str,
    force_reindex: bool = False
) -> Dict[str, Any]:
    """
    Index a specific file for code intelligence.
    
    This tool indexes a single file, analyzing its structure and generating
    embeddings for semantic search. Useful for updating the index after
    file changes.
    
    Args:
        ctx: Run context with dependencies
        file_path: Path to the file to index
        force_reindex: Whether to reindex even if file hasn't changed
        
    Returns:
        Dictionary with indexing result for the file
    """
    with logfire.span("index_file", file_path=file_path):
        try:
            if not code_intelligence_hub:
                return {
                    'status': 'error',
                    'error': 'Code Intelligence Hub not available'
                }
            
            logger.info(f"🔍 Indexing file: {file_path}")
            
            # Index the file
            result = await code_intelligence_hub.index_file(file_path, force_reindex)
            
            logfire.info("File indexing completed", 
                        file_path=file_path,
                        status=result['status'])
            
            return result
            
        except Exception as e:
            logger.error(f"❌ File indexing failed: {e}")
            logfire.error("File indexing failed", error=str(e), file_path=file_path)
            return {
                'status': 'error',
                'error': str(e),
                'file_path': file_path
            }


async def find_similar_code(
    ctx: Any,  # RunContext[CodingDependencies]
    code_snippet: str,
    language: Optional[str] = None,
    limit: int = 10
) -> Dict[str, Any]:
    """
    Find code similar to the provided snippet.
    
    This tool finds code patterns similar to the provided code snippet
    using semantic similarity. Useful for finding existing implementations
    or patterns to reuse.
    
    Args:
        ctx: Run context with dependencies
        code_snippet: Code snippet to find similar code for
        language: Optional language filter
        limit: Maximum number of results
        
    Returns:
        Dictionary with similar code results
    """
    with logfire.span("find_similar_code", language=language):
        try:
            if not code_intelligence_hub:
                return {
                    'status': 'error',
                    'error': 'Code Intelligence Hub not available'
                }
            
            logger.info(f"🔍 Finding similar code (language: {language})")
            
            # Convert language string to enum if provided
            lang_enum = None
            if language:
                try:
                    lang_enum = CodeLanguage(language.lower())
                except ValueError:
                    logger.warning(f"Unknown language: {language}")
            
            # Search for similar code
            results = await code_intelligence_hub.semantic_search.search_similar_code(
                code_snippet=code_snippet,
                language=lang_enum,
                limit=limit
            )
            
            # Format results
            formatted_results = []
            for result in results:
                formatted_result = {
                    'file_path': result.file_path,
                    'content': result.content,
                    'score': result.score,
                    'element_type': result.element_type.value,
                    'start_line': result.start_line,
                    'end_line': result.end_line,
                    'language': result.language.value,
                    'function_name': result.function_name,
                    'class_name': result.class_name
                }
                formatted_results.append(formatted_result)
            
            logfire.info("Similar code search completed", 
                        results_count=len(results))
            
            return {
                'status': 'success',
                'code_snippet': code_snippet,
                'language': language,
                'results': formatted_results,
                'total_results': len(results)
            }
            
        except Exception as e:
            logger.error(f"❌ Similar code search failed: {e}")
            logfire.error("Similar code search failed", error=str(e))
            return {
                'status': 'error',
                'error': str(e)
            }


async def analyze_file_relationships(
    ctx: Any,  # RunContext[CodingDependencies]
    file_path: str,
    analysis_type: str = "dependencies"
) -> Dict[str, Any]:
    """
    Analyze relationships for a specific file.
    
    This tool analyzes code relationships including dependencies, dependents,
    and impact analysis for a specific file. Helps understand how changes
    to a file might affect the rest of the codebase.
    
    Args:
        ctx: Run context with dependencies
        file_path: Path to the file to analyze
        analysis_type: Type of analysis (dependencies, dependents, impact)
        
    Returns:
        Dictionary with relationship analysis results
    """
    with logfire.span("analyze_file_relationships", file_path=file_path, analysis_type=analysis_type):
        try:
            if not code_intelligence_hub:
                return {
                    'status': 'error',
                    'error': 'Code Intelligence Hub not available'
                }
            
            logger.info(f"🔗 Analyzing {analysis_type} for: {file_path}")
            
            if analysis_type == "dependencies":
                # Get file dependencies
                dependencies = await code_intelligence_hub.relationship_mapper.find_dependencies(
                    file_path, list(code_intelligence_hub.indexed_files.values())
                )
                
                return {
                    'status': 'success',
                    'file_path': file_path,
                    'analysis_type': analysis_type,
                    'dependencies': dependencies,
                    'count': len(dependencies)
                }
                
            elif analysis_type == "dependents":
                # Get files that depend on this file
                dependents = await code_intelligence_hub.relationship_mapper.find_dependents(
                    file_path, list(code_intelligence_hub.indexed_files.values())
                )
                
                return {
                    'status': 'success',
                    'file_path': file_path,
                    'analysis_type': analysis_type,
                    'dependents': dependents,
                    'count': len(dependents)
                }
                
            elif analysis_type == "impact":
                # Get impact analysis
                impact_analysis = await code_intelligence_hub.relationship_mapper.analyze_impact(
                    file_path, list(code_intelligence_hub.indexed_files.values())
                )
                
                return {
                    'status': 'success',
                    'file_path': file_path,
                    'analysis_type': analysis_type,
                    'impact_analysis': impact_analysis
                }
                
            else:
                return {
                    'status': 'error',
                    'error': f"Unknown analysis type: {analysis_type}",
                    'supported_types': ['dependencies', 'dependents', 'impact']
                }
            
        except Exception as e:
            logger.error(f"❌ Relationship analysis failed: {e}")
            logfire.error("Relationship analysis failed", error=str(e), file_path=file_path)
            return {
                'status': 'error',
                'error': str(e),
                'file_path': file_path
            }


async def get_code_intelligence_stats(
    ctx: Any  # RunContext[CodingDependencies]
) -> Dict[str, Any]:
    """
    Get Code Intelligence Hub statistics and status.
    
    This tool provides information about the current state of the code
    intelligence system including indexing statistics, system status,
    and performance metrics.
    
    Args:
        ctx: Run context with dependencies
        
    Returns:
        Dictionary with system statistics and status
    """
    with logfire.span("get_code_intelligence_stats"):
        try:
            if not code_intelligence_hub:
                return {
                    'status': 'error',
                    'error': 'Code Intelligence Hub not available'
                }
            
            logger.info("📊 Getting Code Intelligence Hub statistics")
            
            # Get system stats
            stats = await code_intelligence_hub.get_stats()
            
            logfire.info("Code Intelligence stats retrieved", 
                        indexed_files=stats.get('indexed_files_count', 0))
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ Failed to get stats: {e}")
            logfire.error("Code Intelligence stats failed", error=str(e))
            return {
                'status': 'error',
                'error': str(e)
            }
