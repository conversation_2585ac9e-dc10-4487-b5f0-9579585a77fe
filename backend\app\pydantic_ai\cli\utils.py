"""
CLI Utilities for Pydantic AI

This module provides utility functions for the CLI interface including
output formatting, configuration management, and argument parsing.
"""

import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import click

logger = logging.getLogger(__name__)


def format_output(data: Dict[str, Any], format_type: str = 'text') -> str:
    """Format output data according to the specified format."""
    
    if format_type == 'json':
        return json.dumps(data, indent=2, default=str)
    
    elif format_type == 'markdown':
        return format_markdown_output(data)
    
    else:  # text format
        return format_text_output(data)


def format_text_output(data: Dict[str, Any]) -> str:
    """Format data as human-readable text."""
    
    lines = []
    
    # Header
    lines.append("🤖 Pydantic AI Agent Result")
    lines.append("=" * 40)
    
    # Basic info
    if 'prompt' in data:
        lines.append(f"📝 Prompt: {data['prompt']}")
    
    if 'agent' in data:
        lines.append(f"🔧 Agent: {data['agent']}")
    
    if 'execution_time' in data:
        lines.append(f"⏱️  Execution Time: {data['execution_time']:.2f}s")
    
    if 'timestamp' in data:
        lines.append(f"🕒 Timestamp: {data['timestamp']}")
    
    lines.append("")
    
    # Result
    if 'result' in data:
        lines.append("📊 Result:")
        lines.append("-" * 20)
        lines.append(str(data['result']))
    
    # Evaluation (if present)
    if 'evaluation' in data:
        eval_data = data['evaluation']
        lines.append("")
        lines.append("🔍 Evaluation:")
        lines.append("-" * 20)
        lines.append(f"Score: {eval_data.get('score', 'N/A')}")
        lines.append(f"Passed: {'✅ Yes' if eval_data.get('passed') else '❌ No'}")
        if 'feedback' in eval_data:
            lines.append(f"Feedback: {eval_data['feedback']}")
    
    return "\n".join(lines)


def format_markdown_output(data: Dict[str, Any]) -> str:
    """Format data as Markdown."""
    
    lines = []
    
    # Header
    lines.append("# Pydantic AI Agent Result")
    lines.append("")
    
    # Metadata table
    lines.append("## Metadata")
    lines.append("")
    lines.append("| Field | Value |")
    lines.append("|-------|-------|")
    
    if 'agent' in data:
        lines.append(f"| Agent | {data['agent']} |")
    
    if 'execution_time' in data:
        lines.append(f"| Execution Time | {data['execution_time']:.2f}s |")
    
    if 'timestamp' in data:
        lines.append(f"| Timestamp | {data['timestamp']} |")
    
    lines.append("")
    
    # Prompt
    if 'prompt' in data:
        lines.append("## Prompt")
        lines.append("")
        lines.append(f"```")
        lines.append(str(data['prompt']))
        lines.append("```")
        lines.append("")
    
    # Result
    if 'result' in data:
        lines.append("## Result")
        lines.append("")
        lines.append(str(data['result']))
        lines.append("")
    
    # Evaluation
    if 'evaluation' in data:
        eval_data = data['evaluation']
        lines.append("## Evaluation")
        lines.append("")
        lines.append(f"**Score:** {eval_data.get('score', 'N/A')}")
        lines.append("")
        lines.append(f"**Passed:** {'✅ Yes' if eval_data.get('passed') else '❌ No'}")
        lines.append("")
        
        if 'feedback' in eval_data:
            lines.append(f"**Feedback:** {eval_data['feedback']}")
            lines.append("")
    
    return "\n".join(lines)


def parse_agent_args(args_string: str) -> Dict[str, Any]:
    """Parse agent arguments from a string."""
    
    args = {}
    
    if not args_string:
        return args
    
    # Simple key=value parsing
    pairs = args_string.split(',')
    
    for pair in pairs:
        if '=' in pair:
            key, value = pair.split('=', 1)
            key = key.strip()
            value = value.strip()
            
            # Try to parse value as appropriate type
            if value.lower() in ('true', 'false'):
                args[key] = value.lower() == 'true'
            elif value.isdigit():
                args[key] = int(value)
            elif value.replace('.', '').isdigit():
                args[key] = float(value)
            else:
                # Remove quotes if present
                if value.startswith('"') and value.endswith('"'):
                    value = value[1:-1]
                elif value.startswith("'") and value.endswith("'"):
                    value = value[1:-1]
                args[key] = value
    
    return args


def load_cli_config(config_file: str) -> Dict[str, Any]:
    """Load CLI configuration from file."""
    
    config_path = Path(config_file)
    
    # Default configuration
    default_config = {
        "default_agent": "coding",
        "default_workspace": ".",
        "default_output_format": "text",
        "verbose": False,
        "max_history": 50,
        "auto_save_results": False,
        "results_directory": "./results"
    }
    
    if not config_path.exists():
        # Create default config file
        save_cli_config(default_config, config_file)
        return default_config
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Merge with defaults (in case new options were added)
        for key, value in default_config.items():
            if key not in config:
                config[key] = value
        
        return config
    
    except Exception as e:
        logger.warning(f"Failed to load config from {config_file}: {e}")
        return default_config


def save_cli_config(config: Dict[str, Any], config_file: str):
    """Save CLI configuration to file."""
    
    config_path = Path(config_file)
    
    try:
        # Ensure directory exists
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"Configuration saved to {config_file}")
    
    except Exception as e:
        logger.error(f"Failed to save config to {config_file}: {e}")


def validate_workspace(workspace_path: str) -> Tuple[bool, str]:
    """Validate workspace directory."""
    
    workspace = Path(workspace_path)
    
    if not workspace.exists():
        return False, f"Workspace directory does not exist: {workspace_path}"
    
    if not workspace.is_dir():
        return False, f"Workspace path is not a directory: {workspace_path}"
    
    # Check if it's a reasonable workspace (has some common files)
    common_files = [
        'README.md', 'readme.md', 'README.txt',
        'package.json', 'requirements.txt', 'pyproject.toml',
        'Cargo.toml', 'go.mod', 'pom.xml',
        '.git', '.gitignore'
    ]
    
    has_project_files = any((workspace / file).exists() for file in common_files)
    
    if not has_project_files:
        return True, f"Warning: Workspace may not be a project directory: {workspace_path}"
    
    return True, f"Valid workspace: {workspace_path}"


def create_results_directory(base_path: str = "./results") -> Path:
    """Create and return results directory."""
    
    results_dir = Path(base_path)
    results_dir.mkdir(parents=True, exist_ok=True)
    
    return results_dir


def format_duration(seconds: float) -> str:
    """Format duration in human-readable format."""
    
    if seconds < 1:
        return f"{seconds*1000:.0f}ms"
    elif seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}m {secs:.1f}s"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours}h {minutes}m {secs:.1f}s"


def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """Truncate text to specified length."""
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def colorize_status(status: str) -> str:
    """Add color to status text."""
    
    status_lower = status.lower()
    
    if 'pass' in status_lower or 'success' in status_lower or 'ok' in status_lower:
        return click.style(status, fg='green')
    elif 'fail' in status_lower or 'error' in status_lower:
        return click.style(status, fg='red')
    elif 'warn' in status_lower:
        return click.style(status, fg='yellow')
    else:
        return status


def format_table(headers: List[str], rows: List[List[str]], max_width: int = 80) -> str:
    """Format data as a simple table."""
    
    if not rows:
        return "No data to display."
    
    # Calculate column widths
    col_widths = [len(header) for header in headers]
    
    for row in rows:
        for i, cell in enumerate(row):
            if i < len(col_widths):
                col_widths[i] = max(col_widths[i], len(str(cell)))
    
    # Adjust for max width
    total_width = sum(col_widths) + len(headers) * 3 - 1
    if total_width > max_width:
        # Proportionally reduce column widths
        reduction_factor = max_width / total_width
        col_widths = [max(10, int(w * reduction_factor)) for w in col_widths]
    
    # Format table
    lines = []
    
    # Header
    header_line = " | ".join(header.ljust(col_widths[i]) for i, header in enumerate(headers))
    lines.append(header_line)
    lines.append("-" * len(header_line))
    
    # Rows
    for row in rows:
        row_line = " | ".join(
            truncate_text(str(cell), col_widths[i]).ljust(col_widths[i]) 
            for i, cell in enumerate(row)
        )
        lines.append(row_line)
    
    return "\n".join(lines)


# Export utility functions
__all__ = [
    'format_output',
    'format_text_output',
    'format_markdown_output',
    'parse_agent_args',
    'load_cli_config',
    'save_cli_config',
    'validate_workspace',
    'create_results_directory',
    'format_duration',
    'truncate_text',
    'colorize_status',
    'format_table'
]
