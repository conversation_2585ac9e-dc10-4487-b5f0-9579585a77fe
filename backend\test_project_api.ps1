# Project Management API Test Script
# Tests the complete project isolation system via REST API

Write-Host "🚀 Project Management API Test Suite" -ForegroundColor Green
Write-Host "=" * 50

$baseUrl = "http://localhost:8000"
$testResults = @()

# Test configuration
$testProjects = @(
    @{
        name = "Test Python Project"
        slug = "test-python-project"
        description = "A test Python project for validation"
        language = "python"
        framework = "fastapi"
    },
    @{
        name = "Test JavaScript Project"
        slug = "test-js-project"
        description = "A test JavaScript project for validation"
        language = "javascript"
        framework = "react"
    }
)

function Test-ApiEndpoint {
    param(
        [string]$Method,
        [string]$Url,
        [object]$Body = $null,
        [string]$TestName
    )
    
    try {
        Write-Host "🧪 Testing: $TestName" -ForegroundColor Yellow
        
        $headers = @{
            "Content-Type" = "application/json"
        }
        
        if ($Body) {
            $jsonBody = $Body | ConvertTo-Json -Depth 10
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Body $jsonBody -Headers $headers -TimeoutSec 30
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers -TimeoutSec 30
        }
        
        Write-Host "  ✅ $TestName - SUCCESS" -ForegroundColor Green
        return @{
            TestName = $TestName
            Status = "SUCCESS"
            Response = $response
            Error = $null
        }
    }
    catch {
        Write-Host "  ❌ $TestName - FAILED: $($_.Exception.Message)" -ForegroundColor Red
        return @{
            TestName = $TestName
            Status = "FAILED"
            Response = $null
            Error = $_.Exception.Message
        }
    }
}

# Test 1: Health Check
Write-Host "`n📋 Testing System Health..." -ForegroundColor Cyan
$healthResult = Test-ApiEndpoint -Method "GET" -Url "$baseUrl/health" -TestName "System Health Check"
$testResults += $healthResult

if ($healthResult.Status -eq "SUCCESS") {
    Write-Host "  System Status: $($healthResult.Response.status)" -ForegroundColor Green
}

# Test 2: Project Management Health
$projectHealthResult = Test-ApiEndpoint -Method "GET" -Url "$baseUrl/api/projects/health" -TestName "Project Management Health"
$testResults += $projectHealthResult

# Test 3: List Projects (Initially Empty)
$listResult = Test-ApiEndpoint -Method "GET" -Url "$baseUrl/api/projects/list" -TestName "List Projects (Initial)"
$testResults += $listResult

if ($listResult.Status -eq "SUCCESS") {
    Write-Host "  Initial Projects Count: $($listResult.Response.total)" -ForegroundColor Green
}

# Test 4: Create Test Projects
Write-Host "`n🏗️ Testing Project Creation..." -ForegroundColor Cyan
$createdProjects = @()

foreach ($project in $testProjects) {
    $createResult = Test-ApiEndpoint -Method "POST" -Url "$baseUrl/api/projects/create" -Body $project -TestName "Create Project: $($project.name)"
    $testResults += $createResult
    
    if ($createResult.Status -eq "SUCCESS") {
        $createdProjects += $project.slug
        Write-Host "  Created Project ID: $($createResult.Response.project.project_id)" -ForegroundColor Green
    }
}

# Test 5: List Projects (After Creation)
$listAfterResult = Test-ApiEndpoint -Method "GET" -Url "$baseUrl/api/projects/list" -TestName "List Projects (After Creation)"
$testResults += $listAfterResult

if ($listAfterResult.Status -eq "SUCCESS") {
    Write-Host "  Total Projects: $($listAfterResult.Response.total)" -ForegroundColor Green
    Write-Host "  Active Projects: $($listAfterResult.Response.active)" -ForegroundColor Green
}

# Test 6: Get Individual Project Details
Write-Host "`n🔍 Testing Project Details..." -ForegroundColor Cyan
foreach ($projectSlug in $createdProjects) {
    $detailResult = Test-ApiEndpoint -Method "GET" -Url "$baseUrl/api/projects/$projectSlug" -TestName "Get Project Details: $projectSlug"
    $testResults += $detailResult
    
    if ($detailResult.Status -eq "SUCCESS") {
        Write-Host "  Project Name: $($detailResult.Response.config.name)" -ForegroundColor Green
        Write-Host "  Project Language: $($detailResult.Response.config.language)" -ForegroundColor Green
    }
}

# Test 7: Get Workspace Information
Write-Host "`n📁 Testing Workspace Information..." -ForegroundColor Cyan
foreach ($projectSlug in $createdProjects) {
    $workspaceResult = Test-ApiEndpoint -Method "GET" -Url "$baseUrl/api/projects/$projectSlug/workspace/info" -TestName "Get Workspace Info: $projectSlug"
    $testResults += $workspaceResult
    
    if ($workspaceResult.Status -eq "SUCCESS") {
        Write-Host "  Workspace Path: $($workspaceResult.Response.workspace_path)" -ForegroundColor Green
        Write-Host "  Embeddings Collection: $($workspaceResult.Response.embeddings_collection)" -ForegroundColor Green
        Write-Host "  Workspace Exists: $($workspaceResult.Response.workspace_exists)" -ForegroundColor Green
    }
}

# Test 8: Project Activation
Write-Host "`n🎯 Testing Project Activation..." -ForegroundColor Cyan
if ($createdProjects.Count -gt 0) {
    $firstProject = $createdProjects[0]
    $activateResult = Test-ApiEndpoint -Method "POST" -Url "$baseUrl/api/projects/$firstProject/activate" -TestName "Activate Project: $firstProject"
    $testResults += $activateResult
    
    if ($activateResult.Status -eq "SUCCESS") {
        Write-Host "  Activated Project: $($activateResult.Response.project.config.name)" -ForegroundColor Green
    }
    
    # Test current project status
    $currentResult = Test-ApiEndpoint -Method "GET" -Url "$baseUrl/api/projects/current/status" -TestName "Get Current Project Status"
    $testResults += $currentResult
    
    if ($currentResult.Status -eq "SUCCESS") {
        Write-Host "  Current Project: $($currentResult.Response.project.slug)" -ForegroundColor Green
    }
}

# Test 9: Project Archiving
Write-Host "`n📦 Testing Project Archiving..." -ForegroundColor Cyan
foreach ($projectSlug in $createdProjects) {
    $archiveResult = Test-ApiEndpoint -Method "POST" -Url "$baseUrl/api/projects/$projectSlug/archive" -TestName "Archive Project: $projectSlug"
    $testResults += $archiveResult
    
    if ($archiveResult.Status -eq "SUCCESS") {
        Write-Host "  Archived Project: $($archiveResult.Response.project.config.name)" -ForegroundColor Green
    }
}

# Generate Test Summary
Write-Host "`n" + "=" * 50
Write-Host "📊 TEST RESULTS SUMMARY" -ForegroundColor Green
Write-Host "=" * 50

$totalTests = $testResults.Count
$successfulTests = ($testResults | Where-Object { $_.Status -eq "SUCCESS" }).Count
$failedTests = $totalTests - $successfulTests
$successRate = if ($totalTests -gt 0) { [math]::Round(($successfulTests / $totalTests) * 100, 1) } else { 0 }

Write-Host "Total Tests: $totalTests" -ForegroundColor White
Write-Host "Successful: $successfulTests" -ForegroundColor Green
Write-Host "Failed: $failedTests" -ForegroundColor Red
Write-Host "Success Rate: $successRate%" -ForegroundColor $(if ($successRate -eq 100) { "Green" } else { "Yellow" })

if ($successRate -eq 100) {
    Write-Host "`n🎉 All tests passed! Project isolation system is working correctly." -ForegroundColor Green
} else {
    Write-Host "`n⚠️ Some tests failed. Review the implementation." -ForegroundColor Yellow
    
    Write-Host "`nFailed Tests:" -ForegroundColor Red
    $testResults | Where-Object { $_.Status -eq "FAILED" } | ForEach-Object {
        Write-Host "  - $($_.TestName): $($_.Error)" -ForegroundColor Red
    }
}

# Save detailed results
$resultsFile = "project_api_test_results.json"
$testResults | ConvertTo-Json -Depth 10 | Out-File -FilePath $resultsFile -Encoding UTF8
Write-Host "`n📄 Detailed results saved to: $resultsFile" -ForegroundColor Cyan

Write-Host "`n🏁 Test suite completed!" -ForegroundColor Green
