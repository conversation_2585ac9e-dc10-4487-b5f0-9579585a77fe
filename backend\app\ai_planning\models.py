"""
AI Planning System Models - Phase 8D

Pydantic models for the AI-powered planning system.
"""

from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field
import uuid


class TaskPriority(str, Enum):
    """Task priority levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class TaskStatus(str, Enum):
    """Task execution status."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    BLOCKED = "blocked"
    CANCELLED = "cancelled"


class TaskComplexity(str, Enum):
    """Task complexity levels."""
    TRIVIAL = "trivial"      # < 1 hour
    SIMPLE = "simple"        # 1-4 hours
    MODERATE = "moderate"    # 4-8 hours
    COMPLEX = "complex"      # 1-3 days
    VERY_COMPLEX = "very_complex"  # > 3 days


class RiskLevel(str, Enum):
    """Risk assessment levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class PlanTask(BaseModel):
    """Individual task within a development plan."""
    task_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str = Field(..., description="Task title")
    description: str = Field(..., description="Detailed task description")
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM)
    complexity: TaskComplexity = Field(default=TaskComplexity.MODERATE)
    status: TaskStatus = Field(default=TaskStatus.PENDING)
    
    # Dependencies and relationships
    dependencies: List[str] = Field(default_factory=list, description="Task IDs this task depends on")
    blocks: List[str] = Field(default_factory=list, description="Task IDs this task blocks")
    
    # Estimation
    estimated_hours: float = Field(default=0.0, description="Estimated time in hours")
    actual_hours: Optional[float] = Field(default=None, description="Actual time spent")
    
    # Context and requirements
    required_skills: List[str] = Field(default_factory=list)
    required_tools: List[str] = Field(default_factory=list)
    affected_files: List[str] = Field(default_factory=list)
    
    # Execution details
    acceptance_criteria: List[str] = Field(default_factory=list)
    implementation_notes: str = Field(default="")
    test_requirements: List[str] = Field(default_factory=list)
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.now)
    started_at: Optional[datetime] = Field(default=None)
    completed_at: Optional[datetime] = Field(default=None)
    
    # Metadata
    tags: List[str] = Field(default_factory=list)
    assignee: Optional[str] = Field(default=None)
    
    def get_duration_estimate(self) -> timedelta:
        """Get estimated duration as timedelta."""
        return timedelta(hours=self.estimated_hours)
    
    def is_ready_to_start(self, completed_tasks: List[str]) -> bool:
        """Check if task is ready to start based on dependencies."""
        return all(dep_id in completed_tasks for dep_id in self.dependencies)


class PlanRisk(BaseModel):
    """Risk assessment for a plan or task."""
    risk_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str = Field(..., description="Risk title")
    description: str = Field(..., description="Detailed risk description")
    level: RiskLevel = Field(..., description="Risk severity level")
    probability: float = Field(..., ge=0.0, le=1.0, description="Probability (0-1)")
    impact: float = Field(..., ge=0.0, le=1.0, description="Impact score (0-1)")
    
    # Risk details
    category: str = Field(..., description="Risk category (technical, resource, timeline, etc.)")
    affected_tasks: List[str] = Field(default_factory=list)
    
    # Mitigation
    mitigation_strategies: List[str] = Field(default_factory=list)
    contingency_plans: List[str] = Field(default_factory=list)
    
    # Monitoring
    early_warning_signs: List[str] = Field(default_factory=list)
    monitoring_frequency: str = Field(default="daily")
    
    created_at: datetime = Field(default_factory=datetime.now)
    
    @property
    def risk_score(self) -> float:
        """Calculate overall risk score."""
        return self.probability * self.impact


class PlanResource(BaseModel):
    """Resource requirements and estimates."""
    resource_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str = Field(..., description="Resource name")
    type: str = Field(..., description="Resource type (human, tool, service, etc.)")
    
    # Availability and allocation
    required_hours: float = Field(default=0.0)
    available_hours: float = Field(default=0.0)
    allocation_percentage: float = Field(default=100.0, ge=0.0, le=100.0)
    
    # Skills and capabilities
    skills: List[str] = Field(default_factory=list)
    experience_level: str = Field(default="intermediate")
    
    # Cost estimation
    hourly_rate: Optional[float] = Field(default=None)
    total_cost: Optional[float] = Field(default=None)
    
    # Availability windows
    available_from: Optional[datetime] = Field(default=None)
    available_until: Optional[datetime] = Field(default=None)
    
    # Dependencies
    depends_on_resources: List[str] = Field(default_factory=list)
    
    created_at: datetime = Field(default_factory=datetime.now)


class PlanValidation(BaseModel):
    """Validation results from secondary AI analysis."""
    validation_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    plan_id: str = Field(..., description="ID of the plan being validated")
    validator_agent: str = Field(..., description="Name/ID of validating agent")
    
    # Overall assessment
    overall_score: float = Field(..., ge=0.0, le=10.0, description="Overall plan quality (0-10)")
    feasibility_score: float = Field(..., ge=0.0, le=10.0, description="Plan feasibility (0-10)")
    completeness_score: float = Field(..., ge=0.0, le=10.0, description="Plan completeness (0-10)")
    
    # Detailed feedback
    strengths: List[str] = Field(default_factory=list)
    weaknesses: List[str] = Field(default_factory=list)
    improvements: List[str] = Field(default_factory=list)
    missing_elements: List[str] = Field(default_factory=list)
    
    # Task-specific feedback
    task_feedback: Dict[str, Dict[str, Any]] = Field(default_factory=dict)
    
    # Risk assessment
    identified_risks: List[PlanRisk] = Field(default_factory=list)
    risk_mitigation_suggestions: List[str] = Field(default_factory=list)
    
    # Resource analysis
    resource_concerns: List[str] = Field(default_factory=list)
    resource_suggestions: List[str] = Field(default_factory=list)
    
    # Timeline analysis
    timeline_realistic: bool = Field(default=True)
    timeline_concerns: List[str] = Field(default_factory=list)
    suggested_timeline_adjustments: List[str] = Field(default_factory=list)
    
    # Dependencies
    dependency_issues: List[str] = Field(default_factory=list)
    dependency_suggestions: List[str] = Field(default_factory=list)
    
    created_at: datetime = Field(default_factory=datetime.now)


class DevelopmentPlan(BaseModel):
    """Complete development plan with tasks, risks, and resources."""
    plan_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str = Field(..., description="Plan title")
    description: str = Field(..., description="Plan description and objectives")
    version: str = Field(default="1.0.0")
    
    # Plan metadata
    created_by: str = Field(..., description="Creator (agent or user)")
    project_context: str = Field(..., description="Project context and background")
    objectives: List[str] = Field(..., description="Plan objectives and goals")
    success_criteria: List[str] = Field(..., description="Success criteria")
    
    # Tasks and structure
    tasks: List[PlanTask] = Field(default_factory=list)
    milestones: List[Dict[str, Any]] = Field(default_factory=list)
    
    # Risk and resource management
    risks: List[PlanRisk] = Field(default_factory=list)
    resources: List[PlanResource] = Field(default_factory=list)
    
    # Timeline
    estimated_start_date: Optional[datetime] = Field(default=None)
    estimated_end_date: Optional[datetime] = Field(default=None)
    estimated_total_hours: float = Field(default=0.0)
    
    # Validation
    validations: List[PlanValidation] = Field(default_factory=list)
    validation_status: str = Field(default="pending")  # pending, validated, needs_revision
    
    # Execution tracking
    execution_status: str = Field(default="draft")  # draft, approved, in_progress, completed, cancelled
    progress_percentage: float = Field(default=0.0, ge=0.0, le=100.0)
    
    # Metadata
    tags: List[str] = Field(default_factory=list)
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM)
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    approved_at: Optional[datetime] = Field(default=None)
    started_at: Optional[datetime] = Field(default=None)
    completed_at: Optional[datetime] = Field(default=None)
    
    def get_total_estimated_hours(self) -> float:
        """Calculate total estimated hours from all tasks."""
        return sum(task.estimated_hours for task in self.tasks)
    
    def get_completed_tasks(self) -> List[PlanTask]:
        """Get list of completed tasks."""
        return [task for task in self.tasks if task.status == TaskStatus.COMPLETED]
    
    def get_ready_tasks(self) -> List[PlanTask]:
        """Get tasks that are ready to start."""
        completed_task_ids = [task.task_id for task in self.get_completed_tasks()]
        return [
            task for task in self.tasks 
            if task.status == TaskStatus.PENDING and task.is_ready_to_start(completed_task_ids)
        ]
    
    def update_progress(self) -> None:
        """Update overall progress percentage."""
        if not self.tasks:
            self.progress_percentage = 0.0
            return
        
        completed_tasks = len(self.get_completed_tasks())
        self.progress_percentage = (completed_tasks / len(self.tasks)) * 100.0
        self.updated_at = datetime.now()


class PlanningRequest(BaseModel):
    """Request for AI planning system."""
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str = Field(..., description="Planning request title")
    description: str = Field(..., description="Detailed requirements")
    
    # Context
    project_context: Optional[str] = Field(default=None)
    existing_codebase_analysis: bool = Field(default=True)
    include_risk_assessment: bool = Field(default=True)
    include_resource_estimation: bool = Field(default=True)
    
    # Constraints
    max_timeline_days: Optional[int] = Field(default=None)
    max_budget: Optional[float] = Field(default=None)
    required_skills: List[str] = Field(default_factory=list)
    available_resources: List[str] = Field(default_factory=list)
    
    # Preferences
    preferred_complexity: Optional[TaskComplexity] = Field(default=None)
    preferred_approach: Optional[str] = Field(default=None)
    validation_required: bool = Field(default=True)
    
    created_at: datetime = Field(default_factory=datetime.now)


class PlanningResponse(BaseModel):
    """Response from AI planning system."""
    response_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    request_id: str = Field(..., description="Original request ID")
    
    # Generated plan
    plan: DevelopmentPlan = Field(..., description="Generated development plan")
    
    # Planning metadata
    planning_agent: str = Field(..., description="Primary planning agent")
    validation_agent: Optional[str] = Field(default=None)
    planning_duration_seconds: float = Field(default=0.0)
    
    # Quality metrics
    confidence_score: float = Field(..., ge=0.0, le=1.0)
    completeness_score: float = Field(..., ge=0.0, le=1.0)
    feasibility_score: float = Field(..., ge=0.0, le=1.0)
    
    # Additional insights
    alternative_approaches: List[str] = Field(default_factory=list)
    assumptions_made: List[str] = Field(default_factory=list)
    recommendations: List[str] = Field(default_factory=list)
    
    created_at: datetime = Field(default_factory=datetime.now)


class PlanningSession(BaseModel):
    """Active planning session with state management."""
    session_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: Optional[str] = Field(default=None)

    # Session state
    status: str = Field(default="active")  # active, paused, completed, cancelled
    current_request: Optional[PlanningRequest] = Field(default=None)
    current_plan: Optional[DevelopmentPlan] = Field(default=None)

    # Planning history
    requests: List[PlanningRequest] = Field(default_factory=list)
    responses: List[PlanningResponse] = Field(default_factory=list)
    plans: List[DevelopmentPlan] = Field(default_factory=list)

    # Session settings
    auto_validation: bool = Field(default=True)
    validation_agent_preference: Optional[str] = Field(default=None)
    planning_agent_preference: Optional[str] = Field(default=None)

    # Timestamps
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    last_activity: datetime = Field(default_factory=datetime.now)

    def add_request(self, request: PlanningRequest) -> None:
        """Add a new planning request to the session."""
        self.requests.append(request)
        self.current_request = request
        self.updated_at = datetime.now()
        self.last_activity = datetime.now()

    def add_response(self, response: PlanningResponse) -> None:
        """Add a planning response to the session."""
        self.responses.append(response)
        self.current_plan = response.plan
        self.plans.append(response.plan)
        self.updated_at = datetime.now()
        self.last_activity = datetime.now()

    def get_latest_plan(self) -> Optional[DevelopmentPlan]:
        """Get the most recent plan."""
        return self.current_plan or (self.plans[-1] if self.plans else None)
