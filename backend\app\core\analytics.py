"""
Advanced Analytics and Intelligence System

Provides comprehensive analytics and intelligence features:
- Usage pattern analysis
- Performance trend analysis
- Predictive analytics
- Anomaly detection
- User behavior analysis
- System optimization recommendations
"""

import asyncio
import logging
import time
import statistics
from typing import Dict, Any, Optional, List, Tuple, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
import json
import numpy as np
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import IsolationForest

logger = logging.getLogger(__name__)


@dataclass
class AnalyticsEvent:
    """Analytics event data structure."""
    event_id: str
    event_type: str
    timestamp: datetime
    user_id: Optional[str]
    session_id: Optional[str]
    data: Dict[str, Any]
    duration: Optional[float] = None
    success: bool = True
    error_message: Optional[str] = None


@dataclass
class UsagePattern:
    """Usage pattern analysis result."""
    pattern_id: str
    pattern_type: str
    frequency: int
    confidence: float
    description: str
    recommendations: List[str] = field(default_factory=list)


@dataclass
class PerformanceTrend:
    """Performance trend analysis result."""
    metric_name: str
    trend_direction: str  # 'improving', 'degrading', 'stable'
    trend_strength: float  # 0.0 to 1.0
    current_value: float
    predicted_value: float
    confidence_interval: Tuple[float, float]


class AdvancedAnalytics:
    """Advanced analytics and intelligence engine."""
    
    def __init__(self, max_events: int = 100000):
        self.max_events = max_events
        self.events: deque = deque(maxlen=max_events)
        self.patterns: Dict[str, UsagePattern] = {}
        self.trends: Dict[str, PerformanceTrend] = {}
        
        # Analytics models
        self.anomaly_detector = IsolationForest(contamination=0.1, random_state=42)
        self.scaler = StandardScaler()
        
        # Cached analytics
        self._cache = {}
        self._cache_expiry = {}
        
        # Background analysis task
        self._analysis_task = asyncio.create_task(self._periodic_analysis())
    
    def record_event(self, event: AnalyticsEvent) -> None:
        """Record an analytics event."""
        self.events.append(event)
        
        # Invalidate relevant caches
        self._invalidate_cache(['usage_patterns', 'performance_trends'])
    
    def record_simple_event(
        self,
        event_type: str,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        data: Optional[Dict[str, Any]] = None,
        duration: Optional[float] = None,
        success: bool = True,
        error_message: Optional[str] = None
    ) -> None:
        """Record a simple analytics event."""
        event = AnalyticsEvent(
            event_id=f"{event_type}_{int(time.time() * 1000)}",
            event_type=event_type,
            timestamp=datetime.now(),
            user_id=user_id,
            session_id=session_id,
            data=data or {},
            duration=duration,
            success=success,
            error_message=error_message
        )
        self.record_event(event)
    
    async def analyze_usage_patterns(self, time_window_hours: int = 24) -> List[UsagePattern]:
        """Analyze usage patterns in the specified time window."""
        cache_key = f"usage_patterns_{time_window_hours}"
        
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]
        
        cutoff_time = datetime.now() - timedelta(hours=time_window_hours)
        recent_events = [e for e in self.events if e.timestamp >= cutoff_time]
        
        patterns = []
        
        # Analyze event type patterns
        event_type_counts = defaultdict(int)
        for event in recent_events:
            event_type_counts[event.event_type] += 1
        
        # Find frequent patterns
        total_events = len(recent_events)
        for event_type, count in event_type_counts.items():
            frequency = count / total_events if total_events > 0 else 0
            
            if frequency > 0.05:  # More than 5% of events
                pattern = UsagePattern(
                    pattern_id=f"frequent_{event_type}",
                    pattern_type="frequent_operation",
                    frequency=count,
                    confidence=frequency,
                    description=f"Frequent use of {event_type} ({count} times, {frequency:.1%})",
                    recommendations=self._generate_recommendations(event_type, frequency)
                )
                patterns.append(pattern)
        
        # Analyze temporal patterns
        hourly_distribution = defaultdict(int)
        for event in recent_events:
            hour = event.timestamp.hour
            hourly_distribution[hour] += 1
        
        # Find peak usage hours
        if hourly_distribution:
            peak_hour = max(hourly_distribution.items(), key=lambda x: x[1])
            if peak_hour[1] > total_events * 0.15:  # More than 15% in one hour
                pattern = UsagePattern(
                    pattern_id="peak_usage_hour",
                    pattern_type="temporal_pattern",
                    frequency=peak_hour[1],
                    confidence=peak_hour[1] / total_events,
                    description=f"Peak usage at hour {peak_hour[0]} ({peak_hour[1]} events)",
                    recommendations=["Consider resource scaling during peak hours"]
                )
                patterns.append(pattern)
        
        # Analyze user behavior patterns
        user_patterns = self._analyze_user_patterns(recent_events)
        patterns.extend(user_patterns)
        
        # Cache results
        self._cache[cache_key] = patterns
        self._cache_expiry[cache_key] = datetime.now() + timedelta(minutes=30)
        
        return patterns
    
    async def analyze_performance_trends(self, metric_names: List[str]) -> List[PerformanceTrend]:
        """Analyze performance trends for specified metrics."""
        cache_key = f"performance_trends_{hash(tuple(metric_names))}"
        
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]
        
        trends = []
        
        for metric_name in metric_names:
            trend = await self._analyze_metric_trend(metric_name)
            if trend:
                trends.append(trend)
        
        # Cache results
        self._cache[cache_key] = trends
        self._cache_expiry[cache_key] = datetime.now() + timedelta(minutes=15)
        
        return trends
    
    async def detect_anomalies(self, time_window_hours: int = 24) -> List[Dict[str, Any]]:
        """Detect anomalies in system behavior."""
        cutoff_time = datetime.now() - timedelta(hours=time_window_hours)
        recent_events = [e for e in self.events if e.timestamp >= cutoff_time]
        
        if len(recent_events) < 10:
            return []
        
        # Prepare features for anomaly detection
        features = []
        event_refs = []
        
        for event in recent_events:
            feature_vector = [
                event.timestamp.hour,
                event.timestamp.minute,
                len(event.data),
                event.duration or 0,
                1 if event.success else 0
            ]
            features.append(feature_vector)
            event_refs.append(event)
        
        # Normalize features
        features_scaled = self.scaler.fit_transform(features)
        
        # Detect anomalies
        anomaly_labels = self.anomaly_detector.fit_predict(features_scaled)
        
        anomalies = []
        for i, label in enumerate(anomaly_labels):
            if label == -1:  # Anomaly detected
                event = event_refs[i]
                anomalies.append({
                    'event_id': event.event_id,
                    'event_type': event.event_type,
                    'timestamp': event.timestamp.isoformat(),
                    'anomaly_score': self.anomaly_detector.score_samples([features_scaled[i]])[0],
                    'description': f"Anomalous {event.event_type} event detected",
                    'details': event.data
                })
        
        return anomalies
    
    async def generate_insights(self) -> Dict[str, Any]:
        """Generate comprehensive system insights."""
        insights = {
            'timestamp': datetime.now().isoformat(),
            'summary': {},
            'patterns': [],
            'trends': [],
            'anomalies': [],
            'recommendations': []
        }
        
        # Basic statistics
        total_events = len(self.events)
        if total_events > 0:
            recent_24h = [e for e in self.events if e.timestamp >= datetime.now() - timedelta(hours=24)]
            success_rate = sum(1 for e in recent_24h if e.success) / len(recent_24h) if recent_24h else 0
            
            insights['summary'] = {
                'total_events': total_events,
                'events_24h': len(recent_24h),
                'success_rate_24h': success_rate,
                'unique_users_24h': len(set(e.user_id for e in recent_24h if e.user_id)),
                'avg_event_duration': statistics.mean([e.duration for e in recent_24h if e.duration])
                if any(e.duration for e in recent_24h) else 0
            }
        
        # Usage patterns
        patterns = await self.analyze_usage_patterns()
        insights['patterns'] = [
            {
                'pattern_id': p.pattern_id,
                'type': p.pattern_type,
                'description': p.description,
                'confidence': p.confidence,
                'recommendations': p.recommendations
            }
            for p in patterns
        ]
        
        # Performance trends
        metric_names = ['response_time', 'error_rate', 'throughput', 'resource_usage']
        trends = await self.analyze_performance_trends(metric_names)
        insights['trends'] = [
            {
                'metric': t.metric_name,
                'direction': t.trend_direction,
                'strength': t.trend_strength,
                'current_value': t.current_value,
                'predicted_value': t.predicted_value
            }
            for t in trends
        ]
        
        # Anomalies
        anomalies = await self.detect_anomalies()
        insights['anomalies'] = anomalies
        
        # Generate recommendations
        insights['recommendations'] = self._generate_system_recommendations(insights)
        
        return insights
    
    def _analyze_user_patterns(self, events: List[AnalyticsEvent]) -> List[UsagePattern]:
        """Analyze user behavior patterns."""
        patterns = []
        
        # Group events by user
        user_events = defaultdict(list)
        for event in events:
            if event.user_id:
                user_events[event.user_id].append(event)
        
        # Analyze session patterns
        for user_id, user_event_list in user_events.items():
            if len(user_event_list) > 10:  # Sufficient data
                # Analyze session duration
                sessions = defaultdict(list)
                for event in user_event_list:
                    if event.session_id:
                        sessions[event.session_id].append(event)
                
                if sessions:
                    session_durations = []
                    for session_events in sessions.values():
                        if len(session_events) > 1:
                            start_time = min(e.timestamp for e in session_events)
                            end_time = max(e.timestamp for e in session_events)
                            duration = (end_time - start_time).total_seconds()
                            session_durations.append(duration)
                    
                    if session_durations:
                        avg_duration = statistics.mean(session_durations)
                        if avg_duration > 3600:  # Long sessions (> 1 hour)
                            pattern = UsagePattern(
                                pattern_id=f"long_sessions_{user_id}",
                                pattern_type="user_behavior",
                                frequency=len(session_durations),
                                confidence=0.8,
                                description=f"User {user_id} has long sessions (avg: {avg_duration/60:.1f} min)",
                                recommendations=["Consider session optimization features"]
                            )
                            patterns.append(pattern)
        
        return patterns
    
    async def _analyze_metric_trend(self, metric_name: str) -> Optional[PerformanceTrend]:
        """Analyze trend for a specific metric."""
        # Extract metric values from events
        metric_events = [
            e for e in self.events
            if metric_name in e.data and isinstance(e.data[metric_name], (int, float))
        ]
        
        if len(metric_events) < 5:
            return None
        
        # Sort by timestamp
        metric_events.sort(key=lambda x: x.timestamp)
        
        # Extract values and timestamps
        values = [e.data[metric_name] for e in metric_events]
        timestamps = [(e.timestamp - metric_events[0].timestamp).total_seconds() for e in metric_events]
        
        # Calculate trend
        if len(values) > 1:
            # Simple linear regression
            n = len(values)
            sum_x = sum(timestamps)
            sum_y = sum(values)
            sum_xy = sum(x * y for x, y in zip(timestamps, values))
            sum_x2 = sum(x * x for x in timestamps)
            
            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
            
            # Determine trend direction
            if abs(slope) < 0.01:
                direction = "stable"
                strength = 0.0
            elif slope > 0:
                direction = "improving" if metric_name in ['throughput', 'success_rate'] else "degrading"
                strength = min(abs(slope) * 100, 1.0)
            else:
                direction = "degrading" if metric_name in ['throughput', 'success_rate'] else "improving"
                strength = min(abs(slope) * 100, 1.0)
            
            # Predict next value
            next_timestamp = timestamps[-1] + 3600  # 1 hour ahead
            predicted_value = values[-1] + slope * 3600
            
            # Calculate confidence interval (simplified)
            std_dev = statistics.stdev(values) if len(values) > 1 else 0
            confidence_interval = (predicted_value - std_dev, predicted_value + std_dev)
            
            return PerformanceTrend(
                metric_name=metric_name,
                trend_direction=direction,
                trend_strength=strength,
                current_value=values[-1],
                predicted_value=predicted_value,
                confidence_interval=confidence_interval
            )
        
        return None
    
    def _generate_recommendations(self, event_type: str, frequency: float) -> List[str]:
        """Generate recommendations based on usage patterns."""
        recommendations = []
        
        if frequency > 0.3:  # Very frequent
            recommendations.append(f"Consider optimizing {event_type} operations for better performance")
            recommendations.append(f"Add caching for {event_type} if not already implemented")
        
        if event_type.endswith('_error'):
            recommendations.append(f"Investigate and fix recurring {event_type} issues")
            recommendations.append("Implement better error handling and recovery mechanisms")
        
        return recommendations
    
    def _generate_system_recommendations(self, insights: Dict[str, Any]) -> List[str]:
        """Generate system-wide recommendations."""
        recommendations = []
        
        summary = insights.get('summary', {})
        
        # Success rate recommendations
        success_rate = summary.get('success_rate_24h', 1.0)
        if success_rate < 0.95:
            recommendations.append("Success rate is below 95% - investigate error patterns")
        
        # Performance recommendations
        avg_duration = summary.get('avg_event_duration', 0)
        if avg_duration > 5.0:
            recommendations.append("Average event duration is high - consider performance optimization")
        
        # Anomaly recommendations
        anomalies = insights.get('anomalies', [])
        if len(anomalies) > 10:
            recommendations.append("High number of anomalies detected - review system stability")
        
        # Pattern-based recommendations
        patterns = insights.get('patterns', [])
        for pattern in patterns:
            recommendations.extend(pattern.get('recommendations', []))
        
        return list(set(recommendations))  # Remove duplicates
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cache entry is valid."""
        if cache_key not in self._cache:
            return False
        
        expiry = self._cache_expiry.get(cache_key)
        if expiry and datetime.now() > expiry:
            del self._cache[cache_key]
            del self._cache_expiry[cache_key]
            return False
        
        return True
    
    def _invalidate_cache(self, cache_patterns: List[str]) -> None:
        """Invalidate cache entries matching patterns."""
        keys_to_remove = []
        for cache_key in self._cache.keys():
            for pattern in cache_patterns:
                if pattern in cache_key:
                    keys_to_remove.append(cache_key)
                    break
        
        for key in keys_to_remove:
            self._cache.pop(key, None)
            self._cache_expiry.pop(key, None)
    
    async def _periodic_analysis(self) -> None:
        """Periodic background analysis."""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour
                
                # Generate insights
                insights = await self.generate_insights()
                
                # Log important findings
                if insights['anomalies']:
                    logger.warning(f"Detected {len(insights['anomalies'])} anomalies in the last hour")
                
                if insights['recommendations']:
                    logger.info(f"Generated {len(insights['recommendations'])} system recommendations")
                
            except Exception as e:
                logger.error(f"Periodic analysis failed: {e}")
    
    def get_analytics_summary(self) -> Dict[str, Any]:
        """Get analytics system summary."""
        return {
            'total_events': len(self.events),
            'cache_entries': len(self._cache),
            'patterns_identified': len(self.patterns),
            'trends_tracked': len(self.trends),
            'analysis_running': not self._analysis_task.done()
        }


# Global analytics instance
analytics_engine = AdvancedAnalytics()


# Analytics decorators
def track_performance(event_type: str, include_duration: bool = True):
    """Decorator to track function performance."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            error_message = None
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                error_message = str(e)
                raise
            finally:
                duration = time.time() - start_time if include_duration else None
                
                analytics_engine.record_simple_event(
                    event_type=event_type,
                    duration=duration,
                    success=success,
                    error_message=error_message,
                    data={'function': func.__name__}
                )
        
        return wrapper
    return decorator
