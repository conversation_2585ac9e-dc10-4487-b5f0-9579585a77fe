"""
Change Tracking Tools for Pydantic AI Migration

This module provides standalone change tracking tools that can be registered
with Pydantic AI agents, avoiding circular import issues.
"""

import logging
from typing import Optional, List, Dict, Any
from datetime import datetime

import logfire
from pydantic_ai import RunContext

from ..dependencies import CodingDependencies
from ..models import ChangeTrackingResult

logger = logging.getLogger(__name__)


async def track_changes(
    ctx: RunContext[CodingDependencies],
    project_path: str,
    watch_duration: int = 60,
    include_git: bool = True
) -> ChangeTrackingResult:
    """
    Track file changes in the project directory.
    
    Args:
        ctx: Run context with dependencies
        project_path: Path to the project directory to monitor
        watch_duration: Duration to watch for changes (seconds)
        include_git: Whether to include git change detection
        
    Returns:
        ChangeTrackingResult with detected changes
    """
    with logfire.span("track_changes", project_path=project_path, watch_duration=watch_duration):
        try:
            result = await ctx.deps.change_tracker.start_monitoring(
                project_path, watch_duration, include_git
            )
            
            if result['status'] == 'success':
                changes = result.get('changes', {})
                total_changes = sum(len(files) for files in changes.values())
                
                logfire.info("Change tracking completed", 
                           total_changes=total_changes,
                           duration=watch_duration)
                
                return ChangeTrackingResult(
                    changes_detected=total_changes,
                    files_modified=changes.get('modified', []),
                    files_created=changes.get('created', []),
                    files_deleted=changes.get('deleted', []),
                    timestamp=datetime.now(),
                    summary=f"Detected {total_changes} changes in {watch_duration}s"
                )
            else:
                logfire.error("Change tracking failed", error=result.get('error'))
                return ChangeTrackingResult(
                    changes_detected=0,
                    files_modified=[],
                    files_created=[],
                    files_deleted=[],
                    timestamp=datetime.now(),
                    summary=f"Change tracking failed: {result.get('error')}"
                )
                
        except Exception as e:
            logger.error(f"Failed to track changes for {project_path}: {e}")
            logfire.error("Change tracking exception", error=str(e))
            return ChangeTrackingResult(
                changes_detected=0,
                files_modified=[],
                files_created=[],
                files_deleted=[],
                timestamp=datetime.now(),
                summary=f"Change tracking exception: {str(e)}"
            )


async def get_change_summary(
    ctx: RunContext[CodingDependencies],
    project_path: str,
    since_timestamp: Optional[str] = None,
    include_content: bool = False
) -> Dict[str, Any]:
    """
    Get a summary of changes since a specific timestamp.
    
    Args:
        ctx: Run context with dependencies
        project_path: Path to the project directory
        since_timestamp: ISO timestamp to get changes since (optional)
        include_content: Whether to include file content changes
        
    Returns:
        Dictionary with change summary
    """
    with logfire.span("get_change_summary", project_path=project_path, since_timestamp=since_timestamp):
        try:
            result = await ctx.deps.change_tracker.get_change_summary(
                project_path, since_timestamp, include_content
            )
            
            if result['status'] == 'success':
                summary = result.get('summary', {})
                logfire.info("Change summary retrieved", 
                           total_changes=summary.get('total_changes', 0))
                return result
            else:
                logfire.error("Change summary failed", error=result.get('error'))
                return {
                    'status': 'error',
                    'error': result.get('error'),
                    'project_path': project_path
                }
                
        except Exception as e:
            logger.error(f"Failed to get change summary for {project_path}: {e}")
            logfire.error("Change summary exception", error=str(e))
            return {
                'status': 'error',
                'error': str(e),
                'project_path': project_path
            }


async def monitor_file_changes(
    ctx: RunContext[CodingDependencies],
    file_path: str,
    callback_interval: int = 5
) -> Dict[str, Any]:
    """
    Monitor changes to a specific file.
    
    Args:
        ctx: Run context with dependencies
        file_path: Path to the specific file to monitor
        callback_interval: Interval for change callbacks (seconds)
        
    Returns:
        Dictionary with monitoring status
    """
    with logfire.span("monitor_file_changes", file_path=file_path):
        try:
            result = await ctx.deps.change_tracker.monitor_file(
                file_path, callback_interval
            )
            
            if result['status'] == 'success':
                logfire.info("File monitoring started", file_path=file_path)
                return result
            else:
                logfire.error("File monitoring failed", error=result.get('error'))
                return {
                    'status': 'error',
                    'error': result.get('error'),
                    'file_path': file_path
                }
                
        except Exception as e:
            logger.error(f"Failed to monitor file {file_path}: {e}")
            logfire.error("File monitoring exception", error=str(e))
            return {
                'status': 'error',
                'error': str(e),
                'file_path': file_path
            }


async def get_change_statistics(
    ctx: RunContext[CodingDependencies],
    project_path: str,
    time_period: str = "24h"
) -> Dict[str, Any]:
    """
    Get change statistics for the project over a time period.
    
    Args:
        ctx: Run context with dependencies
        project_path: Path to the project directory
        time_period: Time period for statistics (e.g., "1h", "24h", "7d")
        
    Returns:
        Dictionary with change statistics
    """
    with logfire.span("get_change_statistics", project_path=project_path, time_period=time_period):
        try:
            result = await ctx.deps.change_tracker.get_statistics(
                project_path, time_period
            )
            
            if result['status'] == 'success':
                stats = result.get('statistics', {})
                logfire.info("Change statistics retrieved", 
                           total_changes=stats.get('total_changes', 0),
                           time_period=time_period)
                return result
            else:
                logfire.error("Change statistics failed", error=result.get('error'))
                return {
                    'status': 'error',
                    'error': result.get('error'),
                    'project_path': project_path,
                    'time_period': time_period
                }
                
        except Exception as e:
            logger.error(f"Failed to get change statistics for {project_path}: {e}")
            logfire.error("Change statistics exception", error=str(e))
            return {
                'status': 'error',
                'error': str(e),
                'project_path': project_path,
                'time_period': time_period
            }


async def index_code_changes(
    ctx: RunContext[CodingDependencies],
    project_path: str,
    force_reindex: bool = False
) -> Dict[str, Any]:
    """
    Index code changes for semantic search and analysis.
    
    Args:
        ctx: Run context with dependencies
        project_path: Path to the project directory
        force_reindex: Whether to force a complete reindex
        
    Returns:
        Dictionary with indexing status
    """
    with logfire.span("index_code_changes", project_path=project_path, force_reindex=force_reindex):
        try:
            result = await ctx.deps.change_tracker.index_changes(
                project_path, force_reindex
            )
            
            if result['status'] == 'success':
                indexed_files = result.get('indexed_files', 0)
                logfire.info("Code indexing completed", 
                           indexed_files=indexed_files,
                           force_reindex=force_reindex)
                return result
            else:
                logfire.error("Code indexing failed", error=result.get('error'))
                return {
                    'status': 'error',
                    'error': result.get('error'),
                    'project_path': project_path
                }
                
        except Exception as e:
            logger.error(f"Failed to index code changes for {project_path}: {e}")
            logfire.error("Code indexing exception", error=str(e))
            return {
                'status': 'error',
                'error': str(e),
                'project_path': project_path
            }


async def stop_monitoring(
    ctx: RunContext[CodingDependencies],
    project_path: str
) -> Dict[str, Any]:
    """
    Stop monitoring changes for a project.
    
    Args:
        ctx: Run context with dependencies
        project_path: Path to the project directory
        
    Returns:
        Dictionary with stop status
    """
    with logfire.span("stop_monitoring", project_path=project_path):
        try:
            result = await ctx.deps.change_tracker.stop_monitoring(project_path)
            
            if result['status'] == 'success':
                logfire.info("Monitoring stopped", project_path=project_path)
                return result
            else:
                logfire.error("Failed to stop monitoring", error=result.get('error'))
                return {
                    'status': 'error',
                    'error': result.get('error'),
                    'project_path': project_path
                }
                
        except Exception as e:
            logger.error(f"Failed to stop monitoring for {project_path}: {e}")
            logfire.error("Stop monitoring exception", error=str(e))
            return {
                'status': 'error',
                'error': str(e),
                'project_path': project_path
            }
