interactions:
- request:
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '301'
      content-type:
      - application/json
      host:
      - generativelanguage.googleapis.com
    method: POST
    parsed_body:
      contents:
      - parts:
        - text: What is the main content on this document?
        - inlineData:
            data: VGhpcyBpcyBhIHRlc3QgZG9jdW1lbnQu
            mimeType: text/plain
        role: user
      generationConfig: {}
      systemInstruction:
        parts:
        - text: You are a helpful chatbot.
        role: user
    uri: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent
  response:
    headers:
      alt-svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      content-length:
      - '669'
      content-type:
      - application/json; charset=UTF-8
      server-timing:
      - gfet4t7; dur=535
      transfer-encoding:
      - chunked
      vary:
      - Origin
      - X-Origin
      - Referer
    parsed_body:
      candidates:
      - avgLogprobs: -0.014047189553578695
        content:
          parts:
          - text: |
              The main content of the document is that it is a test document.
          role: model
        finishReason: STOP
      modelVersion: gemini-2.0-flash
      usageMetadata:
        candidatesTokenCount: 15
        candidatesTokensDetails:
        - modality: TEXT
          tokenCount: 15
        promptTokenCount: 21
        promptTokensDetails:
        - modality: TEXT
          tokenCount: 21
        totalTokenCount: 36
    status:
      code: 200
      message: OK
version: 1
