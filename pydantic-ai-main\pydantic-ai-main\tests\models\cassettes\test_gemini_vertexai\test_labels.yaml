interactions:
  - request:
      body: grant_type=%5B%27refresh_token%27%5D&client_id=%5B%27************-6qr4p6gpi6hn506pt8ejuq83di341hur.apps.googleusercontent.com%27%5D&client_secret=%5B%27scrubbed%27%5D&refresh_token=%5B%27scrubbed%27%5D
      headers:
        accept:
          - "*/*"
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "268"
        content-type:
          - application/x-www-form-urlencoded
      method: POST
      uri: https://oauth2.googleapis.com/token
    response:
      headers:
        alt-svc:
          - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
        cache-control:
          - no-cache, no-store, max-age=0, must-revalidate
        content-length:
          - "1419"
        content-type:
          - application/json; charset=utf-8
        expires:
          - Mon, 01 Jan 1990 00:00:00 GMT
        pragma:
          - no-cache
        transfer-encoding:
          - chunked
        vary:
          - Origin
          - X-Origin
          - Referer
      parsed_body:
        access_token: scrubbed
        expires_in: 3599
        id_token: eyJhbGciOiJSUzI1NiIsImtpZCI6IjgyMWYzYmM2NmYwNzUxZjc4NDA2MDY3OTliMWFkZjllOWZiNjBkZmIiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.BAvb4TlcIoYcQODNLFqwtUQoSNJJbpAR0lk2OyFxXK9rSZ7m1e1_Dp1O4ApxPUS7f_NX34eSCuDJN2IXgh8VBv4k3IhI7CbMydYeqXuwlbgOOp1Z0farGEKneU1M7TvdngigAJ9wT-2LHjKd_GEcGau-CUvzXpcT1IOnNNyXGVqtuGmEfcw5jjPkKJNECUheeNHE3zeImatTstOLuKmI1ZK-etl41l3poSNuQkZkrbQ80Vst8BdT-b1tnJnXP1KGATBIamDy99OOiB9a7a9m_ikXYEyN91yR76DYot3hpDPlOX0H9hF-BOSqoOtlSS2TMBkMvFiiYWjID1e_9VlNUg
        scope: https://www.googleapis.com/auth/cloud-platform https://www.googleapis.com/auth/userinfo.email openid https://www.googleapis.com/auth/sqlservice.login
        token_type: Bearer
      status:
        code: 200
        message: OK
  - request:
      headers:
        accept:
          - "*/*"
        accept-encoding:
          - gzip, deflate
        connection:
          - keep-alive
        content-length:
          - "133"
        content-type:
          - application/json
        host:
          - us-central1-aiplatform.googleapis.com
      method: POST
      parsed_body:
        contents:
          - parts:
              - text: What is the capital of France?
            role: user
        labels:
          environment: test
          team: analytics
      uri: https://us-central1-aiplatform.googleapis.com/v1/projects/pydantic-ai/locations/us-central1/publishers/google/models/gemini-2.0-flash:generateContent
    response:
      headers:
        alt-svc:
          - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
        content-length:
          - "759"
        content-type:
          - application/json; charset=UTF-8
        transfer-encoding:
          - chunked
        vary:
          - Origin
          - X-Origin
          - Referer
      parsed_body:
        candidates:
          - avgLogprobs: -0.02703852951526642
            content:
              parts:
                - text: |
                    The capital of France is **Paris**.
              role: model
            finishReason: STOP
        createTime: "2025-05-23T07:53:55.494386Z"
        modelVersion: gemini-2.0-flash
        responseId: kykwaLKWHti5nvgPmN2T8AE
        usageMetadata:
          candidatesTokenCount: 9
          candidatesTokensDetails:
            - modality: TEXT
              tokenCount: 9
          promptTokenCount: 7
          promptTokensDetails:
            - modality: TEXT
              tokenCount: 7
          totalTokenCount: 16
          trafficType: ON_DEMAND
      status:
        code: 200
        message: OK
version: 1
