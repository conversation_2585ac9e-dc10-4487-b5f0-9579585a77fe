/* 🌿 DeepNexus Digital Forest Design System 🌿 */
/* Revolutionary Nature-Inspired AI Coding Interface */

/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* 🌱 Spring Growth - Primary Theme */
  --forest-primary: #2D5016;    /* Deep Forest Green */
  --growth-accent: #7CB342;     /* Fresh Leaf Green */
  --bloom-highlight: #FFC107;   /* Golden Sunlight */
  --earth-base: #3E2723;        /* Rich Soil Brown */
  --mist-overlay: #E8F5E8;      /* Morning Mist */

  /* 🌞 Summer Vitality - Active States */
  --canopy-deep: #1B5E20;       /* Dense Canopy */
  --meadow-bright: #8BC34A;     /* Vibrant Meadow */
  --sunset-warm: #FF8F00;       /* Warm Sunset */

  /* 🍂 Autumn Wisdom - Data/Analytics */
  --amber-data: #FF6F00;        /* Amber Insights */
  --crimson-alert: #D32F2F;     /* Alert Red */
  --golden-success: #F57F17;    /* Success Gold */

  /* ❄️ Winter Clarity - Minimal States */
  --frost-light: #F1F8E9;       /* Frost Light */
  --shadow-deep: #263238;       /* Deep Shadow */
  --crystal-clear: #FFFFFF;     /* Pure Crystal */

  /* 🎨 Interactive States */
  --hover-bloom: #8BC34A;       /* Hover effects */
  --active-pulse: #7CB342;      /* Active/selected states */
  --focus-ring: #FF8F00;        /* Focus indicators */

  /* 📏 Spacing Scale (Golden Ratio Based) */
  --space-xs: 0.25rem;    /* 4px */
  --space-sm: 0.5rem;     /* 8px */
  --space-md: 0.75rem;    /* 12px */
  --space-lg: 1rem;       /* 16px */
  --space-xl: 1.618rem;   /* 26px - Golden ratio */
  --space-2xl: 2.618rem;  /* 42px - Golden ratio */
  --space-3xl: 4.236rem;  /* 68px - Golden ratio */

  /* 🔤 Typography Scale */
  --text-xs: 0.75rem;     /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;      /* 16px */
  --text-lg: 1.125rem;    /* 18px */
  --text-xl: 1.25rem;     /* 20px */
  --text-2xl: 1.5rem;     /* 24px */
  --text-3xl: 1.875rem;   /* 30px */
  --text-4xl: 2.25rem;    /* 36px */

  /* 🌊 Border Radius (Organic Curves) */
  --radius-sm: 0.25rem;   /* 4px */
  --radius-md: 0.5rem;    /* 8px */
  --radius-lg: 0.75rem;   /* 12px */
  --radius-xl: 1rem;      /* 16px */
  --radius-organic: 1.5rem 0.5rem 1.5rem 0.5rem; /* Organic shape */

  /* 🎭 Shadows (Natural Depth) */
  --shadow-sm: 0 1px 2px 0 rgba(45, 80, 22, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(45, 80, 22, 0.1), 0 2px 4px -1px rgba(45, 80, 22, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(45, 80, 22, 0.1), 0 4px 6px -2px rgba(45, 80, 22, 0.05);
  --shadow-organic: 0 8px 25px -5px rgba(45, 80, 22, 0.15);

  /* ⏱️ Animation Timing (Natural Motion) */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --ease-organic: cubic-bezier(0.4, 0.0, 0.2, 1);
  --ease-bloom: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ===== TYPOGRAPHY SYSTEM ===== */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600&family=Poppins:wght@400;500;600;700&display=swap');

/* Base Typography */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--earth-base);
  background-color: var(--frost-light);
}

/* Heading Hierarchy */
h1, .text-h1 {
  font-family: 'Poppins', sans-serif;
  font-size: var(--text-4xl);
  font-weight: 700;
  letter-spacing: -0.025em;
  color: var(--forest-primary);
  line-height: 1.2;
}

h2, .text-h2 {
  font-family: 'Poppins', sans-serif;
  font-size: var(--text-3xl);
  font-weight: 600;
  letter-spacing: -0.025em;
  color: var(--forest-primary);
  line-height: 1.3;
}

h3, .text-h3 {
  font-family: 'Poppins', sans-serif;
  font-size: var(--text-2xl);
  font-weight: 500;
  color: var(--canopy-deep);
  line-height: 1.4;
}

/* Code Typography */
code, pre, .font-mono {
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', monospace;
  font-size: var(--text-sm);
  letter-spacing: 0.025em;
}

/* ===== NATURAL ANIMATIONS ===== */

/* 🌸 Bloom Animation - Elements expanding like flowers */
@keyframes bloom {
  0% { 
    transform: scale(0.8) rotate(-5deg);
    opacity: 0;
  }
  50% { 
    transform: scale(1.05) rotate(2deg);
    opacity: 0.8;
  }
  100% { 
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

/* 🫁 Breathing Animation - Subtle pulsing for active states */
@keyframes breathe {
  0%, 100% { 
    transform: scale(1);
    opacity: 1;
  }
  50% { 
    transform: scale(1.02);
    opacity: 0.9;
  }
}

/* 🍃 Leaf Rustle - Gentle hover movement */
@keyframes leafRustle {
  0%, 100% { 
    transform: translateX(0) rotate(0deg);
  }
  25% { 
    transform: translateX(1px) rotate(0.5deg);
  }
  75% { 
    transform: translateX(-1px) rotate(-0.5deg);
  }
}

/* 💧 Water Ripple - Click feedback */
@keyframes waterRipple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* 🌱 Growth Animation - Progress indicators */
@keyframes grow {
  0% {
    transform: scaleY(0);
    transform-origin: bottom;
  }
  100% {
    transform: scaleY(1);
    transform-origin: bottom;
  }
}

/* ===== UTILITY CLASSES ===== */

/* Animation Classes */
.animate-bloom {
  animation: bloom var(--duration-slow) var(--ease-bloom);
}

.animate-breathe {
  animation: breathe 2s var(--ease-organic) infinite;
}

.animate-rustle:hover {
  animation: leafRustle var(--duration-normal) var(--ease-organic);
}

.animate-grow {
  animation: grow var(--duration-slow) var(--ease-organic);
}

/* Interactive States */
.hover-bloom {
  transition: transform var(--duration-normal) var(--ease-bloom);
}

.hover-bloom:hover {
  transform: scale(1.02) rotate(1deg);
}

.focus-ring:focus {
  outline: 2px solid var(--focus-ring);
  outline-offset: 2px;
  border-radius: var(--radius-md);
}

/* Nature-Inspired Backgrounds */
.bg-forest-gradient {
  background: linear-gradient(135deg, var(--forest-primary) 0%, var(--canopy-deep) 100%);
}

.bg-growth-gradient {
  background: linear-gradient(135deg, var(--growth-accent) 0%, var(--meadow-bright) 100%);
}

.bg-mist-gradient {
  background: linear-gradient(135deg, var(--frost-light) 0%, var(--crystal-clear) 100%);
}

/* Organic Shapes */
.shape-organic {
  border-radius: var(--radius-organic);
}

.shape-leaf {
  border-radius: 50% 0 50% 0;
}

/* Glass Morphism Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Seasonal Theme Classes */
.theme-spring {
  --primary: var(--growth-accent);
  --accent: var(--bloom-highlight);
}

.theme-summer {
  --primary: var(--meadow-bright);
  --accent: var(--sunset-warm);
}

.theme-autumn {
  --primary: var(--amber-data);
  --accent: var(--golden-success);
}

.theme-winter {
  --primary: var(--shadow-deep);
  --accent: var(--frost-light);
}

/* Responsive Design (Golden Ratio Breakpoints) */
@media (min-width: 518px) { /* 320 * φ */
  :root {
    --text-base: 1.125rem;
  }
}

@media (min-width: 838px) { /* 518 * φ */
  :root {
    --text-base: 1.25rem;
    --space-lg: 1.25rem;
  }
}

@media (min-width: 1356px) { /* 838 * φ */
  :root {
    --text-base: 1.375rem;
    --space-lg: 1.5rem;
  }
}
