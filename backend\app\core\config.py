"""
Enhanced Configuration Management

Provides centralized, environment-aware configuration management with:
- Environment-specific settings
- Configuration validation
- Hot reloading capabilities
- Performance optimizations
- Security best practices
"""

import os
import json
import logging
from typing import Dict, Any, Optional, Union, List
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum
import yaml

logger = logging.getLogger(__name__)


class Environment(Enum):
    """Environment types."""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


@dataclass
class DatabaseConfig:
    """Database configuration."""
    qdrant_url: str = "http://qdrant:6333"
    qdrant_collection: str = "code_embeddings_bge_m3"
    embedding_dim: int = 1024
    connection_pool_size: int = 10
    timeout: float = 30.0


@dataclass
class LLMConfig:
    """LLM configuration."""
    openrouter_api_key: str = ""
    code_model: str = "deepseek/deepseek-r1-0528:free"
    vision_model: str = "google/gemini-2.0-flash-exp:free"
    summarization_model: str = "deepseek/deepseek-chat-v3-0324:free"
    fallback_code_model: str = "deepseek/deepseek-chat-v3-0324:free"
    fallback_vision_model: str = "google/gemini-2.0-flash-exp:free"
    max_tokens: int = 4000
    temperature: float = 0.7
    timeout: float = 120.0
    retry_attempts: int = 3


@dataclass
class EmbeddingConfig:
    """Embedding configuration."""
    ollama_host: str = "http://ollama:11434"
    embedding_model: str = "bge-m3"
    batch_size: int = 32
    timeout: float = 60.0


@dataclass
class MonitoringConfig:
    """Monitoring configuration."""
    logfire_token: str = "pylf_v1_eu_mFwk5ZXMXFzWgqY8p7W0xJfgLsjwD7vXPsnTzh3wwMCr"
    project_name: str = "deepnexus"
    environment: str = "development"
    enable_metrics: bool = True
    enable_tracing: bool = True
    sample_rate: float = 1.0


@dataclass
class PerformanceConfig:
    """Performance configuration."""
    cache_size: int = 1000
    cache_ttl: float = 300.0
    connection_pool_size: int = 10
    batch_size: int = 100
    worker_threads: int = 4
    enable_compression: bool = True
    enable_caching: bool = True


@dataclass
class SecurityConfig:
    """Security configuration."""
    enable_rate_limiting: bool = True
    rate_limit_per_minute: int = 100
    enable_input_validation: bool = True
    max_file_size_mb: int = 10
    allowed_file_types: List[str] = field(default_factory=lambda: [
        '.py', '.js', '.ts', '.jsx', '.tsx', '.html', '.css', '.json', '.yaml', '.yml', '.md'
    ])
    enable_audit_logging: bool = True


@dataclass
class AppConfig:
    """Main application configuration."""
    environment: Environment = Environment.DEVELOPMENT
    debug: bool = True
    workspace_path: str = "/workspace"
    log_level: str = "INFO"
    
    # Sub-configurations
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    llm: LLMConfig = field(default_factory=LLMConfig)
    embedding: EmbeddingConfig = field(default_factory=EmbeddingConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    performance: PerformanceConfig = field(default_factory=PerformanceConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)


class ConfigManager:
    """Enhanced configuration manager with validation and hot reloading."""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or self._find_config_file()
        self.config = AppConfig()
        self._load_config()
        self._validate_config()
    
    def _find_config_file(self) -> Optional[str]:
        """Find configuration file in standard locations."""
        possible_paths = [
            "config.yaml",
            "config.yml",
            "config.json",
            "app/config.yaml",
            "app/config.yml",
            "app/config.json",
            os.path.expanduser("~/.deepnexus/config.yaml"),
            "/etc/deepnexus/config.yaml"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Found config file: {path}")
                return path
        
        logger.info("No config file found, using defaults")
        return None
    
    def _load_config(self) -> None:
        """Load configuration from file and environment variables."""
        # Load from file if available
        if self.config_path and os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r') as f:
                    if self.config_path.endswith(('.yaml', '.yml')):
                        file_config = yaml.safe_load(f)
                    else:
                        file_config = json.load(f)
                
                self._merge_config(file_config)
                logger.info(f"Loaded configuration from {self.config_path}")
                
            except Exception as e:
                logger.error(f"Failed to load config file {self.config_path}: {e}")
        
        # Override with environment variables
        self._load_env_vars()
    
    def _merge_config(self, file_config: Dict[str, Any]) -> None:
        """Merge file configuration with current config."""
        def merge_dict(target, source):
            for key, value in source.items():
                if key in target.__dict__:
                    if hasattr(target.__dict__[key], '__dict__'):
                        # Nested configuration object
                        if isinstance(value, dict):
                            merge_dict(target.__dict__[key], value)
                    else:
                        # Simple value
                        setattr(target, key, value)
        
        merge_dict(self.config, file_config)
    
    def _load_env_vars(self) -> None:
        """Load configuration from environment variables."""
        env_mappings = {
            'DEEPNEXUS_ENVIRONMENT': ('environment', lambda x: Environment(x)),
            'DEEPNEXUS_DEBUG': ('debug', lambda x: x.lower() == 'true'),
            'DEEPNEXUS_WORKSPACE_PATH': ('workspace_path', str),
            'DEEPNEXUS_LOG_LEVEL': ('log_level', str),
            
            # Database
            'QDRANT_URL': ('database.qdrant_url', str),
            'QDRANT_COLLECTION': ('database.qdrant_collection', str),
            'EMBEDDING_DIM': ('database.embedding_dim', int),
            
            # LLM
            'OPENROUTER_API_KEY': ('llm.openrouter_api_key', str),
            'CODE_MODEL': ('llm.code_model', str),
            'VISION_MODEL': ('llm.vision_model', str),
            
            # Embedding
            'OLLAMA_HOST': ('embedding.ollama_host', str),
            'EMBEDDING_MODEL': ('embedding.embedding_model', str),
            
            # Monitoring
            'LOGFIRE_TOKEN': ('monitoring.logfire_token', str),
            'LOGFIRE_PROJECT': ('monitoring.project_name', str),
        }
        
        for env_var, (config_path, converter) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                try:
                    converted_value = converter(value)
                    self._set_nested_attr(config_path, converted_value)
                    logger.debug(f"Set {config_path} from environment variable {env_var}")
                except Exception as e:
                    logger.error(f"Failed to convert {env_var}={value}: {e}")
    
    def _set_nested_attr(self, path: str, value: Any) -> None:
        """Set nested attribute using dot notation."""
        parts = path.split('.')
        obj = self.config
        
        for part in parts[:-1]:
            obj = getattr(obj, part)
        
        setattr(obj, parts[-1], value)
    
    def _validate_config(self) -> None:
        """Validate configuration values."""
        errors = []
        
        # Validate required fields
        if not self.config.llm.openrouter_api_key:
            errors.append("OpenRouter API key is required")
        
        if self.config.database.embedding_dim <= 0:
            errors.append("Embedding dimension must be positive")
        
        if self.config.performance.cache_size <= 0:
            errors.append("Cache size must be positive")
        
        # Validate file types
        for file_type in self.config.security.allowed_file_types:
            if not file_type.startswith('.'):
                errors.append(f"File type must start with dot: {file_type}")
        
        if errors:
            error_msg = "Configuration validation failed:\n" + "\n".join(f"- {error}" for error in errors)
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        logger.info("Configuration validation passed")
    
    def get_config(self) -> AppConfig:
        """Get the current configuration."""
        return self.config
    
    def reload_config(self) -> None:
        """Reload configuration from file."""
        logger.info("Reloading configuration...")
        self._load_config()
        self._validate_config()
        logger.info("Configuration reloaded successfully")
    
    def get_environment_info(self) -> Dict[str, Any]:
        """Get environment information."""
        return {
            'environment': self.config.environment.value,
            'debug': self.config.debug,
            'workspace_path': self.config.workspace_path,
            'log_level': self.config.log_level,
            'config_file': self.config_path,
            'monitoring_enabled': self.config.monitoring.enable_metrics,
            'performance_optimizations': {
                'caching_enabled': self.config.performance.enable_caching,
                'compression_enabled': self.config.performance.enable_compression,
                'cache_size': self.config.performance.cache_size,
                'batch_size': self.config.performance.batch_size
            }
        }


# Global configuration manager
config_manager = ConfigManager()

# Convenience function to get config
def get_config() -> AppConfig:
    """Get the global configuration."""
    return config_manager.get_config()

# Convenience function to reload config
def reload_config() -> None:
    """Reload the global configuration."""
    config_manager.reload_config()
