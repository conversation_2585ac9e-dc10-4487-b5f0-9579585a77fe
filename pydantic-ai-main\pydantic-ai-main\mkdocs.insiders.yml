INHERIT: mkdocs.yml

markdown_extensions:
  - tables
  - admonition
  - attr_list
  - md_in_html
  - pymdownx.details
  - pymdownx.caret
  - pymdownx.critic
  - pymdownx.mark
  - pymdownx.superfences
  - pymdownx.snippets
  - pymdownx.tilde
  - pymdownx.inlinehilite
  - pymdownx.highlight:
      pygments_lang_class: true
  - pymdownx.extra:
      pymdownx.superfences:
        custom_fences:
          - name: mermaid
            class: mermaid
            format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
      options:
        custom_icons:
            - docs/.overrides/.icons
  - pymdownx.tabbed:
      alternate_style: true
  - pymdownx.tasklist:
      custom_checkbox: true
  - sane_lists # this means you can start a list from any number
  - material.extensions.preview:
      targets:
        include:
          - '*'
