interactions:
- request:
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '1811'
      content-type:
      - application/json
      host:
      - api.openai.com
    method: POST
    parsed_body:
      messages:
      - content: Get me an error, pass False as a value, unless the tool tells you otherwise
        role: user
      model: gpt-4o
      n: 1
      stream: false
      tool_choice: auto
      tools:
      - function:
          description: "Convert Celsius to Fahrenheit.\n\n    Args:\n        celsius: Temperature in Celsius\n\n    Returns:\n
            \       Temperature in Fahrenheit\n    "
          name: celsius_to_fahrenheit
          parameters:
            properties:
              celsius:
                type: number
            required:
            - celsius
            type: object
        type: function
      - function:
          description: "Get the weather forecast for a location.\n\n    Args:\n        location: The location to get the weather
            forecast for.\n\n    Returns:\n        The weather forecast for the location.\n    "
          name: get_weather_forecast
          parameters:
            properties:
              location:
                type: string
            required:
            - location
            type: object
        type: function
      - function:
          description: ''
          name: get_image_resource
          parameters:
            properties: {}
            type: object
        type: function
      - function:
          description: ''
          name: get_image
          parameters:
            properties: {}
            type: object
        type: function
      - function:
          description: ''
          name: get_dict
          parameters:
            properties: {}
            type: object
        type: function
      - function:
          description: ''
          name: get_error
          parameters:
            properties:
              value:
                type: boolean
            type: object
        type: function
      - function:
          description: ''
          name: get_none
          parameters:
            properties: {}
            type: object
        type: function
      - function:
          description: ''
          name: get_multiple_items
          parameters:
            properties: {}
            type: object
        type: function
      - function:
          description: "Get the current log level.\n\n    Returns:\n        The current log level.\n    "
          name: get_log_level
          parameters:
            properties: {}
            type: object
        type: function
    uri: https://api.openai.com/v1/chat/completions
  response:
    headers:
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      connection:
      - keep-alive
      content-length:
      - '1076'
      content-type:
      - application/json
      openai-organization:
      - pydantic-28gund
      openai-processing-ms:
      - '822'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      transfer-encoding:
      - chunked
    parsed_body:
      choices:
      - finish_reason: tool_calls
        index: 0
        logprobs: null
        message:
          annotations: []
          content: null
          refusal: null
          role: assistant
          tool_calls:
          - function:
              arguments: '{"value":false}'
              name: get_error
            id: call_rETXZWddAGZSHyVHAxptPGgc
            type: function
      created: 1745958348
      id: chatcmpl-BRloSNg7aGSp1rXDkhInjMIUHKd7A
      model: gpt-4o-2024-08-06
      object: chat.completion
      service_tier: default
      system_fingerprint: fp_f5bdcc3276
      usage:
        completion_tokens: 15
        completion_tokens_details:
          accepted_prediction_tokens: 0
          audio_tokens: 0
          reasoning_tokens: 0
          rejected_prediction_tokens: 0
        prompt_tokens: 203
        prompt_tokens_details:
          audio_tokens: 0
          cached_tokens: 0
        total_tokens: 218
    status:
      code: 200
      message: OK
- request:
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '2158'
      content-type:
      - application/json
      cookie:
      - __cf_bm=c2NS.3rC8I3WGObElx_aNIgKv2H_mYskh_dsnX0HyUg-1745958348-*******-J_QbUsUlvF1n9lZCHnMyCtnDjg1hmuU1bCSJJsgeDR.TovtLPBxEyigoxq_0ngWXaCyXTVrYwQMeb0hhmlE8D6H77Uby6ZF_sEOpjTG.nUw;
        _cfuvid=5u5sVSA2qWcOU7r01SalxXfsPw_WYPktFT9KV6I0YVU-1745958348818-*******-604800000
      host:
      - api.openai.com
    method: POST
    parsed_body:
      messages:
      - content: Get me an error, pass False as a value, unless the tool tells you otherwise
        role: user
      - role: assistant
        tool_calls:
        - function:
            arguments: '{"value":false}'
            name: get_error
          id: call_rETXZWddAGZSHyVHAxptPGgc
          type: function
      - content: |-
          Error executing tool get_error: This is an error. Call the tool with True instead

          Fix the errors and try again.
        role: tool
        tool_call_id: call_rETXZWddAGZSHyVHAxptPGgc
      model: gpt-4o
      n: 1
      stream: false
      tool_choice: auto
      tools:
      - function:
          description: "Convert Celsius to Fahrenheit.\n\n    Args:\n        celsius: Temperature in Celsius\n\n    Returns:\n
            \       Temperature in Fahrenheit\n    "
          name: celsius_to_fahrenheit
          parameters:
            properties:
              celsius:
                type: number
            required:
            - celsius
            type: object
        type: function
      - function:
          description: "Get the weather forecast for a location.\n\n    Args:\n        location: The location to get the weather
            forecast for.\n\n    Returns:\n        The weather forecast for the location.\n    "
          name: get_weather_forecast
          parameters:
            properties:
              location:
                type: string
            required:
            - location
            type: object
        type: function
      - function:
          description: ''
          name: get_image_resource
          parameters:
            properties: {}
            type: object
        type: function
      - function:
          description: ''
          name: get_image
          parameters:
            properties: {}
            type: object
        type: function
      - function:
          description: ''
          name: get_dict
          parameters:
            properties: {}
            type: object
        type: function
      - function:
          description: ''
          name: get_error
          parameters:
            properties:
              value:
                type: boolean
            type: object
        type: function
      - function:
          description: ''
          name: get_none
          parameters:
            properties: {}
            type: object
        type: function
      - function:
          description: ''
          name: get_multiple_items
          parameters:
            properties: {}
            type: object
        type: function
      - function:
          description: "Get the current log level.\n\n    Returns:\n        The current log level.\n    "
          name: get_log_level
          parameters:
            properties: {}
            type: object
        type: function
    uri: https://api.openai.com/v1/chat/completions
  response:
    headers:
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      connection:
      - keep-alive
      content-length:
      - '1075'
      content-type:
      - application/json
      openai-organization:
      - pydantic-28gund
      openai-processing-ms:
      - '917'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      transfer-encoding:
      - chunked
    parsed_body:
      choices:
      - finish_reason: tool_calls
        index: 0
        logprobs: null
        message:
          annotations: []
          content: null
          refusal: null
          role: assistant
          tool_calls:
          - function:
              arguments: '{"value":true}'
              name: get_error
            id: call_4xGyvdghYKHN8x19KWkRtA5N
            type: function
      created: 1745958349
      id: chatcmpl-BRloTvSkFeX4DZKQLqfH9KbQkWlpt
      model: gpt-4o-2024-08-06
      object: chat.completion
      service_tier: default
      system_fingerprint: fp_f5bdcc3276
      usage:
        completion_tokens: 15
        completion_tokens_details:
          accepted_prediction_tokens: 0
          audio_tokens: 0
          reasoning_tokens: 0
          rejected_prediction_tokens: 0
        prompt_tokens: 250
        prompt_tokens_details:
          audio_tokens: 0
          cached_tokens: 0
        total_tokens: 265
    status:
      code: 200
      message: OK
- request:
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '2410'
      content-type:
      - application/json
      cookie:
      - __cf_bm=c2NS.3rC8I3WGObElx_aNIgKv2H_mYskh_dsnX0HyUg-1745958348-*******-J_QbUsUlvF1n9lZCHnMyCtnDjg1hmuU1bCSJJsgeDR.TovtLPBxEyigoxq_0ngWXaCyXTVrYwQMeb0hhmlE8D6H77Uby6ZF_sEOpjTG.nUw;
        _cfuvid=5u5sVSA2qWcOU7r01SalxXfsPw_WYPktFT9KV6I0YVU-1745958348818-*******-604800000
      host:
      - api.openai.com
    method: POST
    parsed_body:
      messages:
      - content: Get me an error, pass False as a value, unless the tool tells you otherwise
        role: user
      - role: assistant
        tool_calls:
        - function:
            arguments: '{"value":false}'
            name: get_error
          id: call_rETXZWddAGZSHyVHAxptPGgc
          type: function
      - content: |-
          Error executing tool get_error: This is an error. Call the tool with True instead

          Fix the errors and try again.
        role: tool
        tool_call_id: call_rETXZWddAGZSHyVHAxptPGgc
      - role: assistant
        tool_calls:
        - function:
            arguments: '{"value":true}'
            name: get_error
          id: call_4xGyvdghYKHN8x19KWkRtA5N
          type: function
      - content: This is not an error
        role: tool
        tool_call_id: call_4xGyvdghYKHN8x19KWkRtA5N
      model: gpt-4o
      n: 1
      stream: false
      tool_choice: auto
      tools:
      - function:
          description: "Convert Celsius to Fahrenheit.\n\n    Args:\n        celsius: Temperature in Celsius\n\n    Returns:\n
            \       Temperature in Fahrenheit\n    "
          name: celsius_to_fahrenheit
          parameters:
            properties:
              celsius:
                type: number
            required:
            - celsius
            type: object
        type: function
      - function:
          description: "Get the weather forecast for a location.\n\n    Args:\n        location: The location to get the weather
            forecast for.\n\n    Returns:\n        The weather forecast for the location.\n    "
          name: get_weather_forecast
          parameters:
            properties:
              location:
                type: string
            required:
            - location
            type: object
        type: function
      - function:
          description: ''
          name: get_image_resource
          parameters:
            properties: {}
            type: object
        type: function
      - function:
          description: ''
          name: get_image
          parameters:
            properties: {}
            type: object
        type: function
      - function:
          description: ''
          name: get_dict
          parameters:
            properties: {}
            type: object
        type: function
      - function:
          description: ''
          name: get_error
          parameters:
            properties:
              value:
                type: boolean
            type: object
        type: function
      - function:
          description: ''
          name: get_none
          parameters:
            properties: {}
            type: object
        type: function
      - function:
          description: ''
          name: get_multiple_items
          parameters:
            properties: {}
            type: object
        type: function
      - function:
          description: "Get the current log level.\n\n    Returns:\n        The current log level.\n    "
          name: get_log_level
          parameters:
            properties: {}
            type: object
        type: function
    uri: https://api.openai.com/v1/chat/completions
  response:
    headers:
      access-control-expose-headers:
      - X-Request-ID
      alt-svc:
      - h3=":443"; ma=86400
      connection:
      - keep-alive
      content-length:
      - '892'
      content-type:
      - application/json
      openai-organization:
      - pydantic-28gund
      openai-processing-ms:
      - '1142'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      transfer-encoding:
      - chunked
    parsed_body:
      choices:
      - finish_reason: stop
        index: 0
        logprobs: null
        message:
          annotations: []
          content: 'I called the tool with the correct parameter, and it returned: "This is not an error."'
          refusal: null
          role: assistant
      created: 1745958350
      id: chatcmpl-BRloU3MhnqNEqujs28a3ofRbs7VPF
      model: gpt-4o-2024-08-06
      object: chat.completion
      service_tier: default
      system_fingerprint: fp_f5bdcc3276
      usage:
        completion_tokens: 22
        completion_tokens_details:
          accepted_prediction_tokens: 0
          audio_tokens: 0
          reasoning_tokens: 0
          rejected_prediction_tokens: 0
        prompt_tokens: 277
        prompt_tokens_details:
          audio_tokens: 0
          cached_tokens: 0
        total_tokens: 299
    status:
      code: 200
      message: OK
version: 1
