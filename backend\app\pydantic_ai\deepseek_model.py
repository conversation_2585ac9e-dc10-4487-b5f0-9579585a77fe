"""
Custom DeepSeek R1 Model for Pydantic AI

This module implements a custom model class for DeepSeek R1 that supports
JSON-based tool calling, since DeepSeek R1 doesn't natively support OpenAI-style
function calling through OpenRouter.
"""

import json
import logging
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from pydantic_ai.models import Model, ModelRequestParameters, check_allow_model_requests
from pydantic_ai.messages import (
    ModelMessage, ModelRequest, ModelResponse, TextPart, ToolCallPart,
    SystemPromptPart, UserPromptPart, ToolReturnPart, RetryPromptPart
)
from pydantic_ai.tools import ToolDefinition
from pydantic_ai.settings import ModelSettings
from pydantic_ai.usage import Usage

# Import the existing OpenRouter client
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from llm_client import openrouter_client

logger = logging.getLogger(__name__)


class DeepSeekR1Model(Model):
    """
    Custom model for DeepSeek R1 with JSON-based tool calling.
    
    Since DeepSeek R1 doesn't support native OpenAI-style function calling through
    OpenRouter, this model implements a JSON-based approach where tools are described
    in the prompt and the model responds with JSON containing tool calls.
    """
    
    def __init__(self, model_name: str = "deepseek/deepseek-r1-0528:free"):
        """
        Initialize the DeepSeek R1 model.
        
        Args:
            model_name: The model name to use with OpenRouter
        """
        self._model_name = model_name
        self._system = "deepseek"
    
    @property
    def model_name(self) -> str:
        """The model name."""
        return self._model_name
    
    @property
    def system(self) -> str:
        """The system / model provider."""
        return self._system
    
    async def request(
        self,
        messages: List[ModelMessage],
        model_settings: Optional[ModelSettings],
        model_request_parameters: ModelRequestParameters,
    ) -> ModelResponse:
        """Make a request to the DeepSeek R1 model."""
        check_allow_model_requests()
        
        # Convert Pydantic AI messages to a prompt
        prompt = await self._build_prompt(messages, model_request_parameters)
        
        # Make request to OpenRouter
        try:
            # The chat_completion method returns a string directly, not a dict
            content = await openrouter_client.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                model=self._model_name,
                temperature=model_settings.get('temperature', 0.2) if model_settings else 0.2,
                max_tokens=model_settings.get('max_tokens', 4000) if model_settings else 4000
            )

            # Parse the response and convert to ModelResponse
            return await self._parse_response(content, model_request_parameters)
            
        except Exception as e:
            logger.error(f"DeepSeek R1 request failed: {e}")
            # Return error as text response
            return ModelResponse(
                parts=[TextPart(f"Error: {str(e)}")],
                model_name=self._model_name,
                timestamp=datetime.now(timezone.utc),
                usage=Usage(requests=1)
            )
    
    async def _build_prompt(
        self, 
        messages: List[ModelMessage], 
        model_request_parameters: ModelRequestParameters
    ) -> str:
        """
        Build a prompt from Pydantic AI messages and tool definitions.
        """
        prompt_parts = []
        
        # Process messages to extract content
        for message in messages:
            if isinstance(message, ModelRequest):
                for part in message.parts:
                    if isinstance(part, SystemPromptPart):
                        prompt_parts.append(f"SYSTEM: {part.content}")
                    elif isinstance(part, UserPromptPart):
                        if isinstance(part.content, str):
                            prompt_parts.append(f"USER: {part.content}")
                        else:
                            # Handle multimodal content (simplified for now)
                            text_content = []
                            for item in part.content:
                                if isinstance(item, str):
                                    text_content.append(item)
                            if text_content:
                                prompt_parts.append(f"USER: {' '.join(text_content)}")
                    elif isinstance(part, ToolReturnPart):
                        prompt_parts.append(f"TOOL_RESULT[{part.tool_name}]: {part.model_response_str()}")
                    elif isinstance(part, RetryPromptPart):
                        prompt_parts.append(f"RETRY: {part.model_response()}")
            
            elif isinstance(message, ModelResponse):
                for part in message.parts:
                    if isinstance(part, TextPart):
                        prompt_parts.append(f"ASSISTANT: {part.content}")
                    elif isinstance(part, ToolCallPart):
                        args_str = part.args_as_json_str() if part.args else "{}"
                        prompt_parts.append(f"ASSISTANT_TOOL_CALL[{part.tool_name}]: {args_str}")
        
        # Add tool definitions if available
        if model_request_parameters.function_tools:
            tools_description = self._format_tools_for_prompt(model_request_parameters.function_tools)
            prompt_parts.append(f"\nAVAILABLE_TOOLS:\n{tools_description}")
            
            # Add instructions for tool usage
            prompt_parts.append("""
TOOL_USAGE_INSTRUCTIONS:
If you need to use tools, respond with JSON in this exact format:
{
    "tool_calls": [
        {
            "tool": "tool_name",
            "args": {"param1": "value1", "param2": "value2"}
        }
    ]
}

If you don't need tools, respond normally with text.
""")
        
        return "\n\n".join(prompt_parts)
    
    def _format_tools_for_prompt(self, tools: List[ToolDefinition]) -> str:
        """Format tool definitions for inclusion in the prompt."""
        tool_descriptions = []
        
        for tool in tools:
            description = f"- {tool.name}: {tool.description}"
            
            # Add parameter information
            if tool.parameters_json_schema and 'properties' in tool.parameters_json_schema:
                params = []
                properties = tool.parameters_json_schema['properties']
                required = tool.parameters_json_schema.get('required', [])
                
                for param_name, param_info in properties.items():
                    param_desc = param_info.get('description', 'No description')
                    param_type = param_info.get('type', 'unknown')
                    required_marker = " (required)" if param_name in required else " (optional)"
                    params.append(f"    - {param_name} ({param_type}){required_marker}: {param_desc}")
                
                if params:
                    description += "\n" + "\n".join(params)
            
            tool_descriptions.append(description)
        
        return "\n".join(tool_descriptions)
    
    async def _parse_response(
        self,
        content: str,
        model_request_parameters: ModelRequestParameters
    ) -> ModelResponse:
        """
        Parse the model response and convert to ModelResponse.
        """
        parts = []

        # Try to extract JSON from markdown code blocks first
        json_content = self._extract_json_from_markdown(content)
        if json_content:
            try:
                parsed = json.loads(json_content)
                if isinstance(parsed, dict) and 'tool_calls' in parsed:
                    # Handle tool calls
                    for tool_call in parsed['tool_calls']:
                        if isinstance(tool_call, dict) and 'tool' in tool_call:
                            tool_name = tool_call['tool']
                            tool_args = tool_call.get('args', {})
                            tool_call_id = f"deepseek_{uuid.uuid4().hex[:8]}"

                            parts.append(ToolCallPart(
                                tool_name=tool_name,
                                args=tool_args,
                                tool_call_id=tool_call_id
                            ))

                    # If we found tool calls, return them
                    if parts:
                        return ModelResponse(
                            parts=parts,
                            model_name=self._model_name,
                            timestamp=datetime.now(timezone.utc),
                            usage=Usage(requests=1)
                        )
            except json.JSONDecodeError:
                pass

        # Try to parse as raw JSON (fallback)
        try:
            parsed = json.loads(content.strip())
            if isinstance(parsed, dict) and 'tool_calls' in parsed:
                # Handle tool calls
                for tool_call in parsed['tool_calls']:
                    if isinstance(tool_call, dict) and 'tool' in tool_call:
                        tool_name = tool_call['tool']
                        tool_args = tool_call.get('args', {})
                        tool_call_id = f"deepseek_{uuid.uuid4().hex[:8]}"

                        parts.append(ToolCallPart(
                            tool_name=tool_name,
                            args=tool_args,
                            tool_call_id=tool_call_id
                        ))

                # If we found tool calls, return them
                if parts:
                    return ModelResponse(
                        parts=parts,
                        model_name=self._model_name,
                        timestamp=datetime.now(timezone.utc),
                        usage=Usage(requests=1)
                    )

        except json.JSONDecodeError:
            # Not JSON, treat as regular text
            pass

        # Regular text response
        parts.append(TextPart(content=content))

        return ModelResponse(
            parts=parts,
            model_name=self._model_name,
            timestamp=datetime.now(timezone.utc),
            usage=Usage(requests=1)
        )

    def _extract_json_from_markdown(self, content: str) -> Optional[str]:
        """
        Extract JSON content from markdown code blocks.

        DeepSeek R1 often returns JSON inside ```json ... ``` blocks.
        """
        import re

        # Look for JSON code blocks
        json_pattern = r'```(?:json)?\s*\n?(.*?)\n?```'
        matches = re.findall(json_pattern, content, re.DOTALL | re.IGNORECASE)

        for match in matches:
            # Clean up the match
            cleaned = match.strip()
            if cleaned.startswith('{') and cleaned.endswith('}'):
                try:
                    # Validate it's actually JSON
                    json.loads(cleaned)
                    return cleaned
                except json.JSONDecodeError:
                    continue

        return None
