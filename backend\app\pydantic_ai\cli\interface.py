"""
CLI Interface for Pydantic AI

This module provides the main CLI interface and interactive session management
for the Pydantic AI system.
"""

import asyncio
import logging
import sys
from typing import Any, Dict, List, Optional

import click

from .commands import run_agent_command, test_command, benchmark_command, evaluate_command
from .utils import format_output, load_cli_config, save_cli_config

logger = logging.getLogger(__name__)


class CLIInterface:
    """Main CLI interface for Pydantic AI."""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "pydantic_ai_config.json"
        self.config = load_cli_config(self.config_file)
        self.session_history: List[Dict[str, Any]] = []
    
    def add_to_history(self, command: str, result: Any, execution_time: float):
        """Add command to session history."""
        self.session_history.append({
            "command": command,
            "result": str(result)[:200] + "..." if len(str(result)) > 200 else str(result),
            "execution_time": execution_time,
            "timestamp": click.get_current_context().meta.get('timestamp', 'unknown')
        })
    
    def show_history(self, limit: int = 10):
        """Show recent command history."""
        recent_history = self.session_history[-limit:]
        
        if not recent_history:
            click.echo("📝 No command history available.")
            return
        
        click.echo(f"📝 Recent Commands (last {len(recent_history)}):")
        for i, entry in enumerate(recent_history, 1):
            click.echo(f"   {i}. {entry['command']} ({entry['execution_time']:.2f}s)")
            if len(entry['result']) > 0:
                click.echo(f"      → {entry['result'][:100]}...")
    
    def clear_history(self):
        """Clear command history."""
        self.session_history.clear()
        click.echo("🗑️  Command history cleared.")
    
    def show_config(self):
        """Show current configuration."""
        click.echo("⚙️  Current Configuration:")
        for key, value in self.config.items():
            click.echo(f"   {key}: {value}")
    
    def update_config(self, key: str, value: Any):
        """Update configuration setting."""
        self.config[key] = value
        save_cli_config(self.config, self.config_file)
        click.echo(f"✅ Updated {key} = {value}")


@click.group()
@click.option('--config', '-c', type=click.Path(), help='Configuration file path')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
@click.pass_context
def create_cli_app(ctx, config: Optional[str], verbose: bool):
    """Pydantic AI - Advanced AI Agent System with Tool Calling and Vision Analysis."""
    
    # Setup logging
    if verbose:
        logging.basicConfig(level=logging.INFO)
    
    # Create CLI interface
    cli_interface = CLIInterface(config)
    ctx.ensure_object(dict)
    ctx.obj['cli'] = cli_interface
    ctx.obj['verbose'] = verbose


# Add commands to the CLI app
create_cli_app.add_command(run_agent_command, name='run')
create_cli_app.add_command(test_command, name='test')
create_cli_app.add_command(benchmark_command, name='benchmark')
create_cli_app.add_command(evaluate_command, name='evaluate')


@create_cli_app.command()
@click.pass_context
def interactive(ctx):
    """Start an interactive session."""
    cli_interface = ctx.obj['cli']
    verbose = ctx.obj['verbose']
    
    click.echo("🚀 Welcome to Pydantic AI Interactive Session!")
    click.echo("Type 'help' for available commands, 'exit' to quit.")
    
    asyncio.run(run_interactive_session(cli_interface, verbose))


@create_cli_app.command()
@click.pass_context
def history(ctx):
    """Show command history."""
    cli_interface = ctx.obj['cli']
    cli_interface.show_history()


@create_cli_app.command()
@click.pass_context
def config(ctx):
    """Show current configuration."""
    cli_interface = ctx.obj['cli']
    cli_interface.show_config()


@create_cli_app.command()
@click.argument('key', type=str)
@click.argument('value', type=str)
@click.pass_context
def set_config(ctx, key: str, value: str):
    """Set a configuration value."""
    cli_interface = ctx.obj['cli']
    
    # Try to parse value as appropriate type
    try:
        if value.lower() in ('true', 'false'):
            value = value.lower() == 'true'
        elif value.isdigit():
            value = int(value)
        elif '.' in value and value.replace('.', '').isdigit():
            value = float(value)
    except:
        pass  # Keep as string
    
    cli_interface.update_config(key, value)


@create_cli_app.command()
@click.pass_context
def clear(ctx):
    """Clear command history."""
    cli_interface = ctx.obj['cli']
    cli_interface.clear_history()


@create_cli_app.command()
def version():
    """Show version information."""
    click.echo("Pydantic AI v1.0.0")
    click.echo("Advanced AI Agent System with Tool Calling and Vision Analysis")
    click.echo("Built with Pydantic AI, OpenRouter, and DeepSeek/Gemini models")


@create_cli_app.command()
def status():
    """Show system status."""
    click.echo("🔍 System Status Check:")

    try:
        # Check imports
        from ..agents import coding_agent, vision_agent
        from ..tools import get_tool_summary

        click.echo("✅ Agents: Available")

        # Check tools
        tool_summary = get_tool_summary()
        click.echo(f"✅ Tools: {tool_summary}")

        # Check dependencies
        try:
            import pydantic_ai
            click.echo(f"✅ Pydantic AI: {pydantic_ai.__version__}")
        except:
            click.echo("❌ Pydantic AI: Not available")

        try:
            import openai
            click.echo("✅ OpenAI: Available")
        except:
            click.echo("❌ OpenAI: Not available")

        click.echo("🎉 System is ready!")

    except Exception as e:
        click.echo(f"❌ System check failed: {e}")


async def handle_autonomous_command(parts: List[str]):
    """Handle autonomous continue commands."""
    if not parts:
        click.echo("❓ Autonomous command requires an action (start, status, stop, stats)")
        return

    action = parts[0].lower()

    try:
        if action == 'start' and len(parts) >= 3:
            session_name = parts[1]
            task = ' '.join(parts[2:])

            click.echo(f"🚀 Starting autonomous session: {session_name}")
            click.echo(f"📋 Task: {task}")

            # Import autonomous continue tools
            from ...autonomous_continue.core import autonomous_continue_engine
            from ...autonomous_continue.models import ContinueConfig, SafetyLimits
            from datetime import timedelta

            # Create configuration
            config = ContinueConfig(
                session_name=session_name,
                initial_task=task,
                workspace_root=".",
                safety_limits=SafetyLimits(max_iterations=20, max_duration=timedelta(hours=1))
            )

            # Initialize and start session
            await autonomous_continue_engine.initialize()
            result = await autonomous_continue_engine.start_autonomous_session(config)

            if result['status'] == 'success':
                click.echo(f"✅ Session started: {result['session_id']}")
            else:
                click.echo(f"❌ Failed to start session: {result.get('error', 'Unknown error')}")

        elif action == 'status' and len(parts) >= 2:
            session_id = parts[1]

            from ...autonomous_continue.core import autonomous_continue_engine
            result = await autonomous_continue_engine.get_session_status(session_id)

            if result['status'] == 'success':
                session_info = result['session']
                click.echo(f"📊 Session Status: {session_info['state']}")
                click.echo(f"🔄 Iteration: {session_info['current_iteration']}")
                click.echo(f"📈 Success Rate: {session_info['success_rate']:.1f}%")
                click.echo(f"🎯 Current Task: {session_info['current_task']}")
            else:
                click.echo(f"❌ Failed to get status: {result.get('error', 'Unknown error')}")

        elif action == 'stop' and len(parts) >= 2:
            session_id = parts[1]

            from ...autonomous_continue.core import autonomous_continue_engine
            result = await autonomous_continue_engine.stop_session(session_id)

            if result['status'] == 'success':
                click.echo(f"⏹️ Session stopped: {session_id}")
            else:
                click.echo(f"❌ Failed to stop session: {result.get('error', 'Unknown error')}")

        elif action == 'stats':
            from ...autonomous_continue.core import autonomous_continue_engine
            result = await autonomous_continue_engine.get_system_statistics()

            if result['status'] == 'success':
                click.echo("📊 Autonomous System Statistics:")
                sessions = result.get('sessions', {})
                click.echo(f"   Total Sessions: {sessions.get('total_sessions', 0)}")
                click.echo(f"   Active Sessions: {sessions.get('active_sessions', 0)}")
                click.echo(f"   Completed Sessions: {sessions.get('completed_sessions', 0)}")

                error_stats = result.get('error_intelligence', {})
                click.echo(f"   Error Patterns: {error_stats.get('total_patterns', 0)}")
                click.echo(f"   Total Errors: {error_stats.get('total_occurrences', 0)}")
            else:
                click.echo(f"❌ Failed to get statistics: {result.get('error', 'Unknown error')}")

        else:
            click.echo("❓ Unknown autonomous action. Available: start, status, stop, stats")

    except Exception as e:
        click.echo(f"❌ Autonomous command failed: {e}")


async def run_interactive_session(cli_interface: CLIInterface, verbose: bool = False):
    """Run an interactive CLI session."""
    
    while True:
        try:
            # Get user input
            user_input = click.prompt("pydantic-ai", type=str, default="", show_default=False)
            
            if not user_input.strip():
                continue
            
            # Parse command
            parts = user_input.strip().split()
            command = parts[0].lower()
            
            if command in ['exit', 'quit', 'q']:
                click.echo("👋 Goodbye!")
                break
            
            elif command == 'help':
                click.echo("\n📚 Available Commands:")
                click.echo("   run <prompt>              - Run coding agent with prompt")
                click.echo("   vision <prompt>           - Run vision agent with prompt")
                click.echo("   test [dataset]            - Run tests (coding/vision/comprehensive)")
                click.echo("   benchmark [agent]         - Run performance benchmark")
                click.echo("   evaluate <prompt>         - Evaluate agent response")
                click.echo("   autonomous start <name> <task> - Start autonomous continue session")
                click.echo("   autonomous status <id>    - Get autonomous session status")
                click.echo("   autonomous stop <id>      - Stop autonomous session")
                click.echo("   autonomous stats          - Get autonomous system statistics")
                click.echo("   history                   - Show command history")
                click.echo("   config                    - Show configuration")
                click.echo("   set <key> <value>         - Set configuration value")
                click.echo("   clear                     - Clear history")
                click.echo("   status                    - Show system status")
                click.echo("   help                      - Show this help")
                click.echo("   exit/quit/q               - Exit session")
            
            elif command == 'run' and len(parts) > 1:
                prompt = ' '.join(parts[1:])
                import time
                start_time = time.time()
                
                # Simulate running the agent (simplified for interactive mode)
                from ..agents import coding_agent
                from ..dependencies import create_dependencies
                
                deps = await create_dependencies(workspace_root=".")
                result = await coding_agent.run(prompt, deps=deps)
                
                end_time = time.time()
                execution_time = end_time - start_time
                
                click.echo(f"\n🤖 Agent Response:")
                click.echo(result.output)
                click.echo(f"\n⏱️  Execution time: {execution_time:.2f}s")
                
                cli_interface.add_to_history(user_input, result.output, execution_time)
            
            elif command == 'vision' and len(parts) > 1:
                prompt = ' '.join(parts[1:])
                import time
                start_time = time.time()
                
                # Run vision agent
                from ..agents import simple_vision_agent
                
                result = await simple_vision_agent.run(prompt)
                
                end_time = time.time()
                execution_time = end_time - start_time
                
                click.echo(f"\n👁️  Vision Agent Response:")
                click.echo(result.output)
                click.echo(f"\n⏱️  Execution time: {execution_time:.2f}s")
                
                cli_interface.add_to_history(user_input, result.output, execution_time)
            
            elif command == 'history':
                cli_interface.show_history()
            
            elif command == 'config':
                cli_interface.show_config()
            
            elif command == 'set' and len(parts) >= 3:
                key = parts[1]
                value = ' '.join(parts[2:])
                cli_interface.update_config(key, value)
            
            elif command == 'clear':
                cli_interface.clear_history()
            
            elif command == 'autonomous' and len(parts) > 1:
                await handle_autonomous_command(parts[1:])

            elif command == 'status':
                # Run status check
                click.echo("🔍 System Status:")
                try:
                    from ..tools import get_tool_summary
                    tool_summary = get_tool_summary()
                    click.echo(f"✅ Tools: {tool_summary}")
                    click.echo("✅ System is ready!")
                except Exception as e:
                    click.echo(f"❌ Status check failed: {e}")

            else:
                click.echo(f"❓ Unknown command: {command}")
                click.echo("Type 'help' for available commands.")
        
        except KeyboardInterrupt:
            click.echo("\n👋 Goodbye!")
            break
        except EOFError:
            click.echo("\n👋 Goodbye!")
            break
        except Exception as e:
            click.echo(f"❌ Error: {e}")
            if verbose:
                import traceback
                traceback.print_exc()


# Export main interface
__all__ = [
    'CLIInterface',
    'create_cli_app',
    'run_interactive_session'
]
