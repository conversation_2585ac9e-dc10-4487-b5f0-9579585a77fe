# Quick Development Commands for AI Coder Agent

# Function to show available commands
function Show-DevCommands {
    Write-Host @"
🚀 AI Coder Agent - Development Commands

Quick Start:
  dev-start           - Start development environment (Docker with volumes)
  dev-start-local     - Start local development (fastest)
  dev-stop            - Stop all services
  dev-restart         - Restart backend service
  dev-logs            - Show backend logs
  dev-test            - Run Phase 3 tests

Development:
  dev-backend         - Run backend locally
  dev-frontend        - Run frontend locally
  dev-services        - Start only external services (Qdrant, Ollama)

Utilities:
  dev-clean           - Clean up containers and volumes
  dev-reset           - Reset everything (clean + rebuild)
  dev-shell           - Open shell in backend container

Examples:
  dev-start           # Start with Docker volumes (recommended)
  dev-start-local     # Start everything locally (fastest)
  dev-backend         # Run only backend locally
"@ -ForegroundColor Cyan
}

# Start development with Docker volumes (recommended)
function dev-start {
    Write-Host "🚀 Starting development environment with Docker volumes..." -ForegroundColor Green
    docker-compose -f docker-compose.dev.yml up -d
    Start-Sleep 5
    Write-Host "✅ Development environment started!" -ForegroundColor Green
    Write-Host "Backend: http://localhost:8000" -ForegroundColor Yellow
    Write-Host "Frontend: http://localhost:3000" -ForegroundColor Yellow
    Write-Host "Use 'dev-logs' to see backend logs" -ForegroundColor Cyan
}

# Start local development (fastest)
function dev-start-local {
    Write-Host "💻 Starting local development environment..." -ForegroundColor Green
    
    # Start external services only
    docker-compose up -d qdrant ollama
    
    Write-Host @"
✅ External services started!

To start backend locally:
  dev-backend

To start frontend locally:
  dev-frontend

URLs:
  Qdrant: http://localhost:6333
  Ollama: http://localhost:11434
"@ -ForegroundColor Green
}

# Run backend locally
function dev-backend {
    Write-Host "🐍 Starting backend locally..." -ForegroundColor Green
    Set-Location backend
    $env:PYTHONPATH = "."
    $env:OLLAMA_HOST = "http://localhost:11434"
    $env:QDRANT_URL = "http://localhost:6333"
    $env:LOG_LEVEL = "DEBUG"
    python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
}

# Run frontend locally
function dev-frontend {
    Write-Host "⚛️ Starting frontend locally..." -ForegroundColor Green
    Set-Location frontend
    npm run dev
}

# Start only external services
function dev-services {
    Write-Host "🔧 Starting external services..." -ForegroundColor Green
    docker-compose up -d qdrant ollama
    Write-Host "✅ Qdrant and Ollama started!" -ForegroundColor Green
}

# Stop all services
function dev-stop {
    Write-Host "🛑 Stopping development environment..." -ForegroundColor Yellow
    docker-compose -f docker-compose.dev.yml down
    docker-compose down
    Write-Host "✅ All services stopped!" -ForegroundColor Green
}

# Restart backend
function dev-restart {
    Write-Host "🔄 Restarting backend..." -ForegroundColor Yellow
    docker-compose -f docker-compose.dev.yml restart backend
    Write-Host "✅ Backend restarted!" -ForegroundColor Green
}

# Show backend logs
function dev-logs {
    docker-compose -f docker-compose.dev.yml logs -f backend
}

# Run Phase 3 tests
function dev-test {
    Write-Host "🧪 Running Phase 3 tests..." -ForegroundColor Green
    
    # Wait for backend to be ready
    $maxAttempts = 30
    $attempt = 0
    do {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:8000/health" -Method GET -TimeoutSec 2
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ Backend is ready!" -ForegroundColor Green
                break
            }
        } catch {
            $attempt++
            if ($attempt -ge $maxAttempts) {
                Write-Host "❌ Backend not ready after $maxAttempts attempts" -ForegroundColor Red
                return
            }
            Write-Host "⏳ Waiting for backend... ($attempt/$maxAttempts)" -ForegroundColor Yellow
            Start-Sleep 2
        }
    } while ($true)
    
    # Run tests
    try {
        Write-Host "Testing Phase 3 integration..." -ForegroundColor Cyan
        $result = Invoke-WebRequest -Uri "http://localhost:8000/api/test/phase3-integration" -Method POST -ContentType "application/json" -Body "{}"
        Write-Host "✅ Phase 3 integration test passed!" -ForegroundColor Green
        
        Write-Host "Testing file operations..." -ForegroundColor Cyan
        $result = Invoke-WebRequest -Uri "http://localhost:8000/api/files/info" -Method POST -ContentType "application/json" -Body "{`"file_path`": `"/app/main.py`"}"
        Write-Host "✅ File operations test passed!" -ForegroundColor Green
        
        Write-Host "🎉 All tests passed!" -ForegroundColor Green
    } catch {
        Write-Host "❌ Tests failed: $_" -ForegroundColor Red
    }
}

# Clean up
function dev-clean {
    Write-Host "🧹 Cleaning up containers and volumes..." -ForegroundColor Yellow
    docker-compose -f docker-compose.dev.yml down -v
    docker-compose down -v
    docker system prune -f
    Write-Host "✅ Cleanup complete!" -ForegroundColor Green
}

# Reset everything
function dev-reset {
    Write-Host "🔄 Resetting development environment..." -ForegroundColor Yellow
    dev-clean
    docker-compose -f docker-compose.dev.yml build --no-cache
    Write-Host "✅ Reset complete!" -ForegroundColor Green
}

# Open shell in backend container
function dev-shell {
    docker exec -it ai_coder_backend_dev /bin/bash
}

# Show commands by default
Show-DevCommands
