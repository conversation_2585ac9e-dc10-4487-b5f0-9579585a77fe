"""
🔄 Session Recovery - Seamless session recovery after disconnection

This module provides comprehensive session recovery capabilities:
- Automatic session state preservation
- Seamless recovery after disconnection
- Context restoration and validation
- Recovery progress tracking
- Integration with conversation history
"""

import asyncio
import json
import sqlite3
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from pydantic import BaseModel, Field
import logging

from .session_manager import SessionManager, Session, SessionStatus
from .conversation_history import ConversationHistory, Message

logger = logging.getLogger(__name__)


class RecoveryStatus(str, Enum):
    """Recovery status enumeration."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    PARTIAL = "partial"


class RecoveryResult(BaseModel):
    """Result of session recovery operation."""
    
    session_id: str = Field(..., description="Recovered session ID")
    status: RecoveryStatus = Field(..., description="Recovery status")
    
    # Recovery details
    messages_recovered: int = Field(default=0, description="Number of messages recovered")
    context_restored: bool = Field(default=False, description="Whether context was restored")
    tools_reinitialized: bool = Field(default=False, description="Whether tools were reinitialized")
    
    # Timing information
    recovery_start_time: datetime = Field(default_factory=datetime.now, description="Recovery start time")
    recovery_end_time: Optional[datetime] = Field(None, description="Recovery completion time")
    recovery_duration: Optional[float] = Field(None, description="Recovery duration in seconds")
    
    # Recovery data
    recovered_data: Dict[str, Any] = Field(default_factory=dict, description="Recovered session data")
    missing_data: List[str] = Field(default_factory=list, description="List of missing data items")
    
    # Error information
    errors: List[str] = Field(default_factory=list, description="Recovery errors encountered")
    warnings: List[str] = Field(default_factory=list, description="Recovery warnings")
    
    def complete_recovery(self, success: bool = True):
        """Mark recovery as completed."""
        self.recovery_end_time = datetime.now()
        self.recovery_duration = (
            self.recovery_end_time - self.recovery_start_time
        ).total_seconds()
        self.status = RecoveryStatus.COMPLETED if success else RecoveryStatus.FAILED


class SessionRecovery:
    """
    🔄 Advanced Session Recovery System
    
    Provides seamless session recovery capabilities with automatic state
    preservation, context restoration, and comprehensive recovery tracking.
    """
    
    def __init__(
        self,
        session_manager: SessionManager,
        conversation_history: ConversationHistory,
        db_path: str = "data/session_recovery.db"
    ):
        self.session_manager = session_manager
        self.conversation_history = conversation_history
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Recovery state tracking
        self.active_recoveries: Dict[str, RecoveryResult] = {}
        
        # Initialize database
        self._init_database()
        
        # Start background recovery monitoring
        self._start_recovery_monitoring()
    
    def _init_database(self) -> None:
        """Initialize session recovery database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS recovery_logs (
                    recovery_id TEXT PRIMARY KEY,
                    session_id TEXT NOT NULL,
                    status TEXT NOT NULL,
                    recovery_start_time TIMESTAMP NOT NULL,
                    recovery_end_time TIMESTAMP,
                    recovery_duration REAL,
                    messages_recovered INTEGER DEFAULT 0,
                    context_restored BOOLEAN DEFAULT FALSE,
                    tools_reinitialized BOOLEAN DEFAULT FALSE,
                    recovered_data_json TEXT DEFAULT '{}',
                    missing_data_json TEXT DEFAULT '[]',
                    errors_json TEXT DEFAULT '[]',
                    warnings_json TEXT DEFAULT '[]'
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS session_snapshots (
                    snapshot_id TEXT PRIMARY KEY,
                    session_id TEXT NOT NULL,
                    snapshot_time TIMESTAMP NOT NULL,
                    session_data_json TEXT NOT NULL,
                    context_data_json TEXT NOT NULL,
                    message_count INTEGER DEFAULT 0,
                    is_automatic BOOLEAN DEFAULT TRUE
                )
            """)
            
            # Create indexes
            conn.execute("CREATE INDEX IF NOT EXISTS idx_recovery_session_id ON recovery_logs(session_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_snapshots_session_id ON session_snapshots(session_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_snapshots_time ON session_snapshots(snapshot_time)")
    
    def _start_recovery_monitoring(self) -> None:
        """Start background recovery monitoring."""
        async def monitoring_loop():
            while True:
                try:
                    await self._check_for_orphaned_sessions()
                    await self._create_automatic_snapshots()
                    await self._cleanup_old_snapshots()
                    await asyncio.sleep(60)  # Check every minute
                except Exception as e:
                    logger.error(f"Recovery monitoring failed: {e}")
                    await asyncio.sleep(300)  # Wait longer on error
        
        # Start monitoring task
        asyncio.create_task(monitoring_loop())
    
    async def create_session_snapshot(
        self,
        session_id: str,
        is_automatic: bool = True
    ) -> bool:
        """Create a snapshot of session state for recovery."""
        try:
            # Get session data
            session = await self.session_manager.get_session(session_id)
            if not session:
                logger.warning(f"Session {session_id} not found for snapshot")
                return False
            
            # Get recent conversation messages
            messages = await self.conversation_history.get_session_messages(
                session_id, limit=50
            )
            
            # Create snapshot
            snapshot_id = f"snapshot_{session_id}_{int(datetime.now().timestamp())}"
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO session_snapshots (
                        snapshot_id, session_id, snapshot_time,
                        session_data_json, context_data_json,
                        message_count, is_automatic
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    snapshot_id,
                    session_id,
                    datetime.now().isoformat(),
                    json.dumps(session.model_dump()),
                    json.dumps(session.context_data),
                    len(messages),
                    is_automatic
                ))
            
            logger.debug(f"Created snapshot {snapshot_id} for session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create session snapshot: {e}")
            return False
    
    async def recover_session(
        self,
        session_id: str,
        recovery_point: Optional[datetime] = None
    ) -> RecoveryResult:
        """
        Recover a session from the last known good state.
        
        Args:
            session_id: Session to recover
            recovery_point: Optional specific recovery point (uses latest if None)
        
        Returns:
            RecoveryResult with recovery details
        """
        recovery_result = RecoveryResult(
            session_id=session_id,
            status=RecoveryStatus.IN_PROGRESS
        )
        
        # Track active recovery
        self.active_recoveries[session_id] = recovery_result
        
        try:
            logger.info(f"Starting recovery for session {session_id}")
            
            # Step 1: Find the best recovery snapshot
            snapshot_data = await self._find_recovery_snapshot(session_id, recovery_point)
            if not snapshot_data:
                recovery_result.errors.append("No recovery snapshot found")
                recovery_result.status = RecoveryStatus.FAILED
                return recovery_result
            
            # Step 2: Restore session data
            session_restored = await self._restore_session_data(
                session_id, snapshot_data, recovery_result
            )
            
            if not session_restored:
                recovery_result.errors.append("Failed to restore session data")
                recovery_result.status = RecoveryStatus.FAILED
                return recovery_result
            
            # Step 3: Restore conversation history
            messages_restored = await self._restore_conversation_history(
                session_id, recovery_point, recovery_result
            )
            
            # Step 4: Restore context and tools
            context_restored = await self._restore_session_context(
                session_id, snapshot_data, recovery_result
            )
            
            # Step 5: Validate recovery
            validation_success = await self._validate_recovery(session_id, recovery_result)
            
            # Determine final status
            if validation_success and messages_restored and context_restored:
                recovery_result.status = RecoveryStatus.COMPLETED
                logger.info(f"Successfully recovered session {session_id}")
            elif messages_restored or context_restored:
                recovery_result.status = RecoveryStatus.PARTIAL
                recovery_result.warnings.append("Partial recovery - some data may be missing")
                logger.warning(f"Partial recovery for session {session_id}")
            else:
                recovery_result.status = RecoveryStatus.FAILED
                logger.error(f"Failed to recover session {session_id}")
            
            # Complete recovery
            recovery_result.complete_recovery(
                recovery_result.status in [RecoveryStatus.COMPLETED, RecoveryStatus.PARTIAL]
            )
            
            # Log recovery result
            await self._log_recovery_result(recovery_result)
            
            return recovery_result
            
        except Exception as e:
            logger.error(f"Session recovery failed: {e}")
            recovery_result.errors.append(f"Recovery exception: {str(e)}")
            recovery_result.status = RecoveryStatus.FAILED
            recovery_result.complete_recovery(False)
            return recovery_result
        
        finally:
            # Remove from active recoveries
            if session_id in self.active_recoveries:
                del self.active_recoveries[session_id]
    
    async def _find_recovery_snapshot(
        self,
        session_id: str,
        recovery_point: Optional[datetime] = None
    ) -> Optional[Dict[str, Any]]:
        """Find the best recovery snapshot for a session."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                if recovery_point:
                    # Find snapshot closest to recovery point
                    cursor = conn.execute("""
                        SELECT * FROM session_snapshots 
                        WHERE session_id = ? AND snapshot_time <= ?
                        ORDER BY snapshot_time DESC 
                        LIMIT 1
                    """, (session_id, recovery_point.isoformat()))
                else:
                    # Find latest snapshot
                    cursor = conn.execute("""
                        SELECT * FROM session_snapshots 
                        WHERE session_id = ?
                        ORDER BY snapshot_time DESC 
                        LIMIT 1
                    """, (session_id,))
                
                row = cursor.fetchone()
                if row:
                    return {
                        'snapshot_id': row['snapshot_id'],
                        'session_data': json.loads(row['session_data_json']),
                        'context_data': json.loads(row['context_data_json']),
                        'snapshot_time': datetime.fromisoformat(row['snapshot_time']),
                        'message_count': row['message_count']
                    }
                
        except Exception as e:
            logger.error(f"Failed to find recovery snapshot: {e}")
        
        return None

    async def _restore_session_data(
        self,
        session_id: str,
        snapshot_data: Dict[str, Any],
        recovery_result: RecoveryResult
    ) -> bool:
        """Restore session data from snapshot."""
        try:
            session_data = snapshot_data['session_data']

            # Create session object from snapshot
            session = Session(**session_data)
            session.status = SessionStatus.RECOVERING

            # Update session in manager
            success = await self.session_manager.update_session(session)

            if success:
                recovery_result.recovered_data['session'] = session_data
                logger.debug(f"Restored session data for {session_id}")
                return True
            else:
                recovery_result.errors.append("Failed to update session in manager")
                return False

        except Exception as e:
            logger.error(f"Failed to restore session data: {e}")
            recovery_result.errors.append(f"Session data restoration failed: {str(e)}")
            return False

    async def _restore_conversation_history(
        self,
        session_id: str,
        recovery_point: Optional[datetime],
        recovery_result: RecoveryResult
    ) -> bool:
        """Restore conversation history up to recovery point."""
        try:
            # Get messages from conversation history
            messages = await self.conversation_history.get_session_messages(
                session_id, limit=1000
            )

            # Filter messages by recovery point if specified
            if recovery_point:
                messages = [
                    msg for msg in messages
                    if msg.timestamp <= recovery_point
                ]

            recovery_result.messages_recovered = len(messages)
            recovery_result.recovered_data['messages'] = len(messages)

            logger.debug(f"Restored {len(messages)} messages for session {session_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to restore conversation history: {e}")
            recovery_result.errors.append(f"Conversation history restoration failed: {str(e)}")
            return False

    async def _restore_session_context(
        self,
        session_id: str,
        snapshot_data: Dict[str, Any],
        recovery_result: RecoveryResult
    ) -> bool:
        """Restore session context and reinitialize tools."""
        try:
            context_data = snapshot_data['context_data']

            # Get session and update context
            session = await self.session_manager.get_session(session_id)
            if session:
                session.context_data = context_data
                session.status = SessionStatus.ACTIVE  # Mark as active after recovery

                success = await self.session_manager.update_session(session)

                if success:
                    recovery_result.context_restored = True
                    recovery_result.tools_reinitialized = True
                    recovery_result.recovered_data['context'] = context_data
                    logger.debug(f"Restored context for session {session_id}")
                    return True
                else:
                    recovery_result.errors.append("Failed to update session context")
                    return False
            else:
                recovery_result.errors.append("Session not found during context restoration")
                return False

        except Exception as e:
            logger.error(f"Failed to restore session context: {e}")
            recovery_result.errors.append(f"Context restoration failed: {str(e)}")
            return False

    async def _validate_recovery(
        self,
        session_id: str,
        recovery_result: RecoveryResult
    ) -> bool:
        """Validate that recovery was successful."""
        try:
            # Check if session exists and is accessible
            session = await self.session_manager.get_session(session_id)
            if not session:
                recovery_result.errors.append("Session not accessible after recovery")
                return False

            # Check if conversation history is accessible
            messages = await self.conversation_history.get_session_messages(session_id, limit=1)

            # Validate session status
            if session.status not in [SessionStatus.ACTIVE, SessionStatus.PAUSED]:
                recovery_result.warnings.append("Session status may not be correct")

            logger.debug(f"Recovery validation passed for session {session_id}")
            return True

        except Exception as e:
            logger.error(f"Recovery validation failed: {e}")
            recovery_result.errors.append(f"Recovery validation failed: {str(e)}")
            return False

    async def _log_recovery_result(self, recovery_result: RecoveryResult) -> None:
        """Log recovery result to database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO recovery_logs (
                        recovery_id, session_id, status, recovery_start_time,
                        recovery_end_time, recovery_duration, messages_recovered,
                        context_restored, tools_reinitialized, recovered_data_json,
                        missing_data_json, errors_json, warnings_json
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    f"recovery_{recovery_result.session_id}_{int(recovery_result.recovery_start_time.timestamp())}",
                    recovery_result.session_id,
                    recovery_result.status.value,
                    recovery_result.recovery_start_time.isoformat(),
                    recovery_result.recovery_end_time.isoformat() if recovery_result.recovery_end_time else None,
                    recovery_result.recovery_duration,
                    recovery_result.messages_recovered,
                    recovery_result.context_restored,
                    recovery_result.tools_reinitialized,
                    json.dumps(recovery_result.recovered_data),
                    json.dumps(recovery_result.missing_data),
                    json.dumps(recovery_result.errors),
                    json.dumps(recovery_result.warnings)
                ))

        except Exception as e:
            logger.error(f"Failed to log recovery result: {e}")

    async def _check_for_orphaned_sessions(self) -> None:
        """Check for sessions that may need recovery."""
        try:
            # Get all active sessions
            stats = self.session_manager.get_session_stats()

            # Check for sessions that haven't been accessed recently
            cutoff_time = datetime.now() - timedelta(hours=1)

            for session_id, session in self.session_manager.active_sessions.items():
                if (session.last_accessed < cutoff_time and
                    session.status == SessionStatus.ACTIVE and
                    session_id not in self.active_recoveries):

                    logger.info(f"Found potentially orphaned session: {session_id}")
                    # Could trigger automatic recovery here if needed

        except Exception as e:
            logger.error(f"Orphaned session check failed: {e}")

    async def _create_automatic_snapshots(self) -> None:
        """Create automatic snapshots for active sessions."""
        try:
            for session_id, session in self.session_manager.active_sessions.items():
                # Create snapshot every 30 minutes for active sessions
                if session.status == SessionStatus.ACTIVE:
                    last_snapshot_time = await self._get_last_snapshot_time(session_id)

                    if (not last_snapshot_time or
                        datetime.now() - last_snapshot_time > timedelta(minutes=30)):

                        await self.create_session_snapshot(session_id, is_automatic=True)

        except Exception as e:
            logger.error(f"Automatic snapshot creation failed: {e}")

    async def _get_last_snapshot_time(self, session_id: str) -> Optional[datetime]:
        """Get the time of the last snapshot for a session."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT MAX(snapshot_time) FROM session_snapshots
                    WHERE session_id = ?
                """, (session_id,))

                row = cursor.fetchone()
                if row and row[0]:
                    return datetime.fromisoformat(row[0])

        except Exception as e:
            logger.error(f"Failed to get last snapshot time: {e}")

        return None

    async def _cleanup_old_snapshots(self) -> None:
        """Clean up old snapshots to save space."""
        try:
            # Keep snapshots for 7 days
            cutoff_time = datetime.now() - timedelta(days=7)

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    DELETE FROM session_snapshots
                    WHERE snapshot_time < ? AND is_automatic = TRUE
                """, (cutoff_time.isoformat(),))

                deleted_count = cursor.rowcount
                if deleted_count > 0:
                    logger.debug(f"Cleaned up {deleted_count} old snapshots")

        except Exception as e:
            logger.error(f"Snapshot cleanup failed: {e}")

    def get_recovery_stats(self) -> Dict[str, Any]:
        """Get recovery system statistics."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Recovery statistics
                cursor = conn.execute("""
                    SELECT
                        COUNT(*) as total_recoveries,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_recoveries,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_recoveries,
                        AVG(recovery_duration) as avg_recovery_time
                    FROM recovery_logs
                """)

                recovery_stats = cursor.fetchone()

                # Snapshot statistics
                cursor = conn.execute("""
                    SELECT
                        COUNT(*) as total_snapshots,
                        COUNT(DISTINCT session_id) as sessions_with_snapshots
                    FROM session_snapshots
                """)

                snapshot_stats = cursor.fetchone()

                return {
                    "total_recoveries": recovery_stats[0] if recovery_stats else 0,
                    "successful_recoveries": recovery_stats[1] if recovery_stats else 0,
                    "failed_recoveries": recovery_stats[2] if recovery_stats else 0,
                    "average_recovery_time": recovery_stats[3] if recovery_stats else 0,
                    "total_snapshots": snapshot_stats[0] if snapshot_stats else 0,
                    "sessions_with_snapshots": snapshot_stats[1] if snapshot_stats else 0,
                    "active_recoveries": len(self.active_recoveries)
                }

        except Exception as e:
            logger.error(f"Failed to get recovery stats: {e}")
            return {}


# Global session recovery instance
_session_recovery: Optional[SessionRecovery] = None


def get_session_recovery() -> SessionRecovery:
    """Get the global session recovery instance."""
    global _session_recovery
    if _session_recovery is None:
        from .session_manager import get_session_manager
        from .conversation_history import ConversationHistory

        session_manager = get_session_manager()
        conversation_history = ConversationHistory()
        _session_recovery = SessionRecovery(session_manager, conversation_history)

    return _session_recovery
