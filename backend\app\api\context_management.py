"""
🎯 Context Management API Endpoints

This module provides comprehensive context management API endpoints:
- Auto-compaction system with user-friendly feedback
- Context optimization and performance metrics
- Relevance scoring and analytics
- Context caching and performance optimization
- Smart summarization with quality control
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field
import logging

from ..context import (
    get_context_optimizer,
    get_auto_compactor,
    get_relevance_scorer,
    get_context_cache,
    get_context_summarizer,
    OptimizationStrategy,
    CompressionStrategy,
    ScoringCriteria,
    SummarizationStrategy,
    SummaryQuality,
    SummarizationConfig,
    Message
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/context", tags=["context_management"])

# Request/Response Models
class OptimizeContextRequest(BaseModel):
    """Request model for context optimization."""
    content: str = Field(..., description="Content to optimize")
    strategy: OptimizationStrategy = Field(default=OptimizationStrategy.ADAPTIVE, description="Optimization strategy")
    target_reduction: Optional[float] = Field(None, description="Target reduction percentage")
    preserve_code: bool = Field(default=True, description="Preserve code blocks")
    preserve_decisions: bool = Field(default=True, description="Preserve decisions")


class CompactContextRequest(BaseModel):
    """Request model for context compaction."""
    messages: List[Dict[str, Any]] = Field(..., description="Messages to compact")
    strategy: CompressionStrategy = Field(default=CompressionStrategy.ADAPTIVE, description="Compression strategy")
    preserve_recent: int = Field(default=10, description="Number of recent messages to preserve")
    quality_threshold: float = Field(default=0.85, description="Minimum quality threshold")


class ScoreRelevanceRequest(BaseModel):
    """Request model for relevance scoring."""
    message_content: str = Field(..., description="Message content to score")
    context_messages: List[Dict[str, Any]] = Field(default_factory=list, description="Context messages")
    current_topic: Optional[str] = Field(None, description="Current conversation topic")
    user_focus: Optional[List[str]] = Field(None, description="User focus keywords")


class SummarizeContentRequest(BaseModel):
    """Request model for content summarization."""
    content: str = Field(..., description="Content to summarize")
    strategy: SummarizationStrategy = Field(default=SummarizationStrategy.HYBRID, description="Summarization strategy")
    quality: SummaryQuality = Field(default=SummaryQuality.MEDIUM, description="Target quality level")
    max_length: int = Field(default=500, description="Maximum summary length")
    preserve_code: bool = Field(default=True, description="Preserve code blocks")
    context_type: str = Field(default="conversation", description="Context type for template selection")


# Context Optimization Endpoints
@router.post("/optimize")
async def optimize_context(request: OptimizeContextRequest):
    """Optimize context content to reduce token usage."""
    try:
        context_optimizer = get_context_optimizer()
        
        result = context_optimizer.optimize_context(
            content=request.content,
            strategy=request.strategy,
            target_reduction=request.target_reduction,
            preserve_code=request.preserve_code,
            preserve_decisions=request.preserve_decisions
        )
        
        return {
            "optimization_result": result,
            "user_message": f"✅ Context optimized! Reduced {result.reduction_percentage:.1f}% tokens while maintaining {result.quality_score:.0%} quality.",
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Context optimization failed: {e}")
        raise HTTPException(status_code=500, detail=f"Context optimization failed: {str(e)}")


@router.get("/optimize/stats")
async def get_optimization_stats():
    """Get context optimization statistics."""
    try:
        context_optimizer = get_context_optimizer()
        stats = context_optimizer.get_optimization_stats()
        
        return {
            "optimization_stats": stats,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get optimization stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get optimization stats: {str(e)}")


# Auto-Compaction Endpoints
@router.post("/compact")
async def compact_context(request: CompactContextRequest):
    """🎯 Auto-compact context with intelligent compression."""
    try:
        auto_compactor = get_auto_compactor()
        
        # Convert dict messages to Message objects
        messages = []
        for msg_dict in request.messages:
            message = Message(
                role=msg_dict.get("role", "user"),
                content=msg_dict.get("content", ""),
                timestamp=datetime.fromisoformat(msg_dict.get("timestamp", datetime.now().isoformat())),
                metadata=msg_dict.get("metadata", {})
            )
            messages.append(message)
        
        # Check if compaction is needed
        if not auto_compactor.should_compact(messages):
            return {
                "compaction_needed": False,
                "user_message": "🎯 Context is already optimized! No compaction needed.",
                "original_messages": len(messages),
                "timestamp": datetime.now()
            }
        
        # Perform compaction
        result = await auto_compactor.compact_context(
            messages=messages,
            strategy=request.strategy,
            preserve_recent=request.preserve_recent,
            quality_threshold=request.quality_threshold
        )
        
        return {
            "compaction_needed": True,
            "compaction_result": result,
            "user_message": result.user_notification,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Context compaction failed: {e}")
        raise HTTPException(status_code=500, detail=f"Context compaction failed: {str(e)}")


@router.get("/compact/should-compact")
async def should_compact_context(
    message_count: int = Query(..., description="Number of messages"),
    total_tokens: int = Query(..., description="Total token count")
):
    """Check if context should be compacted."""
    try:
        auto_compactor = get_auto_compactor()
        
        # Create dummy messages for threshold check
        dummy_messages = [
            Message(role="user", content="x" * (total_tokens // message_count))
            for _ in range(message_count)
        ]
        
        should_compact = auto_compactor.should_compact(dummy_messages)
        
        return {
            "should_compact": should_compact,
            "threshold": auto_compactor.compression_threshold,
            "current_tokens": total_tokens,
            "recommendation": (
                "🎯 Auto-compaction recommended to optimize performance!"
                if should_compact else
                "✅ Context size is optimal, no compaction needed."
            ),
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to check compaction need: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to check compaction need: {str(e)}")


@router.get("/compact/stats")
async def get_compaction_stats():
    """Get auto-compaction statistics."""
    try:
        auto_compactor = get_auto_compactor()
        stats = auto_compactor.get_compaction_stats()
        
        return {
            "compaction_stats": stats,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get compaction stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get compaction stats: {str(e)}")


# Relevance Scoring Endpoints
@router.post("/relevance/score")
async def score_message_relevance(request: ScoreRelevanceRequest):
    """Score message relevance using multiple criteria."""
    try:
        relevance_scorer = get_relevance_scorer()
        
        score = relevance_scorer.score_message_relevance(
            message_content=request.message_content,
            message_timestamp=datetime.now(),
            context_messages=request.context_messages,
            current_topic=request.current_topic,
            user_focus=request.user_focus
        )
        
        return {
            "relevance_score": score,
            "interpretation": (
                "🎯 Highly relevant" if score.overall_score >= 0.8 else
                "✅ Moderately relevant" if score.overall_score >= 0.5 else
                "📊 Low relevance"
            ),
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Relevance scoring failed: {e}")
        raise HTTPException(status_code=500, detail=f"Relevance scoring failed: {str(e)}")


@router.post("/relevance/score-multiple")
async def score_multiple_messages(
    messages: List[Dict[str, Any]],
    current_topic: Optional[str] = None,
    user_focus: Optional[List[str]] = None
):
    """Score relevance for multiple messages."""
    try:
        relevance_scorer = get_relevance_scorer()
        
        scores = relevance_scorer.score_multiple_messages(
            messages=messages,
            current_topic=current_topic,
            user_focus=user_focus
        )
        
        # Calculate summary statistics
        if scores:
            avg_score = sum(score.overall_score for score in scores) / len(scores)
            high_relevance_count = sum(1 for score in scores if score.overall_score >= 0.8)
        else:
            avg_score = 0.0
            high_relevance_count = 0
        
        return {
            "relevance_scores": scores,
            "summary": {
                "total_messages": len(scores),
                "average_relevance": avg_score,
                "high_relevance_count": high_relevance_count,
                "high_relevance_percentage": (high_relevance_count / len(scores) * 100) if scores else 0
            },
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Multiple relevance scoring failed: {e}")
        raise HTTPException(status_code=500, detail=f"Multiple relevance scoring failed: {str(e)}")


@router.get("/relevance/stats")
async def get_relevance_stats():
    """Get relevance scoring statistics."""
    try:
        relevance_scorer = get_relevance_scorer()
        stats = relevance_scorer.get_relevance_statistics()
        
        return {
            "relevance_stats": stats,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get relevance stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get relevance stats: {str(e)}")


# Context Caching Endpoints
@router.get("/cache/get/{cache_key}")
async def get_cached_content(cache_key: str):
    """Get content from context cache."""
    try:
        context_cache = get_context_cache()
        content = context_cache.get(cache_key)
        
        if content is None:
            return {
                "cache_hit": False,
                "message": "Content not found in cache",
                "timestamp": datetime.now()
            }
        
        return {
            "cache_hit": True,
            "content": content,
            "message": "✅ Content retrieved from cache",
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Cache retrieval failed: {e}")
        raise HTTPException(status_code=500, detail=f"Cache retrieval failed: {str(e)}")


@router.post("/cache/put")
async def cache_content(
    cache_key: str,
    content: str,
    content_type: str = "general",
    ttl_seconds: Optional[int] = None
):
    """Put content into context cache."""
    try:
        context_cache = get_context_cache()
        
        success = context_cache.put(
            cache_key=cache_key,
            content=content,
            content_type=content_type,
            ttl_seconds=ttl_seconds
        )
        
        return {
            "cached": success,
            "cache_key": cache_key,
            "message": "✅ Content cached successfully" if success else "❌ Failed to cache content",
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Cache storage failed: {e}")
        raise HTTPException(status_code=500, detail=f"Cache storage failed: {str(e)}")


@router.get("/cache/stats")
async def get_cache_stats():
    """Get context cache statistics."""
    try:
        context_cache = get_context_cache()
        stats = context_cache.get_cache_stats()
        
        return {
            "cache_stats": stats,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get cache stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get cache stats: {str(e)}")


@router.post("/cache/optimize")
async def optimize_cache():
    """Optimize context cache performance."""
    try:
        context_cache = get_context_cache()
        optimization_result = context_cache.optimize_cache()
        
        return {
            "optimization_result": optimization_result,
            "message": f"🚀 Cache optimized! Saved {optimization_result['memory_saved']} bytes.",
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Cache optimization failed: {e}")
        raise HTTPException(status_code=500, detail=f"Cache optimization failed: {str(e)}")


# Content Summarization Endpoints
@router.post("/summarize")
async def summarize_content(request: SummarizeContentRequest):
    """Summarize content efficiently with quality control."""
    try:
        context_summarizer = get_context_summarizer()
        
        config = SummarizationConfig(
            strategy=request.strategy,
            quality=request.quality,
            max_length=request.max_length,
            preserve_code=request.preserve_code
        )
        
        result = context_summarizer.summarize_content(
            content=request.content,
            config=config,
            context_type=request.context_type
        )
        
        return {
            "summarization_result": result,
            "user_message": (
                f"📝 Content summarized! Reduced to {result.compression_ratio:.0%} of original size "
                f"with {result.semantic_score:.0%} quality preservation."
            ),
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Content summarization failed: {e}")
        raise HTTPException(status_code=500, detail=f"Content summarization failed: {str(e)}")


@router.get("/summarize/stats")
async def get_summarization_stats():
    """Get content summarization statistics."""
    try:
        context_summarizer = get_context_summarizer()
        stats = context_summarizer.get_summarization_stats()
        
        return {
            "summarization_stats": stats,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Failed to get summarization stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get summarization stats: {str(e)}")


# System Health and Overview
@router.get("/health")
async def get_context_system_health():
    """Get overall context management system health."""
    try:
        # Get stats from all components
        context_optimizer = get_context_optimizer()
        auto_compactor = get_auto_compactor()
        relevance_scorer = get_relevance_scorer()
        context_cache = get_context_cache()
        context_summarizer = get_context_summarizer()
        
        optimization_stats = context_optimizer.get_optimization_stats()
        compaction_stats = auto_compactor.get_compaction_stats()
        relevance_stats = relevance_scorer.get_relevance_statistics()
        cache_stats = context_cache.get_cache_stats()
        summarization_stats = context_summarizer.get_summarization_stats()
        
        # Calculate overall health
        total_operations = (
            optimization_stats["total_optimizations"] +
            compaction_stats["total_compactions"] +
            relevance_stats["total_messages_scored"] +
            cache_stats["total_requests"] +
            summarization_stats["total_summaries"]
        )
        
        return {
            "healthy": True,
            "system_status": "operational",
            "components": {
                "context_optimizer": {
                    "status": "healthy",
                    "total_optimizations": optimization_stats["total_optimizations"],
                    "average_reduction": optimization_stats["average_reduction"]
                },
                "auto_compactor": {
                    "status": "healthy",
                    "total_compactions": compaction_stats["total_compactions"],
                    "average_compression": compaction_stats["average_compression_ratio"]
                },
                "relevance_scorer": {
                    "status": "healthy",
                    "messages_scored": relevance_stats["total_messages_scored"]
                },
                "context_cache": {
                    "status": "healthy",
                    "hit_rate": cache_stats["hit_rate"],
                    "cache_size": cache_stats["cache_size"]
                },
                "context_summarizer": {
                    "status": "healthy",
                    "total_summaries": summarization_stats["total_summaries"],
                    "average_quality": summarization_stats["average_semantic_score"]
                }
            },
            "overall_metrics": {
                "total_operations": total_operations,
                "system_uptime": "operational"
            },
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Context system health check failed: {e}")
        return {
            "healthy": False,
            "error": str(e),
            "timestamp": datetime.now()
        }
