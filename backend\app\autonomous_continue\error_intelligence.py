"""
Error Intelligence System for Autonomous Continue Mode - Phase 8C

Provides intelligent error pattern recognition, categorization, and resolution
suggestions for autonomous development loops.
"""

import re
import logging
import hashlib
from typing import List, Dict, Optional, Set, Tuple
from datetime import datetime, timedelta
from collections import defaultdict

from .models import (
    ErrorPattern, ErrorSeverity, IterationResult, ContinueSession
)

logger = logging.getLogger(__name__)


class ErrorIntelligenceSystem:
    """
    Intelligent error analysis and pattern recognition system.
    
    Provides:
    - Error categorization and analysis
    - Pattern detection for recurring issues
    - Error history tracking
    - Intelligent resolution suggestions
    - Learning from successful resolutions
    """
    
    def __init__(self):
        """Initialize the error intelligence system."""
        self.error_patterns: Dict[str, ErrorPattern] = {}
        self.error_history: List[Dict] = []
        self.resolution_strategies: Dict[str, List[str]] = {}
        
        # Error classification patterns
        self.error_classifiers = {
            'syntax_error': [
                r'SyntaxError',
                r'IndentationError',
                r'TabError',
                r'invalid syntax',
                r'unexpected token'
            ],
            'import_error': [
                r'ImportError',
                r'ModuleNotFoundError',
                r'No module named',
                r'cannot import name'
            ],
            'type_error': [
                r'TypeError',
                r'AttributeError',
                r'object has no attribute',
                r'not callable'
            ],
            'value_error': [
                r'ValueError',
                r'KeyError',
                r'IndexError',
                r'invalid literal'
            ],
            'file_error': [
                r'FileNotFoundError',
                r'PermissionError',
                r'IsADirectoryError',
                r'No such file or directory'
            ],
            'network_error': [
                r'ConnectionError',
                r'TimeoutError',
                r'HTTPError',
                r'URLError'
            ],
            'git_error': [
                r'git',
                r'repository',
                r'branch',
                r'merge conflict',
                r'uncommitted changes'
            ],
            'dependency_error': [
                r'package',
                r'version',
                r'requirement',
                r'dependency'
            ]
        }
        
        # Severity classification
        self.severity_patterns = {
            ErrorSeverity.CRITICAL: [
                r'fatal',
                r'critical',
                r'system',
                r'crash',
                r'abort'
            ],
            ErrorSeverity.HIGH: [
                r'error',
                r'failed',
                r'exception',
                r'broken'
            ],
            ErrorSeverity.MEDIUM: [
                r'warning',
                r'deprecated',
                r'invalid'
            ],
            ErrorSeverity.LOW: [
                r'info',
                r'notice',
                r'debug'
            ]
        }
        
        logger.info("🧠 Error Intelligence System initialized")
    
    async def analyze_error(self, iteration_result: IterationResult) -> Optional[ErrorPattern]:
        """
        Analyze an error and create or update error pattern.
        
        Args:
            iteration_result: Failed iteration result
            
        Returns:
            ErrorPattern if error was analyzed, None otherwise
        """
        if iteration_result.success or not iteration_result.error_message:
            return None
        
        try:
            error_message = iteration_result.error_message
            
            # Classify error type
            error_type = self._classify_error_type(error_message)
            
            # Determine severity
            severity = self._classify_severity(error_message)
            
            # Generate pattern ID
            pattern_id = self._generate_pattern_id(error_type, error_message)
            
            # Check if pattern exists
            if pattern_id in self.error_patterns:
                # Update existing pattern
                pattern = self.error_patterns[pattern_id]
                pattern.occurrence_count += 1
                pattern.last_seen = datetime.now()
                
                # Update file patterns
                if iteration_result.files_changed:
                    pattern.file_patterns.extend(iteration_result.files_changed)
                    pattern.file_patterns = list(set(pattern.file_patterns))
                
                logger.debug(f"🔄 Updated error pattern: {pattern_id} (count: {pattern.occurrence_count})")
            else:
                # Create new pattern
                pattern = ErrorPattern(
                    pattern_id=pattern_id,
                    error_type=error_type,
                    error_message=self._normalize_error_message(error_message),
                    severity=severity,
                    file_patterns=iteration_result.files_changed.copy(),
                    tags=self._extract_error_tags(error_message)
                )
                
                self.error_patterns[pattern_id] = pattern
                logger.info(f"🆕 New error pattern detected: {error_type} - {pattern_id}")
            
            # Add to error history
            self.error_history.append({
                'timestamp': datetime.now(),
                'pattern_id': pattern_id,
                'iteration_id': iteration_result.iteration_id,
                'error_message': error_message,
                'files_changed': iteration_result.files_changed
            })
            
            return pattern
            
        except Exception as e:
            logger.error(f"❌ Error analysis failed: {e}")
            return None
    
    async def suggest_resolution(self, error_pattern: ErrorPattern) -> List[str]:
        """
        Suggest resolution strategies for an error pattern.
        
        Args:
            error_pattern: Error pattern to resolve
            
        Returns:
            List of resolution suggestions
        """
        try:
            suggestions = []
            
            # Get type-specific suggestions
            type_suggestions = self._get_type_specific_suggestions(error_pattern.error_type)
            suggestions.extend(type_suggestions)
            
            # Get pattern-specific suggestions
            if error_pattern.successful_resolutions:
                suggestions.extend(error_pattern.successful_resolutions)
            
            # Get general suggestions based on error message
            general_suggestions = self._get_general_suggestions(error_pattern.error_message)
            suggestions.extend(general_suggestions)
            
            # Remove duplicates and prioritize
            unique_suggestions = list(dict.fromkeys(suggestions))
            
            logger.debug(f"💡 Generated {len(unique_suggestions)} resolution suggestions for {error_pattern.pattern_id}")
            
            return unique_suggestions[:5]  # Return top 5 suggestions
            
        except Exception as e:
            logger.error(f"❌ Resolution suggestion failed: {e}")
            return []
    
    async def learn_from_success(self, error_pattern_id: str, resolution_strategy: str):
        """
        Learn from a successful error resolution.
        
        Args:
            error_pattern_id: ID of the resolved error pattern
            resolution_strategy: Strategy that led to success
        """
        try:
            if error_pattern_id in self.error_patterns:
                pattern = self.error_patterns[error_pattern_id]
                
                # Add to successful resolutions
                if resolution_strategy not in pattern.successful_resolutions:
                    pattern.successful_resolutions.append(resolution_strategy)
                
                # Update confidence score
                pattern.confidence_score = min(pattern.confidence_score + 0.1, 1.0)
                
                logger.info(f"📚 Learned successful resolution for {error_pattern_id}: {resolution_strategy}")
            
        except Exception as e:
            logger.error(f"❌ Learning from success failed: {e}")
    
    async def learn_from_failure(self, error_pattern_id: str, failed_strategy: str):
        """
        Learn from a failed resolution attempt.
        
        Args:
            error_pattern_id: ID of the error pattern
            failed_strategy: Strategy that failed
        """
        try:
            if error_pattern_id in self.error_patterns:
                pattern = self.error_patterns[error_pattern_id]
                
                # Add to failed resolutions
                if failed_strategy not in pattern.failed_resolutions:
                    pattern.failed_resolutions.append(failed_strategy)
                
                # Decrease confidence score slightly
                pattern.confidence_score = max(pattern.confidence_score - 0.05, 0.1)
                
                logger.debug(f"📝 Recorded failed resolution for {error_pattern_id}: {failed_strategy}")
            
        except Exception as e:
            logger.error(f"❌ Learning from failure failed: {e}")
    
    def get_error_statistics(self) -> Dict[str, any]:
        """Get error statistics and insights."""
        try:
            total_patterns = len(self.error_patterns)
            total_occurrences = sum(pattern.occurrence_count for pattern in self.error_patterns.values())
            
            # Group by error type
            type_counts = defaultdict(int)
            severity_counts = defaultdict(int)
            
            for pattern in self.error_patterns.values():
                type_counts[pattern.error_type] += pattern.occurrence_count
                severity_counts[pattern.severity.value] += pattern.occurrence_count
            
            # Find most common patterns
            most_common = sorted(
                self.error_patterns.values(),
                key=lambda p: p.occurrence_count,
                reverse=True
            )[:5]
            
            # Recent error trends
            recent_errors = [
                entry for entry in self.error_history
                if entry['timestamp'] > datetime.now() - timedelta(hours=1)
            ]
            
            return {
                'total_patterns': total_patterns,
                'total_occurrences': total_occurrences,
                'type_distribution': dict(type_counts),
                'severity_distribution': dict(severity_counts),
                'most_common_patterns': [
                    {
                        'pattern_id': p.pattern_id,
                        'error_type': p.error_type,
                        'count': p.occurrence_count,
                        'severity': p.severity.value
                    }
                    for p in most_common
                ],
                'recent_errors_count': len(recent_errors),
                'patterns_with_resolutions': len([
                    p for p in self.error_patterns.values()
                    if p.successful_resolutions
                ])
            }
            
        except Exception as e:
            logger.error(f"❌ Error statistics generation failed: {e}")
            return {}
    
    def _classify_error_type(self, error_message: str) -> str:
        """Classify error type based on message content."""
        error_message_lower = error_message.lower()
        
        for error_type, patterns in self.error_classifiers.items():
            for pattern in patterns:
                if re.search(pattern, error_message_lower):
                    return error_type
        
        return 'unknown_error'
    
    def _classify_severity(self, error_message: str) -> ErrorSeverity:
        """Classify error severity based on message content."""
        error_message_lower = error_message.lower()
        
        for severity, patterns in self.severity_patterns.items():
            for pattern in patterns:
                if re.search(pattern, error_message_lower):
                    return severity
        
        return ErrorSeverity.MEDIUM
    
    def _generate_pattern_id(self, error_type: str, error_message: str) -> str:
        """Generate unique pattern ID for error."""
        # Normalize error message for pattern matching
        normalized = self._normalize_error_message(error_message)
        
        # Create hash from error type and normalized message
        content = f"{error_type}:{normalized}"
        pattern_hash = hashlib.md5(content.encode()).hexdigest()[:8]
        
        return f"{error_type}_{pattern_hash}"
    
    def _normalize_error_message(self, error_message: str) -> str:
        """Normalize error message for pattern matching."""
        # Remove file paths, line numbers, and other variable content
        normalized = error_message
        
        # Remove file paths
        normalized = re.sub(r'/[^\s]+\.py', '/path/to/file.py', normalized)
        normalized = re.sub(r'\\\\[^\s]+\.py', '\\\\path\\\\to\\\\file.py', normalized)
        
        # Remove line numbers
        normalized = re.sub(r'line \d+', 'line X', normalized)
        normalized = re.sub(r':\d+:', ':X:', normalized)
        
        # Remove specific values
        normalized = re.sub(r"'[^']*'", "'VALUE'", normalized)
        normalized = re.sub(r'"[^"]*"', '"VALUE"', normalized)
        
        # Remove numbers
        normalized = re.sub(r'\b\d+\b', 'N', normalized)
        
        return normalized.strip()
    
    def _extract_error_tags(self, error_message: str) -> List[str]:
        """Extract relevant tags from error message."""
        tags = []
        error_message_lower = error_message.lower()
        
        # Common error tags
        tag_patterns = {
            'async': r'async|await|coroutine',
            'import': r'import|module',
            'file': r'file|directory|path',
            'network': r'http|url|connection|timeout',
            'database': r'database|sql|query',
            'json': r'json|parse|decode',
            'encoding': r'encoding|utf|ascii',
            'permission': r'permission|access|denied'
        }
        
        for tag, pattern in tag_patterns.items():
            if re.search(pattern, error_message_lower):
                tags.append(tag)
        
        return tags
    
    def _get_type_specific_suggestions(self, error_type: str) -> List[str]:
        """Get suggestions specific to error type."""
        suggestions_map = {
            'syntax_error': [
                "Check for missing colons, parentheses, or brackets",
                "Verify proper indentation",
                "Look for typos in keywords",
                "Check for mixing tabs and spaces"
            ],
            'import_error': [
                "Verify the module is installed",
                "Check the module name spelling",
                "Ensure the module is in the Python path",
                "Check for circular imports"
            ],
            'type_error': [
                "Check variable types and method signatures",
                "Verify object attributes exist",
                "Ensure proper type conversion",
                "Check for None values"
            ],
            'file_error': [
                "Verify file path exists",
                "Check file permissions",
                "Ensure directory structure is correct",
                "Check for file locks"
            ],
            'git_error': [
                "Check git repository status",
                "Resolve merge conflicts",
                "Commit or stash changes",
                "Verify branch exists"
            ]
        }
        
        return suggestions_map.get(error_type, [])
    
    def _get_general_suggestions(self, error_message: str) -> List[str]:
        """Get general suggestions based on error message content."""
        suggestions = []
        error_lower = error_message.lower()
        
        if 'not found' in error_lower:
            suggestions.append("Check if the referenced item exists")
        
        if 'permission' in error_lower:
            suggestions.append("Check file/directory permissions")
        
        if 'timeout' in error_lower:
            suggestions.append("Increase timeout or check network connectivity")
        
        if 'memory' in error_lower:
            suggestions.append("Check memory usage and optimize data structures")
        
        if 'version' in error_lower:
            suggestions.append("Check version compatibility")
        
        return suggestions
