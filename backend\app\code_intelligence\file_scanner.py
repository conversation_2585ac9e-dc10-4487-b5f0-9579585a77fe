"""
File Scanner for Code Intelligence Hub - Phase 8B

Universal file scanning and metadata extraction for all programming languages.
"""

import os
import hashlib
import mimetypes
from typing import List, Dict, Optional, Any, Set
from pathlib import Path
from datetime import datetime
import logging

from .models import CodeLanguage

logger = logging.getLogger(__name__)


class FileScanner:
    """
    Universal file scanner for code intelligence.
    
    Scans workspace directories and extracts metadata from all supported
    programming languages and file types.
    """
    
    def __init__(self, workspace_root: Path):
        """Initialize the file scanner."""
        self.workspace_root = workspace_root
        
        # Language detection mappings
        self.extension_to_language = {
            '.py': CodeLanguage.PYTHON,
            '.js': CodeLanguage.JAVASCRIPT,
            '.jsx': CodeLanguage.JAVASCRIPT,
            '.ts': CodeLanguage.TYPESCRIPT,
            '.tsx': CodeLanguage.TYPESCRIPT,
            '.java': CodeLanguage.JAVA,
            '.cpp': CodeLanguage.CPP,
            '.cc': CodeLanguage.CPP,
            '.cxx': CodeLanguage.CPP,
            '.c': CodeLanguage.CPP,
            '.h': CodeLanguage.CPP,
            '.hpp': CodeLanguage.CPP,
            '.go': CodeLanguage.GO,
            '.rs': CodeLanguage.RUST,
            '.html': CodeLanguage.HTML,
            '.htm': CodeLanguage.HTML,
            '.css': CodeLanguage.CSS,
            '.scss': CodeLanguage.CSS,
            '.sass': CodeLanguage.CSS,
            '.sql': CodeLanguage.SQL,
            '.md': CodeLanguage.MARKDOWN,
            '.markdown': CodeLanguage.MARKDOWN,
            '.json': CodeLanguage.JSON,
            '.yaml': CodeLanguage.YAML,
            '.yml': CodeLanguage.YAML,
            '.sh': CodeLanguage.SHELL,
            '.bash': CodeLanguage.SHELL,
            '.zsh': CodeLanguage.SHELL,
            '.fish': CodeLanguage.SHELL,
            '.bat': CodeLanguage.SHELL,
            '.cmd': CodeLanguage.SHELL,
            '.ps1': CodeLanguage.SHELL,
        }
        
        # Default exclude patterns
        self.default_exclude_patterns = {
            # Version control
            '.git/*', '.svn/*', '.hg/*',
            # Dependencies
            'node_modules/*', 'venv/*', 'env/*', '__pycache__/*',
            'target/*', 'build/*', 'dist/*', '.next/*',
            # IDE files
            '.vscode/*', '.idea/*', '*.swp', '*.swo',
            # Temporary files
            '*.tmp', '*.temp', '*.log', '*.cache',
            # Binary files
            '*.exe', '*.dll', '*.so', '*.dylib',
            '*.jpg', '*.jpeg', '*.png', '*.gif', '*.ico',
            '*.pdf', '*.zip', '*.tar', '*.gz',
            # Compiled files
            '*.pyc', '*.pyo', '*.class', '*.o', '*.obj'
        }
        
        logger.info(f"📁 File scanner initialized for workspace: {workspace_root}")
    
    async def scan_workspace(self, 
                           include_patterns: Optional[List[str]] = None,
                           exclude_patterns: Optional[List[str]] = None) -> List[Path]:
        """
        Scan the workspace for files to index.
        
        Args:
            include_patterns: File patterns to include (e.g., ['*.py', '*.js'])
            exclude_patterns: Additional patterns to exclude
            
        Returns:
            List of file paths to index
        """
        try:
            logger.info("🔍 Scanning workspace for files...")
            
            # Combine exclude patterns
            all_exclude_patterns = set(self.default_exclude_patterns)
            if exclude_patterns:
                all_exclude_patterns.update(exclude_patterns)
            
            files_to_index = []
            
            # Walk through workspace directory
            for root, dirs, files in os.walk(self.workspace_root):
                root_path = Path(root)
                
                # Filter directories to avoid excluded paths
                dirs[:] = [d for d in dirs if not self._is_excluded(root_path / d, all_exclude_patterns)]
                
                # Process files in current directory
                for file_name in files:
                    file_path = root_path / file_name
                    
                    # Skip excluded files
                    if self._is_excluded(file_path, all_exclude_patterns):
                        continue
                    
                    # Check include patterns if specified
                    if include_patterns and not self._matches_patterns(file_path, include_patterns):
                        continue
                    
                    # Check if file is supported
                    if self._is_supported_file(file_path):
                        files_to_index.append(file_path)
            
            logger.info(f"📊 Found {len(files_to_index)} files to index")
            return files_to_index
            
        except Exception as e:
            logger.error(f"❌ Failed to scan workspace: {e}")
            return []
    
    async def analyze_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """
        Analyze a single file and extract metadata.
        
        Args:
            file_path: Path to the file to analyze
            
        Returns:
            File metadata dictionary or None if analysis failed
        """
        try:
            if not file_path.exists() or not file_path.is_file():
                logger.warning(f"⚠️ File not found or not a file: {file_path}")
                return None
            
            # Get file statistics
            stat = file_path.stat()
            
            # Calculate file hash for change detection
            file_hash = await self._calculate_file_hash(file_path)
            
            # Detect language
            language = self._detect_language(file_path)
            
            # Count lines
            lines_count = await self._count_lines(file_path)
            
            # Generate unique file ID
            file_id = self._generate_file_id(file_path)
            
            # Create file metadata
            file_metadata = {
                'file_id': file_id,
                'path': str(file_path.relative_to(self.workspace_root)),
                'absolute_path': str(file_path.resolve()),
                'language': language,
                'size': stat.st_size,
                'lines_count': lines_count,
                'modified_time': datetime.fromtimestamp(stat.st_mtime),
                'created_time': datetime.fromtimestamp(stat.st_ctime),
                'encoding': await self._detect_encoding(file_path),
                'hash': file_hash
            }
            
            logger.debug(f"📄 Analyzed file: {file_path} ({language.value}, {lines_count} lines)")
            
            return file_metadata
            
        except Exception as e:
            logger.error(f"❌ Failed to analyze file {file_path}: {e}")
            return None
    
    def _detect_language(self, file_path: Path) -> CodeLanguage:
        """Detect programming language from file extension and content."""
        try:
            # Check extension first
            extension = file_path.suffix.lower()
            if extension in self.extension_to_language:
                return self.extension_to_language[extension]
            
            # Check MIME type as fallback
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if mime_type:
                if 'javascript' in mime_type:
                    return CodeLanguage.JAVASCRIPT
                elif 'python' in mime_type:
                    return CodeLanguage.PYTHON
                elif 'html' in mime_type:
                    return CodeLanguage.HTML
                elif 'css' in mime_type:
                    return CodeLanguage.CSS
                elif 'json' in mime_type:
                    return CodeLanguage.JSON
            
            # Check shebang for shell scripts
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    first_line = f.readline().strip()
                    if first_line.startswith('#!'):
                        if 'python' in first_line:
                            return CodeLanguage.PYTHON
                        elif any(shell in first_line for shell in ['bash', 'sh', 'zsh', 'fish']):
                            return CodeLanguage.SHELL
            except:
                pass
            
            return CodeLanguage.UNKNOWN
            
        except Exception as e:
            logger.debug(f"Language detection failed for {file_path}: {e}")
            return CodeLanguage.UNKNOWN
    
    def _is_supported_file(self, file_path: Path) -> bool:
        """Check if file type is supported for indexing."""
        try:
            # Check if it's a regular file
            if not file_path.is_file():
                return False
            
            # Check file size (skip very large files)
            max_size = 10 * 1024 * 1024  # 10MB
            if file_path.stat().st_size > max_size:
                logger.debug(f"⏭️ Skipping large file: {file_path}")
                return False
            
            # Check if language is supported
            language = self._detect_language(file_path)
            if language == CodeLanguage.UNKNOWN:
                return False
            
            # Check if file is readable
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    f.read(100)  # Try to read first 100 characters
                return True
            except:
                return False
                
        except Exception as e:
            logger.debug(f"Support check failed for {file_path}: {e}")
            return False
    
    def _is_excluded(self, path: Path, exclude_patterns: Set[str]) -> bool:
        """Check if path matches any exclude pattern."""
        try:
            relative_path = path.relative_to(self.workspace_root)
            path_str = str(relative_path).replace('\\', '/')
            
            for pattern in exclude_patterns:
                # Simple glob-like matching
                if pattern.endswith('/*'):
                    # Directory pattern
                    dir_pattern = pattern[:-2]
                    if path_str.startswith(dir_pattern + '/') or path_str == dir_pattern:
                        return True
                elif '*' in pattern:
                    # Wildcard pattern
                    import fnmatch
                    if fnmatch.fnmatch(path_str, pattern):
                        return True
                else:
                    # Exact match
                    if path_str == pattern or path.name == pattern:
                        return True
            
            return False
            
        except Exception:
            return False
    
    def _matches_patterns(self, path: Path, patterns: List[str]) -> bool:
        """Check if path matches any include pattern."""
        try:
            import fnmatch
            path_str = str(path.relative_to(self.workspace_root)).replace('\\', '/')
            
            for pattern in patterns:
                if fnmatch.fnmatch(path_str, pattern) or fnmatch.fnmatch(path.name, pattern):
                    return True
            
            return False
            
        except Exception:
            return False
    
    async def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA-256 hash of file content."""
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
            
        except Exception as e:
            logger.debug(f"Hash calculation failed for {file_path}: {e}")
            return ""
    
    async def _count_lines(self, file_path: Path) -> int:
        """Count number of lines in file."""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                return sum(1 for _ in f)
                
        except Exception as e:
            logger.debug(f"Line counting failed for {file_path}: {e}")
            return 0
    
    async def _detect_encoding(self, file_path: Path) -> str:
        """Detect file encoding."""
        try:
            import chardet
            
            with open(file_path, 'rb') as f:
                raw_data = f.read(min(10000, file_path.stat().st_size))
                result = chardet.detect(raw_data)
                encoding = result.get('encoding', 'utf-8')
                confidence = result.get('confidence', 0)
                
                # Fall back to utf-8 if confidence is low
                if confidence < 0.7:
                    encoding = 'utf-8'
                
                return encoding
                
        except Exception as e:
            logger.debug(f"Encoding detection failed for {file_path}: {e}")
            return 'utf-8'
    
    def _generate_file_id(self, file_path: Path) -> str:
        """Generate unique file ID."""
        try:
            # Use relative path as base for ID
            relative_path = file_path.relative_to(self.workspace_root)
            path_str = str(relative_path).replace('\\', '/')
            
            # Create hash-based ID
            file_id = hashlib.md5(path_str.encode('utf-8')).hexdigest()
            return f"file_{file_id}"
            
        except Exception as e:
            logger.debug(f"File ID generation failed for {file_path}: {e}")
            return f"file_{hash(str(file_path))}"
