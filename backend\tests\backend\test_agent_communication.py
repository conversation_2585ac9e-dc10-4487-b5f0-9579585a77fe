#!/usr/bin/env python3
"""
Agent Communication Test Script

This script validates that agents can actually communicate and execute tools
properly, testing the complete Phase 7A system end-to-end.
"""

import asyncio
import logging
import sys
import tempfile
import os
from pathlib import Path

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def print_status(test_name: str, success: bool, details: str = ""):
    """Print test status with color coding."""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} - {test_name}")
    if details:
        print(f"    {details}")


async def test_agent_communication():
    """Test agent communication and tool execution."""
    print("🤖 Testing Agent Communication & Tool Execution")
    print("=" * 70)
    
    all_tests_passed = True
    
    try:
        # Test 1: Import agents and dependencies
        print("\n📦 Testing agent and dependency imports...")
        try:
            from app.pydantic_ai.agents import coding_agent, vision_agent
            from app.pydantic_ai.dependencies import create_dependencies, create_vision_dependencies
            from app.pydantic_ai.models import TaskResult, VisionAnalysisResult
            print_status("Agent imports", True, "All agents and dependencies imported successfully")
        except ImportError as e:
            print_status("Agent imports", False, f"Import error: {e}")
            all_tests_passed = False
            return all_tests_passed
        
        # Test 2: Create dependencies
        print("\n🔧 Testing dependency creation...")
        try:
            # Create a temporary workspace for testing
            with tempfile.TemporaryDirectory() as temp_dir:
                workspace_path = Path(temp_dir)
                
                # Create coding dependencies
                coding_deps = await create_dependencies(workspace_root=workspace_path)

                # Create vision dependencies
                vision_deps = await create_vision_dependencies(workspace_root=workspace_path)
                
                print_status("Dependency creation", True, f"Dependencies created with workspace: {workspace_path}")
                
                # Test 3: Test simple agent run (no tools)
                print("\n💬 Testing basic agent communication...")
                try:
                    # Test coding agent with simple prompt
                    result = await coding_agent.run(
                        "Hello! Please respond with a simple greeting and confirm you can access your tools.",
                        deps=coding_deps
                    )
                    
                    if result and hasattr(result, 'data'):
                        print_status("Basic agent communication", True, f"Agent responded: {str(result.data)[:100]}...")
                    else:
                        print_status("Basic agent communication", False, "Agent did not respond properly")
                        all_tests_passed = False
                        
                except Exception as e:
                    print_status("Basic agent communication", False, f"Communication error: {e}")
                    all_tests_passed = False
                
                # Test 4: Test tool execution - File operations
                print("\n📁 Testing file operation tools...")
                try:
                    # Create a test file
                    test_file_path = workspace_path / "test_file.txt"
                    test_content = "Hello from Phase 7A test!"
                    
                    # Test file creation through agent
                    result = await coding_agent.run(
                        f"Please create a file at '{test_file_path}' with the content: '{test_content}'",
                        deps=coding_deps
                    )
                    
                    # Check if file was actually created
                    if test_file_path.exists():
                        actual_content = test_file_path.read_text()
                        if test_content in actual_content:
                            print_status("File creation tool", True, f"File created successfully with correct content")
                        else:
                            print_status("File creation tool", False, f"File created but content mismatch")
                            all_tests_passed = False
                    else:
                        print_status("File creation tool", False, "File was not created")
                        all_tests_passed = False
                        
                except Exception as e:
                    print_status("File creation tool", False, f"File operation error: {e}")
                    all_tests_passed = False
                
                # Test 5: Test file reading tool
                print("\n📖 Testing file reading tools...")
                try:
                    if test_file_path.exists():
                        result = await coding_agent.run(
                            f"Please read the contents of the file '{test_file_path}' and tell me what it contains.",
                            deps=coding_deps
                        )
                        
                        if result and hasattr(result, 'data'):
                            response = str(result.data)
                            if test_content in response or "Hello from Phase 7A" in response:
                                print_status("File reading tool", True, "Agent successfully read file content")
                            else:
                                print_status("File reading tool", False, "Agent did not read correct content")
                                all_tests_passed = False
                        else:
                            print_status("File reading tool", False, "Agent did not respond to file read request")
                            all_tests_passed = False
                    else:
                        print_status("File reading tool", False, "Test file does not exist for reading test")
                        all_tests_passed = False
                        
                except Exception as e:
                    print_status("File reading tool", False, f"File reading error: {e}")
                    all_tests_passed = False
                
                # Test 6: Test directory listing tool
                print("\n📂 Testing directory listing tools...")
                try:
                    result = await coding_agent.run(
                        f"Please list the contents of the directory '{workspace_path}' and tell me what files you find.",
                        deps=coding_deps
                    )
                    
                    if result and hasattr(result, 'data'):
                        response = str(result.data)
                        if "test_file.txt" in response:
                            print_status("Directory listing tool", True, "Agent successfully listed directory contents")
                        else:
                            print_status("Directory listing tool", False, "Agent did not find expected files")
                            all_tests_passed = False
                    else:
                        print_status("Directory listing tool", False, "Agent did not respond to directory listing request")
                        all_tests_passed = False
                        
                except Exception as e:
                    print_status("Directory listing tool", False, f"Directory listing error: {e}")
                    all_tests_passed = False
                
                # Test 7: Test git operations (if git is available)
                print("\n🔀 Testing git operation tools...")
                try:
                    # Initialize git repo in temp directory
                    import subprocess
                    subprocess.run(['git', 'init'], cwd=workspace_path, capture_output=True)
                    subprocess.run(['git', 'config', 'user.email', '<EMAIL>'], cwd=workspace_path, capture_output=True)
                    subprocess.run(['git', 'config', 'user.name', 'Test User'], cwd=workspace_path, capture_output=True)
                    
                    result = await coding_agent.run(
                        "Please check the git status of this repository and tell me what you find.",
                        deps=coding_deps
                    )
                    
                    if result and hasattr(result, 'data'):
                        response = str(result.data)
                        if "git" in response.lower() or "status" in response.lower():
                            print_status("Git operations tool", True, "Agent successfully executed git operations")
                        else:
                            print_status("Git operations tool", False, "Agent response did not indicate git operation")
                            all_tests_passed = False
                    else:
                        print_status("Git operations tool", False, "Agent did not respond to git status request")
                        all_tests_passed = False
                        
                except Exception as e:
                    print_status("Git operations tool", False, f"Git operation error: {e}")
                    # Don't fail the entire test for git issues
                    logger.warning(f"Git test failed (may not be available): {e}")
                
                # Test 8: Test structured output
                print("\n📋 Testing structured output...")
                try:
                    result = await coding_agent.run(
                        "Please analyze the current workspace and provide a structured summary of what you found.",
                        deps=coding_deps
                    )
                    
                    if result and hasattr(result, 'data'):
                        # Check if we got a TaskResult structure
                        if hasattr(result.data, 'status') or isinstance(result.data, dict):
                            print_status("Structured output", True, "Agent provided structured output")
                        else:
                            print_status("Structured output", True, f"Agent provided output: {type(result.data)}")
                    else:
                        print_status("Structured output", False, "Agent did not provide structured output")
                        all_tests_passed = False
                        
                except Exception as e:
                    print_status("Structured output", False, f"Structured output error: {e}")
                    all_tests_passed = False
                
                # Test 9: Test vision agent (basic)
                print("\n👁️ Testing vision agent communication...")
                try:
                    result = await vision_agent.run(
                        "Hello! Please confirm you are the vision analysis agent and describe your capabilities.",
                        deps=vision_deps
                    )
                    
                    if result and hasattr(result, 'data'):
                        response = str(result.data)
                        if "vision" in response.lower() or "image" in response.lower() or "visual" in response.lower():
                            print_status("Vision agent communication", True, "Vision agent responded appropriately")
                        else:
                            print_status("Vision agent communication", True, f"Vision agent responded: {response[:100]}...")
                    else:
                        print_status("Vision agent communication", False, "Vision agent did not respond")
                        all_tests_passed = False
                        
                except Exception as e:
                    print_status("Vision agent communication", False, f"Vision agent error: {e}")
                    all_tests_passed = False
                
        except Exception as e:
            print_status("Dependency creation", False, f"Error: {e}")
            all_tests_passed = False
            return all_tests_passed
        
    except Exception as e:
        logger.error(f"Unexpected error during agent communication testing: {e}")
        all_tests_passed = False
    
    # Summary
    print("\n" + "=" * 70)
    if all_tests_passed:
        print("🎉 Agent Communication: ALL TESTS PASSED")
        print("✅ Agents can communicate successfully")
        print("✅ Tools are executing properly")
        print("✅ File operations working")
        print("✅ Structured outputs functioning")
        print("✅ Both coding and vision agents operational")
        print("✅ Phase 7A agent communication is COMPLETE")
    else:
        print("❌ Agent Communication: SOME TESTS FAILED")
        print("🔧 Please check the errors above and fix the issues")
    
    return all_tests_passed


async def main():
    """Main test function."""
    try:
        success = await test_agent_communication()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
