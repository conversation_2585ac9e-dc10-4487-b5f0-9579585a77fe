With a fresh set of eyes, please look at @PHASE_7_IMPLEMENT file to check what was done by the previous AI. Then look at the @IMPLEMENTATION_PLA file (which is very outdated) and update it with the changes that was previously implemented. Add new phases with new USEFUL features which will build on what was already implemented and what I will tell you next to finish the backend entirely (not going to build frontend just now - just keep it in mind at all times when building new features). 

Now here are features I want to add. Add them, as additions to your suggested features, to the implementation plan with a very good set of steps to implement each and describe each feature in a paragraph at each step - to make it implemented exactly as I want.

<features_I_want>
*The vision system should be accessible by the main model (probably via a tool? or the next best thing? - look at pydantic in @pydantic-ai-main folder (github repository of pydantic) for ideas. Thus it should be able to call the vision model when it wants to ensure it did something correct - like eg. changing css code and wanting to make sure it is displayed correctly. 

*Implement a "Continue" mode (please give it a better name than continue - also we will later make this toggleable in the frontend via a button - document this to remember I told you) which should progressively continue to run in a loop (user should be able to limit how long this loop goes - like only for 20 cycles etc.). The idea of this loop is to make the model code something, then first check for errors, get feedback, if_error: fix, if_no_error: continue, until everything is implemented in its entirety and completely error free. Also, this loop should entice the model to think differently or think from a totally different angle or give the model better context - using the context "engine" IF errors are repeated. This mode  should have error-handling which ensures an infinite loop of errors doesnt occur.

*Implement an indexing functionality which indexes ALL files in the project to make it easily accessible to the model. Then, create a tool like a "Context Engine" - inspired by Augment code's system but please give it another name than engine but similar, which should allow the coding model to ask the "engine" what it wants to see or find in the code and the "engine" should tell the model what it found in the code to enable it to understand everything.

*Implement a planning mode that will be built into the frontend which will allow the AI to create a plan - this plan should then FIRST be analysed by ANOTHER AI to improve it or find additions or potential problems to create an absolute robust plan - with the context of the project using embeddings etc and indexed code by the engine.

*The CLI feature should be optional. This entire app should be able to run in the command later (not the main focus - but keep in mind). We will NOT develop it further now.

*I don't know how this works, but I want you to ensure that (IF possible) the model doesn't get overloaded with prompts, so ensure prompt engineering is correct. Although, this is probably very efficient with pydantic, right?

</features_I_want>

===================================================================================================================

FRONTEND: Create the plan for the frontend development to create a sleek app design with ALL the functionality of the backend built in. An incredibly important note is that absolutely everything should have feedback. So when the "Models":
 - "think" it should display thinking/wizarding/vibing or some cool words (like claude code does) when the models are working. 
- "analysing" should display its own set of words when visual analysis is used.
- "" - think of more ways this system can be made more visually attractive and completely transparent - like giving feedback as to what is happening by using these types of small visual features (like saying "thinking" when actually busy).

The CLI feature should be optional. This entire app should be able to run in the command later (not the main focus - but keep in mind). We will NOT develop it further now.
