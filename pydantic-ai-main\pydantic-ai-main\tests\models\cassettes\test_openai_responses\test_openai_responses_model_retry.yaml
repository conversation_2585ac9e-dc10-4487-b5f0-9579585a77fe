interactions:
- request:
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '349'
      content-type:
      - application/json
      host:
      - api.openai.com
    method: POST
    parsed_body:
      input:
      - content: What is the location of Londos and London?
        role: user
      instructions: ''
      model: gpt-4o
      tool_choice: auto
      tools:
      - description: ''
        name: get_location
        parameters:
          additionalProperties: false
          properties:
            loc_name:
              type: string
          required:
          - loc_name
          type: object
        strict: true
        type: function
    uri: https://api.openai.com/v1/responses
  response:
    headers:
      alt-svc:
      - h3=":443"; ma=86400
      connection:
      - keep-alive
      content-length:
      - '1760'
      content-type:
      - application/json
      openai-organization:
      - pydantic-28gund
      openai-processing-ms:
      - '937'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      transfer-encoding:
      - chunked
    parsed_body:
      created_at: 1743079364
      error: null
      id: resp_67e547c48c9481918c5c4394464ce0c60ae6111e84dd5c08
      incomplete_details: null
      instructions: ''
      max_output_tokens: null
      metadata: {}
      model: gpt-4o-2024-08-06
      object: response
      output:
      - arguments: '{"loc_name":"Londos"}'
        call_id: call_LWVp74L5HaH2KNvgVz9PJsrj
        id: fc_67e547c540648191bc7505ac667e023f0ae6111e84dd5c08
        name: get_location
        status: completed
        type: function_call
      - arguments: '{"loc_name":"London"}'
        call_id: call_YnRAWeTyxI91m5uNa5bxXwVO
        id: fc_67e547c55c3081919da7a3f7fe81a1030ae6111e84dd5c08
        name: get_location
        status: completed
        type: function_call
      parallel_tool_calls: true
      previous_response_id: null
      reasoning:
        effort: null
        generate_summary: null
      status: completed
      store: true
      temperature: 1.0
      text:
        format:
          type: text
      tool_choice: auto
      tools:
      - description: null
        name: get_location
        parameters:
          additionalProperties: false
          properties:
            loc_name:
              type: string
          required:
          - loc_name
          type: object
        strict: true
        type: function
      top_p: 1.0
      truncation: disabled
      usage:
        input_tokens: 0
        input_tokens_details:
          cached_tokens: 0
        output_tokens: 0
        output_tokens_details:
          reasoning_tokens: 0
        total_tokens: 0
      user: null
    status:
      code: 200
      message: OK
- request:
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '916'
      content-type:
      - application/json
      cookie:
      - __cf_bm=FXiExTqEtHZ9VYYE34bz9rrvBGk.F9NXeKOkrkz_BXM-1743079365-*******-GhZiYx3yzKmVPAJ41yRB9OsIRpAGxFRIIjSdIKFwy.KS4xfngHKZN9IPfG.HBRW9QMukS0aZXyZq4FsyVima3YcS_xxRN_NYEtrgREmaYok;
        _cfuvid=BOWqVzNGDS.nJOHLSo8azK4r0Y28bwI2njGAEVa7T.o-1743079365513-*******-604800000
      host:
      - api.openai.com
    method: POST
    parsed_body:
      input:
      - content: What is the location of Londos and London?
        role: user
      - content: ''
        role: assistant
      - arguments: '{"loc_name":"Londos"}'
        call_id: call_LWVp74L5HaH2KNvgVz9PJsrj
        name: get_location
        type: function_call
      - arguments: '{"loc_name":"London"}'
        call_id: call_YnRAWeTyxI91m5uNa5bxXwVO
        name: get_location
        type: function_call
      - call_id: call_LWVp74L5HaH2KNvgVz9PJsrj
        output: |-
          Wrong location, I only know about "London".

          Fix the errors and try again.
        type: function_call_output
      - call_id: call_YnRAWeTyxI91m5uNa5bxXwVO
        output: '{"lat": 51, "lng": 0}'
        type: function_call_output
      instructions: ''
      model: gpt-4o
      tool_choice: auto
      tools:
      - description: ''
        name: get_location
        parameters:
          additionalProperties: false
          properties:
            loc_name:
              type: string
          required:
          - loc_name
          type: object
        strict: true
        type: function
    uri: https://api.openai.com/v1/responses
  response:
    headers:
      alt-svc:
      - h3=":443"; ma=86400
      connection:
      - keep-alive
      content-length:
      - '1712'
      content-type:
      - application/json
      openai-organization:
      - pydantic-28gund
      openai-processing-ms:
      - '1209'
      openai-version:
      - '2020-10-01'
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      transfer-encoding:
      - chunked
    parsed_body:
      created_at: 1743079365
      error: null
      id: resp_67e547c5a2f08191802a1f43620f348503a2086afed73b47
      incomplete_details: null
      instructions: ''
      max_output_tokens: null
      metadata: {}
      model: gpt-4o-2024-08-06
      object: response
      output:
      - content:
        - annotations: []
          text: |-
            It seems "Londos" might be incorrect or unknown. If you meant something else, please clarify.

            For **London**, it's located at approximately latitude 51° N and longitude 0° W.
          type: output_text
        id: msg_67e547c615ec81918d6671a184f82a1803a2086afed73b47
        role: assistant
        status: completed
        type: message
      parallel_tool_calls: true
      previous_response_id: null
      reasoning:
        effort: null
        generate_summary: null
      status: completed
      store: true
      temperature: 1.0
      text:
        format:
          type: text
      tool_choice: auto
      tools:
      - description: null
        name: get_location
        parameters:
          additionalProperties: false
          properties:
            loc_name:
              type: string
          required:
          - loc_name
          type: object
        strict: true
        type: function
      top_p: 1.0
      truncation: disabled
      usage:
        input_tokens: 335
        input_tokens_details:
          cached_tokens: 0
        output_tokens: 44
        output_tokens_details:
          reasoning_tokens: 0
        total_tokens: 379
      user: null
    status:
      code: 200
      message: OK
version: 1
