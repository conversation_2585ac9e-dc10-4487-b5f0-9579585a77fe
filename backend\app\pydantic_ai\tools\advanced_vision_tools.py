"""
Advanced Vision Analysis Tools

This module provides sophisticated vision analysis capabilities including
AI-powered image analysis, accessibility checking, and comprehensive UI evaluation.
"""

import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
import asyncio

from pydantic_ai import RunContext
from ..dependencies import VisionDependencies
from ..image_utils import (
    validate_image_file, 
    get_image_info, 
    encode_image_to_base64,
    create_data_url
)

logger = logging.getLogger(__name__)


async def ai_analyze_image(
    ctx: RunContext[VisionDependencies],
    image_path: str,
    analysis_focus: str = "comprehensive",
    include_accessibility: bool = True,
    include_usability: bool = True
) -> Dict[str, Any]:
    """
    AI-powered comprehensive image analysis using vision models.
    
    Args:
        ctx: Pydantic AI run context with vision dependencies
        image_path: Path to the image file
        analysis_focus: Focus area (comprehensive, accessibility, design, bugs)
        include_accessibility: Include accessibility analysis
        include_usability: Include usability analysis
        
    Returns:
        Dict with comprehensive AI analysis results
    """
    try:
        # Validate image
        image_file = Path(image_path)
        if not image_file.exists():
            return {
                "status": "error",
                "error": f"Image file not found: {image_path}",
                "error_type": "FileNotFoundError"
            }
        
        is_valid, validation_msg = validate_image_file(str(image_file))
        if not is_valid:
            return {
                "status": "error",
                "error": validation_msg,
                "error_type": "ValidationError"
            }
        
        # Get image metadata
        image_info = get_image_info(str(image_file))
        
        # Encode image for AI analysis
        image_base64 = encode_image_to_base64(str(image_file))
        if not image_base64:
            return {
                "status": "error",
                "error": "Failed to encode image for analysis",
                "error_type": "EncodingError"
            }
        
        # Build comprehensive analysis prompt
        analysis_prompts = {
            "comprehensive": """Analyze this image comprehensively for:
1. Overall visual design and layout quality
2. User interface elements and their effectiveness
3. Color scheme and visual hierarchy
4. Content organization and readability
5. Professional appearance and polish""",
            
            "accessibility": """Analyze this image specifically for accessibility issues:
1. Color contrast ratios and readability
2. Text size and legibility
3. Button and interactive element sizes
4. Visual indicators and feedback
5. Compliance with WCAG guidelines""",
            
            "design": """Analyze this image for design quality:
1. Visual hierarchy and information architecture
2. Consistency in styling and branding
3. Use of whitespace and layout balance
4. Typography and font choices
5. Overall aesthetic appeal""",
            
            "bugs": """Analyze this image for visual bugs and issues:
1. Layout problems and misalignments
2. Overlapping or cut-off elements
3. Broken or missing visual components
4. Inconsistent styling
5. Rendering issues or artifacts"""
        }
        
        base_prompt = analysis_prompts.get(analysis_focus, analysis_prompts["comprehensive"])
        
        # Add specific analysis requests
        additional_analysis = []
        if include_accessibility:
            additional_analysis.append("- Detailed accessibility compliance assessment")
        if include_usability:
            additional_analysis.append("- Usability and user experience evaluation")
        
        if additional_analysis:
            base_prompt += f"\n\nAdditionally include:\n" + "\n".join(additional_analysis)
        
        base_prompt += """

Provide specific, actionable feedback with:
- Concrete observations about what you see
- Specific locations of issues (if any)
- Detailed recommendations for improvements
- Confidence scores for your assessments
- Priority levels for any issues identified"""
        
        # Use OpenRouter vision analysis
        analysis_result = await ctx.deps.openrouter_client.vision_analyze(
            image_base64=image_base64,
            prompt=base_prompt
        )
        
        return {
            "status": "success",
            "analysis_focus": analysis_focus,
            "analysis_result": analysis_result,
            "image_metadata": image_info,
            "analysis_settings": {
                "include_accessibility": include_accessibility,
                "include_usability": include_usability
            },
            "confidence": "high"  # AI-powered analysis
        }
        
    except Exception as e:
        logger.error(f"AI image analysis failed for {image_path}: {e}")
        return {
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__
        }


async def detect_ui_elements(
    ctx: RunContext[VisionDependencies],
    image_path: str,
    element_types: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Detect and analyze UI elements in an image.
    
    Args:
        ctx: Pydantic AI run context with vision dependencies
        image_path: Path to the image file
        element_types: Specific element types to focus on
        
    Returns:
        Dict with detected UI elements and analysis
    """
    try:
        # Validate image
        is_valid, validation_msg = validate_image_file(image_path)
        if not is_valid:
            return {
                "status": "error",
                "error": validation_msg,
                "error_type": "ValidationError"
            }
        
        # Encode image
        image_base64 = encode_image_to_base64(image_path)
        if not image_base64:
            return {
                "status": "error",
                "error": "Failed to encode image",
                "error_type": "EncodingError"
            }
        
        # Default element types if not specified
        if element_types is None:
            element_types = [
                "buttons", "forms", "navigation", "headers", "content areas",
                "sidebars", "footers", "modals", "dropdowns", "icons"
            ]
        
        # Build element detection prompt
        prompt = f"""Analyze this image and identify the following UI elements:
{', '.join(element_types)}

For each element you identify, provide:
1. Element type and description
2. Location/position in the interface
3. Size and visual properties
4. Accessibility assessment
5. Usability evaluation
6. Any issues or recommendations

Focus on providing specific, detailed observations about the user interface elements."""
        
        # Analyze with AI
        analysis_result = await ctx.deps.openrouter_client.vision_analyze(
            image_base64=image_base64,
            prompt=prompt
        )
        
        return {
            "status": "success",
            "element_types_searched": element_types,
            "detected_elements": analysis_result,
            "image_path": image_path
        }
        
    except Exception as e:
        logger.error(f"UI element detection failed for {image_path}: {e}")
        return {
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__
        }


async def accessibility_audit(
    ctx: RunContext[VisionDependencies],
    image_path: str,
    wcag_level: str = "AA"
) -> Dict[str, Any]:
    """
    Perform comprehensive accessibility audit of an image.
    
    Args:
        ctx: Pydantic AI run context with vision dependencies
        image_path: Path to the image file
        wcag_level: WCAG compliance level (A, AA, AAA)
        
    Returns:
        Dict with detailed accessibility audit results
    """
    try:
        # Validate image
        is_valid, validation_msg = validate_image_file(image_path)
        if not is_valid:
            return {
                "status": "error",
                "error": validation_msg,
                "error_type": "ValidationError"
            }
        
        # Encode image
        image_base64 = encode_image_to_base64(image_path)
        if not image_base64:
            return {
                "status": "error",
                "error": "Failed to encode image",
                "error_type": "EncodingError"
            }
        
        # Build accessibility audit prompt
        prompt = f"""Perform a comprehensive accessibility audit of this interface according to WCAG {wcag_level} guidelines.

Analyze and report on:

1. **Color and Contrast**:
   - Text-to-background contrast ratios
   - Color-only information conveyance
   - Color accessibility for colorblind users

2. **Text and Typography**:
   - Font sizes and readability
   - Text spacing and line height
   - Text alternatives for images

3. **Interactive Elements**:
   - Button and link sizes (minimum 44x44px)
   - Touch target spacing
   - Visual focus indicators

4. **Layout and Structure**:
   - Logical reading order
   - Heading hierarchy
   - Content organization

5. **Visual Design**:
   - Sufficient visual indicators
   - Error identification
   - Status and feedback visibility

For each issue found, provide:
- Specific location and description
- WCAG guideline reference
- Severity level (Critical, High, Medium, Low)
- Recommended fix
- Impact on users with disabilities

Provide an overall accessibility score and compliance assessment."""
        
        # Analyze with AI
        analysis_result = await ctx.deps.openrouter_client.vision_analyze(
            image_base64=image_base64,
            prompt=prompt
        )
        
        return {
            "status": "success",
            "wcag_level": wcag_level,
            "accessibility_audit": analysis_result,
            "image_path": image_path,
            "audit_type": "comprehensive"
        }
        
    except Exception as e:
        logger.error(f"Accessibility audit failed for {image_path}: {e}")
        return {
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__
        }


async def visual_regression_analysis(
    ctx: RunContext[VisionDependencies],
    before_image: str,
    after_image: str,
    focus_areas: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Perform visual regression analysis between two images.
    
    Args:
        ctx: Pydantic AI run context with vision dependencies
        before_image: Path to the "before" image
        after_image: Path to the "after" image
        focus_areas: Specific areas to focus analysis on
        
    Returns:
        Dict with detailed regression analysis
    """
    try:
        # Validate both images
        for img_path, img_name in [(before_image, "before"), (after_image, "after")]:
            is_valid, validation_msg = validate_image_file(img_path)
            if not is_valid:
                return {
                    "status": "error",
                    "error": f"{img_name} image invalid: {validation_msg}",
                    "error_type": "ValidationError"
                }
        
        # Encode both images
        before_base64 = encode_image_to_base64(before_image)
        after_base64 = encode_image_to_base64(after_image)
        
        if not before_base64 or not after_base64:
            return {
                "status": "error",
                "error": "Failed to encode one or both images",
                "error_type": "EncodingError"
            }
        
        # Default focus areas
        if focus_areas is None:
            focus_areas = [
                "layout changes", "color differences", "text modifications",
                "button changes", "navigation updates", "content shifts"
            ]
        
        # Analyze before image
        before_analysis = await ctx.deps.openrouter_client.vision_analyze(
            image_base64=before_base64,
            prompt="Describe this interface in detail, focusing on layout, colors, text, buttons, and overall design."
        )
        
        # Analyze after image
        after_analysis = await ctx.deps.openrouter_client.vision_analyze(
            image_base64=after_base64,
            prompt="Describe this interface in detail, focusing on layout, colors, text, buttons, and overall design."
        )
        
        # Perform comparison analysis
        comparison_prompt = f"""Compare these two interface descriptions and identify all differences:

BEFORE: {before_analysis}

AFTER: {after_analysis}

Focus on these areas: {', '.join(focus_areas)}

Provide a detailed regression analysis including:
1. **Visual Changes**: What changed visually?
2. **Layout Differences**: Any layout or positioning changes?
3. **Content Changes**: Text, images, or content modifications?
4. **Functional Impact**: How might these changes affect usability?
5. **Regression Risk**: Are any changes potentially problematic?
6. **Recommendations**: Suggestions for addressing any issues

Categorize each change as:
- Improvement: Positive change
- Neutral: No significant impact
- Regression: Potentially negative change
- Critical: Serious issue requiring immediate attention"""
        
        comparison_result = await ctx.deps.openrouter_client.chat_completion(
            messages=[{"role": "user", "content": comparison_prompt}],
            model="google/gemini-2.0-flash-exp:free"
        )
        
        return {
            "status": "success",
            "before_image": before_image,
            "after_image": after_image,
            "focus_areas": focus_areas,
            "before_analysis": before_analysis,
            "after_analysis": after_analysis,
            "regression_analysis": comparison_result,
            "analysis_type": "visual_regression"
        }
        
    except Exception as e:
        logger.error(f"Visual regression analysis failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__
        }


# Export advanced vision tools
__all__ = [
    'ai_analyze_image',
    'detect_ui_elements', 
    'accessibility_audit',
    'visual_regression_analysis'
]
