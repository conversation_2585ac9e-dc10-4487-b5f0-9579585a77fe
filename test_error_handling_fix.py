#!/usr/bin/env python3
"""
Test script to verify that all error handling components are working correctly
after fixing the import issues.
"""

import sys
import os
import asyncio
import requests
import json
from datetime import datetime

# Add the backend app to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend', 'app'))

def test_api_endpoints():
    """Test the error handling API endpoints."""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing Error Handling API Endpoints...")
    
    # Test health endpoint
    try:
        response = requests.get(f"{base_url}/api/error-handling/health")
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Health endpoint working: {health_data['overall_health']}")
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")
    
    # Test circuit breakers endpoint
    try:
        response = requests.get(f"{base_url}/api/error-handling/circuit-breakers")
        if response.status_code == 200:
            cb_data = response.json()
            print(f"✅ Circuit breakers endpoint working: {len(cb_data)} breakers")
        else:
            print(f"❌ Circuit breakers endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Circuit breakers endpoint error: {e}")
    
    # Test notifications endpoint
    try:
        response = requests.get(f"{base_url}/api/error-handling/notifications")
        if response.status_code == 200:
            notifications = response.json()
            print(f"✅ Notifications endpoint working: {len(notifications)} notifications")
        else:
            print(f"❌ Notifications endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Notifications endpoint error: {e}")

def test_imports():
    """Test that all error handling modules can be imported."""
    print("\n🧪 Testing Error Handling Module Imports...")
    
    try:
        from app.error_handling.retry_manager import EnhancedRetryManager, RetryConfig
        print("✅ Retry manager imported successfully")
    except Exception as e:
        print(f"❌ Retry manager import failed: {e}")
        return False
    
    try:
        from app.error_handling.error_categorizer import ErrorCategorizer
        print("✅ Error categorizer imported successfully")
    except Exception as e:
        print(f"❌ Error categorizer import failed: {e}")
        return False
    
    try:
        from app.error_handling.recovery_strategies import RecoveryStrategies
        print("✅ Recovery strategies imported successfully")
    except Exception as e:
        print(f"❌ Recovery strategies import failed: {e}")
        return False
    
    try:
        from app.error_handling.user_notifications import UserNotificationManager
        print("✅ User notifications imported successfully")
    except Exception as e:
        print(f"❌ User notifications import failed: {e}")
        return False
    
    try:
        from app.error_handling.integration import ErrorHandlingIntegration
        print("✅ Integration imported successfully")
    except Exception as e:
        print(f"❌ Integration import failed: {e}")
        return False
    
    try:
        from app.error_handling import get_error_integration
        print("✅ Error integration function imported successfully")
    except Exception as e:
        print(f"❌ Error integration function import failed: {e}")
        return False
    
    return True

async def test_error_integration():
    """Test the error integration functionality."""
    print("\n🧪 Testing Error Integration Functionality...")
    
    try:
        from app.error_handling import get_error_integration
        
        # Get the error integration instance
        error_integration = get_error_integration()
        print("✅ Error integration instance created")
        
        # Test error categorization
        test_error = Exception("Test connection timeout")
        category = error_integration.error_categorizer.categorize_error(test_error)
        print(f"✅ Error categorization working: {category}")
        
        # Test notification manager
        notification_id = error_integration.notification_manager.create_notification(
            message="Test notification",
            error_type="test",
            session_id="test_session"
        )
        print(f"✅ Notification creation working: {notification_id}")
        
        # Test retry manager
        retry_config = error_integration.retry_manager.get_retry_config("api_call")
        print(f"✅ Retry configuration working: max_attempts={retry_config.max_attempts}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error integration test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Error Handling System Tests")
    print("=" * 50)
    
    # Test imports first
    imports_ok = test_imports()
    
    if imports_ok:
        # Test error integration functionality
        integration_ok = asyncio.run(test_error_integration())
        
        # Test API endpoints
        test_api_endpoints()
        
        print("\n" + "=" * 50)
        if imports_ok and integration_ok:
            print("🎉 All error handling components are working correctly!")
            print("✅ Import issues have been resolved")
            print("✅ Error handling system is operational")
        else:
            print("❌ Some components are still having issues")
    else:
        print("\n❌ Import issues still exist - skipping other tests")
    
    print(f"\n📅 Test completed at: {datetime.now()}")

if __name__ == "__main__":
    main()
