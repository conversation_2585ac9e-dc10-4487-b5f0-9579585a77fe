"""
Advanced State Management and Caching System

Provides intelligent state management with:
- Multi-level caching (memory, disk, distributed)
- State persistence and recovery
- Session management
- Context preservation
- Intelligent cache invalidation
"""

import asyncio
import json
import pickle
import logging
import time
import hashlib
from typing import Dict, Any, Optional, List, Union, Callable
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict
import threading
import sqlite3
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """Cache entry with metadata."""
    key: str
    value: Any
    created_at: datetime
    expires_at: Optional[datetime]
    access_count: int = 0
    last_accessed: Optional[datetime] = None
    size_bytes: int = 0
    tags: List[str] = field(default_factory=list)


class IntelligentCache:
    """Multi-level intelligent caching system."""
    
    def __init__(
        self,
        memory_size: int = 1000,
        disk_cache_dir: Optional[str] = None,
        default_ttl: float = 3600.0,
        enable_persistence: bool = True
    ):
        self.memory_size = memory_size
        self.default_ttl = default_ttl
        self.enable_persistence = enable_persistence
        
        # Memory cache
        self._memory_cache: Dict[str, CacheEntry] = {}
        self._memory_lock = threading.RLock()
        
        # Disk cache
        self.disk_cache_dir = Path(disk_cache_dir or "cache")
        self.disk_cache_dir.mkdir(exist_ok=True)
        
        # Statistics
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'disk_reads': 0,
            'disk_writes': 0
        }
        
        # Background cleanup task
        self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
    
    def _calculate_size(self, value: Any) -> int:
        """Calculate approximate size of value in bytes."""
        try:
            if isinstance(value, str):
                return len(value.encode('utf-8'))
            elif isinstance(value, (int, float)):
                return 8
            elif isinstance(value, (list, dict)):
                return len(pickle.dumps(value))
            else:
                return len(str(value).encode('utf-8'))
        except:
            return 100  # Default estimate
    
    def _generate_key_hash(self, key: str) -> str:
        """Generate hash for disk storage."""
        return hashlib.md5(key.encode()).hexdigest()
    
    async def get(self, key: str, default: Any = None) -> Any:
        """Get value from cache with intelligent fallback."""
        with self._memory_lock:
            # Check memory cache first
            if key in self._memory_cache:
                entry = self._memory_cache[key]
                
                # Check if expired
                if entry.expires_at and datetime.now() > entry.expires_at:
                    del self._memory_cache[key]
                else:
                    # Update access statistics
                    entry.access_count += 1
                    entry.last_accessed = datetime.now()
                    self.stats['hits'] += 1
                    return entry.value
        
        # Check disk cache
        if self.enable_persistence:
            disk_value = await self._get_from_disk(key)
            if disk_value is not None:
                # Promote to memory cache
                await self.set(key, disk_value, promote_from_disk=True)
                self.stats['hits'] += 1
                self.stats['disk_reads'] += 1
                return disk_value
        
        self.stats['misses'] += 1
        return default
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[float] = None,
        tags: Optional[List[str]] = None,
        promote_from_disk: bool = False
    ) -> None:
        """Set value in cache with intelligent storage."""
        if ttl is None:
            ttl = self.default_ttl
        
        expires_at = datetime.now() + timedelta(seconds=ttl) if ttl > 0 else None
        size_bytes = self._calculate_size(value)
        
        entry = CacheEntry(
            key=key,
            value=value,
            created_at=datetime.now(),
            expires_at=expires_at,
            size_bytes=size_bytes,
            tags=tags or []
        )
        
        with self._memory_lock:
            # Check if we need to evict entries
            if len(self._memory_cache) >= self.memory_size:
                await self._evict_lru()
            
            self._memory_cache[key] = entry
        
        # Store to disk if persistence is enabled and not promoting from disk
        if self.enable_persistence and not promote_from_disk:
            await self._store_to_disk(key, value, expires_at)
            self.stats['disk_writes'] += 1
    
    async def invalidate(self, key: str) -> bool:
        """Invalidate cache entry."""
        removed = False
        
        with self._memory_lock:
            if key in self._memory_cache:
                del self._memory_cache[key]
                removed = True
        
        # Remove from disk
        if self.enable_persistence:
            disk_removed = await self._remove_from_disk(key)
            removed = removed or disk_removed
        
        return removed
    
    async def invalidate_by_tags(self, tags: List[str]) -> int:
        """Invalidate all entries with specified tags."""
        removed_count = 0
        
        with self._memory_lock:
            keys_to_remove = []
            for key, entry in self._memory_cache.items():
                if any(tag in entry.tags for tag in tags):
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                del self._memory_cache[key]
                removed_count += 1
        
        return removed_count
    
    async def clear(self) -> None:
        """Clear all cache entries."""
        with self._memory_lock:
            self._memory_cache.clear()
        
        if self.enable_persistence:
            # Clear disk cache
            for cache_file in self.disk_cache_dir.glob("*.cache"):
                cache_file.unlink()
    
    async def _evict_lru(self) -> None:
        """Evict least recently used entry."""
        if not self._memory_cache:
            return
        
        # Find LRU entry
        lru_key = min(
            self._memory_cache.keys(),
            key=lambda k: self._memory_cache[k].last_accessed or self._memory_cache[k].created_at
        )
        
        del self._memory_cache[lru_key]
        self.stats['evictions'] += 1
    
    async def _get_from_disk(self, key: str) -> Any:
        """Get value from disk cache."""
        try:
            cache_file = self.disk_cache_dir / f"{self._generate_key_hash(key)}.cache"
            if not cache_file.exists():
                return None
            
            with open(cache_file, 'rb') as f:
                data = pickle.load(f)
            
            # Check expiration
            if data.get('expires_at') and datetime.now() > data['expires_at']:
                cache_file.unlink()
                return None
            
            return data['value']
        except Exception as e:
            logger.debug(f"Failed to read from disk cache: {e}")
            return None
    
    async def _store_to_disk(self, key: str, value: Any, expires_at: Optional[datetime]) -> None:
        """Store value to disk cache."""
        try:
            cache_file = self.disk_cache_dir / f"{self._generate_key_hash(key)}.cache"
            
            data = {
                'key': key,
                'value': value,
                'created_at': datetime.now(),
                'expires_at': expires_at
            }
            
            with open(cache_file, 'wb') as f:
                pickle.dump(data, f)
        except Exception as e:
            logger.debug(f"Failed to write to disk cache: {e}")
    
    async def _remove_from_disk(self, key: str) -> bool:
        """Remove value from disk cache."""
        try:
            cache_file = self.disk_cache_dir / f"{self._generate_key_hash(key)}.cache"
            if cache_file.exists():
                cache_file.unlink()
                return True
        except Exception as e:
            logger.debug(f"Failed to remove from disk cache: {e}")
        return False
    
    async def _periodic_cleanup(self) -> None:
        """Periodic cleanup of expired entries."""
        while True:
            try:
                await asyncio.sleep(300)  # Cleanup every 5 minutes
                
                # Cleanup memory cache
                with self._memory_lock:
                    expired_keys = []
                    for key, entry in self._memory_cache.items():
                        if entry.expires_at and datetime.now() > entry.expires_at:
                            expired_keys.append(key)
                    
                    for key in expired_keys:
                        del self._memory_cache[key]
                
                # Cleanup disk cache
                if self.enable_persistence:
                    for cache_file in self.disk_cache_dir.glob("*.cache"):
                        try:
                            with open(cache_file, 'rb') as f:
                                data = pickle.load(f)
                            
                            if data.get('expires_at') and datetime.now() > data['expires_at']:
                                cache_file.unlink()
                        except:
                            # Remove corrupted files
                            cache_file.unlink()
                
                if expired_keys:
                    logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
                    
            except Exception as e:
                logger.error(f"Cache cleanup failed: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._memory_lock:
            memory_entries = len(self._memory_cache)
            total_memory_size = sum(entry.size_bytes for entry in self._memory_cache.values())
        
        hit_rate = self.stats['hits'] / (self.stats['hits'] + self.stats['misses']) if (self.stats['hits'] + self.stats['misses']) > 0 else 0
        
        return {
            'memory_entries': memory_entries,
            'total_memory_size_bytes': total_memory_size,
            'hit_rate': hit_rate,
            **self.stats
        }


class SessionManager:
    """Advanced session management with persistence."""
    
    def __init__(self, db_path: str = "sessions.db"):
        self.db_path = db_path
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self._init_database()
    
    def _init_database(self) -> None:
        """Initialize session database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id TEXT PRIMARY KEY,
                    data TEXT,
                    created_at TIMESTAMP,
                    last_accessed TIMESTAMP,
                    expires_at TIMESTAMP
                )
            """)
    
    async def create_session(
        self,
        session_id: str,
        data: Dict[str, Any],
        ttl: float = 3600.0
    ) -> str:
        """Create a new session."""
        expires_at = datetime.now() + timedelta(seconds=ttl)
        
        session_data = {
            'session_id': session_id,
            'data': data,
            'created_at': datetime.now(),
            'last_accessed': datetime.now(),
            'expires_at': expires_at
        }
        
        self.active_sessions[session_id] = session_data
        
        # Persist to database
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                "INSERT OR REPLACE INTO sessions VALUES (?, ?, ?, ?, ?)",
                (
                    session_id,
                    json.dumps(data),
                    session_data['created_at'].isoformat(),
                    session_data['last_accessed'].isoformat(),
                    expires_at.isoformat()
                )
            )
        
        return session_id
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data."""
        # Check active sessions first
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            if datetime.now() < session['expires_at']:
                session['last_accessed'] = datetime.now()
                return session['data']
            else:
                del self.active_sessions[session_id]
        
        # Check database
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT data, expires_at FROM sessions WHERE session_id = ?",
                (session_id,)
            )
            row = cursor.fetchone()
            
            if row:
                data, expires_at_str = row
                expires_at = datetime.fromisoformat(expires_at_str)
                
                if datetime.now() < expires_at:
                    session_data = json.loads(data)
                    # Load back to active sessions
                    self.active_sessions[session_id] = {
                        'session_id': session_id,
                        'data': session_data,
                        'created_at': datetime.now(),
                        'last_accessed': datetime.now(),
                        'expires_at': expires_at
                    }
                    return session_data
                else:
                    # Clean up expired session
                    conn.execute("DELETE FROM sessions WHERE session_id = ?", (session_id,))
        
        return None
    
    async def update_session(self, session_id: str, data: Dict[str, Any]) -> bool:
        """Update session data."""
        if session_id in self.active_sessions:
            self.active_sessions[session_id]['data'] = data
            self.active_sessions[session_id]['last_accessed'] = datetime.now()
            
            # Update database
            with sqlite3.connect(self.db_path) as conn:
                conn.execute(
                    "UPDATE sessions SET data = ?, last_accessed = ? WHERE session_id = ?",
                    (json.dumps(data), datetime.now().isoformat(), session_id)
                )
            return True
        
        return False
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete session."""
        removed = session_id in self.active_sessions
        
        if removed:
            del self.active_sessions[session_id]
        
        # Remove from database
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("DELETE FROM sessions WHERE session_id = ?", (session_id,))
            removed = removed or cursor.rowcount > 0
        
        return removed


# Global instances
intelligent_cache = IntelligentCache()
session_manager = SessionManager()


# Decorators for caching
def cached_result(ttl: float = 3600.0, tags: Optional[List[str]] = None):
    """Decorator for caching function results with intelligent invalidation."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Generate cache key
            key_data = f"{func.__name__}:{args}:{sorted(kwargs.items())}"
            cache_key = hashlib.md5(key_data.encode()).hexdigest()
            
            # Try cache first
            result = await intelligent_cache.get(cache_key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            await intelligent_cache.set(cache_key, result, ttl=ttl, tags=tags)
            return result
        
        return wrapper
    return decorator


@asynccontextmanager
async def managed_session(session_id: str, initial_data: Optional[Dict[str, Any]] = None):
    """Context manager for session management."""
    if initial_data:
        await session_manager.create_session(session_id, initial_data)
    
    try:
        session_data = await session_manager.get_session(session_id)
        yield session_data
    finally:
        # Session cleanup is handled automatically by expiration
        pass
