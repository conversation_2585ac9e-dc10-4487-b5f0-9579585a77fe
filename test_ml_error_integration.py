#!/usr/bin/env python3
"""
Test script to verify that the ML Error Intelligence System is properly integrated
with the Error Handling System.
"""

import sys
import os
import asyncio
import requests
import json
from datetime import datetime

# Add the backend app to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend', 'app'))

def test_api_endpoints():
    """Test the enhanced error handling API endpoints."""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing Enhanced Error Handling API Endpoints...")
    
    # Test health endpoint with ML intelligence
    try:
        response = requests.get(f"{base_url}/api/error-handling/health")
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Health endpoint working: {health_data['overall_health']}")
            
            # Check if ML intelligence is included
            if 'component_health' in health_data and 'ml_intelligence' in health_data['component_health']:
                ml_stats = health_data['component_health']['ml_intelligence']
                print(f"🧠 ML Intelligence Status: {ml_stats}")
                if ml_stats.get('enabled'):
                    print(f"   - Patterns learned: {ml_stats.get('patterns_learned', 0)}")
                    print(f"   - Total patterns: {ml_stats.get('total_patterns', 0)}")
                else:
                    print(f"   - Reason: {ml_stats.get('reason', 'Unknown')}")
            else:
                print("❌ ML Intelligence not found in health response")
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")

def test_imports():
    """Test that all integrated error handling modules can be imported."""
    print("\n🧪 Testing Integrated Error Handling Module Imports...")
    
    try:
        from app.error_handling import get_error_integration
        error_integration = get_error_integration(enable_ml_intelligence=True)
        print("✅ Error integration with ML intelligence imported successfully")
        
        # Check if ML intelligence is enabled
        if hasattr(error_integration, 'ml_enabled') and error_integration.ml_enabled:
            print("✅ ML Intelligence is enabled")
            if hasattr(error_integration, 'ml_intelligence') and error_integration.ml_intelligence:
                print("✅ ML Intelligence system is initialized")
            else:
                print("❌ ML Intelligence system not initialized")
        else:
            print("❌ ML Intelligence is not enabled")
            
        return error_integration
        
    except Exception as e:
        print(f"❌ Integration import failed: {e}")
        return None

async def test_ml_error_integration(error_integration):
    """Test the ML error integration functionality."""
    print("\n🧪 Testing ML Error Integration Functionality...")
    
    if not error_integration:
        print("❌ No error integration available for testing")
        return False
    
    try:
        # Test 1: Handle an error and check ML analysis
        print("\n📝 Test 1: Error handling with ML analysis")
        test_error = ConnectionError("Test connection timeout for ML analysis")
        
        result = await error_integration.handle_error_with_recovery(
            error=test_error,
            operation="test_ml_connection_error",
            context={'files_changed': ['test_connection.py']},
            session_id="test_session_ml",
            show_technical_details=True
        )
        
        print(f"   - Error handled: {result['error_handled']}")
        print(f"   - Error category: {result['error_category']}")
        print(f"   - Recovery attempted: {result['recovery_attempted']}")
        
        # Check ML intelligence results
        ml_info = result.get('ml_intelligence', {})
        print(f"   - ML enabled: {ml_info.get('enabled', False)}")
        print(f"   - Pattern detected: {ml_info.get('pattern_detected', False)}")
        if ml_info.get('pattern_id'):
            print(f"   - Pattern ID: {ml_info['pattern_id']}")
            print(f"   - ML suggestions: {len(ml_info.get('ml_suggestions', []))}")
            print(f"   - Pattern confidence: {ml_info.get('pattern_confidence', 'N/A')}")
        
        # Test 2: Handle the same error again to test learning
        print("\n📝 Test 2: Same error again (testing pattern recognition)")
        result2 = await error_integration.handle_error_with_recovery(
            error=test_error,
            operation="test_ml_connection_error_repeat",
            context={'files_changed': ['test_connection.py']},
            session_id="test_session_ml_2",
            show_technical_details=True
        )
        
        ml_info2 = result2.get('ml_intelligence', {})
        if ml_info2.get('pattern_detected'):
            print(f"   - Pattern recognized: {ml_info2['pattern_id']}")
            print(f"   - Occurrence count: {ml_info2.get('occurrence_count', 'N/A')}")
        
        # Test 3: Test system health with ML stats
        print("\n📝 Test 3: System health with ML statistics")
        health = error_integration.get_system_health()
        
        ml_health = health.get('component_health', {}).get('ml_intelligence', {})
        if ml_health.get('enabled'):
            print(f"   - ML patterns learned: {error_integration.ml_patterns_learned}")
            print(f"   - Total ML patterns: {ml_health.get('total_patterns', 0)}")
            print(f"   - Error history size: {ml_health.get('error_history_size', 0)}")
            
            top_errors = ml_health.get('top_error_types', [])
            if top_errors:
                print(f"   - Top error types: {len(top_errors)}")
                for error_type in top_errors[:3]:
                    print(f"     * {error_type['error_type']}: {error_type['total_occurrences']} occurrences")
        
        # Test 4: Test the comprehensive test system
        print("\n📝 Test 4: Comprehensive system test")
        test_results = await error_integration.test_error_handling_system()
        
        print(f"   - Total tests: {test_results['test_summary']['total_tests']}")
        print(f"   - Passed tests: {test_results['test_summary']['passed_tests']}")
        print(f"   - Success rate: {test_results['test_summary']['success_rate']:.2%}")
        print(f"   - ML intelligence enabled: {test_results['test_summary']['ml_intelligence_enabled']}")
        print(f"   - System ready: {test_results['system_ready']}")
        
        # Check ML intelligence test specifically
        ml_test = test_results['test_results'].get('ml_intelligence_test', {})
        print(f"   - ML intelligence test passed: {ml_test.get('passed', False)}")
        
        return test_results['system_ready']
        
    except Exception as e:
        print(f"❌ ML error integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all integration tests."""
    print("🚀 Starting ML Error Intelligence Integration Tests")
    print("=" * 60)
    
    # Test imports first
    error_integration = test_imports()
    
    if error_integration:
        # Test ML error integration functionality
        integration_ok = asyncio.run(test_ml_error_integration(error_integration))
        
        # Test API endpoints
        test_api_endpoints()
        
        print("\n" + "=" * 60)
        if integration_ok:
            print("🎉 ML Error Intelligence Integration is working correctly!")
            print("✅ Error handling system enhanced with ML capabilities")
            print("✅ Pattern recognition and learning operational")
            print("✅ Cross-system integration successful")
        else:
            print("❌ Some ML integration components are having issues")
    else:
        print("\n❌ Integration issues exist - skipping ML tests")
    
    print(f"\n📅 Test completed at: {datetime.now()}")

if __name__ == "__main__":
    main()
