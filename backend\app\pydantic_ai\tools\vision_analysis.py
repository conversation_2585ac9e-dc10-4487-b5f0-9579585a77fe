"""
Vision Analysis Tools for Pydantic AI

This module provides comprehensive vision analysis capabilities including
screenshot analysis, UI component detection, and visual bug identification.
"""

import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
import asyncio

from pydantic_ai import RunContext
from ..dependencies import VisionDependencies
from ..image_utils import (
    encode_image_to_base64,
    create_data_url,
    get_image_info,
    validate_image_file,
    resize_image_if_needed
)

logger = logging.getLogger(__name__)


async def analyze_screenshot(
    ctx: RunContext[VisionDependencies],
    image_path: str,
    analysis_type: str = "general",
    focus_areas: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Analyze a screenshot for UI/UX issues, bugs, and improvements.
    
    Args:
        ctx: Pydantic AI run context with vision dependencies
        image_path: Path to the screenshot image
        analysis_type: Type of analysis (general, accessibility, layout, bugs)
        focus_areas: Specific areas to focus on (optional)
        
    Returns:
        Dict with analysis results and recommendations
    """
    try:
        # Validate image path
        image_file = Path(image_path)
        if not image_file.exists():
            return {
                "status": "error",
                "error": f"Image file not found: {image_path}",
                "error_type": "FileNotFoundError"
            }
        
        # Validate and encode image
        is_valid, validation_msg = validate_image_file(str(image_file))
        if not is_valid:
            return {
                "status": "error",
                "error": validation_msg,
                "error_type": "ValidationError"
            }

        # Encode image to base64
        image_base64 = encode_image_to_base64(str(image_file))
        if not image_base64:
            return {
                "status": "error",
                "error": "Failed to encode image",
                "error_type": "EncodingError"
            }
        
        # Build analysis prompt based on type
        prompts = {
            "general": "Analyze this screenshot for overall UI/UX quality, layout, and user experience. Identify any issues or improvements.",
            "accessibility": "Analyze this screenshot for accessibility issues including color contrast, text readability, button sizes, and navigation clarity.",
            "layout": "Analyze this screenshot for layout issues including alignment, spacing, responsive design, and visual hierarchy.",
            "bugs": "Analyze this screenshot for visual bugs including broken layouts, missing elements, overlapping content, and rendering issues."
        }
        
        prompt = prompts.get(analysis_type, prompts["general"])
        
        if focus_areas:
            prompt += f"\n\nPay special attention to these areas: {', '.join(focus_areas)}"
        
        # Use OpenRouter vision model for analysis
        analysis_result = await ctx.deps.openrouter_client.vision_analyze(
            image_base64=image_base64,
            prompt=prompt
        )
        
        # Get image metadata using our utility
        image_info = get_image_info(str(image_file))

        return {
            "status": "success",
            "analysis_type": analysis_type,
            "analysis_result": analysis_result,
            "image_metadata": image_info,
            "focus_areas": focus_areas or []
        }
        
    except Exception as e:
        logger.error(f"Failed to analyze screenshot {image_path}: {e}")
        return {
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__
        }


async def analyze_ui_component(
    ctx: RunContext[VisionDependencies],
    image_path: str,
    component_type: str,
    expected_behavior: Optional[str] = None
) -> Dict[str, Any]:
    """
    Analyze a specific UI component in an image.
    
    Args:
        ctx: Pydantic AI run context with vision dependencies
        image_path: Path to the image containing the UI component
        component_type: Type of component (button, form, menu, modal, etc.)
        expected_behavior: Expected behavior or appearance
        
    Returns:
        Dict with component analysis results
    """
    try:
        # Validate image path
        image_file = Path(image_path)
        if not image_file.exists():
            return {
                "status": "error",
                "error": f"Image file not found: {image_path}",
                "error_type": "FileNotFoundError"
            }
        
        # Validate and encode image
        is_valid, validation_msg = validate_image_file(str(image_file))
        if not is_valid:
            return {
                "status": "error",
                "error": validation_msg,
                "error_type": "ValidationError"
            }

        # Encode image to base64
        image_base64 = encode_image_to_base64(str(image_file))
        if not image_base64:
            return {
                "status": "error",
                "error": "Failed to encode image",
                "error_type": "EncodingError"
            }
        
        # Build component-specific analysis prompt
        prompt = f"""Analyze the {component_type} component in this image. 

Focus on:
1. Visual design and styling
2. Accessibility (contrast, size, clarity)
3. Usability and user experience
4. Consistency with design patterns
5. Any visual bugs or issues

Component type: {component_type}"""
        
        if expected_behavior:
            prompt += f"\nExpected behavior: {expected_behavior}"
        
        prompt += "\n\nProvide specific, actionable feedback for improvements."
        
        # Analyze with vision model
        analysis_result = await ctx.deps.openrouter_client.vision_analyze(
            image_base64=image_base64,
            prompt=prompt
        )
        
        return {
            "status": "success",
            "component_type": component_type,
            "analysis_result": analysis_result,
            "expected_behavior": expected_behavior,
            "image_path": str(image_file)
        }
        
    except Exception as e:
        logger.error(f"Failed to analyze UI component in {image_path}: {e}")
        return {
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__
        }


async def compare_screenshots(
    ctx: RunContext[VisionDependencies],
    before_image: str,
    after_image: str,
    comparison_type: str = "changes"
) -> Dict[str, Any]:
    """
    Compare two screenshots to identify differences.
    
    Args:
        ctx: Pydantic AI run context with vision dependencies
        before_image: Path to the "before" screenshot
        after_image: Path to the "after" screenshot
        comparison_type: Type of comparison (changes, improvements, regressions)
        
    Returns:
        Dict with comparison results
    """
    try:
        # Validate both image paths
        before_file = Path(before_image)
        after_file = Path(after_image)
        
        if not before_file.exists():
            return {
                "status": "error",
                "error": f"Before image not found: {before_image}",
                "error_type": "FileNotFoundError"
            }
        
        if not after_file.exists():
            return {
                "status": "error",
                "error": f"After image not found: {after_image}",
                "error_type": "FileNotFoundError"
            }
        
        # Validate and encode both images
        is_valid_before, msg_before = validate_image_file(str(before_file))
        if not is_valid_before:
            return {
                "status": "error",
                "error": f"Before image invalid: {msg_before}",
                "error_type": "ValidationError"
            }

        is_valid_after, msg_after = validate_image_file(str(after_file))
        if not is_valid_after:
            return {
                "status": "error",
                "error": f"After image invalid: {msg_after}",
                "error_type": "ValidationError"
            }

        # Encode both images
        before_base64 = encode_image_to_base64(str(before_file))
        after_base64 = encode_image_to_base64(str(after_file))

        if not before_base64 or not after_base64:
            return {
                "status": "error",
                "error": "Failed to encode one or both images",
                "error_type": "EncodingError"
            }
        
        # Build comparison prompt
        comparison_prompts = {
            "changes": "Compare these two screenshots and identify all visual changes, differences, and modifications.",
            "improvements": "Compare these two screenshots and identify improvements, enhancements, and positive changes.",
            "regressions": "Compare these two screenshots and identify any regressions, bugs, or negative changes."
        }
        
        prompt = comparison_prompts.get(comparison_type, comparison_prompts["changes"])
        prompt += """

Please analyze:
1. Layout changes
2. Content differences  
3. Color and styling changes
4. New or removed elements
5. Functional changes
6. Overall impact on user experience

Provide specific details about what changed and where."""
        
        # For comparison, we'll analyze both images separately and then compare
        # Note: This is a simplified approach - advanced comparison would need specialized tools
        
        before_analysis = await ctx.deps.openrouter_client.vision_analyze(
            image_base64=before_base64,
            prompt="Describe this screenshot in detail, focusing on layout, content, and visual elements."
        )
        
        after_analysis = await ctx.deps.openrouter_client.vision_analyze(
            image_base64=after_base64,
            prompt="Describe this screenshot in detail, focusing on layout, content, and visual elements."
        )
        
        # Generate comparison analysis
        comparison_prompt = f"""Based on these two image descriptions, identify the key differences:

BEFORE: {before_analysis}

AFTER: {after_analysis}

{prompt}"""
        
        comparison_result = await ctx.deps.openrouter_client.chat_completion(
            messages=[{"role": "user", "content": comparison_prompt}],
            model="google/gemini-2.0-flash-exp:free"  # Use vision model for comparison
        )
        
        return {
            "status": "success",
            "comparison_type": comparison_type,
            "before_image": str(before_file),
            "after_image": str(after_file),
            "before_analysis": before_analysis,
            "after_analysis": after_analysis,
            "comparison_result": comparison_result
        }
        
    except Exception as e:
        logger.error(f"Failed to compare screenshots: {e}")
        return {
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__
        }


async def generate_visual_report(
    ctx: RunContext[VisionDependencies],
    analysis_results: List[Dict[str, Any]],
    report_title: str = "Visual Analysis Report"
) -> Dict[str, Any]:
    """
    Generate a comprehensive visual analysis report.
    
    Args:
        ctx: Pydantic AI run context with vision dependencies
        analysis_results: List of analysis results from other vision tools
        report_title: Title for the report
        
    Returns:
        Dict with formatted report
    """
    try:
        # Compile all analysis data
        total_analyses = len(analysis_results)
        successful_analyses = sum(1 for result in analysis_results if result.get("status") == "success")
        failed_analyses = total_analyses - successful_analyses
        
        # Extract key findings
        findings = []
        
        for result in analysis_results:
            if result.get("status") == "success":
                analysis_text = result.get("analysis_result", "")
                if analysis_text:
                    findings.append({
                        "type": result.get("analysis_type", "unknown"),
                        "source": result.get("image_path", "unknown"),
                        "content": analysis_text[:500] + "..." if len(analysis_text) > 500 else analysis_text
                    })
        
        # Generate summary using AI
        summary_prompt = f"""Generate a comprehensive visual analysis summary based on these findings:

{chr(10).join([f"- {finding['type']}: {finding['content']}" for finding in findings])}

Provide:
1. Executive summary
2. Key issues identified
3. Priority recommendations
4. Overall assessment

Format as a professional report."""
        
        summary = await ctx.deps.openrouter_client.chat_completion(
            messages=[{"role": "user", "content": summary_prompt}],
            model="google/gemini-2.0-flash-exp:free"
        )
        
        return {
            "status": "success",
            "report_title": report_title,
            "summary": summary,
            "statistics": {
                "total_analyses": total_analyses,
                "successful_analyses": successful_analyses,
                "failed_analyses": failed_analyses,
                "success_rate": f"{(successful_analyses/total_analyses*100):.1f}%" if total_analyses > 0 else "0%"
            },
            "findings": findings,
            "raw_results": analysis_results
        }
        
    except Exception as e:
        logger.error(f"Failed to generate visual report: {e}")
        return {
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__
        }


# Export all vision analysis functions
__all__ = [
    'analyze_screenshot',
    'analyze_ui_component', 
    'compare_screenshots',
    'generate_visual_report'
]
