"""
Recovery Strategies for Auto-Recovery from Common Issues

Provides intelligent recovery mechanisms for different types of errors.
"""

import asyncio
import logging
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Union

from pydantic import BaseModel

# Import Pydantic AI exceptions with fallback for testing
try:
    from pydantic_ai import ModelRetry
except ImportError:
    # Fallback for testing or when pydantic_ai is not available
    class ModelRetry(Exception):
        def __init__(self, message: str):
            self.message = message
            super().__init__(message)

from .error_categorizer import ErrorCategory, ErrorContext, ErrorSeverity

logger = logging.getLogger(__name__)


class RecoveryAction(Enum):
    """Types of recovery actions."""
    RETRY_WITH_BACKOFF = "retry_with_backoff"
    SWITCH_MODEL = "switch_model"
    REDUCE_COMPLEXITY = "reduce_complexity"
    WAIT_AND_RETRY = "wait_and_retry"
    FALLBACK_STRATEGY = "fallback_strategy"
    USER_INTERVENTION = "user_intervention"
    CIRCUIT_BREAKER_RESET = "circuit_breaker_reset"
    CONTEXT_CLEANUP = "context_cleanup"
    PARAMETER_ADJUSTMENT = "parameter_adjustment"
    ALTERNATIVE_APPROACH = "alternative_approach"


class RecoveryStatus(Enum):
    """Status of recovery attempt."""
    SUCCESS = "success"
    PARTIAL_SUCCESS = "partial_success"
    FAILED = "failed"
    SKIPPED = "skipped"
    IN_PROGRESS = "in_progress"


@dataclass
class RecoveryResult:
    """Result of a recovery attempt."""
    action: RecoveryAction
    status: RecoveryStatus
    success: bool
    message: str
    
    # Timing information
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    duration: Optional[timedelta] = None
    
    # Recovery details
    original_error: Optional[Exception] = None
    recovered_result: Any = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def complete(self, status: RecoveryStatus, message: str, result: Any = None):
        """Mark recovery as complete."""
        self.status = status
        self.success = status in [RecoveryStatus.SUCCESS, RecoveryStatus.PARTIAL_SUCCESS]
        self.message = message
        self.recovered_result = result
        self.end_time = datetime.now()
        self.duration = self.end_time - self.start_time


class RecoveryStrategies:
    """Intelligent recovery strategies for different error types."""
    
    def __init__(self):
        self.recovery_history: List[RecoveryResult] = []
        self.fallback_models = [
            "deepseek/deepseek-r1-0528:free",
            "google/gemini-2.0-flash-exp:free",
            "anthropic/claude-3-haiku:beta"
        ]
        self.recovery_stats: Dict[str, Dict[str, int]] = {}
    
    async def apply_recovery(
        self, 
        error_context: ErrorContext,
        original_function: Optional[Callable] = None,
        function_args: Optional[tuple] = None,
        function_kwargs: Optional[Dict[str, Any]] = None
    ) -> RecoveryResult:
        """Apply appropriate recovery strategy based on error context."""
        
        # Determine best recovery action
        recovery_action = self._select_recovery_action(error_context)
        
        # Create recovery result
        result = RecoveryResult(
            action=recovery_action,
            status=RecoveryStatus.IN_PROGRESS,
            success=False,
            message=f"Starting {recovery_action.value} recovery",
            original_error=error_context.error
        )
        
        try:
            # Apply the recovery strategy
            if recovery_action == RecoveryAction.RETRY_WITH_BACKOFF:
                await self._retry_with_backoff(error_context, result, original_function, function_args, function_kwargs)
            
            elif recovery_action == RecoveryAction.SWITCH_MODEL:
                await self._switch_model(error_context, result, original_function, function_args, function_kwargs)
            
            elif recovery_action == RecoveryAction.REDUCE_COMPLEXITY:
                await self._reduce_complexity(error_context, result, original_function, function_args, function_kwargs)
            
            elif recovery_action == RecoveryAction.WAIT_AND_RETRY:
                await self._wait_and_retry(error_context, result, original_function, function_args, function_kwargs)
            
            elif recovery_action == RecoveryAction.FALLBACK_STRATEGY:
                await self._fallback_strategy(error_context, result, original_function, function_args, function_kwargs)
            
            elif recovery_action == RecoveryAction.CONTEXT_CLEANUP:
                await self._context_cleanup(error_context, result, original_function, function_args, function_kwargs)
            
            elif recovery_action == RecoveryAction.PARAMETER_ADJUSTMENT:
                await self._parameter_adjustment(error_context, result, original_function, function_args, function_kwargs)
            
            else:
                result.complete(
                    RecoveryStatus.SKIPPED,
                    f"No recovery strategy implemented for {recovery_action.value}"
                )
        
        except Exception as recovery_error:
            result.complete(
                RecoveryStatus.FAILED,
                f"Recovery failed: {recovery_error}",
            )
            logger.error(f"Recovery strategy {recovery_action.value} failed: {recovery_error}")
        
        # Record result
        self.recovery_history.append(result)
        self._update_stats(recovery_action, result.status)
        
        return result
    
    def _select_recovery_action(self, error_context: ErrorContext) -> RecoveryAction:
        """Select the most appropriate recovery action."""
        
        category = error_context.category
        severity = error_context.severity
        
        # High-priority recovery actions based on error category
        if category == ErrorCategory.API_RATE_LIMIT:
            return RecoveryAction.WAIT_AND_RETRY
        
        elif category == ErrorCategory.MODEL_RETRY:
            return RecoveryAction.PARAMETER_ADJUSTMENT
        
        elif category == ErrorCategory.MODEL_ERROR:
            if severity == ErrorSeverity.HIGH:
                return RecoveryAction.SWITCH_MODEL
            else:
                return RecoveryAction.RETRY_WITH_BACKOFF
        
        elif category == ErrorCategory.NETWORK_CONNECTION:
            return RecoveryAction.RETRY_WITH_BACKOFF
        
        elif category == ErrorCategory.NETWORK_TIMEOUT:
            return RecoveryAction.REDUCE_COMPLEXITY
        
        elif category == ErrorCategory.MODEL_CONTEXT:
            return RecoveryAction.CONTEXT_CLEANUP
        
        elif category == ErrorCategory.API_UNAVAILABLE:
            return RecoveryAction.FALLBACK_STRATEGY
        
        elif category == ErrorCategory.USER_INPUT:
            return RecoveryAction.USER_INTERVENTION
        
        else:
            return RecoveryAction.RETRY_WITH_BACKOFF
    
    async def _retry_with_backoff(
        self, 
        error_context: ErrorContext, 
        result: RecoveryResult,
        original_function: Optional[Callable],
        function_args: Optional[tuple],
        function_kwargs: Optional[Dict[str, Any]]
    ):
        """Retry with exponential backoff."""
        
        if not original_function:
            result.complete(RecoveryStatus.SKIPPED, "No function to retry")
            return
        
        max_retries = min(error_context.max_retries, 3)
        base_delay = 1.0
        
        for attempt in range(max_retries):
            delay = base_delay * (2 ** attempt)
            
            logger.info(f"Retry attempt {attempt + 1}/{max_retries} after {delay}s delay")
            await asyncio.sleep(delay)
            
            try:
                if asyncio.iscoroutinefunction(original_function):
                    recovered_result = await original_function(*(function_args or ()), **(function_kwargs or {}))
                else:
                    recovered_result = original_function(*(function_args or ()), **(function_kwargs or {}))
                
                result.complete(
                    RecoveryStatus.SUCCESS,
                    f"Retry successful after {attempt + 1} attempts",
                    recovered_result
                )
                return
                
            except Exception as retry_error:
                if attempt == max_retries - 1:
                    result.complete(
                        RecoveryStatus.FAILED,
                        f"All {max_retries} retry attempts failed: {retry_error}"
                    )
                else:
                    logger.warning(f"Retry attempt {attempt + 1} failed: {retry_error}")
    
    async def _switch_model(
        self, 
        error_context: ErrorContext, 
        result: RecoveryResult,
        original_function: Optional[Callable],
        function_args: Optional[tuple],
        function_kwargs: Optional[Dict[str, Any]]
    ):
        """Switch to a fallback model."""
        
        # This would need integration with the model management system
        # For now, we'll simulate the strategy
        
        for fallback_model in self.fallback_models:
            try:
                logger.info(f"Attempting recovery with fallback model: {fallback_model}")
                
                # In a real implementation, this would switch the model
                # and retry the operation
                
                # Simulate model switch success
                await asyncio.sleep(0.1)  # Simulate processing time
                
                result.complete(
                    RecoveryStatus.SUCCESS,
                    f"Successfully switched to fallback model: {fallback_model}",
                    {"model_switched": fallback_model}
                )
                return
                
            except Exception as switch_error:
                logger.warning(f"Fallback model {fallback_model} also failed: {switch_error}")
                continue
        
        result.complete(
            RecoveryStatus.FAILED,
            "All fallback models failed"
        )
    
    async def _reduce_complexity(
        self, 
        error_context: ErrorContext, 
        result: RecoveryResult,
        original_function: Optional[Callable],
        function_args: Optional[tuple],
        function_kwargs: Optional[Dict[str, Any]]
    ):
        """Reduce operation complexity to avoid timeouts."""
        
        # Strategies to reduce complexity:
        # 1. Break large operations into smaller chunks
        # 2. Reduce context length
        # 3. Simplify parameters
        
        logger.info("Applying complexity reduction strategies")
        
        # Simulate complexity reduction
        reduced_kwargs = (function_kwargs or {}).copy()
        
        # Example reductions
        if 'max_tokens' in reduced_kwargs:
            reduced_kwargs['max_tokens'] = min(reduced_kwargs['max_tokens'], 1000)
        
        if 'context_length' in reduced_kwargs:
            reduced_kwargs['context_length'] = min(reduced_kwargs['context_length'], 4000)
        
        result.complete(
            RecoveryStatus.PARTIAL_SUCCESS,
            "Complexity reduced - operation may have limited functionality",
            {"reduced_parameters": reduced_kwargs}
        )
    
    async def _wait_and_retry(
        self, 
        error_context: ErrorContext, 
        result: RecoveryResult,
        original_function: Optional[Callable],
        function_args: Optional[tuple],
        function_kwargs: Optional[Dict[str, Any]]
    ):
        """Wait for rate limits to reset and retry."""
        
        # Calculate wait time based on error type
        if error_context.category == ErrorCategory.API_RATE_LIMIT:
            wait_time = 60  # Wait 1 minute for rate limit reset
        else:
            wait_time = 30  # Default wait time
        
        logger.info(f"Waiting {wait_time}s for rate limit reset")
        await asyncio.sleep(wait_time)
        
        # Retry the operation
        if original_function:
            try:
                if asyncio.iscoroutinefunction(original_function):
                    recovered_result = await original_function(*(function_args or ()), **(function_kwargs or {}))
                else:
                    recovered_result = original_function(*(function_args or ()), **(function_kwargs or {}))
                
                result.complete(
                    RecoveryStatus.SUCCESS,
                    f"Retry successful after {wait_time}s wait",
                    recovered_result
                )
            except Exception as retry_error:
                result.complete(
                    RecoveryStatus.FAILED,
                    f"Retry failed after wait: {retry_error}"
                )
        else:
            result.complete(
                RecoveryStatus.PARTIAL_SUCCESS,
                f"Waited {wait_time}s - manual retry required"
            )
    
    async def _fallback_strategy(
        self, 
        error_context: ErrorContext, 
        result: RecoveryResult,
        original_function: Optional[Callable],
        function_args: Optional[tuple],
        function_kwargs: Optional[Dict[str, Any]]
    ):
        """Apply fallback strategy for service unavailability."""
        
        # Fallback strategies could include:
        # 1. Use cached results
        # 2. Use alternative service
        # 3. Provide degraded functionality
        
        logger.info("Applying fallback strategy")
        
        # Simulate fallback logic
        fallback_result = {
            "status": "fallback_active",
            "message": "Using fallback strategy due to service unavailability",
            "limited_functionality": True
        }
        
        result.complete(
            RecoveryStatus.PARTIAL_SUCCESS,
            "Fallback strategy applied - limited functionality available",
            fallback_result
        )
    
    async def _context_cleanup(
        self, 
        error_context: ErrorContext, 
        result: RecoveryResult,
        original_function: Optional[Callable],
        function_args: Optional[tuple],
        function_kwargs: Optional[Dict[str, Any]]
    ):
        """Clean up context to reduce memory/token usage."""
        
        logger.info("Performing context cleanup")
        
        # Simulate context cleanup
        cleanup_actions = [
            "Removed old conversation history",
            "Compressed context data",
            "Cleared temporary variables"
        ]
        
        result.complete(
            RecoveryStatus.SUCCESS,
            "Context cleanup completed",
            {"cleanup_actions": cleanup_actions}
        )
    
    async def _parameter_adjustment(
        self, 
        error_context: ErrorContext, 
        result: RecoveryResult,
        original_function: Optional[Callable],
        function_args: Optional[tuple],
        function_kwargs: Optional[Dict[str, Any]]
    ):
        """Adjust parameters based on ModelRetry guidance."""
        
        if isinstance(error_context.error, ModelRetry):
            guidance = error_context.error.message
            logger.info(f"Adjusting parameters based on model guidance: {guidance}")
            
            # Parse guidance and adjust parameters
            adjusted_kwargs = (function_kwargs or {}).copy()
            
            # Example parameter adjustments based on common guidance patterns
            if "too long" in guidance.lower():
                if 'max_length' in adjusted_kwargs:
                    adjusted_kwargs['max_length'] = min(adjusted_kwargs['max_length'], 500)
            
            if "invalid format" in guidance.lower():
                if 'format' in adjusted_kwargs:
                    adjusted_kwargs['format'] = 'simple'
            
            result.complete(
                RecoveryStatus.SUCCESS,
                f"Parameters adjusted based on guidance: {guidance}",
                {"adjusted_parameters": adjusted_kwargs}
            )
        else:
            result.complete(
                RecoveryStatus.SKIPPED,
                "No parameter adjustment guidance available"
            )
    
    def _update_stats(self, action: RecoveryAction, status: RecoveryStatus):
        """Update recovery statistics."""
        action_key = action.value
        if action_key not in self.recovery_stats:
            self.recovery_stats[action_key] = {
                'success': 0,
                'partial_success': 0,
                'failed': 0,
                'skipped': 0
            }
        
        if status == RecoveryStatus.SUCCESS:
            self.recovery_stats[action_key]['success'] += 1
        elif status == RecoveryStatus.PARTIAL_SUCCESS:
            self.recovery_stats[action_key]['partial_success'] += 1
        elif status == RecoveryStatus.FAILED:
            self.recovery_stats[action_key]['failed'] += 1
        elif status == RecoveryStatus.SKIPPED:
            self.recovery_stats[action_key]['skipped'] += 1
    
    def get_recovery_statistics(self) -> Dict[str, Any]:
        """Get recovery statistics."""
        total_attempts = len(self.recovery_history)
        if total_attempts == 0:
            return {"total_attempts": 0}
        
        successful_recoveries = sum(1 for r in self.recovery_history if r.success)
        success_rate = successful_recoveries / total_attempts
        
        return {
            "total_attempts": total_attempts,
            "successful_recoveries": successful_recoveries,
            "success_rate": success_rate,
            "action_stats": self.recovery_stats.copy(),
            "recent_recoveries": [
                {
                    "action": r.action.value,
                    "status": r.status.value,
                    "message": r.message,
                    "duration": r.duration.total_seconds() if r.duration else None
                }
                for r in self.recovery_history[-10:]  # Last 10 recoveries
            ]
        }
