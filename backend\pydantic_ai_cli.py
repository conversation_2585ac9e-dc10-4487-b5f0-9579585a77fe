#!/usr/bin/env python3
"""
Pydantic AI CLI Entry Point

Main command-line interface for the Pydantic AI system.
Provides access to all agent functionality, testing, and evaluation tools.

Usage:
    python pydantic_ai_cli.py --help
    python pydantic_ai_cli.py run "Write a hello world function"
    python pydantic_ai_cli.py test --dataset coding
    python pydantic_ai_cli.py benchmark --agent vision
    python pydantic_ai_cli.py interactive
"""

import sys
from pathlib import Path

# Add the backend directory to the path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.pydantic_ai.cli.interface import create_cli_app

if __name__ == '__main__':
    create_cli_app()
