interactions:
- request:
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '245'
      content-type:
      - application/json
      host:
      - generativelanguage.googleapis.com
    method: POST
    parsed_body:
      contents:
      - parts:
        - text: What is the capital of France?
        role: user
      generationConfig:
        thinkingConfig:
          includeThoughts: false
      systemInstruction:
        parts:
        - text: You are a helpful chatbot.
        role: user
    uri: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro-preview-03-25:generateContent
  response:
    headers:
      alt-svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      content-length:
      - '554'
      content-type:
      - application/json; charset=UTF-8
      server-timing:
      - gfet4t7; dur=1781
      transfer-encoding:
      - chunked
      vary:
      - Origin
      - X-Origin
      - Referer
    parsed_body:
      candidates:
      - content:
          parts:
          - text: The capital of France is **Paris**.
          role: model
        finishReason: STOP
        index: 0
      modelVersion: models/gemini-2.5-pro-preview-05-06
      usageMetadata:
        candidatesTokenCount: 8
        promptTokenCount: 15
        promptTokensDetails:
        - modality: TEXT
          tokenCount: 15
        thoughtsTokenCount: 100
        totalTokenCount: 123
    status:
      code: 200
      message: OK
version: 1
