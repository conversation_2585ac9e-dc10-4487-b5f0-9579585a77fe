interactions:
- request:
    headers:
      accept:
      - application/json
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '267'
      content-type:
      - application/json
      host:
      - api.anthropic.com
    method: POST
    parsed_body:
      max_tokens: 1024
      messages:
      - content:
        - text: What is the main content on this document?
          type: text
        - source:
            type: url
            url: https://pdfobject.com/pdf/sample.pdf
          type: document
        role: user
      model: claude-3-5-sonnet-latest
      stream: false
    uri: https://api.anthropic.com/v1/messages?beta=true
  response:
    headers:
      connection:
      - keep-alive
      content-length:
      - '813'
      content-type:
      - application/json
      strict-transport-security:
      - max-age=31536000; includeSubDomains; preload
      transfer-encoding:
      - chunked
    parsed_body:
      content:
      - text: This document appears to be a sample PDF file that contains Lorem ipsum text, which is placeholder text commonly
          used in design and publishing. The document starts with "Sample PDF" and includes the line "This is a simple PDF
          file. Fun fun fun." followed by several paragraphs of Lorem ipsum text. Lorem ipsum is dummy text that has no meaningful
          content - it's typically used to demonstrate the visual form of a document or typeface without the distraction of
          meaningful content.
        type: text
      id: msg_015t94iUurjyZ7Jvi7kvDe5b
      model: claude-3-5-sonnet-20241022
      role: assistant
      stop_reason: end_turn
      stop_sequence: null
      type: message
      usage:
        cache_creation_input_tokens: 0
        cache_read_input_tokens: 0
        input_tokens: 2682
        output_tokens: 98
        service_tier: standard
    status:
      code: 200
      message: OK
version: 1
